'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  MapPin, 
  Navigation, 
  Bell, 
  Settings,
  X,
  Package,
  TrendingUp,
  AlertTriangle,
  Store,
  Clock,
  Route,
  Zap
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Slider } from '@/components/ui/slider'
import { toast } from 'sonner'

interface LocationData {
  latitude: number
  longitude: number
  accuracy: number
  timestamp: number
}

interface GeolocationNotification {
  id: string
  type: 'stock' | 'price' | 'store' | 'event' | 'alert'
  title: string
  message: string
  location: {
    name: string
    address: string
    latitude: number
    longitude: number
    distance: number // en km
  }
  priority: 'low' | 'medium' | 'high' | 'critical'
  timestamp: string
  actionUrl?: string
  metadata?: {
    productName?: string
    price?: number
    discount?: number
    stock?: number
    storeName?: string
  }
}

interface GeolocationSettings {
  enabled: boolean
  radius: number // en km
  types: {
    stock: boolean
    price: boolean
    store: boolean
    event: boolean
    alert: boolean
  }
  priorities: {
    low: boolean
    medium: boolean
    high: boolean
    critical: boolean
  }
  autoNotify: boolean
  soundEnabled: boolean
}

interface GeolocationNotificationsProps {
  className?: string
}

export default function GeolocationNotifications({ className = '' }: GeolocationNotificationsProps) {
  const [currentLocation, setCurrentLocation] = useState<LocationData | null>(null)
  const [notifications, setNotifications] = useState<GeolocationNotification[]>([])
  const [settings, setSettings] = useState<GeolocationSettings>({
    enabled: true,
    radius: 10,
    types: {
      stock: true,
      price: true,
      store: true,
      event: true,
      alert: true
    },
    priorities: {
      low: false,
      medium: true,
      high: true,
      critical: true
    },
    autoNotify: true,
    soundEnabled: true
  })
  const [isTracking, setIsTracking] = useState(false)
  const [showSettings, setShowSettings] = useState(false)
  const [permissionStatus, setPermissionStatus] = useState<'granted' | 'denied' | 'prompt'>('prompt')

  // Données d'exemple pour les notifications géolocalisées
  const sampleNotifications: Omit<GeolocationNotification, 'id' | 'timestamp' | 'distance'>[] = [
    {
      type: 'stock',
      title: 'Stock disponible à proximité',
      message: 'Disjoncteurs C60N 32A en stock chez CFAO Abidjan',
      location: {
        name: 'CFAO Abidjan',
        address: 'Boulevard Lagunaire, Abidjan',
        latitude: 5.3364,
        longitude: -4.0267
      },
      priority: 'medium',
      metadata: {
        productName: 'Disjoncteur C60N 32A',
        stock: 150,
        storeName: 'CFAO Abidjan'
      }
    },
    {
      type: 'price',
      title: 'Promotion exceptionnelle',
      message: 'Câbles électriques -20% chez ElectroDistrib',
      location: {
        name: 'ElectroDistrib',
        address: 'Zone Industrielle, Abidjan',
        latitude: 5.3456,
        longitude: -4.0123
      },
      priority: 'high',
      metadata: {
        productName: 'Câbles électriques',
        discount: 20,
        storeName: 'ElectroDistrib'
      }
    },
    {
      type: 'event',
      title: 'Formation technique',
      message: 'Formation NF C 15-100 demain à 9h',
      location: {
        name: 'Centre de Formation Schneider',
        address: 'Plateau, Abidjan',
        latitude: 5.3198,
        longitude: -4.0267
      },
      priority: 'medium'
    },
    {
      type: 'alert',
      title: 'Alerte sécurité',
      message: 'Rappel produit : Prises défectueuses série 752xx',
      location: {
        name: 'Magasin Legrand',
        address: 'Marcory, Abidjan',
        latitude: 5.2867,
        longitude: -3.9889
      },
      priority: 'critical'
    }
  ]

  // Demander la permission de géolocalisation
  const requestLocationPermission = async () => {
    try {
      if (!navigator.geolocation) {
        toast.error('Géolocalisation non supportée par ce navigateur')
        return
      }

      const permission = await navigator.permissions.query({ name: 'geolocation' })
      setPermissionStatus(permission.state)

      if (permission.state === 'granted') {
        startTracking()
      } else if (permission.state === 'prompt') {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            setPermissionStatus('granted')
            updateLocation(position)
            startTracking()
          },
          (error) => {
            console.error('Erreur géolocalisation:', error)
            setPermissionStatus('denied')
            toast.error('Permission de géolocalisation refusée')
          }
        )
      }
    } catch (error) {
      console.error('Erreur permission:', error)
      toast.error('Erreur lors de la demande de permission')
    }
  }

  // Mettre à jour la position
  const updateLocation = (position: GeolocationPosition) => {
    const locationData: LocationData = {
      latitude: position.coords.latitude,
      longitude: position.coords.longitude,
      accuracy: position.coords.accuracy,
      timestamp: Date.now()
    }
    setCurrentLocation(locationData)
    checkNearbyNotifications(locationData)
  }

  // Démarrer le suivi de position
  const startTracking = () => {
    if (!navigator.geolocation || !settings.enabled) return

    setIsTracking(true)
    
    const watchId = navigator.geolocation.watchPosition(
      updateLocation,
      (error) => {
        console.error('Erreur suivi position:', error)
        setIsTracking(false)
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 60000
      }
    )

    return () => {
      navigator.geolocation.clearWatch(watchId)
      setIsTracking(false)
    }
  }

  // Calculer la distance entre deux points
  const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
    const R = 6371 // Rayon de la Terre en km
    const dLat = (lat2 - lat1) * Math.PI / 180
    const dLon = (lon2 - lon1) * Math.PI / 180
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
      Math.sin(dLon/2) * Math.sin(dLon/2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
    return R * c
  }

  // Vérifier les notifications à proximité
  const checkNearbyNotifications = (location: LocationData) => {
    const nearbyNotifications = sampleNotifications
      .map(notif => {
        const distance = calculateDistance(
          location.latitude,
          location.longitude,
          notif.location.latitude,
          notif.location.longitude
        )
        
        return {
          ...notif,
          id: `${notif.type}-${Date.now()}-${Math.random()}`,
          timestamp: new Date().toISOString(),
          location: {
            ...notif.location,
            distance
          }
        }
      })
      .filter(notif => {
        // Filtrer par distance
        if (notif.location.distance > settings.radius) return false
        
        // Filtrer par type
        if (!settings.types[notif.type]) return false
        
        // Filtrer par priorité
        if (!settings.priorities[notif.priority]) return false
        
        return true
      })
      .sort((a, b) => a.location.distance - b.location.distance)

    // Ajouter les nouvelles notifications
    setNotifications(prev => {
      const existingIds = prev.map(n => n.id)
      const newNotifications = nearbyNotifications.filter(n => !existingIds.includes(n.id))
      
      if (newNotifications.length > 0 && settings.autoNotify) {
        newNotifications.forEach(notif => {
          if (settings.soundEnabled) {
            // Jouer un son de notification
            const audio = new Audio('/notification-sound.mp3')
            audio.play().catch(() => {})
          }
          
          toast.info(notif.title, {
            description: `${notif.message} (${notif.location.distance.toFixed(1)}km)`,
            action: notif.actionUrl ? {
              label: 'Voir',
              onClick: () => window.location.href = notif.actionUrl!
            } : undefined
          })
        })
      }
      
      return [...newNotifications, ...prev].slice(0, 20) // Garder max 20 notifications
    })
  }

  // Initialiser la géolocalisation
  useEffect(() => {
    if (settings.enabled && permissionStatus === 'granted') {
      startTracking()
    }
  }, [settings.enabled])

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'stock': return <Package className="h-4 w-4" />
      case 'price': return <TrendingUp className="h-4 w-4" />
      case 'store': return <Store className="h-4 w-4" />
      case 'event': return <Clock className="h-4 w-4" />
      case 'alert': return <AlertTriangle className="h-4 w-4" />
      default: return <Bell className="h-4 w-4" />
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200'
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'medium': return 'bg-blue-100 text-blue-800 border-blue-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'stock': return 'text-green-600'
      case 'price': return 'text-amber-600'
      case 'store': return 'text-blue-600'
      case 'event': return 'text-purple-600'
      case 'alert': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header avec contrôles */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-400 to-blue-500 rounded-lg flex items-center justify-center">
                <MapPin className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle>Notifications Géolocalisées</CardTitle>
                <CardDescription>
                  Alertes basées sur votre position
                  {currentLocation && (
                    <span className="ml-2 text-green-600">
                      • Position active ({currentLocation.accuracy.toFixed(0)}m)
                    </span>
                  )}
                </CardDescription>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowSettings(!showSettings)}
              >
                <Settings className="h-4 w-4" />
              </Button>
              
              <Button
                onClick={requestLocationPermission}
                disabled={isTracking}
                className={isTracking ? 'bg-green-500 hover:bg-green-600' : 'bg-blue-500 hover:bg-blue-600'}
              >
                <Navigation className="h-4 w-4 mr-2" />
                {isTracking ? 'Actif' : 'Activer'}
              </Button>
            </div>
          </div>
        </CardHeader>

        {/* Paramètres */}
        {showSettings && (
          <CardContent className="border-t border-gray-200 bg-gray-50">
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-900 mb-2 block">
                    Rayon de détection: {settings.radius}km
                  </label>
                  <Slider
                    value={[settings.radius]}
                    onValueChange={(value) => setSettings(prev => ({ ...prev, radius: value[0] }))}
                    max={50}
                    min={1}
                    step={1}
                    className="w-full"
                  />
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Notifications automatiques</span>
                    <Switch
                      checked={settings.autoNotify}
                      onCheckedChange={(checked) => setSettings(prev => ({ ...prev, autoNotify: checked }))}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Son</span>
                    <Switch
                      checked={settings.soundEnabled}
                      onCheckedChange={(checked) => setSettings(prev => ({ ...prev, soundEnabled: checked }))}
                    />
                  </div>
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-2">Types de notifications</h4>
                <div className="grid grid-cols-5 gap-2">
                  {Object.entries(settings.types).map(([type, enabled]) => (
                    <div key={type} className="flex items-center space-x-2">
                      <Switch
                        checked={enabled}
                        onCheckedChange={(checked) => 
                          setSettings(prev => ({ 
                            ...prev, 
                            types: { ...prev.types, [type]: checked }
                          }))
                        }
                      />
                      <span className="text-xs capitalize">{type}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        )}
      </Card>

      {/* Liste des notifications */}
      <div className="space-y-3">
        {notifications.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <MapPin className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Aucune notification à proximité</h3>
              <p className="text-gray-600 mb-4">
                {permissionStatus === 'denied'
                  ? 'Activez la géolocalisation pour recevoir des notifications basées sur votre position'
                  : 'Déplacez-vous pour découvrir des offres et informations près de vous'
                }
              </p>
              {permissionStatus === 'denied' && (
                <Button onClick={requestLocationPermission}>
                  <Navigation className="h-4 w-4 mr-2" />
                  Activer la géolocalisation
                </Button>
              )}
            </CardContent>
          </Card>
        ) : (
          notifications.map((notification) => (
            <motion.div
              key={notification.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              className="group"
            >
              <Card className="hover:shadow-md transition-shadow cursor-pointer">
                <CardContent className="p-4">
                  <div className="flex items-start space-x-4">
                    <div className={`p-2 rounded-lg ${getTypeColor(notification.type)} bg-opacity-10`}>
                      {getTypeIcon(notification.type)}
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between mb-2">
                        <div>
                          <h4 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                            {notification.title}
                          </h4>
                          <p className="text-sm text-gray-600 mt-1">
                            {notification.message}
                          </p>
                        </div>

                        <div className="flex items-center space-x-2 ml-4">
                          <Badge className={getPriorityColor(notification.priority)}>
                            {notification.priority}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {notification.location.distance.toFixed(1)}km
                          </Badge>
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                          <div className="flex items-center space-x-1">
                            <Store className="h-3 w-3" />
                            <span>{notification.location.name}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <MapPin className="h-3 w-3" />
                            <span>{notification.location.address}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Clock className="h-3 w-3" />
                            <span>
                              {new Date(notification.timestamp).toLocaleTimeString('fr-FR', {
                                hour: '2-digit',
                                minute: '2-digit'
                              })}
                            </span>
                          </div>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              const url = `https://maps.google.com/maps?q=${notification.location.latitude},${notification.location.longitude}`
                              window.open(url, '_blank')
                            }}
                          >
                            <Route className="h-3 w-3 mr-1" />
                            Itinéraire
                          </Button>

                          {notification.actionUrl && (
                            <Button
                              size="sm"
                              onClick={() => window.location.href = notification.actionUrl!}
                              className="bg-amber-500 hover:bg-amber-600"
                            >
                              Voir détails
                            </Button>
                          )}
                        </div>
                      </div>

                      {/* Métadonnées spécifiques */}
                      {notification.metadata && (
                        <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            {notification.metadata.productName && (
                              <div>
                                <span className="text-gray-600">Produit :</span>
                                <span className="ml-2 font-medium">{notification.metadata.productName}</span>
                              </div>
                            )}
                            {notification.metadata.price && (
                              <div>
                                <span className="text-gray-600">Prix :</span>
                                <span className="ml-2 font-medium">{notification.metadata.price.toLocaleString()} FCFA</span>
                              </div>
                            )}
                            {notification.metadata.discount && (
                              <div>
                                <span className="text-gray-600">Remise :</span>
                                <span className="ml-2 font-medium text-green-600">-{notification.metadata.discount}%</span>
                              </div>
                            )}
                            {notification.metadata.stock && (
                              <div>
                                <span className="text-gray-600">Stock :</span>
                                <span className="ml-2 font-medium">{notification.metadata.stock} unités</span>
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))
        )}
      </div>

      {/* Statut de géolocalisation */}
      {currentLocation && (
        <Card className="bg-green-50 border-green-200">
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                <Navigation className="h-4 w-4 text-white" />
              </div>
              <div>
                <h4 className="font-medium text-green-900">Géolocalisation active</h4>
                <p className="text-sm text-green-700">
                  Position mise à jour • Précision: {currentLocation.accuracy.toFixed(0)}m •
                  Rayon de détection: {settings.radius}km
                </p>
              </div>
              <div className="ml-auto">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
