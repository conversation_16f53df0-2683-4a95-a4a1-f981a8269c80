'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { toast } from 'sonner'
import { initializeSupabaseStorage, checkStorageStatus } from '@/lib/scripts/initStorage'
import { FileService } from '@/lib/services/fileService'
import { RefreshCw, Database, CheckCircle, XCircle, Settings } from 'lucide-react'

export default function StorageAdminPage() {
  const [loading, setLoading] = useState(false)
  const [checking, setChecking] = useState(false)
  const [bucketStatus, setBucketStatus] = useState<Record<string, boolean>>({})

  const handleInitialize = async () => {
    setLoading(true)
    toast.loading('Initialisation du stockage...', { id: 'storage-init' })

    try {
      const result = await initializeSupabaseStorage()
      
      if (result.success) {
        toast.success('Stockage initialisé avec succès', { id: 'storage-init' })
        await handleCheckStatus()
      } else {
        toast.error('Erreur lors de l\'initialisation', { id: 'storage-init' })
      }
    } catch (error) {
      console.error('Erreur initialisation:', error)
      toast.error('Erreur lors de l\'initialisation', { id: 'storage-init' })
    } finally {
      setLoading(false)
    }
  }

  const handleCheckStatus = async () => {
    setChecking(true)
    toast.loading('Vérification du statut...', { id: 'storage-check' })

    try {
      const status = await checkStorageStatus()
      setBucketStatus(status)
      toast.success('Statut vérifié', { id: 'storage-check' })
    } catch (error) {
      console.error('Erreur vérification:', error)
      toast.error('Erreur lors de la vérification', { id: 'storage-check' })
    } finally {
      setChecking(false)
    }
  }

  const buckets = Object.values(FileService.BUCKETS)
  const allBucketsOk = buckets.every(bucket => bucketStatus[bucket])

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Administration du Stockage</h1>
          <p className="text-gray-600 mt-2">
            Gestion des buckets Supabase Storage pour l'application
          </p>
        </div>
        
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleCheckStatus}
            disabled={checking}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${checking ? 'animate-spin' : ''}`} />
            Vérifier le statut
          </Button>
          
          <Button
            onClick={handleInitialize}
            disabled={loading}
          >
            <Database className={`h-4 w-4 mr-2 ${loading ? 'animate-pulse' : ''}`} />
            Initialiser le stockage
          </Button>
        </div>
      </div>

      {/* Statut global */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Statut Global du Stockage
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            {Object.keys(bucketStatus).length === 0 ? (
              <Badge variant="secondary">Non vérifié</Badge>
            ) : allBucketsOk ? (
              <Badge variant="default" className="bg-green-500">
                <CheckCircle className="h-4 w-4 mr-1" />
                Tous les buckets sont opérationnels
              </Badge>
            ) : (
              <Badge variant="destructive">
                <XCircle className="h-4 w-4 mr-1" />
                Certains buckets nécessitent une attention
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Détail des buckets */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {buckets.map(bucket => {
          const isOk = bucketStatus[bucket]
          const isChecked = bucket in bucketStatus
          
          return (
            <Card key={bucket}>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center justify-between">
                  {bucket}
                  {isChecked && (
                    isOk ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-500" />
                    )
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="text-sm text-gray-600">
                    <strong>Usage:</strong>
                    {bucket === 'validations' && ' Fichiers de validation technique'}
                    {bucket === 'kits' && ' Kits de prescription'}
                    {bucket === 'avatars' && ' Photos de profil'}
                    {bucket === 'documents' && ' Documents généraux'}
                  </div>
                  
                  <div className="text-sm">
                    <strong>Statut:</strong>{' '}
                    {!isChecked ? (
                      <Badge variant="secondary">Non vérifié</Badge>
                    ) : isOk ? (
                      <Badge variant="default" className="bg-green-500">Opérationnel</Badge>
                    ) : (
                      <Badge variant="destructive">Erreur</Badge>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Informations techniques */}
      <Card>
        <CardHeader>
          <CardTitle>Informations Techniques</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-semibold mb-2">Buckets configurés :</h4>
            <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
              <li><strong>validations</strong> : Stockage des fichiers de validation technique (PDF, images)</li>
              <li><strong>kits</strong> : Stockage des kits de prescription téléchargeables</li>
              <li><strong>avatars</strong> : Photos de profil des utilisateurs</li>
              <li><strong>documents</strong> : Documents généraux et fichiers divers</li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-semibold mb-2">Types de fichiers autorisés :</h4>
            <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
              <li><strong>Images :</strong> JPEG, PNG, WebP (max 5MB)</li>
              <li><strong>Documents :</strong> PDF, Word (max 10MB)</li>
              <li><strong>Kits :</strong> PDF, Word (max 50MB)</li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-semibold mb-2">Sécurité :</h4>
            <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
              <li>Tous les buckets sont publics pour faciliter l'accès</li>
              <li>Les permissions sont gérées au niveau applicatif</li>
              <li>Les fichiers sont organisés par utilisateur et type</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
