// Service de génération de badges SVG dynamiques

export interface BadgeConfig {
  userName: string
  userRole: 'member' | 'vip' | 'admin'
  memberSince: Date
  specialties?: string[]
  achievements?: string[]
  customText?: string
}

export interface BadgeStyle {
  primaryColor: string
  secondaryColor: string
  accentColor: string
  textColor: string
  backgroundColor: string
}

export class BadgeService {
  // Styles prédéfinis par rôle
  static readonly ROLE_STYLES: Record<string, BadgeStyle> = {
    member: {
      primaryColor: '#3B82F6',
      secondaryColor: '#1E40AF',
      accentColor: '#60A5FA',
      textColor: '#FFFFFF',
      backgroundColor: '#F8FAFC'
    },
    vip: {
      primaryColor: '#F59E0B',
      secondaryColor: '#D97706',
      accentColor: '#FCD34D',
      textColor: '#FFFFFF',
      backgroundColor: '#FFFBEB'
    },
    admin: {
      primaryColor: '#DC2626',
      secondaryColor: '#B91C1C',
      accentColor: '#F87171',
      textColor: '#FFFFFF',
      backgroundColor: '#FEF2F2'
    }
  }

  /**
   * Génère un badge SVG personnalisé
   */
  static generateBadge(config: BadgeConfig): string {
    const style = BadgeService.ROLE_STYLES[config.userRole]
    const membershipDuration = BadgeService.calculateMembershipDuration(config.memberSince)
    
    const svg = `
    <svg width="400" height="250" viewBox="0 0 400 250" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <!-- Gradients -->
        <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:${style.primaryColor};stop-opacity:1" />
          <stop offset="100%" style="stop-color:${style.secondaryColor};stop-opacity:1" />
        </linearGradient>
        
        <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" style="stop-color:${style.accentColor};stop-opacity:0.8" />
          <stop offset="100%" style="stop-color:${style.primaryColor};stop-opacity:0.8" />
        </linearGradient>
        
        <!-- Filtres pour les effets -->
        <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
          <feDropShadow dx="2" dy="4" stdDeviation="3" flood-color="rgba(0,0,0,0.3)"/>
        </filter>
        
        <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
          <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
          <feMerge> 
            <feMergeNode in="coloredBlur"/>
            <feMergeNode in="SourceGraphic"/>
          </feMerge>
        </filter>
      </defs>
      
      <!-- Fond principal -->
      <rect width="400" height="250" rx="20" ry="20" fill="${style.backgroundColor}" stroke="url(#primaryGradient)" stroke-width="3" filter="url(#shadow)"/>
      
      <!-- Header avec dégradé -->
      <rect x="0" y="0" width="400" height="80" rx="20" ry="20" fill="url(#primaryGradient)"/>
      <rect x="0" y="60" width="400" height="20" fill="url(#primaryGradient)"/>
      
      <!-- Logo/Icône du rôle -->
      ${BadgeService.getRoleIcon(config.userRole, 30, 25)}
      
      <!-- Titre du badge -->
      <text x="80" y="35" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="${style.textColor}">
        PRO MATOS AFRIQUE OUEST
      </text>
      <text x="80" y="55" font-family="Arial, sans-serif" font-size="14" fill="${style.textColor}" opacity="0.9">
        ${BadgeService.getRoleTitle(config.userRole)}
      </text>
      
      <!-- Nom de l'utilisateur -->
      <text x="20" y="110" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="${style.primaryColor}">
        ${config.userName}
      </text>
      
      <!-- Informations de membership -->
      <text x="20" y="135" font-family="Arial, sans-serif" font-size="12" fill="#6B7280">
        Membre depuis ${membershipDuration}
      </text>
      
      <!-- Spécialités (si présentes) -->
      ${config.specialties && config.specialties.length > 0 ? `
        <text x="20" y="155" font-family="Arial, sans-serif" font-size="10" fill="#6B7280">
          Spécialités: ${config.specialties.slice(0, 3).join(', ')}
        </text>
      ` : ''}
      
      <!-- Barre décorative -->
      <rect x="20" y="170" width="360" height="3" rx="1.5" fill="url(#accentGradient)"/>
      
      <!-- Achievements/Badges -->
      ${BadgeService.generateAchievements(config.achievements || [], style)}
      
      <!-- Texte personnalisé -->
      ${config.customText ? `
        <text x="20" y="220" font-family="Arial, sans-serif" font-size="10" fill="${style.primaryColor}" font-style="italic">
          "${config.customText}"
        </text>
      ` : ''}
      
      <!-- QR Code placeholder (optionnel) -->
      <rect x="320" y="180" width="60" height="60" rx="5" fill="${style.backgroundColor}" stroke="${style.primaryColor}" stroke-width="1" opacity="0.3"/>
      <text x="350" y="215" font-family="Arial, sans-serif" font-size="8" text-anchor="middle" fill="${style.primaryColor}" opacity="0.7">
        QR
      </text>
      
      <!-- Watermark -->
      <text x="380" y="245" font-family="Arial, sans-serif" font-size="8" text-anchor="end" fill="#9CA3AF" opacity="0.5">
        promatos.com
      </text>
    </svg>`
    
    return svg.trim()
  }

  /**
   * Génère une version compacte du badge
   */
  static generateCompactBadge(config: BadgeConfig): string {
    const style = BadgeService.ROLE_STYLES[config.userRole]
    
    const svg = `
    <svg width="200" height="60" viewBox="0 0 200 60" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="compactGradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" style="stop-color:${style.primaryColor};stop-opacity:1" />
          <stop offset="100%" style="stop-color:${style.secondaryColor};stop-opacity:1" />
        </linearGradient>
      </defs>
      
      <!-- Fond -->
      <rect width="200" height="60" rx="30" fill="url(#compactGradient)"/>
      
      <!-- Icône -->
      ${BadgeService.getRoleIcon(config.userRole, 15, 15, 'compact')}
      
      <!-- Texte -->
      <text x="45" y="25" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="${style.textColor}">
        ${config.userName}
      </text>
      <text x="45" y="40" font-family="Arial, sans-serif" font-size="10" fill="${style.textColor}" opacity="0.9">
        ${BadgeService.getRoleTitle(config.userRole)}
      </text>
    </svg>`
    
    return svg.trim()
  }

  /**
   * Génère les icônes selon le rôle
   */
  private static getRoleIcon(role: string, x: number, y: number, variant: 'full' | 'compact' = 'full'): string {
    const size = variant === 'compact' ? 20 : 30
    const color = '#FFFFFF'
    
    switch (role) {
      case 'vip':
        return `
          <g transform="translate(${x}, ${y})">
            <polygon points="${size/2},0 ${size*0.6},${size*0.4} ${size},${size*0.4} ${size*0.7},${size*0.65} ${size*0.8},${size} ${size/2},${size*0.8} ${size*0.2},${size} ${size*0.3},${size*0.65} 0,${size*0.4} ${size*0.4},${size*0.4}" 
                     fill="${color}" opacity="0.9"/>
          </g>`
      case 'admin':
        return `
          <g transform="translate(${x}, ${y})">
            <rect x="0" y="${size*0.3}" width="${size}" height="${size*0.4}" fill="${color}" opacity="0.9"/>
            <circle cx="${size/2}" cy="${size*0.25}" r="${size*0.2}" fill="${color}" opacity="0.9"/>
          </g>`
      default: // member
        return `
          <g transform="translate(${x}, ${y})">
            <circle cx="${size/2}" cy="${size/2}" r="${size*0.4}" fill="none" stroke="${color}" stroke-width="3" opacity="0.9"/>
            <circle cx="${size/2}" cy="${size/2}" r="${size*0.15}" fill="${color}" opacity="0.9"/>
          </g>`
    }
  }

  /**
   * Retourne le titre selon le rôle
   */
  private static getRoleTitle(role: string): string {
    switch (role) {
      case 'vip': return 'Membre VIP'
      case 'admin': return 'Administrateur'
      default: return 'Membre Professionnel'
    }
  }

  /**
   * Calcule la durée d'adhésion
   */
  private static calculateMembershipDuration(memberSince: Date): string {
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - memberSince.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    if (diffDays < 30) {
      return `${diffDays} jour${diffDays > 1 ? 's' : ''}`
    } else if (diffDays < 365) {
      const months = Math.floor(diffDays / 30)
      return `${months} mois`
    } else {
      const years = Math.floor(diffDays / 365)
      const remainingMonths = Math.floor((diffDays % 365) / 30)
      return `${years} an${years > 1 ? 's' : ''}${remainingMonths > 0 ? ` et ${remainingMonths} mois` : ''}`
    }
  }

  /**
   * Génère les achievements/badges
   */
  private static generateAchievements(achievements: string[], style: BadgeStyle): string {
    if (achievements.length === 0) return ''
    
    const maxAchievements = 4
    const displayAchievements = achievements.slice(0, maxAchievements)
    
    return displayAchievements.map((achievement, index) => {
      const x = 20 + (index * 90)
      const y = 185
      
      return `
        <rect x="${x}" y="${y}" width="80" height="20" rx="10" fill="${style.accentColor}" opacity="0.3"/>
        <text x="${x + 40}" y="${y + 14}" font-family="Arial, sans-serif" font-size="8" text-anchor="middle" fill="${style.primaryColor}">
          ${achievement.substring(0, 12)}${achievement.length > 12 ? '...' : ''}
        </text>
      `
    }).join('')
  }

  /**
   * Convertit le SVG en Data URL pour utilisation directe
   */
  static svgToDataUrl(svg: string): string {
    const encoded = encodeURIComponent(svg)
    return `data:image/svg+xml,${encoded}`
  }

  /**
   * Génère un badge pour téléchargement
   */
  static generateDownloadableBadge(config: BadgeConfig, format: 'svg' | 'png' = 'svg'): string {
    const svg = BadgeService.generateBadge(config)
    
    if (format === 'svg') {
      return svg
    }
    
    // Pour PNG, il faudrait utiliser une librairie comme sharp ou canvas
    // Pour l'instant, retourner le SVG
    return svg
  }

  /**
   * Valide la configuration du badge
   */
  static validateConfig(config: BadgeConfig): { valid: boolean; errors: string[] } {
    const errors: string[] = []
    
    if (!config.userName || config.userName.trim().length === 0) {
      errors.push('Le nom d\'utilisateur est requis')
    }
    
    if (config.userName && config.userName.length > 50) {
      errors.push('Le nom d\'utilisateur ne peut pas dépasser 50 caractères')
    }
    
    if (!['member', 'vip', 'admin'].includes(config.userRole)) {
      errors.push('Le rôle doit être member, vip ou admin')
    }
    
    if (!config.memberSince || config.memberSince > new Date()) {
      errors.push('La date d\'adhésion doit être valide et dans le passé')
    }
    
    if (config.customText && config.customText.length > 100) {
      errors.push('Le texte personnalisé ne peut pas dépasser 100 caractères')
    }
    
    return {
      valid: errors.length === 0,
      errors
    }
  }
}
