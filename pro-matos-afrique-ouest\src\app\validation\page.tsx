'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Upload, 
  FileText, 
  CheckCircle, 
  Clock,
  AlertTriangle,
  ArrowLeft,
  History,
  Send
} from 'lucide-react'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { toast } from 'sonner'
import { useStore } from '@/store/useStore'
import { FileService, formatFileSize } from '@/lib/services/fileService'
import GlobalNavigation from '@/components/navigation/GlobalNavigation'

export default function ValidationPage() {
  const { user } = useStore()
  const [activeTab, setActiveTab] = useState<'upload' | 'history'>('upload')
  const [isUploading, setIsUploading] = useState(false)
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    projectType: '',
    urgency: 'normal' as 'low' | 'normal' | 'high'
  })
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [validations, setValidations] = useState<any[]>([])

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      // Utiliser le service de validation
      const validation = FileService.validateFile(
        file,
        [...FileService.ALLOWED_TYPES.IMAGES, ...FileService.ALLOWED_TYPES.DOCUMENTS],
        FileService.MAX_SIZES.DOCUMENT
      )

      if (!validation.valid) {
        toast.error('Fichier invalide', {
          description: validation.error
        })
        return
      }

      setSelectedFile(file)
      toast.success('Fichier sélectionné', {
        description: `${file.name} (${formatFileSize(file.size)})`
      })
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.title || !formData.description) {
      toast.error('Veuillez remplir tous les champs obligatoires')
      return
    }

    if (!user) {
      toast.error('Vous devez être connecté pour soumettre une validation')
      return
    }

    setIsUploading(true)
    toast.loading('Envoi en cours...', { id: 'validation-submit' })

    try {
      // Préparer les données du formulaire
      const formDataToSend = new FormData()
      formDataToSend.append('type', 'validation')
      formDataToSend.append('title', formData.title)
      formDataToSend.append('description', formData.description)
      formDataToSend.append('projectType', formData.projectType)
      formDataToSend.append('urgency', formData.urgency)

      if (selectedFile) {
        formDataToSend.append('file', selectedFile)
      }

      // Utiliser notre API unifiée
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formDataToSend
      })

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || 'Erreur lors de la soumission')
      }

      toast.success('Validation soumise avec succès !', {
        id: 'validation-submit',
        description: `Vous recevrez une réponse sous 24-48h${result.data.emailSent ? ' - Email de confirmation envoyé' : ''}`
      })

      // Reset form
      setFormData({ title: '', description: '', projectType: '', urgency: 'normal' })
      setSelectedFile(null)
      
      // Refresh history
      fetchValidations()

    } catch (error: any) {
      console.error('Erreur lors de la soumission:', error)
      toast.error('Erreur lors de l\'envoi', { 
        id: 'validation-submit',
        description: error.message || 'Veuillez réessayer'
      })
    } finally {
      setIsUploading(false)
    }
  }

  const fetchValidations = async () => {
    if (!user) return

    try {
      const response = await fetch('/api/validations')
      const result = await response.json()

      if (result.success) {
        setValidations(result.data || [])
      } else {
        console.error('Erreur chargement validations:', result.error)
      }
    } catch (error) {
      console.error('Erreur lors du chargement de l\'historique:', error)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'En cours': return <Clock className="h-4 w-4 text-yellow-500" />
      case 'Validé': return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'Refusé': return <AlertTriangle className="h-4 w-4 text-red-500" />
      default: return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'En cours': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'Validé': return 'bg-green-100 text-green-800 border-green-200'
      case 'Refusé': return 'bg-red-100 text-red-800 border-red-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  return (
    <>
      <GlobalNavigation />
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
        {/* Header */}
        <div className="bg-white border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Link
                  href="/hub"
                  className="flex items-center space-x-2 text-slate-700 hover:text-blue-600 transition-colors"
                >
                  <ArrowLeft className="h-5 w-5" />
                  <span>Retour au Hub</span>
                </Link>
                <div className="h-6 w-px bg-gray-300" />
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">Validation Technique</h1>
                  <p className="text-gray-600">Soumettez vos documents pour validation par nos experts</p>
                </div>
              </div>
              <Badge className="bg-blue-100 text-blue-800">
                Service Expert
              </Badge>
            </div>
          </div>
        </div>

        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
            <TabsList className="grid w-full grid-cols-2 mb-8">
              <TabsTrigger value="upload" className="flex items-center space-x-2">
                <Upload className="h-4 w-4" />
                <span>Nouvelle Validation</span>
              </TabsTrigger>
              <TabsTrigger value="history" className="flex items-center space-x-2" onClick={fetchValidations}>
                <History className="h-4 w-4" />
                <span>Historique</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="upload">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="grid grid-cols-1 lg:grid-cols-3 gap-8"
              >
                {/* Formulaire */}
                <div className="lg:col-span-2">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <FileText className="h-5 w-5" />
                        <span>Détails de la Validation</span>
                      </CardTitle>
                      <CardDescription>
                        Remplissez les informations sur votre projet pour une validation optimale
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <form onSubmit={handleSubmit} className="space-y-6">
                        <div>
                          <Label htmlFor="title">Titre du Projet *</Label>
                          <Input
                            id="title"
                            value={formData.title}
                            onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                            placeholder="Ex: Installation électrique bureau 200m²"
                            required
                          />
                        </div>

                        <div>
                          <Label htmlFor="description">Description Détaillée *</Label>
                          <Textarea
                            id="description"
                            value={formData.description}
                            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                            placeholder="Décrivez votre projet, les contraintes, les objectifs..."
                            rows={4}
                            required
                          />
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor="projectType">Type de Projet</Label>
                            <Input
                              id="projectType"
                              value={formData.projectType}
                              onChange={(e) => setFormData(prev => ({ ...prev, projectType: e.target.value }))}
                              placeholder="Ex: Résidentiel, Industriel, Tertiaire"
                            />
                          </div>
                          <div>
                            <Label htmlFor="urgency">Urgence</Label>
                            <select
                              id="urgency"
                              value={formData.urgency}
                              onChange={(e) => setFormData(prev => ({ ...prev, urgency: e.target.value as any }))}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                              <option value="low">Faible</option>
                              <option value="normal">Normale</option>
                              <option value="high">Élevée</option>
                            </select>
                          </div>
                        </div>

                        <div>
                          <Label htmlFor="file">Document (PDF, JPEG, PNG - Max 10MB)</Label>
                          <div className="mt-2">
                            <input
                              id="file"
                              type="file"
                              accept=".pdf,.jpg,.jpeg,.png"
                              onChange={handleFileSelect}
                              className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                            />
                            {selectedFile && (
                              <p className="mt-2 text-sm text-green-600">
                                ✓ {selectedFile.name} ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
                              </p>
                            )}
                          </div>
                        </div>

                        <Button 
                          type="submit" 
                          disabled={isUploading}
                          className="w-full bg-blue-600 hover:bg-blue-700"
                        >
                          {isUploading ? (
                            <>
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                              Envoi en cours...
                            </>
                          ) : (
                            <>
                              <Send className="h-4 w-4 mr-2" />
                              Soumettre pour Validation
                            </>
                          )}
                        </Button>
                      </form>
                    </CardContent>
                  </Card>
                </div>

                {/* Informations */}
                <div>
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Processus de Validation</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex items-start space-x-3">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-blue-600 font-semibold text-sm">1</span>
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">Soumission</h4>
                          <p className="text-sm text-gray-600">Envoyez votre document et description</p>
                        </div>
                      </div>
                      <div className="flex items-start space-x-3">
                        <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                          <span className="text-yellow-600 font-semibold text-sm">2</span>
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">Analyse</h4>
                          <p className="text-sm text-gray-600">Examen par nos experts (24-48h)</p>
                        </div>
                      </div>
                      <div className="flex items-start space-x-3">
                        <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                          <span className="text-green-600 font-semibold text-sm">3</span>
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">Retour</h4>
                          <p className="text-sm text-gray-600">Rapport détaillé avec recommandations</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </motion.div>
            </TabsContent>

            <TabsContent value="history">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
              >
                <Card>
                  <CardHeader>
                    <CardTitle>Historique des Validations</CardTitle>
                    <CardDescription>
                      Suivez l'état de vos demandes de validation
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {validations.length === 0 ? (
                      <div className="text-center py-8">
                        <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-500">Aucune validation soumise pour le moment</p>
                        <Button 
                          onClick={() => setActiveTab('upload')}
                          className="mt-4"
                        >
                          Créer une validation
                        </Button>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {validations.map((validation) => (
                          <div key={validation.id} className="border border-gray-200 rounded-lg p-4">
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <div className="flex items-center space-x-2 mb-2">
                                  {getStatusIcon(validation.status)}
                                  <h3 className="font-medium text-gray-900">{validation.title}</h3>
                                  <Badge className={getStatusColor(validation.status)}>
                                    {validation.status}
                                  </Badge>
                                </div>
                                <p className="text-gray-600 text-sm mb-2">{validation.description}</p>
                                <div className="flex items-center space-x-4 text-xs text-gray-500">
                                  <span>Soumis le {new Date(validation.created_at).toLocaleDateString('fr-FR')}</span>
                                  {validation.file_name && (
                                    <span>📎 {validation.file_name}</span>
                                  )}
                                  {validation.urgency && (
                                    <span className={`px-2 py-1 rounded ${
                                      validation.urgency === 'high' ? 'bg-red-100 text-red-800' :
                                      validation.urgency === 'normal' ? 'bg-yellow-100 text-yellow-800' :
                                      'bg-green-100 text-green-800'
                                    }`}>
                                      {validation.urgency === 'high' ? 'Urgent' : 
                                       validation.urgency === 'normal' ? 'Normal' : 'Faible'}
                                    </span>
                                  )}
                                </div>
                                {validation.admin_notes && (
                                  <div className="mt-3 p-3 bg-blue-50 rounded-md">
                                    <h4 className="font-medium text-blue-900 text-sm">Notes de l'expert :</h4>
                                    <p className="text-blue-800 text-sm mt-1">{validation.admin_notes}</p>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </motion.div>
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </>
  )
}
