'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  FileCheck, 
  Users, 
  MessageSquare, 
  BookOpen,
  Upload,
  Search,
  Filter,
  Clock,
  Star,
  CheckCircle,
  AlertCircle,
  FileText,
  Zap
} from 'lucide-react'
import { useStore } from '@/store/useStore'
import { ExpertService } from '@/services/expertService'
import { formatDate, getMembershipColor } from '@/lib/utils'

interface ExpertSpaceProps {
  className?: string
}

export default function ExpertSpace({ className = '' }: ExpertSpaceProps) {
  const {
    technicalDocuments,
    compatibilityChecks,
    expertConsultations,
    expertProfiles,
    technicalResources,
    selectedExpert,
    setTechnicalDocuments,
    setCompatibilityChecks,
    setExpertConsultations,
    setExpertProfiles,
    setTechnicalResources,
    setSelectedExpert
  } = useStore()

  const [activeTab, setActiveTab] = useState<'validation' | 'compatibility' | 'consultation' | 'resources'>('validation')
  const [searchQuery, setSearchQuery] = useState('')
  const [filterStatus, setFilterStatus] = useState<'all' | 'pending' | 'approved' | 'rejected'>('all')

  // Initialisation des données
  useEffect(() => {
    const loadInitialData = () => {
      setTechnicalDocuments(ExpertService.generateTechnicalDocuments())
      setCompatibilityChecks(ExpertService.generateCompatibilityChecks())
      setExpertConsultations(ExpertService.generateExpertConsultations())
      setExpertProfiles(ExpertService.generateExpertProfiles())
      setTechnicalResources(ExpertService.generateTechnicalResources())
    }

    loadInitialData()
  }, [])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
      case 'resolved':
      case 'compatible':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'rejected':
      case 'incompatible':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'pending':
      case 'open':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'in_review':
      case 'in_progress':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'conditional':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
      case 'resolved':
      case 'compatible':
        return <CheckCircle className="h-4 w-4" />
      case 'rejected':
      case 'incompatible':
        return <AlertCircle className="h-4 w-4" />
      case 'pending':
      case 'open':
        return <Clock className="h-4 w-4" />
      case 'in_review':
      case 'in_progress':
        return <Zap className="h-4 w-4" />
      default:
        return <FileText className="h-4 w-4" />
    }
  }

  const tabs = [
    { 
      id: 'validation', 
      label: 'Validation Technique', 
      icon: FileCheck, 
      count: technicalDocuments.length,
      description: 'Upload et validation de documents techniques'
    },
    { 
      id: 'compatibility', 
      label: 'Compatibilité', 
      icon: Zap, 
      count: compatibilityChecks.length,
      description: 'Vérification de compatibilité entre composants'
    },
    { 
      id: 'consultation', 
      label: 'Consultation Expert', 
      icon: MessageSquare, 
      count: expertConsultations.length,
      description: 'Chat et conseils avec nos experts certifiés'
    },
    { 
      id: 'resources', 
      label: 'Bibliothèque', 
      icon: BookOpen, 
      count: technicalResources.length,
      description: 'Ressources techniques et formations'
    }
  ]

  return (
    <div className={`bg-white rounded-lg shadow-lg ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Espace Conseil Technique Expert</h2>
            <p className="text-gray-600 mt-1">Validation, compatibilité et expertise technique professionnelle</p>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="text-right">
              <div className="text-sm text-gray-600">Experts disponibles</div>
              <div className="text-lg font-bold text-green-600">
                {expertProfiles.filter(e => e.availability_status === 'available').length}
              </div>
            </div>
            
            <button className="btn-premium">
              Nouvelle Consultation
            </button>
          </div>
        </div>

        {/* Barre de recherche et filtres */}
        <div className="mt-4 flex items-center space-x-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Rechercher dans l'espace expert..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400 focus:border-transparent"
            />
          </div>
          
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value as any)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400 focus:border-transparent"
          >
            <option value="all">Tous les statuts</option>
            <option value="pending">En attente</option>
            <option value="approved">Approuvé</option>
            <option value="rejected">Rejeté</option>
          </select>
        </div>
      </div>

      {/* Onglets */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6">
          {tabs.map((tab) => {
            const Icon = tab.icon
            const isActive = activeTab === tab.id
            
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  isActive
                    ? 'border-amber-500 text-amber-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="h-5 w-5" />
                <span>{tab.label}</span>
                {tab.count > 0 && (
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    isActive ? 'bg-amber-100 text-amber-800' : 'bg-gray-100 text-gray-600'
                  }`}>
                    {tab.count}
                  </span>
                )}
              </button>
            )
          })}
        </nav>
      </div>

      {/* Description de l'onglet actif */}
      <div className="px-6 py-3 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-200">
        <p className="text-sm text-blue-800">
          {tabs.find(tab => tab.id === activeTab)?.description}
        </p>
      </div>

      {/* Contenu des onglets */}
      <div className="p-6">
        <AnimatePresence mode="wait">
          {activeTab === 'validation' && (
            <motion.div
              key="validation"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-6"
            >
              {/* Zone d'upload */}
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-amber-400 transition-colors">
                <Upload className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Uploader un Document Technique
                </h3>
                <p className="text-gray-600 mb-4">
                  Glissez-déposez vos fichiers ou cliquez pour sélectionner
                </p>
                <button className="btn-premium">
                  Sélectionner des fichiers
                </button>
                <p className="text-xs text-gray-500 mt-2">
                  Formats acceptés: PDF, DOC, CAD, Excel • Max 10MB
                </p>
              </div>

              {/* Liste des documents */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Documents en Validation</h3>
                {technicalDocuments.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <FileCheck className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <p>Aucun document en cours de validation</p>
                  </div>
                ) : (
                  technicalDocuments.map((doc) => (
                    <motion.div
                      key={doc.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      className="industrial-card p-4"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(doc.validation_status)}`}>
                              {getStatusIcon(doc.validation_status)}
                              <span className="ml-1">{doc.validation_status}</span>
                            </span>
                            <span className="text-xs text-gray-500">{doc.category}</span>
                          </div>
                          <h4 className="font-semibold text-gray-900 mb-1">{doc.title}</h4>
                          <p className="text-sm text-gray-600 mb-2">{doc.description}</p>
                          <div className="flex items-center space-x-4 text-xs text-gray-500">
                            <span>Taille: {(doc.file_size / 1024 / 1024).toFixed(1)} MB</span>
                            <span>Type: {doc.file_type.toUpperCase()}</span>
                            <span>Créé: {formatDate(doc.created_at)}</span>
                          </div>
                          {doc.validation_notes && (
                            <div className="mt-2 p-2 bg-blue-50 rounded text-sm text-blue-800">
                              <strong>Notes:</strong> {doc.validation_notes}
                            </div>
                          )}
                        </div>
                        <div className="ml-4">
                          <button className="text-amber-600 hover:text-amber-700 text-sm font-medium">
                            Voir détails →
                          </button>
                        </div>
                      </div>
                    </motion.div>
                  ))
                )}
              </div>
            </motion.div>
          )}

          {activeTab === 'compatibility' && (
            <motion.div
              key="compatibility"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-6"
            >
              {/* Simulateur de compatibilité */}
              <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-6 border border-green-200">
                <h3 className="text-lg font-semibold text-green-900 mb-4">
                  Simulateur de Compatibilité
                </h3>
                <div className="grid md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <label className="block text-sm font-medium text-green-800 mb-2">
                      Produit Principal
                    </label>
                    <select className="w-full px-3 py-2 border border-green-300 rounded-lg focus:ring-2 focus:ring-green-400">
                      <option>Sélectionner un produit...</option>
                      <option>Disjoncteur C60N 32A</option>
                      <option>Câble U1000R2V 3x2.5mm²</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-green-800 mb-2">
                      Produit Secondaire
                    </label>
                    <select className="w-full px-3 py-2 border border-green-300 rounded-lg focus:ring-2 focus:ring-green-400">
                      <option>Sélectionner un produit...</option>
                      <option>Câble U1000R2V 3x2.5mm²</option>
                      <option>Contacteur LC1D32</option>
                    </select>
                  </div>
                </div>
                <button className="btn-premium">
                  Vérifier la Compatibilité
                </button>
              </div>

              {/* Résultats de compatibilité */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Vérifications Récentes</h3>
                {compatibilityChecks.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <Zap className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <p>Aucune vérification de compatibilité</p>
                  </div>
                ) : (
                  compatibilityChecks.map((check) => (
                    <motion.div
                      key={check.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      className="industrial-card p-4"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(check.compatibility_status)}`}>
                              {getStatusIcon(check.compatibility_status)}
                              <span className="ml-1">{check.compatibility_status}</span>
                            </span>
                            <div className="flex items-center space-x-1">
                              <Star className="h-4 w-4 text-yellow-500" />
                              <span className="text-sm font-medium">{check.compatibility_score}%</span>
                            </div>
                          </div>
                          <h4 className="font-semibold text-gray-900 mb-1">
                            Produit #{check.primary_product_id} ↔ Produit #{check.secondary_product_id}
                          </h4>
                          <p className="text-sm text-gray-600 mb-2">{check.notes}</p>

                          {check.conditions && check.conditions.length > 0 && (
                            <div className="mb-2">
                              <h5 className="text-xs font-medium text-blue-800 mb-1">Conditions:</h5>
                              <ul className="text-xs text-blue-700 space-y-1">
                                {check.conditions.map((condition, idx) => (
                                  <li key={idx}>• {condition}</li>
                                ))}
                              </ul>
                            </div>
                          )}

                          {check.warnings && check.warnings.length > 0 && (
                            <div className="mb-2">
                              <h5 className="text-xs font-medium text-orange-800 mb-1">Avertissements:</h5>
                              <ul className="text-xs text-orange-700 space-y-1">
                                {check.warnings.map((warning, idx) => (
                                  <li key={idx}>⚠ {warning}</li>
                                ))}
                              </ul>
                            </div>
                          )}

                          <div className="flex items-center space-x-4 text-xs text-gray-500">
                            <span>Vérifié: {formatDate(check.created_at)}</span>
                            {check.verified_by && <span>Par: Expert #{check.verified_by}</span>}
                          </div>
                        </div>
                        <div className="ml-4">
                          <button className="text-amber-600 hover:text-amber-700 text-sm font-medium">
                            Rapport détaillé →
                          </button>
                        </div>
                      </div>
                    </motion.div>
                  ))
                )}
              </div>
            </motion.div>
          )}

          {activeTab === 'consultation' && (
            <motion.div
              key="consultation"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-6"
            >
              {/* Experts disponibles */}
              <div className="grid md:grid-cols-3 gap-4">
                {expertProfiles.map((expert) => (
                  <motion.div
                    key={expert.id}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className={`industrial-card p-4 cursor-pointer transition-all ${
                      selectedExpert?.id === expert.id ? 'ring-2 ring-amber-400' : ''
                    }`}
                    onClick={() => setSelectedExpert(expert)}
                  >
                    <div className="text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-amber-400 to-amber-500 rounded-full flex items-center justify-center mx-auto mb-3">
                        <Users className="h-8 w-8 text-white" />
                      </div>
                      <h4 className="font-semibold text-gray-900 mb-1">{expert.display_name}</h4>
                      <p className="text-sm text-gray-600 mb-2">{expert.title}</p>
                      <div className="flex items-center justify-center space-x-1 mb-2">
                        <Star className="h-4 w-4 text-yellow-500" />
                        <span className="text-sm font-medium">{expert.rating}</span>
                        <span className="text-xs text-gray-500">({expert.total_consultations})</span>
                      </div>
                      <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        expert.availability_status === 'available' ? 'bg-green-100 text-green-800' :
                        expert.availability_status === 'busy' ? 'bg-orange-100 text-orange-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {expert.availability_status}
                      </div>
                      <div className="mt-2 text-sm font-medium text-amber-600">
                        {expert.hourly_rate.toLocaleString()} FCFA/h
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Consultations actives */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Consultations en Cours</h3>
                {expertConsultations.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <MessageSquare className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <p>Aucune consultation active</p>
                  </div>
                ) : (
                  expertConsultations.map((consultation) => (
                    <motion.div
                      key={consultation.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      className="industrial-card p-4"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(consultation.status)}`}>
                              {getStatusIcon(consultation.status)}
                              <span className="ml-1">{consultation.status}</span>
                            </span>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              consultation.priority === 'urgent' ? 'bg-red-100 text-red-800' :
                              consultation.priority === 'high' ? 'bg-orange-100 text-orange-800' :
                              consultation.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-blue-100 text-blue-800'
                            }`}>
                              {consultation.priority}
                            </span>
                          </div>
                          <h4 className="font-semibold text-gray-900 mb-1">{consultation.title}</h4>
                          <p className="text-sm text-gray-600 mb-2">{consultation.description}</p>
                          <div className="flex items-center space-x-4 text-xs text-gray-500">
                            <span>Expert: #{consultation.expert_id}</span>
                            <span>Durée estimée: {consultation.estimated_duration}min</span>
                            <span>Coût: {consultation.cost.toLocaleString()} FCFA</span>
                            <span>Créé: {formatDate(consultation.created_at)}</span>
                          </div>
                        </div>
                        <div className="ml-4">
                          <button className="text-amber-600 hover:text-amber-700 text-sm font-medium">
                            Rejoindre →
                          </button>
                        </div>
                      </div>
                    </motion.div>
                  ))
                )}
              </div>
            </motion.div>
          )}

          {activeTab === 'resources' && (
            <motion.div
              key="resources"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-6"
            >
              {/* Filtres par niveau */}
              <div className="flex items-center space-x-4">
                <span className="text-sm font-medium text-gray-700">Filtrer par niveau:</span>
                {['beginner', 'intermediate', 'advanced', 'expert'].map((level) => (
                  <button
                    key={level}
                    className="px-3 py-1 rounded-full text-xs font-medium border border-gray-300 hover:border-amber-400 hover:text-amber-600 transition-colors"
                  >
                    {level}
                  </button>
                ))}
              </div>

              {/* Ressources techniques */}
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {technicalResources.map((resource) => (
                  <motion.div
                    key={resource.id}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="industrial-card p-4"
                  >
                    <div className="mb-3">
                      <div className="w-full h-32 bg-gray-200 rounded-lg mb-3 flex items-center justify-center">
                        <BookOpen className="h-8 w-8 text-gray-400" />
                      </div>
                      <div className="flex items-center space-x-2 mb-2">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          resource.type === 'tutorial' ? 'bg-blue-100 text-blue-800' :
                          resource.type === 'guide' ? 'bg-green-100 text-green-800' :
                          resource.type === 'video' ? 'bg-purple-100 text-purple-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {resource.type}
                        </span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          resource.difficulty_level === 'expert' ? 'bg-red-100 text-red-800' :
                          resource.difficulty_level === 'advanced' ? 'bg-orange-100 text-orange-800' :
                          resource.difficulty_level === 'intermediate' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-green-100 text-green-800'
                        }`}>
                          {resource.difficulty_level}
                        </span>
                      </div>
                      <h4 className="font-semibold text-gray-900 mb-1">{resource.title}</h4>
                      <p className="text-sm text-gray-600 mb-2">{resource.description}</p>
                      <div className="flex items-center space-x-4 text-xs text-gray-500 mb-2">
                        {resource.duration_minutes && <span>{resource.duration_minutes}min</span>}
                        <span>{resource.views_count} vues</span>
                        <div className="flex items-center space-x-1">
                          <Star className="h-3 w-3 text-yellow-500" />
                          <span>{resource.rating}</span>
                        </div>
                      </div>
                      {resource.membership_required && (
                        <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getMembershipColor(resource.membership_required)}`}>
                          {resource.membership_required}+ requis
                        </div>
                      )}
                    </div>
                    <button className="w-full btn-premium text-sm py-2">
                      Accéder à la ressource
                    </button>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}
