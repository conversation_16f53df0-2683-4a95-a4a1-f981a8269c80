'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown,
  DollarSign,
  Users,
  Package,
  Globe,
  Calendar,
  Target,
  Award,
  Zap,
  Eye,
  MousePointer,
  Clock,
  Percent,
  ArrowUpRight,
  ArrowDownRight,
  Filter,
  Download,
  RefreshCw
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Progress } from '@/components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

interface MetricData {
  id: string
  name: string
  value: number
  previousValue: number
  unit: string
  category: 'revenue' | 'users' | 'engagement' | 'conversion' | 'performance'
  trend: 'up' | 'down' | 'stable'
  changePercent: number
  target?: number
  description: string
}

interface RegionalData {
  region: string
  revenue: number
  users: number
  orders: number
  growth: number
}

interface ProductPerformance {
  category: string
  revenue: number
  units: number
  margin: number
  trend: number
}

interface AdvancedMetricsProps {
  className?: string
}

export default function AdvancedMetrics({ className = '' }: AdvancedMetricsProps) {
  const [timeRange, setTimeRange] = useState('30d')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [isLoading, setIsLoading] = useState(false)

  const [metrics] = useState<MetricData[]>([
    {
      id: '1',
      name: 'Chiffre d\'Affaires',
      value: 12500000,
      previousValue: 10800000,
      unit: 'FCFA',
      category: 'revenue',
      trend: 'up',
      changePercent: 15.7,
      target: 15000000,
      description: 'Revenus totaux générés sur la période'
    },
    {
      id: '2',
      name: 'Utilisateurs Actifs',
      value: 8450,
      previousValue: 7890,
      unit: 'utilisateurs',
      category: 'users',
      trend: 'up',
      changePercent: 7.1,
      target: 10000,
      description: 'Nombre d\'utilisateurs actifs mensuels'
    },
    {
      id: '3',
      name: 'Taux de Conversion',
      value: 3.2,
      previousValue: 2.8,
      unit: '%',
      category: 'conversion',
      trend: 'up',
      changePercent: 14.3,
      target: 4.0,
      description: 'Pourcentage de visiteurs qui effectuent un achat'
    },
    {
      id: '4',
      name: 'Panier Moyen',
      value: 185000,
      previousValue: 172000,
      unit: 'FCFA',
      category: 'revenue',
      trend: 'up',
      changePercent: 7.6,
      target: 200000,
      description: 'Valeur moyenne des commandes'
    },
    {
      id: '5',
      name: 'Temps de Session',
      value: 8.5,
      previousValue: 9.2,
      unit: 'minutes',
      category: 'engagement',
      trend: 'down',
      changePercent: -7.6,
      description: 'Durée moyenne des sessions utilisateur'
    },
    {
      id: '6',
      name: 'Taux de Rebond',
      value: 32.1,
      previousValue: 35.8,
      unit: '%',
      category: 'engagement',
      trend: 'up',
      changePercent: -10.3,
      description: 'Pourcentage de visiteurs qui quittent après une page'
    },
    {
      id: '7',
      name: 'Vitesse de Chargement',
      value: 1.8,
      previousValue: 2.3,
      unit: 'secondes',
      category: 'performance',
      trend: 'up',
      changePercent: -21.7,
      description: 'Temps moyen de chargement des pages'
    },
    {
      id: '8',
      name: 'NPS Score',
      value: 72,
      previousValue: 68,
      unit: 'points',
      category: 'engagement',
      trend: 'up',
      changePercent: 5.9,
      target: 80,
      description: 'Net Promoter Score - satisfaction client'
    }
  ])

  const [regionalData] = useState<RegionalData[]>([
    { region: 'Côte d\'Ivoire', revenue: 5200000, users: 3200, orders: 1450, growth: 18.5 },
    { region: 'Sénégal', revenue: 3100000, users: 2100, orders: 890, growth: 12.3 },
    { region: 'Mali', revenue: 1800000, users: 1200, orders: 520, growth: 22.1 },
    { region: 'Burkina Faso', revenue: 1400000, users: 950, orders: 380, growth: 15.7 },
    { region: 'Ghana', revenue: 1000000, users: 800, orders: 290, growth: 8.9 }
  ])

  const [productPerformance] = useState<ProductPerformance[]>([
    { category: 'Disjoncteurs', revenue: 4200000, units: 2800, margin: 28.5, trend: 15.2 },
    { category: 'Câbles', revenue: 2800000, units: 5200, margin: 22.1, trend: 8.7 },
    { category: 'Tableaux électriques', revenue: 2100000, units: 180, margin: 35.8, trend: 12.4 },
    { category: 'Prises et interrupteurs', revenue: 1900000, units: 8900, margin: 18.9, trend: 6.3 },
    { category: 'Éclairage', revenue: 1500000, units: 3400, margin: 25.7, trend: -2.1 }
  ])

  // Filtrer les métriques
  const filteredMetrics = metrics.filter(metric => 
    selectedCategory === 'all' || metric.category === selectedCategory
  )

  // Calculer les totaux
  const totalRevenue = regionalData.reduce((sum, region) => sum + region.revenue, 0)
  const totalUsers = regionalData.reduce((sum, region) => sum + region.users, 0)
  const totalOrders = regionalData.reduce((sum, region) => sum + region.orders, 0)
  const averageGrowth = regionalData.reduce((sum, region) => sum + region.growth, 0) / regionalData.length

  const refreshData = () => {
    setIsLoading(true)
    setTimeout(() => {
      setIsLoading(false)
    }, 2000)
  }

  const exportMetrics = () => {
    const data = {
      metrics: filteredMetrics,
      regionalData,
      productPerformance,
      timeRange,
      exportDate: new Date().toISOString()
    }

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `metriques-avancees-${timeRange}.json`
    a.click()
    URL.revokeObjectURL(url)
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'revenue': return <DollarSign className="h-4 w-4" />
      case 'users': return <Users className="h-4 w-4" />
      case 'engagement': return <Eye className="h-4 w-4" />
      case 'conversion': return <Target className="h-4 w-4" />
      case 'performance': return <Zap className="h-4 w-4" />
      default: return <BarChart3 className="h-4 w-4" />
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'revenue': return 'bg-green-100 text-green-800 border-green-200'
      case 'users': return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'engagement': return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'conversion': return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'performance': return 'bg-red-100 text-red-800 border-red-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const formatValue = (value: number, unit: string) => {
    if (unit === 'FCFA') {
      if (value >= 1000000) {
        return `${(value / 1000000).toFixed(1)}M FCFA`
      } else if (value >= 1000) {
        return `${(value / 1000).toFixed(0)}k FCFA`
      }
      return `${value.toLocaleString()} FCFA`
    }
    
    if (unit === 'utilisateurs' && value >= 1000) {
      return `${(value / 1000).toFixed(1)}k ${unit}`
    }
    
    return `${value.toLocaleString()} ${unit}`
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-purple-400 to-purple-500 rounded-lg flex items-center justify-center">
                <BarChart3 className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle>Métriques Avancées</CardTitle>
                <CardDescription>
                  Analytics détaillées et insights business
                </CardDescription>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Select value={timeRange} onValueChange={setTimeRange}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7d">7 jours</SelectItem>
                  <SelectItem value="30d">30 jours</SelectItem>
                  <SelectItem value="90d">90 jours</SelectItem>
                  <SelectItem value="1y">1 an</SelectItem>
                </SelectContent>
              </Select>
              
              <Button
                variant="outline"
                size="sm"
                onClick={refreshData}
                disabled={isLoading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                Actualiser
              </Button>
              
              <Button onClick={exportMetrics} size="sm">
                <Download className="h-4 w-4 mr-2" />
                Exporter
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Vue d'ensemble */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">CA Total</p>
                <p className="text-2xl font-bold text-green-600">
                  {(totalRevenue / 1000000).toFixed(1)}M FCFA
                </p>
                <div className="flex items-center text-xs text-green-600">
                  <ArrowUpRight className="h-3 w-3 mr-1" />
                  +{averageGrowth.toFixed(1)}%
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Utilisateurs</p>
                <p className="text-2xl font-bold text-blue-600">
                  {(totalUsers / 1000).toFixed(1)}k
                </p>
                <div className="flex items-center text-xs text-blue-600">
                  <ArrowUpRight className="h-3 w-3 mr-1" />
                  +7.1%
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                <Package className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Commandes</p>
                <p className="text-2xl font-bold text-purple-600">{totalOrders.toLocaleString()}</p>
                <div className="flex items-center text-xs text-purple-600">
                  <ArrowUpRight className="h-3 w-3 mr-1" />
                  +12.4%
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                <Target className="h-6 w-6 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Conversion</p>
                <p className="text-2xl font-bold text-orange-600">3.2%</p>
                <div className="flex items-center text-xs text-orange-600">
                  <ArrowUpRight className="h-3 w-3 mr-1" />
                  +14.3%
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Contenu principal avec onglets */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Vue d'ensemble</TabsTrigger>
          <TabsTrigger value="regional">Régional</TabsTrigger>
          <TabsTrigger value="products">Produits</TabsTrigger>
          <TabsTrigger value="detailed">Détaillé</TabsTrigger>
        </TabsList>

        {/* Vue d'ensemble */}
        <TabsContent value="overview" className="space-y-6">
          {/* Filtres */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <Filter className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-medium text-gray-700">Catégorie :</span>
                </div>

                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Toutes les catégories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Toutes les catégories</SelectItem>
                    <SelectItem value="revenue">Revenus</SelectItem>
                    <SelectItem value="users">Utilisateurs</SelectItem>
                    <SelectItem value="engagement">Engagement</SelectItem>
                    <SelectItem value="conversion">Conversion</SelectItem>
                    <SelectItem value="performance">Performance</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Métriques principales */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {filteredMetrics.map((metric) => (
              <motion.div
                key={metric.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="group"
              >
                <Card className="hover:shadow-lg transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center space-x-2">
                        <div className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
                          {getCategoryIcon(metric.category)}
                        </div>
                        <Badge className={getCategoryColor(metric.category)}>
                          {metric.category}
                        </Badge>
                      </div>

                      <div className="flex items-center space-x-1">
                        {metric.trend === 'up' ? (
                          <ArrowUpRight className="h-4 w-4 text-green-500" />
                        ) : metric.trend === 'down' ? (
                          <ArrowDownRight className="h-4 w-4 text-red-500" />
                        ) : (
                          <div className="w-4 h-4" />
                        )}
                        <span className={`text-sm font-medium ${
                          metric.trend === 'up' ? 'text-green-600' :
                          metric.trend === 'down' ? 'text-red-600' :
                          'text-gray-600'
                        }`}>
                          {metric.changePercent > 0 ? '+' : ''}{metric.changePercent.toFixed(1)}%
                        </span>
                      </div>
                    </div>

                    <div className="mb-2">
                      <h4 className="font-medium text-gray-900 mb-1">{metric.name}</h4>
                      <p className="text-2xl font-bold text-gray-900">
                        {formatValue(metric.value, metric.unit)}
                      </p>
                    </div>

                    <p className="text-xs text-gray-600 mb-3">{metric.description}</p>

                    {metric.target && (
                      <div className="space-y-1">
                        <div className="flex justify-between text-xs text-gray-600">
                          <span>Objectif</span>
                          <span>{formatValue(metric.target, metric.unit)}</span>
                        </div>
                        <Progress
                          value={(metric.value / metric.target) * 100}
                          className="h-1"
                        />
                      </div>
                    )}
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>

        {/* Analyse régionale */}
        <TabsContent value="regional" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Performance par Région</CardTitle>
              <CardDescription>
                Analyse des performances commerciales par zone géographique
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {regionalData.map((region, index) => (
                  <motion.div
                    key={region.region}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <Globe className="h-5 w-5 text-blue-600" />
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">{region.region}</h4>
                        <p className="text-sm text-gray-600">
                          {region.users.toLocaleString()} utilisateurs • {region.orders} commandes
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-6">
                      <div className="text-right">
                        <p className="font-bold text-gray-900">
                          {(region.revenue / 1000000).toFixed(1)}M FCFA
                        </p>
                        <div className="flex items-center text-sm">
                          <ArrowUpRight className="h-3 w-3 text-green-500 mr-1" />
                          <span className="text-green-600">+{region.growth.toFixed(1)}%</span>
                        </div>
                      </div>

                      <div className="w-24">
                        <Progress value={(region.revenue / totalRevenue) * 100} className="h-2" />
                        <p className="text-xs text-gray-500 mt-1">
                          {((region.revenue / totalRevenue) * 100).toFixed(1)}% du total
                        </p>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Performance produits */}
        <TabsContent value="products" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Performance par Catégorie de Produits</CardTitle>
              <CardDescription>
                Analyse des ventes et marges par catégorie
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {productPerformance.map((product, index) => (
                  <motion.div
                    key={product.category}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="border border-gray-200 rounded-lg p-4"
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                          <Package className="h-5 w-5 text-purple-600" />
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">{product.category}</h4>
                          <p className="text-sm text-gray-600">{product.units.toLocaleString()} unités vendues</p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-1">
                        {product.trend > 0 ? (
                          <ArrowUpRight className="h-4 w-4 text-green-500" />
                        ) : (
                          <ArrowDownRight className="h-4 w-4 text-red-500" />
                        )}
                        <span className={`text-sm font-medium ${
                          product.trend > 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {product.trend > 0 ? '+' : ''}{product.trend.toFixed(1)}%
                        </span>
                      </div>
                    </div>

                    <div className="grid grid-cols-3 gap-4">
                      <div className="text-center">
                        <p className="text-2xl font-bold text-green-600">
                          {(product.revenue / 1000000).toFixed(1)}M
                        </p>
                        <p className="text-xs text-gray-600">Chiffre d'affaires</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-blue-600">{product.margin.toFixed(1)}%</p>
                        <p className="text-xs text-gray-600">Marge</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-purple-600">
                          {((product.revenue / productPerformance.reduce((sum, p) => sum + p.revenue, 0)) * 100).toFixed(1)}%
                        </p>
                        <p className="text-xs text-gray-600">Part du CA</p>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Métriques détaillées */}
        <TabsContent value="detailed" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Métriques d'engagement */}
            <Card>
              <CardHeader>
                <CardTitle>Engagement Utilisateur</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Eye className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">Pages vues par session</span>
                    </div>
                    <span className="font-medium">4.2</span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">Temps moyen sur site</span>
                    </div>
                    <span className="font-medium">8m 32s</span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <MousePointer className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">Taux de clic CTR</span>
                    </div>
                    <span className="font-medium">2.8%</span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Percent className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">Taux de rebond</span>
                    </div>
                    <span className="font-medium">32.1%</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Métriques de performance */}
            <Card>
              <CardHeader>
                <CardTitle>Performance Technique</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Zap className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">Vitesse de chargement</span>
                    </div>
                    <span className="font-medium">1.8s</span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Award className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">Score Core Web Vitals</span>
                    </div>
                    <span className="font-medium">92/100</span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Globe className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">Disponibilité</span>
                    </div>
                    <span className="font-medium">99.9%</span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <BarChart3 className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">Erreurs 4xx/5xx</span>
                    </div>
                    <span className="font-medium">0.1%</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
