import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { AlertService } from '@/lib/services/alertService'

export async function GET(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })

    // Récupérer la session (optionnel pour les alertes publiques)
    const { data: { session } } = await supabase.auth.getSession()

    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')
    const stats = searchParams.get('stats') === 'true'

    // Si demande de statistiques
    if (stats) {
      const result = await AlertService.getAlertStats()
      
      if (!result.success) {
        return NextResponse.json(
          { error: result.error },
          { status: 500 }
        )
      }

      return NextResponse.json({
        success: true,
        data: result.data
      })
    }

    // Récupérer les alertes
    let result
    if (category) {
      result = await AlertService.getAlertsByCategory(category, session?.user.id)
    } else {
      result = await AlertService.getAlertsWithSubscriptions(session?.user.id)
    }

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.data
    })

  } catch (error) {
    console.error('Erreur API alerts:', error)
    return NextResponse.json(
      { error: 'Erreur serveur' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })

    // Vérifier l'authentification
    const { data: { session }, error: authError } = await supabase.auth.getSession()
    
    if (authError || !session) {
      return NextResponse.json(
        { error: 'Non authentifié' },
        { status: 401 }
      )
    }

    // Vérifier les permissions admin
    const { data: user } = await supabase
      .from('users')
      .select('role')
      .eq('id', session.user.id)
      .single()

    if (!user || user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Permissions administrateur requises' },
        { status: 403 }
      )
    }

    const { title, body, type, category } = await request.json()

    if (!title || !body || !type || !category) {
      return NextResponse.json(
        { error: 'Titre, contenu, type et catégorie requis' },
        { status: 400 }
      )
    }

    // Créer l'alerte
    const result = await AlertService.createAlert({
      title,
      body,
      type,
      category
    })

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.data,
      message: 'Alerte créée avec succès'
    })

  } catch (error) {
    console.error('Erreur API create alert:', error)
    return NextResponse.json(
      { error: 'Erreur serveur' },
      { status: 500 }
    )
  }
}
