"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/advanced/page",{

/***/ "(app-pages-browser)/./src/components/advanced/OfflineManager.tsx":
/*!****************************************************!*\
  !*** ./src/components/advanced/OfflineManager.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ OfflineManager; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Cloud,Database,Download,HardDrive,RefreshCw,Settings,Upload,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Cloud,Database,Download,HardDrive,RefreshCw,Settings,Upload,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Cloud,Database,Download,HardDrive,RefreshCw,Settings,Upload,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Cloud,Database,Download,HardDrive,RefreshCw,Settings,Upload,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Cloud,Database,Download,HardDrive,RefreshCw,Settings,Upload,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Cloud,Database,Download,HardDrive,RefreshCw,Settings,Upload,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Cloud,Database,Download,HardDrive,RefreshCw,Settings,Upload,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Cloud,Database,Download,HardDrive,RefreshCw,Settings,Upload,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hard-drive.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Cloud,Database,Download,HardDrive,RefreshCw,Settings,Upload,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Cloud,Database,Download,HardDrive,RefreshCw,Settings,Upload,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Cloud,Database,Download,HardDrive,RefreshCw,Settings,Upload,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Cloud,Database,Download,HardDrive,RefreshCw,Settings,Upload,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cloud.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction OfflineManager(param) {\n    let { className = \"\" } = param;\n    _s();\n    const [syncStatus, setSyncStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOnline: navigator.onLine,\n        lastSync: new Date().toISOString(),\n        pendingUploads: 0,\n        pendingDownloads: 0,\n        totalSize: 0,\n        availableStorage: 0,\n        autoSync: true\n    });\n    const [offlineData, setOfflineData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"1\",\n            type: \"products\",\n            name: \"Catalogue Produits Schneider\",\n            size: 15728640,\n            lastSync: new Date(Date.now() - 3600000).toISOString(),\n            status: \"synced\",\n            priority: \"high\"\n        },\n        {\n            id: \"2\",\n            type: \"alerts\",\n            name: \"Alertes et Notifications\",\n            size: 2097152,\n            lastSync: new Date(Date.now() - 1800000).toISOString(),\n            status: \"pending\",\n            priority: \"medium\"\n        },\n        {\n            id: \"3\",\n            type: \"documents\",\n            name: \"Fiches Techniques\",\n            size: 52428800,\n            lastSync: new Date(Date.now() - 7200000).toISOString(),\n            status: \"synced\",\n            priority: \"high\"\n        },\n        {\n            id: \"4\",\n            type: \"calculations\",\n            name: \"Calculs Sauvegard\\xe9s\",\n            size: 1048576,\n            lastSync: new Date(Date.now() - 900000).toISOString(),\n            status: \"error\",\n            priority: \"low\"\n        },\n        {\n            id: \"5\",\n            type: \"reports\",\n            name: \"Rapports Techniques\",\n            size: 10485760,\n            lastSync: new Date().toISOString(),\n            status: \"downloading\",\n            priority: \"medium\"\n        }\n    ]);\n    const [isSyncing, setIsSyncing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [downloadProgress, setDownloadProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Surveiller le statut de connexion\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleOnline = ()=>{\n            setSyncStatus((prev)=>({\n                    ...prev,\n                    isOnline: true\n                }));\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Connexion r\\xe9tablie - Synchronisation automatique\");\n            if (syncStatus.autoSync) {\n                handleAutoSync();\n            }\n        };\n        const handleOffline = ()=>{\n            setSyncStatus((prev)=>({\n                    ...prev,\n                    isOnline: false\n                }));\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.warning(\"Mode hors ligne activ\\xe9\");\n        };\n        window.addEventListener(\"online\", handleOnline);\n        window.addEventListener(\"offline\", handleOffline);\n        return ()=>{\n            window.removeEventListener(\"online\", handleOnline);\n            window.removeEventListener(\"offline\", handleOffline);\n        };\n    }, [\n        syncStatus.autoSync\n    ]);\n    // Calculer l'espace de stockage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const calculateStorage = async ()=>{\n            try {\n                if (\"storage\" in navigator && \"estimate\" in navigator.storage) {\n                    const estimate = await navigator.storage.estimate();\n                    const totalSize = offlineData.reduce((sum, item)=>sum + item.size, 0);\n                    setSyncStatus((prev)=>({\n                            ...prev,\n                            totalSize,\n                            availableStorage: estimate.quota || 0\n                        }));\n                }\n            } catch (error) {\n                console.error(\"Erreur calcul stockage:\", error);\n            }\n        };\n        calculateStorage();\n    }, [\n        offlineData\n    ]);\n    // Synchronisation automatique\n    const handleAutoSync = async ()=>{\n        if (!syncStatus.isOnline || isSyncing) return;\n        setIsSyncing(true);\n        try {\n            // Simuler la synchronisation\n            const pendingItems = offlineData.filter((item)=>item.status === \"pending\" || item.status === \"error\");\n            for (const item of pendingItems){\n                await simulateSync(item.id);\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n            }\n            setSyncStatus((prev)=>({\n                    ...prev,\n                    lastSync: new Date().toISOString(),\n                    pendingUploads: 0,\n                    pendingDownloads: 0\n                }));\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Synchronisation termin\\xe9e\");\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Erreur lors de la synchronisation\");\n        } finally{\n            setIsSyncing(false);\n        }\n    };\n    // Simuler la synchronisation d'un élément\n    const simulateSync = async (itemId)=>{\n        setOfflineData((prev)=>prev.map((item)=>item.id === itemId ? {\n                    ...item,\n                    status: \"downloading\"\n                } : item));\n        // Simuler le progrès de téléchargement\n        for(let progress = 0; progress <= 100; progress += 10){\n            setDownloadProgress((prev)=>({\n                    ...prev,\n                    [itemId]: progress\n                }));\n            await new Promise((resolve)=>setTimeout(resolve, 100));\n        }\n        setOfflineData((prev)=>prev.map((item)=>item.id === itemId ? {\n                    ...item,\n                    status: \"synced\",\n                    lastSync: new Date().toISOString()\n                } : item));\n        setDownloadProgress((prev)=>{\n            const newProgress = {\n                ...prev\n            };\n            delete newProgress[itemId];\n            return newProgress;\n        });\n    };\n    // Télécharger un élément spécifique\n    const downloadItem = async (itemId)=>{\n        if (!syncStatus.isOnline) {\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Connexion requise pour t\\xe9l\\xe9charger\");\n            return;\n        }\n        await simulateSync(itemId);\n        sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"T\\xe9l\\xe9chargement termin\\xe9\");\n    };\n    // Supprimer un élément du cache\n    const removeFromCache = (itemId)=>{\n        setOfflineData((prev)=>prev.filter((item)=>item.id !== itemId));\n        sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"\\xc9l\\xe9ment supprim\\xe9 du cache\");\n    };\n    // Vider tout le cache\n    const clearAllCache = ()=>{\n        setOfflineData([]);\n        setSyncStatus((prev)=>({\n                ...prev,\n                totalSize: 0\n            }));\n        sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Cache vid\\xe9\");\n    };\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return \"0 B\";\n        const k = 1024;\n        const sizes = [\n            \"B\",\n            \"KB\",\n            \"MB\",\n            \"GB\"\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"synced\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                    lineNumber: 249,\n                    columnNumber: 29\n                }, this);\n            case \"pending\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 30\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-4 w-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                    lineNumber: 251,\n                    columnNumber: 28\n                }, this);\n            case \"downloading\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-4 w-4 text-blue-500 animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 34\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const getTypeIcon = (type)=>{\n        switch(type){\n            case \"products\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 31\n                }, this);\n            case \"alerts\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                    lineNumber: 260,\n                    columnNumber: 29\n                }, this);\n            case \"documents\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                    lineNumber: 261,\n                    columnNumber: 32\n                }, this);\n            case \"calculations\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 35\n                }, this);\n            case \"reports\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                    lineNumber: 263,\n                    columnNumber: 30\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                    lineNumber: 264,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case \"critical\":\n                return \"bg-red-100 text-red-800 border-red-200\";\n            case \"high\":\n                return \"bg-orange-100 text-orange-800 border-orange-200\";\n            case \"medium\":\n                return \"bg-blue-100 text-blue-800 border-blue-200\";\n            default:\n                return \"bg-gray-100 text-gray-800 border-gray-200\";\n        }\n    };\n    const storageUsagePercent = syncStatus.availableStorage > 0 ? syncStatus.totalSize / syncStatus.availableStorage * 100 : 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 rounded-lg flex items-center justify-center \".concat(syncStatus.isOnline ? \"bg-gradient-to-r from-green-400 to-green-500\" : \"bg-gradient-to-r from-gray-400 to-gray-500\"),\n                                            children: syncStatus.isOnline ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-6 w-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-6 w-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Gestionnaire Hors Ligne\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            className: syncStatus.isOnline ? \"bg-green-100 text-green-800\" : \"bg-gray-100 text-gray-800\",\n                                                            children: syncStatus.isOnline ? \"En ligne\" : \"Hors ligne\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                    children: [\n                                                        \"Derni\\xe8re sync: \",\n                                                        new Date(syncStatus.lastSync).toLocaleString(\"fr-FR\")\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>setShowSettings(!showSettings),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: handleAutoSync,\n                                            disabled: !syncStatus.isOnline || isSyncing,\n                                            className: \"bg-blue-500 hover:bg-blue-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Sync, {\n                                                    className: \"h-4 w-4 mr-2 \".concat(isSyncing ? \"animate-spin\" : \"\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 17\n                                                }, this),\n                                                isSyncing ? \"Synchronisation...\" : \"Synchroniser\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 9\n                    }, this),\n                    showSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"border-t border-gray-200 bg-gray-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: \"Synchronisation automatique\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Synchroniser automatiquement quand la connexion est r\\xe9tablie\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_6__.Switch, {\n                                            checked: syncStatus.autoSync,\n                                            onCheckedChange: (checked)=>setSyncStatus((prev)=>({\n                                                        ...prev,\n                                                        autoSync: checked\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-900\",\n                                            children: \"Vider tout le cache\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"destructive\",\n                                            size: \"sm\",\n                                            onClick: clearAllCache,\n                                            children: \"Vider\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                lineNumber: 284,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Espace utilis\\xe9\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg font-bold\",\n                                                children: formatFileSize(syncStatus.totalSize)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                            lineNumber: 363,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-8 w-8 text-green-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"\\xc9l\\xe9ments synchronis\\xe9s\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg font-bold\",\n                                                children: offlineData.filter((item)=>item.status === \"synced\").length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                lineNumber: 376,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                            lineNumber: 375,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                        lineNumber: 374,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-8 w-8 text-yellow-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"En attente\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg font-bold\",\n                                                children: offlineData.filter((item)=>item.status === \"pending\" || item.status === \"error\").length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                            lineNumber: 389,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                lineNumber: 361,\n                columnNumber: 7\n            }, this),\n            syncStatus.availableStorage > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-lg\",\n                            children: \"Utilisation du Stockage\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                            lineNumber: 407,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                        lineNumber: 406,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Utilis\\xe9: \",\n                                                formatFileSize(syncStatus.totalSize)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Disponible: \",\n                                                formatFileSize(syncStatus.availableStorage)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_5__.Progress, {\n                                    value: storageUsagePercent,\n                                    className: \"h-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-600\",\n                                    children: [\n                                        storageUsagePercent.toFixed(1),\n                                        \"% de l'espace de stockage utilis\\xe9\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                            lineNumber: 410,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                        lineNumber: 409,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                lineNumber: 405,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                children: \"Donn\\xe9es Hors Ligne\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                lineNumber: 427,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: \"G\\xe9rez vos donn\\xe9es disponibles en mode hors ligne\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                lineNumber: 428,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                        lineNumber: 426,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: offlineData.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_20__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    className: \"flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        getTypeIcon(item.type),\n                                                        getStatusIcon(item.status)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: item.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                            lineNumber: 448,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-4 text-sm text-gray-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: formatFileSize(item.size)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                                    lineNumber: 450,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"•\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                                    lineNumber: 451,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"Sync: \",\n                                                                        new Date(item.lastSync).toLocaleString(\"fr-FR\")\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                                    lineNumber: 452,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    className: getPriorityColor(item.priority),\n                                                                    children: item.priority\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                                    lineNumber: 453,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                            lineNumber: 449,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        item.status === \"downloading\" && downloadProgress[item.id] !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_5__.Progress, {\n                                                                    value: downloadProgress[item.id],\n                                                                    className: \"h-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                                    lineNumber: 461,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500 mt-1\",\n                                                                    children: [\n                                                                        \"T\\xe9l\\xe9chargement: \",\n                                                                        downloadProgress[item.id],\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                                    lineNumber: 462,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                item.status === \"error\" || item.status === \"pending\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    size: \"sm\",\n                                                    onClick: ()=>downloadItem(item.id),\n                                                    disabled: !syncStatus.isOnline,\n                                                    className: \"bg-blue-500 hover:bg-blue-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                            lineNumber: 478,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"T\\xe9l\\xe9charger\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 21\n                                                }, this) : item.status === \"synced\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>downloadItem(item.id),\n                                                    disabled: !syncStatus.isOnline,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                            lineNumber: 488,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Actualiser\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 21\n                                                }, this) : null,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>removeFromCache(item.id),\n                                                    children: \"Supprimer\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                    lineNumber: 493,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                            lineNumber: 470,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, item.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                            lineNumber: 433,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                        lineNumber: 432,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                lineNumber: 425,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_21__.AnimatePresence, {\n                children: !syncStatus.isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_20__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 50\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        y: 50\n                    },\n                    className: \"fixed bottom-4 right-4 z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-yellow-50 border-yellow-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-5 w-5 text-yellow-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                        lineNumber: 519,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium text-yellow-900\",\n                                                children: \"Mode Hors Ligne\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                lineNumber: 521,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-yellow-700\",\n                                                children: \"Utilisation des donn\\xe9es en cache\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                        lineNumber: 520,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                lineNumber: 518,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                            lineNumber: 517,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                        lineNumber: 516,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                    lineNumber: 510,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                lineNumber: 508,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n        lineNumber: 282,\n        columnNumber: 5\n    }, this);\n}\n_s(OfflineManager, \"hEhoeJc53JJhzOfhaWIHZsE9vFU=\");\n_c = OfflineManager;\nvar _c;\n$RefreshReg$(_c, \"OfflineManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/advanced/OfflineManager.tsx\n"));

/***/ })

});