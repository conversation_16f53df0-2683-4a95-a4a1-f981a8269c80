import { supabase, uploadFile, getFileUrl, downloadFile } from '@/lib/supabase/client'

export interface FileUploadResult {
  success: boolean
  url?: string
  path?: string
  error?: string
}

export interface FileDownloadResult {
  success: boolean
  blob?: Blob
  error?: string
}

export class FileService {
  // Buckets Supabase
  static readonly BUCKETS = {
    VALIDATIONS: 'validations',
    KITS: 'kits',
    AVATARS: 'avatars',
    DOCUMENTS: 'documents'
  } as const

  // Types de fichiers autorisés
  static readonly ALLOWED_TYPES = {
    IMAGES: ['image/jpeg', 'image/png', 'image/jpg', 'image/webp'],
    DOCUMENTS: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
    ALL: ['image/jpeg', 'image/png', 'image/jpg', 'image/webp', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
  } as const

  // Tailles maximales (en bytes)
  static readonly MAX_SIZES = {
    IMAGE: 5 * 1024 * 1024, // 5MB
    DOCUMENT: 10 * 1024 * 1024, // 10MB
    KIT: 50 * 1024 * 1024 // 50MB
  } as const

  /**
   * Valide un fichier selon les critères
   */
  static validateFile(
    file: File, 
    allowedTypes: string[] = FileService.ALLOWED_TYPES.ALL,
    maxSize: number = FileService.MAX_SIZES.DOCUMENT
  ): { valid: boolean; error?: string } {
    // Vérifier le type
    if (!allowedTypes.includes(file.type)) {
      return {
        valid: false,
        error: `Type de fichier non autorisé. Types acceptés: ${allowedTypes.join(', ')}`
      }
    }

    // Vérifier la taille
    if (file.size > maxSize) {
      const maxSizeMB = Math.round(maxSize / (1024 * 1024))
      return {
        valid: false,
        error: `Fichier trop volumineux. Taille maximale: ${maxSizeMB}MB`
      }
    }

    return { valid: true }
  }

  /**
   * Génère un nom de fichier unique
   */
  static generateFileName(originalName: string, userId: string): string {
    const timestamp = Date.now()
    const extension = originalName.split('.').pop()
    const baseName = originalName.split('.').slice(0, -1).join('.')
      .replace(/[^a-zA-Z0-9]/g, '_')
      .substring(0, 50)
    
    return `${userId}/${timestamp}_${baseName}.${extension}`
  }

  /**
   * Upload un fichier de validation
   */
  static async uploadValidationFile(
    file: File,
    userId: string
  ): Promise<FileUploadResult> {
    try {
      // Validation
      const validation = FileService.validateFile(
        file,
        [...FileService.ALLOWED_TYPES.IMAGES, ...FileService.ALLOWED_TYPES.DOCUMENTS],
        FileService.MAX_SIZES.DOCUMENT
      )

      if (!validation.valid) {
        return { success: false, error: validation.error }
      }

      // Générer le chemin
      const filePath = FileService.generateFileName(file.name, userId)

      // Upload
      const { data, error } = await uploadFile(
        FileService.BUCKETS.VALIDATIONS,
        filePath,
        file
      )

      if (error) {
        return { success: false, error: error.message }
      }

      // Obtenir l'URL publique
      const { data: urlData } = getFileUrl(FileService.BUCKETS.VALIDATIONS, filePath)

      return {
        success: true,
        url: urlData.publicUrl,
        path: filePath
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erreur inconnue'
      }
    }
  }

  /**
   * Upload un fichier de kit
   */
  static async uploadKitFile(
    file: File,
    kitId: string
  ): Promise<FileUploadResult> {
    try {
      // Validation
      const validation = FileService.validateFile(
        file,
        FileService.ALLOWED_TYPES.DOCUMENTS,
        FileService.MAX_SIZES.KIT
      )

      if (!validation.valid) {
        return { success: false, error: validation.error }
      }

      // Générer le chemin
      const filePath = `kits/${kitId}/${file.name}`

      // Upload
      const { data, error } = await uploadFile(
        FileService.BUCKETS.KITS,
        filePath,
        file
      )

      if (error) {
        return { success: false, error: error.message }
      }

      // Obtenir l'URL publique
      const { data: urlData } = getFileUrl(FileService.BUCKETS.KITS, filePath)

      return {
        success: true,
        url: urlData.publicUrl,
        path: filePath
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erreur inconnue'
      }
    }
  }

  /**
   * Télécharge un fichier
   */
  static async downloadFile(
    bucket: string,
    path: string
  ): Promise<FileDownloadResult> {
    try {
      const { data, error } = await downloadFile(bucket, path)

      if (error) {
        return { success: false, error: error.message }
      }

      return { success: true, blob: data }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erreur de téléchargement'
      }
    }
  }

  /**
   * Supprime un fichier
   */
  static async deleteFile(bucket: string, path: string): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await supabase.storage
        .from(bucket)
        .remove([path])

      if (error) {
        return { success: false, error: error.message }
      }

      return { success: true }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erreur de suppression'
      }
    }
  }

  /**
   * Obtient les métadonnées d'un fichier
   */
  static async getFileMetadata(bucket: string, path: string) {
    try {
      const { data, error } = await supabase.storage
        .from(bucket)
        .list(path.split('/').slice(0, -1).join('/'), {
          search: path.split('/').pop()
        })

      if (error) throw error

      return { success: true, data: data?.[0] }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erreur métadonnées'
      }
    }
  }

  /**
   * Crée les buckets nécessaires (à exécuter une fois)
   */
  static async createBuckets() {
    const buckets = Object.values(FileService.BUCKETS)
    
    for (const bucket of buckets) {
      try {
        const { error } = await supabase.storage.createBucket(bucket, {
          public: true,
          allowedMimeTypes: FileService.ALLOWED_TYPES.ALL,
          fileSizeLimit: FileService.MAX_SIZES.KIT
        })

        if (error && !error.message.includes('already exists')) {
          console.error(`Erreur création bucket ${bucket}:`, error)
        } else {
          console.log(`✅ Bucket ${bucket} créé ou existe déjà`)
        }
      } catch (error) {
        console.error(`Erreur bucket ${bucket}:`, error)
      }
    }
  }
}

// Utilitaires pour les formats de fichiers
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

export const getFileExtension = (filename: string): string => {
  return filename.split('.').pop()?.toLowerCase() || ''
}

export const isImageFile = (filename: string): boolean => {
  const ext = getFileExtension(filename)
  return ['jpg', 'jpeg', 'png', 'webp', 'gif'].includes(ext)
}

export const isPdfFile = (filename: string): boolean => {
  return getFileExtension(filename) === 'pdf'
}
