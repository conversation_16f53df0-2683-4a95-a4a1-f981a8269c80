{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/store/useStore.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { persist } from 'zustand/middleware'\nimport {\n  User,\n  Product,\n  Alert,\n  MarketAlert,\n  StockUpdate,\n  TrainingEvent,\n  NewsUpdate,\n  TechnicalDocument,\n  CompatibilityCheck,\n  ExpertConsultation,\n  ExpertProfile,\n  TechnicalResource,\n  TechnicalReport,\n  PrescriptionTemplate,\n  PrescriptionProject,\n  TechnicalNote,\n  ComplianceCertificate,\n  CalculationEngine\n} from '@/lib/supabase'\n\ninterface AppState {\n  // User state\n  user: User | null\n  isAuthenticated: boolean\n  \n  // Products state\n  products: Product[]\n  filteredProducts: Product[]\n  searchQuery: string\n  selectedCategory: string\n  \n  // Alerts state\n  alerts: Alert[]\n  unreadAlertsCount: number\n\n  // Hub d'Information state\n  marketAlerts: MarketAlert[]\n  stockUpdates: StockUpdate[]\n  trainingEvents: TrainingEvent[]\n  newsUpdates: NewsUpdate[]\n  realTimeConnected: boolean\n  lastUpdateTime: string\n\n  // Espace Conseil Technique state\n  technicalDocuments: TechnicalDocument[]\n  compatibilityChecks: CompatibilityCheck[]\n  expertConsultations: ExpertConsultation[]\n  expertProfiles: ExpertProfile[]\n  technicalResources: TechnicalResource[]\n  technicalReports: TechnicalReport[]\n  selectedExpert: ExpertProfile | null\n  activeConsultation: ExpertConsultation | null\n\n  // Module Prescripteur Professionnel state\n  prescriptionTemplates: PrescriptionTemplate[]\n  prescriptionProjects: PrescriptionProject[]\n  technicalNotes: TechnicalNote[]\n  complianceCertificates: ComplianceCertificate[]\n  calculationEngines: CalculationEngine[]\n  activeProject: PrescriptionProject | null\n  selectedTemplate: PrescriptionTemplate | null\n\n  // UI state\n  isLoading: boolean\n  isDarkMode: boolean\n  sidebarOpen: boolean\n  \n  // Actions\n  setUser: (user: User | null) => void\n  setProducts: (products: Product[]) => void\n  setSearchQuery: (query: string) => void\n  setSelectedCategory: (category: string) => void\n  setAlerts: (alerts: Alert[]) => void\n  markAlertAsRead: (alertId: string) => void\n\n  // Hub d'Information actions\n  setMarketAlerts: (alerts: MarketAlert[]) => void\n  addMarketAlert: (alert: MarketAlert) => void\n  setStockUpdates: (updates: StockUpdate[]) => void\n  addStockUpdate: (update: StockUpdate) => void\n  setTrainingEvents: (events: TrainingEvent[]) => void\n  setNewsUpdates: (news: NewsUpdate[]) => void\n  addNewsUpdate: (news: NewsUpdate) => void\n  setRealTimeConnected: (connected: boolean) => void\n  updateLastUpdateTime: () => void\n\n  // Espace Conseil Technique actions\n  setTechnicalDocuments: (documents: TechnicalDocument[]) => void\n  addTechnicalDocument: (document: TechnicalDocument) => void\n  updateTechnicalDocument: (id: string, updates: Partial<TechnicalDocument>) => void\n  setCompatibilityChecks: (checks: CompatibilityCheck[]) => void\n  addCompatibilityCheck: (check: CompatibilityCheck) => void\n  setExpertConsultations: (consultations: ExpertConsultation[]) => void\n  addExpertConsultation: (consultation: ExpertConsultation) => void\n  updateExpertConsultation: (id: string, updates: Partial<ExpertConsultation>) => void\n  setExpertProfiles: (profiles: ExpertProfile[]) => void\n  setTechnicalResources: (resources: TechnicalResource[]) => void\n  setTechnicalReports: (reports: TechnicalReport[]) => void\n  addTechnicalReport: (report: TechnicalReport) => void\n  setSelectedExpert: (expert: ExpertProfile | null) => void\n  setActiveConsultation: (consultation: ExpertConsultation | null) => void\n\n  // Module Prescripteur Professionnel actions\n  setPrescriptionTemplates: (templates: PrescriptionTemplate[]) => void\n  setPrescriptionProjects: (projects: PrescriptionProject[]) => void\n  addPrescriptionProject: (project: PrescriptionProject) => void\n  updatePrescriptionProject: (id: string, updates: Partial<PrescriptionProject>) => void\n  setTechnicalNotes: (notes: TechnicalNote[]) => void\n  addTechnicalNote: (note: TechnicalNote) => void\n  setComplianceCertificates: (certificates: ComplianceCertificate[]) => void\n  addComplianceCertificate: (certificate: ComplianceCertificate) => void\n  setCalculationEngines: (engines: CalculationEngine[]) => void\n  setActiveProject: (project: PrescriptionProject | null) => void\n  setSelectedTemplate: (template: PrescriptionTemplate | null) => void\n\n  setLoading: (loading: boolean) => void\n  toggleDarkMode: () => void\n  toggleSidebar: () => void\n  filterProducts: () => void\n}\n\nexport const useStore = create<AppState>()(\n  persist(\n    (set, get) => ({\n      // Initial state\n      user: null,\n      isAuthenticated: false,\n      products: [],\n      filteredProducts: [],\n      searchQuery: '',\n      selectedCategory: '',\n      alerts: [],\n      unreadAlertsCount: 0,\n\n      // Hub d'Information initial state\n      marketAlerts: [],\n      stockUpdates: [],\n      trainingEvents: [],\n      newsUpdates: [],\n      realTimeConnected: false,\n      lastUpdateTime: new Date().toISOString(),\n\n      // Espace Conseil Technique initial state\n      technicalDocuments: [],\n      compatibilityChecks: [],\n      expertConsultations: [],\n      expertProfiles: [],\n      technicalResources: [],\n      technicalReports: [],\n      selectedExpert: null,\n      activeConsultation: null,\n\n      // Module Prescripteur Professionnel initial state\n      prescriptionTemplates: [],\n      prescriptionProjects: [],\n      technicalNotes: [],\n      complianceCertificates: [],\n      calculationEngines: [],\n      activeProject: null,\n      selectedTemplate: null,\n\n      isLoading: false,\n      isDarkMode: false,\n      sidebarOpen: false,\n\n      // Actions\n      setUser: (user) => set({ \n        user, \n        isAuthenticated: !!user \n      }),\n\n      setProducts: (products) => {\n        set({ products })\n        get().filterProducts()\n      },\n\n      setSearchQuery: (searchQuery) => {\n        set({ searchQuery })\n        get().filterProducts()\n      },\n\n      setSelectedCategory: (selectedCategory) => {\n        set({ selectedCategory })\n        get().filterProducts()\n      },\n\n      setAlerts: (alerts) => {\n        const unreadAlertsCount = alerts.filter(alert => !alert.is_read).length\n        set({ alerts, unreadAlertsCount })\n      },\n\n      markAlertAsRead: (alertId) => {\n        const alerts = get().alerts.map(alert =>\n          alert.id === alertId ? { ...alert, is_read: true } : alert\n        )\n        const unreadAlertsCount = alerts.filter(alert => !alert.is_read).length\n        set({ alerts, unreadAlertsCount })\n      },\n\n      // Hub d'Information actions\n      setMarketAlerts: (marketAlerts) => {\n        set({ marketAlerts })\n        get().updateLastUpdateTime()\n      },\n\n      addMarketAlert: (alert) => {\n        const marketAlerts = [alert, ...get().marketAlerts].slice(0, 50) // Garder les 50 plus récentes\n        set({ marketAlerts })\n        get().updateLastUpdateTime()\n      },\n\n      setStockUpdates: (stockUpdates) => {\n        set({ stockUpdates })\n        get().updateLastUpdateTime()\n      },\n\n      addStockUpdate: (update) => {\n        const stockUpdates = [update, ...get().stockUpdates].slice(0, 100) // Garder les 100 plus récentes\n        set({ stockUpdates })\n        get().updateLastUpdateTime()\n      },\n\n      setTrainingEvents: (trainingEvents) => set({ trainingEvents }),\n\n      setNewsUpdates: (newsUpdates) => set({ newsUpdates }),\n\n      addNewsUpdate: (news) => {\n        const newsUpdates = [news, ...get().newsUpdates].slice(0, 20) // Garder les 20 plus récentes\n        set({ newsUpdates })\n        get().updateLastUpdateTime()\n      },\n\n      setRealTimeConnected: (realTimeConnected) => set({ realTimeConnected }),\n\n      updateLastUpdateTime: () => set({ lastUpdateTime: new Date().toISOString() }),\n\n      // Espace Conseil Technique actions\n      setTechnicalDocuments: (technicalDocuments) => set({ technicalDocuments }),\n\n      addTechnicalDocument: (document) => {\n        const technicalDocuments = [document, ...get().technicalDocuments]\n        set({ technicalDocuments })\n      },\n\n      updateTechnicalDocument: (id, updates) => {\n        const technicalDocuments = get().technicalDocuments.map(doc =>\n          doc.id === id ? { ...doc, ...updates } : doc\n        )\n        set({ technicalDocuments })\n      },\n\n      setCompatibilityChecks: (compatibilityChecks) => set({ compatibilityChecks }),\n\n      addCompatibilityCheck: (check) => {\n        const compatibilityChecks = [check, ...get().compatibilityChecks]\n        set({ compatibilityChecks })\n      },\n\n      setExpertConsultations: (expertConsultations) => set({ expertConsultations }),\n\n      addExpertConsultation: (consultation) => {\n        const expertConsultations = [consultation, ...get().expertConsultations]\n        set({ expertConsultations })\n      },\n\n      updateExpertConsultation: (id, updates) => {\n        const expertConsultations = get().expertConsultations.map(consult =>\n          consult.id === id ? { ...consult, ...updates } : consult\n        )\n        set({ expertConsultations })\n      },\n\n      setExpertProfiles: (expertProfiles) => set({ expertProfiles }),\n\n      setTechnicalResources: (technicalResources) => set({ technicalResources }),\n\n      setTechnicalReports: (technicalReports) => set({ technicalReports }),\n\n      addTechnicalReport: (report) => {\n        const technicalReports = [report, ...get().technicalReports]\n        set({ technicalReports })\n      },\n\n      setSelectedExpert: (selectedExpert) => set({ selectedExpert }),\n\n      setActiveConsultation: (activeConsultation) => set({ activeConsultation }),\n\n      // Module Prescripteur Professionnel actions\n      setPrescriptionTemplates: (prescriptionTemplates) => set({ prescriptionTemplates }),\n\n      setPrescriptionProjects: (prescriptionProjects) => set({ prescriptionProjects }),\n\n      addPrescriptionProject: (project) => {\n        const prescriptionProjects = [project, ...get().prescriptionProjects]\n        set({ prescriptionProjects })\n      },\n\n      updatePrescriptionProject: (id, updates) => {\n        const prescriptionProjects = get().prescriptionProjects.map(project =>\n          project.id === id ? { ...project, ...updates } : project\n        )\n        set({ prescriptionProjects })\n      },\n\n      setTechnicalNotes: (technicalNotes) => set({ technicalNotes }),\n\n      addTechnicalNote: (note) => {\n        const technicalNotes = [note, ...get().technicalNotes]\n        set({ technicalNotes })\n      },\n\n      setComplianceCertificates: (complianceCertificates) => set({ complianceCertificates }),\n\n      addComplianceCertificate: (certificate) => {\n        const complianceCertificates = [certificate, ...get().complianceCertificates]\n        set({ complianceCertificates })\n      },\n\n      setCalculationEngines: (calculationEngines) => set({ calculationEngines }),\n\n      setActiveProject: (activeProject) => set({ activeProject }),\n\n      setSelectedTemplate: (selectedTemplate) => set({ selectedTemplate }),\n\n      setLoading: (isLoading) => set({ isLoading }),\n\n      toggleDarkMode: () => set((state) => ({ \n        isDarkMode: !state.isDarkMode \n      })),\n\n      toggleSidebar: () => set((state) => ({ \n        sidebarOpen: !state.sidebarOpen \n      })),\n\n      filterProducts: () => {\n        const { products, searchQuery, selectedCategory } = get()\n        \n        let filtered = products\n\n        if (selectedCategory) {\n          filtered = filtered.filter(product => \n            product.category === selectedCategory\n          )\n        }\n\n        if (searchQuery) {\n          const query = searchQuery.toLowerCase()\n          filtered = filtered.filter(product =>\n            product.name.toLowerCase().includes(query) ||\n            product.description.toLowerCase().includes(query) ||\n            product.brand.toLowerCase().includes(query) ||\n            product.model.toLowerCase().includes(query)\n          )\n        }\n\n        set({ filteredProducts: filtered })\n      },\n    }),\n    {\n      name: 'pro-matos-storage',\n      partialize: (state) => ({\n        user: state.user,\n        isAuthenticated: state.isAuthenticated,\n        isDarkMode: state.isDarkMode,\n      }),\n    }\n  )\n)\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AA2HO,MAAM,WAAW,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC3B,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,MAAM;QACN,iBAAiB;QACjB,UAAU,EAAE;QACZ,kBAAkB,EAAE;QACpB,aAAa;QACb,kBAAkB;QAClB,QAAQ,EAAE;QACV,mBAAmB;QAEnB,kCAAkC;QAClC,cAAc,EAAE;QAChB,cAAc,EAAE;QAChB,gBAAgB,EAAE;QAClB,aAAa,EAAE;QACf,mBAAmB;QACnB,gBAAgB,IAAI,OAAO,WAAW;QAEtC,yCAAyC;QACzC,oBAAoB,EAAE;QACtB,qBAAqB,EAAE;QACvB,qBAAqB,EAAE;QACvB,gBAAgB,EAAE;QAClB,oBAAoB,EAAE;QACtB,kBAAkB,EAAE;QACpB,gBAAgB;QAChB,oBAAoB;QAEpB,kDAAkD;QAClD,uBAAuB,EAAE;QACzB,sBAAsB,EAAE;QACxB,gBAAgB,EAAE;QAClB,wBAAwB,EAAE;QAC1B,oBAAoB,EAAE;QACtB,eAAe;QACf,kBAAkB;QAElB,WAAW;QACX,YAAY;QACZ,aAAa;QAEb,UAAU;QACV,SAAS,CAAC,OAAS,IAAI;gBACrB;gBACA,iBAAiB,CAAC,CAAC;YACrB;QAEA,aAAa,CAAC;YACZ,IAAI;gBAAE;YAAS;YACf,MAAM,cAAc;QACtB;QAEA,gBAAgB,CAAC;YACf,IAAI;gBAAE;YAAY;YAClB,MAAM,cAAc;QACtB;QAEA,qBAAqB,CAAC;YACpB,IAAI;gBAAE;YAAiB;YACvB,MAAM,cAAc;QACtB;QAEA,WAAW,CAAC;YACV,MAAM,oBAAoB,OAAO,MAAM,CAAC,CAAA,QAAS,CAAC,MAAM,OAAO,EAAE,MAAM;YACvE,IAAI;gBAAE;gBAAQ;YAAkB;QAClC;QAEA,iBAAiB,CAAC;YAChB,MAAM,SAAS,MAAM,MAAM,CAAC,GAAG,CAAC,CAAA,QAC9B,MAAM,EAAE,KAAK,UAAU;oBAAE,GAAG,KAAK;oBAAE,SAAS;gBAAK,IAAI;YAEvD,MAAM,oBAAoB,OAAO,MAAM,CAAC,CAAA,QAAS,CAAC,MAAM,OAAO,EAAE,MAAM;YACvE,IAAI;gBAAE;gBAAQ;YAAkB;QAClC;QAEA,4BAA4B;QAC5B,iBAAiB,CAAC;YAChB,IAAI;gBAAE;YAAa;YACnB,MAAM,oBAAoB;QAC5B;QAEA,gBAAgB,CAAC;YACf,MAAM,eAAe;gBAAC;mBAAU,MAAM,YAAY;aAAC,CAAC,KAAK,CAAC,GAAG,IAAI,8BAA8B;;YAC/F,IAAI;gBAAE;YAAa;YACnB,MAAM,oBAAoB;QAC5B;QAEA,iBAAiB,CAAC;YAChB,IAAI;gBAAE;YAAa;YACnB,MAAM,oBAAoB;QAC5B;QAEA,gBAAgB,CAAC;YACf,MAAM,eAAe;gBAAC;mBAAW,MAAM,YAAY;aAAC,CAAC,KAAK,CAAC,GAAG,KAAK,+BAA+B;;YAClG,IAAI;gBAAE;YAAa;YACnB,MAAM,oBAAoB;QAC5B;QAEA,mBAAmB,CAAC,iBAAmB,IAAI;gBAAE;YAAe;QAE5D,gBAAgB,CAAC,cAAgB,IAAI;gBAAE;YAAY;QAEnD,eAAe,CAAC;YACd,MAAM,cAAc;gBAAC;mBAAS,MAAM,WAAW;aAAC,CAAC,KAAK,CAAC,GAAG,IAAI,8BAA8B;;YAC5F,IAAI;gBAAE;YAAY;YAClB,MAAM,oBAAoB;QAC5B;QAEA,sBAAsB,CAAC,oBAAsB,IAAI;gBAAE;YAAkB;QAErE,sBAAsB,IAAM,IAAI;gBAAE,gBAAgB,IAAI,OAAO,WAAW;YAAG;QAE3E,mCAAmC;QACnC,uBAAuB,CAAC,qBAAuB,IAAI;gBAAE;YAAmB;QAExE,sBAAsB,CAAC;YACrB,MAAM,qBAAqB;gBAAC;mBAAa,MAAM,kBAAkB;aAAC;YAClE,IAAI;gBAAE;YAAmB;QAC3B;QAEA,yBAAyB,CAAC,IAAI;YAC5B,MAAM,qBAAqB,MAAM,kBAAkB,CAAC,GAAG,CAAC,CAAA,MACtD,IAAI,EAAE,KAAK,KAAK;oBAAE,GAAG,GAAG;oBAAE,GAAG,OAAO;gBAAC,IAAI;YAE3C,IAAI;gBAAE;YAAmB;QAC3B;QAEA,wBAAwB,CAAC,sBAAwB,IAAI;gBAAE;YAAoB;QAE3E,uBAAuB,CAAC;YACtB,MAAM,sBAAsB;gBAAC;mBAAU,MAAM,mBAAmB;aAAC;YACjE,IAAI;gBAAE;YAAoB;QAC5B;QAEA,wBAAwB,CAAC,sBAAwB,IAAI;gBAAE;YAAoB;QAE3E,uBAAuB,CAAC;YACtB,MAAM,sBAAsB;gBAAC;mBAAiB,MAAM,mBAAmB;aAAC;YACxE,IAAI;gBAAE;YAAoB;QAC5B;QAEA,0BAA0B,CAAC,IAAI;YAC7B,MAAM,sBAAsB,MAAM,mBAAmB,CAAC,GAAG,CAAC,CAAA,UACxD,QAAQ,EAAE,KAAK,KAAK;oBAAE,GAAG,OAAO;oBAAE,GAAG,OAAO;gBAAC,IAAI;YAEnD,IAAI;gBAAE;YAAoB;QAC5B;QAEA,mBAAmB,CAAC,iBAAmB,IAAI;gBAAE;YAAe;QAE5D,uBAAuB,CAAC,qBAAuB,IAAI;gBAAE;YAAmB;QAExE,qBAAqB,CAAC,mBAAqB,IAAI;gBAAE;YAAiB;QAElE,oBAAoB,CAAC;YACnB,MAAM,mBAAmB;gBAAC;mBAAW,MAAM,gBAAgB;aAAC;YAC5D,IAAI;gBAAE;YAAiB;QACzB;QAEA,mBAAmB,CAAC,iBAAmB,IAAI;gBAAE;YAAe;QAE5D,uBAAuB,CAAC,qBAAuB,IAAI;gBAAE;YAAmB;QAExE,4CAA4C;QAC5C,0BAA0B,CAAC,wBAA0B,IAAI;gBAAE;YAAsB;QAEjF,yBAAyB,CAAC,uBAAyB,IAAI;gBAAE;YAAqB;QAE9E,wBAAwB,CAAC;YACvB,MAAM,uBAAuB;gBAAC;mBAAY,MAAM,oBAAoB;aAAC;YACrE,IAAI;gBAAE;YAAqB;QAC7B;QAEA,2BAA2B,CAAC,IAAI;YAC9B,MAAM,uBAAuB,MAAM,oBAAoB,CAAC,GAAG,CAAC,CAAA,UAC1D,QAAQ,EAAE,KAAK,KAAK;oBAAE,GAAG,OAAO;oBAAE,GAAG,OAAO;gBAAC,IAAI;YAEnD,IAAI;gBAAE;YAAqB;QAC7B;QAEA,mBAAmB,CAAC,iBAAmB,IAAI;gBAAE;YAAe;QAE5D,kBAAkB,CAAC;YACjB,MAAM,iBAAiB;gBAAC;mBAAS,MAAM,cAAc;aAAC;YACtD,IAAI;gBAAE;YAAe;QACvB;QAEA,2BAA2B,CAAC,yBAA2B,IAAI;gBAAE;YAAuB;QAEpF,0BAA0B,CAAC;YACzB,MAAM,yBAAyB;gBAAC;mBAAgB,MAAM,sBAAsB;aAAC;YAC7E,IAAI;gBAAE;YAAuB;QAC/B;QAEA,uBAAuB,CAAC,qBAAuB,IAAI;gBAAE;YAAmB;QAExE,kBAAkB,CAAC,gBAAkB,IAAI;gBAAE;YAAc;QAEzD,qBAAqB,CAAC,mBAAqB,IAAI;gBAAE;YAAiB;QAElE,YAAY,CAAC,YAAc,IAAI;gBAAE;YAAU;QAE3C,gBAAgB,IAAM,IAAI,CAAC,QAAU,CAAC;oBACpC,YAAY,CAAC,MAAM,UAAU;gBAC/B,CAAC;QAED,eAAe,IAAM,IAAI,CAAC,QAAU,CAAC;oBACnC,aAAa,CAAC,MAAM,WAAW;gBACjC,CAAC;QAED,gBAAgB;YACd,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,gBAAgB,EAAE,GAAG;YAEpD,IAAI,WAAW;YAEf,IAAI,kBAAkB;gBACpB,WAAW,SAAS,MAAM,CAAC,CAAA,UACzB,QAAQ,QAAQ,KAAK;YAEzB;YAEA,IAAI,aAAa;gBACf,MAAM,QAAQ,YAAY,WAAW;gBACrC,WAAW,SAAS,MAAM,CAAC,CAAA,UACzB,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,UACpC,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,UAC3C,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,UACrC,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC;YAEzC;YAEA,IAAI;gBAAE,kBAAkB;YAAS;QACnC;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,iBAAiB,MAAM,eAAe;YACtC,YAAY,MAAM,UAAU;QAC9B,CAAC;AACH", "debugId": null}}, {"offset": {"line": 356, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/services/prescriptorService.ts"], "sourcesContent": ["import { \n  PrescriptionTemplate, \n  PrescriptionProject, \n  TechnicalNote, \n  ComplianceCertificate, \n  CalculationEngine,\n  CalculationParameter,\n  ValidationRule,\n  WarrantyContract,\n  Product \n} from '@/lib/supabase'\n\n// Service pour le Module Prescripteur Professionnel\nexport class PrescriptorService {\n  \n  // Simulation des templates de prescription\n  static generatePrescriptionTemplates(): PrescriptionTemplate[] {\n    return [\n      {\n        id: 'template_001',\n        name: 'Installation Électrique Tertiaire',\n        description: 'Template complet pour installations électriques de bâtiments tertiaires selon NF C 15-100',\n        category: 'electrical',\n        target_audience: 'engineer',\n        complexity_level: 'intermediate',\n        template_data: {\n          sections: ['Analyse des besoins', 'Calculs de puissance', 'Schémas unifilaires', 'Liste du matériel'],\n          default_values: { tension: 400, frequence: 50, facteur_puissance: 0.8 }\n        },\n        required_fields: ['surface_totale', 'nombre_postes_travail', 'puissance_eclairage', 'puissance_prises'],\n        optional_fields: ['climatisation', 'ascenseurs', 'parking'],\n        calculations: [\n          {\n            id: 'calc_001',\n            name: 'Puissance totale installée',\n            formula: 'P_total = P_eclairage + P_prises + P_force + P_climatisation',\n            variables: { P_eclairage: 'number', P_prises: 'number', P_force: 'number', P_climatisation: 'number' },\n            unit: 'kW',\n            validation_rules: ['P_total > 0', 'P_total < 1000'],\n            safety_factors: { simultaneite: 0.8, reserve: 1.2 }\n          }\n        ],\n        compliance_standards: ['NF C 15-100', 'RT 2012', 'Accessibilité PMR'],\n        membership_required: 'silver',\n        is_featured: true,\n        usage_count: 247,\n        rating: 4.8,\n        created_by: 'Pro Matos Team',\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString(),\n      },\n      {\n        id: 'template_002',\n        name: 'Installation Photovoltaïque Résidentielle',\n        description: 'Dimensionnement et prescription pour installations PV résidentielles avec stockage',\n        category: 'energy',\n        target_audience: 'engineer',\n        complexity_level: 'advanced',\n        template_data: {\n          sections: ['Étude d\\'ensoleillement', 'Dimensionnement PV', 'Stockage batterie', 'Raccordement réseau'],\n          default_values: { irradiation_annuelle: 1800, rendement_onduleur: 0.95, degradation_annuelle: 0.005 }\n        },\n        required_fields: ['consommation_annuelle', 'surface_toiture', 'orientation', 'inclinaison'],\n        optional_fields: ['masques_solaires', 'stockage_souhaite', 'injection_reseau'],\n        calculations: [\n          {\n            id: 'calc_002',\n            name: 'Production annuelle estimée',\n            formula: 'E_prod = P_crete * irradiation * rendement_systeme * (1 - degradation)^annees',\n            variables: { P_crete: 'number', irradiation: 'number', rendement_systeme: 'number', degradation: 'number', annees: 'number' },\n            unit: 'kWh/an',\n            validation_rules: ['P_crete > 0', 'irradiation > 800', 'rendement_systeme < 1'],\n            safety_factors: { meteo: 0.9, vieillissement: 0.95 }\n          }\n        ],\n        compliance_standards: ['NF C 15-100', 'UTE C 15-712-1', 'Arrêté tarifaire'],\n        membership_required: 'gold',\n        is_featured: true,\n        usage_count: 189,\n        rating: 4.9,\n        created_by: 'Dr. Aminata Traoré',\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString(),\n      },\n      {\n        id: 'template_003',\n        name: 'Automatisme Industriel',\n        description: 'Prescription d\\'automatismes pour lignes de production industrielle',\n        category: 'automation',\n        target_audience: 'engineer',\n        complexity_level: 'expert',\n        template_data: {\n          sections: ['Analyse fonctionnelle', 'Architecture automate', 'Réseaux communication', 'Supervision'],\n          default_values: { cycle_time: 100, safety_level: 'SIL2', communication: 'Ethernet' }\n        },\n        required_fields: ['nombre_entrees', 'nombre_sorties', 'vitesse_production', 'niveau_securite'],\n        optional_fields: ['vision_industrielle', 'robotique', 'traçabilite'],\n        calculations: [\n          {\n            id: 'calc_003',\n            name: 'Temps de cycle automate',\n            formula: 'T_cycle = (N_entrees * T_lecture + N_sorties * T_ecriture + T_traitement) * facteur_securite',\n            variables: { N_entrees: 'number', T_lecture: 'number', N_sorties: 'number', T_ecriture: 'number', T_traitement: 'number', facteur_securite: 'number' },\n            unit: 'ms',\n            validation_rules: ['T_cycle < 100', 'T_cycle > 1'],\n            safety_factors: { marge_calcul: 1.5, reserve_memoire: 2.0 }\n          }\n        ],\n        compliance_standards: ['IEC 61131', 'IEC 61508', 'Directive Machines'],\n        membership_required: 'platinum',\n        is_featured: false,\n        usage_count: 67,\n        rating: 4.7,\n        created_by: 'Ing. Jean-Baptiste Kone',\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString(),\n      }\n    ]\n  }\n\n  // Simulation des projets de prescription\n  static generatePrescriptionProjects(): PrescriptionProject[] {\n    return [\n      {\n        id: 'project_001',\n        user_id: 'user_001',\n        template_id: 'template_001',\n        project_name: 'Immeuble de bureaux Plateau - Abidjan',\n        client_name: 'SODECI Immobilier',\n        client_company: 'SODECI',\n        project_description: 'Installation électrique complète pour immeuble de bureaux 8 étages',\n        location: 'Plateau, Abidjan, Côte d\\'Ivoire',\n        project_data: {\n          surface_totale: 4500,\n          nombre_etages: 8,\n          nombre_postes_travail: 180,\n          puissance_eclairage: 45,\n          puissance_prises: 72,\n          climatisation: true,\n          ascenseurs: 2\n        },\n        calculated_values: {\n          puissance_totale: 285.6,\n          courant_nominal: 410,\n          section_cable_principal: 185,\n          nombre_tableaux: 9\n        },\n        selected_products: ['prod_001', 'prod_002', 'prod_003'],\n        total_cost: 45000000, // 45M FCFA\n        status: 'in_progress',\n        compliance_verified: true,\n        warranty_terms: 'Garantie 2 ans pièces et main d\\'œuvre',\n        delivery_date: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString(), // Dans 90 jours\n        notes: 'Projet prioritaire - Livraison Q2 2024',\n        created_at: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(), // Il y a 15 jours\n        updated_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // Il y a 2 jours\n      },\n      {\n        id: 'project_002',\n        user_id: 'user_002',\n        template_id: 'template_002',\n        project_name: 'Villa solaire Almadies - Dakar',\n        client_name: 'Famille Diop',\n        client_company: 'Particulier',\n        project_description: 'Installation photovoltaïque 15kWc avec stockage batterie lithium',\n        location: 'Almadies, Dakar, Sénégal',\n        project_data: {\n          consommation_annuelle: 8500,\n          surface_toiture: 120,\n          orientation: 'Sud',\n          inclinaison: 15,\n          stockage_souhaite: true,\n          capacite_batterie: 30\n        },\n        calculated_values: {\n          puissance_crete: 15.2,\n          production_annuelle: 24680,\n          autonomie_jours: 3.5,\n          taux_autoconsommation: 85\n        },\n        selected_products: ['prod_pv_001', 'prod_bat_001', 'prod_ond_001'],\n        total_cost: 18500000, // 18.5M FCFA\n        status: 'approved',\n        compliance_verified: true,\n        warranty_terms: 'Garantie 10 ans panneaux, 5 ans onduleur, 8 ans batteries',\n        delivery_date: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000).toISOString(), // Dans 45 jours\n        notes: 'Installation avec monitoring IoT inclus',\n        created_at: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000).toISOString(), // Il y a 8 jours\n        updated_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), // Il y a 1 jour\n      }\n    ]\n  }\n\n  // Simulation des moteurs de calcul\n  static generateCalculationEngines(): CalculationEngine[] {\n    return [\n      {\n        id: 'calc_engine_001',\n        name: 'Dimensionnement Câbles BT',\n        description: 'Calcul automatique des sections de câbles basse tension selon NF C 15-100',\n        category: 'cable_sizing',\n        input_parameters: [\n          { name: 'courant', type: 'number', unit: 'A', min_value: 1, max_value: 1000, required: true, description: 'Courant nominal du circuit' },\n          { name: 'longueur', type: 'number', unit: 'm', min_value: 1, max_value: 500, required: true, description: 'Longueur du câble' },\n          { name: 'chute_tension_max', type: 'number', unit: '%', default_value: 3, required: true, description: 'Chute de tension maximale admissible' },\n          { name: 'mode_pose', type: 'select', options: ['enterré', 'aérien', 'goulotte', 'chemin_cables'], required: true, description: 'Mode de pose du câble' }\n        ],\n        output_parameters: [\n          { name: 'section_minimale', type: 'number', unit: 'mm²', required: true, description: 'Section minimale calculée' },\n          { name: 'section_normalisee', type: 'number', unit: 'mm²', required: true, description: 'Section normalisée supérieure' },\n          { name: 'chute_tension_reelle', type: 'number', unit: '%', required: true, description: 'Chute de tension réelle' }\n        ],\n        formula_set: {\n          'section_minimale': 'S = (ρ * L * I) / (U * ΔU_max)',\n          'chute_tension': 'ΔU = (ρ * L * I) / (S * U)'\n        },\n        validation_rules: [\n          { condition: 'chute_tension_reelle <= chute_tension_max', message: 'Chute de tension respectée', severity: 'info' },\n          { condition: 'section_normalisee >= section_minimale', message: 'Section normalisée suffisante', severity: 'info' },\n          { condition: 'courant <= intensite_admissible', message: 'Intensité admissible dépassée', severity: 'error' }\n        ],\n        safety_standards: ['NF C 15-100', 'IEC 60364'],\n        is_certified: true,\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString(),\n      }\n    ]\n  }\n\n  // Simulation des notes techniques\n  static generateTechnicalNotes(projectId: string): TechnicalNote[] {\n    return [\n      {\n        id: 'note_001',\n        project_id: projectId,\n        title: 'Calcul de la puissance totale installée',\n        content: 'La puissance totale installée a été calculée en tenant compte des facteurs de simultanéité et des coefficients de sécurité selon la norme NF C 15-100.',\n        note_type: 'calculation',\n        auto_generated: true,\n        template_section: 'Calculs de puissance',\n        products_referenced: ['prod_001'],\n        standards_referenced: ['NF C 15-100'],\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString(),\n      },\n      {\n        id: 'note_002',\n        project_id: projectId,\n        title: 'Recommandations de mise en œuvre',\n        content: 'Il est recommandé d\\'installer une protection différentielle 30mA sur tous les circuits prises et éclairage selon l\\'article 411.3.3 de la NF C 15-100.',\n        note_type: 'recommendation',\n        auto_generated: false,\n        template_section: 'Spécifications techniques',\n        products_referenced: ['prod_002'],\n        standards_referenced: ['NF C 15-100'],\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString(),\n      }\n    ]\n  }\n\n  // Méthode pour calculer automatiquement les valeurs d'un projet\n  static calculateProjectValues(projectData: Record<string, any>, template: PrescriptionTemplate): Record<string, any> {\n    const results: Record<string, any> = {}\n    \n    // Simulation de calculs basés sur le template\n    if (template.id === 'template_001') {\n      // Calculs pour installation électrique tertiaire\n      const surface = projectData.surface_totale || 0\n      const postes = projectData.nombre_postes_travail || 0\n      \n      results.puissance_eclairage = surface * 10 // 10W/m²\n      results.puissance_prises = postes * 400 // 400W/poste\n      results.puissance_totale = results.puissance_eclairage + results.puissance_prises\n      results.courant_nominal = results.puissance_totale / (400 * Math.sqrt(3) * 0.8) // Triphasé 400V\n      results.section_cable_principal = this.calculateCableSection(results.courant_nominal)\n    }\n    \n    return results\n  }\n\n  // Méthode utilitaire pour calculer la section de câble\n  private static calculateCableSection(current: number): number {\n    const sections = [1.5, 2.5, 4, 6, 10, 16, 25, 35, 50, 70, 95, 120, 150, 185, 240, 300]\n    const intensities = [16, 24, 32, 41, 57, 76, 101, 129, 157, 196, 246, 284, 319, 353, 419, 483]\n    \n    for (let i = 0; i < intensities.length; i++) {\n      if (current <= intensities[i]) {\n        return sections[i]\n      }\n    }\n    \n    return sections[sections.length - 1] // Section maximale si dépassement\n  }\n\n  // Méthode pour générer un certificat de conformité\n  static generateComplianceCertificate(project: PrescriptionProject): ComplianceCertificate {\n    return {\n      id: `cert_${Date.now()}`,\n      project_id: project.id,\n      certificate_type: 'design',\n      certificate_number: `PMO-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`,\n      issued_by: 'Pro Matos Afrique Ouest - Bureau d\\'Études Certifié',\n      issued_to: `${project.client_name} - ${project.client_company}`,\n      valid_from: new Date().toISOString(),\n      valid_until: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(), // 1 an\n      standards_covered: ['NF C 15-100', 'RT 2012'],\n      test_results: {\n        'Calculs de puissance': 'Conforme',\n        'Dimensionnement câbles': 'Conforme',\n        'Protection différentielle': 'Conforme'\n      },\n      limitations: ['Valable uniquement pour la conception présentée', 'Révision nécessaire en cas de modification'],\n      digital_signature: 'SHA256:a1b2c3d4e5f6...',\n      qr_verification: `https://promatos.com/verify/${Date.now()}`,\n      is_active: true,\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString(),\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAaO,MAAM;IAEX,2CAA2C;IAC3C,OAAO,gCAAwD;QAC7D,OAAO;YACL;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,UAAU;gBACV,iBAAiB;gBACjB,kBAAkB;gBAClB,eAAe;oBACb,UAAU;wBAAC;wBAAuB;wBAAwB;wBAAuB;qBAAoB;oBACrG,gBAAgB;wBAAE,SAAS;wBAAK,WAAW;wBAAI,mBAAmB;oBAAI;gBACxE;gBACA,iBAAiB;oBAAC;oBAAkB;oBAAyB;oBAAuB;iBAAmB;gBACvG,iBAAiB;oBAAC;oBAAiB;oBAAc;iBAAU;gBAC3D,cAAc;oBACZ;wBACE,IAAI;wBACJ,MAAM;wBACN,SAAS;wBACT,WAAW;4BAAE,aAAa;4BAAU,UAAU;4BAAU,SAAS;4BAAU,iBAAiB;wBAAS;wBACrG,MAAM;wBACN,kBAAkB;4BAAC;4BAAe;yBAAiB;wBACnD,gBAAgB;4BAAE,cAAc;4BAAK,SAAS;wBAAI;oBACpD;iBACD;gBACD,sBAAsB;oBAAC;oBAAe;oBAAW;iBAAoB;gBACrE,qBAAqB;gBACrB,aAAa;gBACb,aAAa;gBACb,QAAQ;gBACR,YAAY;gBACZ,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,UAAU;gBACV,iBAAiB;gBACjB,kBAAkB;gBAClB,eAAe;oBACb,UAAU;wBAAC;wBAA2B;wBAAsB;wBAAqB;qBAAsB;oBACvG,gBAAgB;wBAAE,sBAAsB;wBAAM,oBAAoB;wBAAM,sBAAsB;oBAAM;gBACtG;gBACA,iBAAiB;oBAAC;oBAAyB;oBAAmB;oBAAe;iBAAc;gBAC3F,iBAAiB;oBAAC;oBAAoB;oBAAqB;iBAAmB;gBAC9E,cAAc;oBACZ;wBACE,IAAI;wBACJ,MAAM;wBACN,SAAS;wBACT,WAAW;4BAAE,SAAS;4BAAU,aAAa;4BAAU,mBAAmB;4BAAU,aAAa;4BAAU,QAAQ;wBAAS;wBAC5H,MAAM;wBACN,kBAAkB;4BAAC;4BAAe;4BAAqB;yBAAwB;wBAC/E,gBAAgB;4BAAE,OAAO;4BAAK,gBAAgB;wBAAK;oBACrD;iBACD;gBACD,sBAAsB;oBAAC;oBAAe;oBAAkB;iBAAmB;gBAC3E,qBAAqB;gBACrB,aAAa;gBACb,aAAa;gBACb,QAAQ;gBACR,YAAY;gBACZ,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,UAAU;gBACV,iBAAiB;gBACjB,kBAAkB;gBAClB,eAAe;oBACb,UAAU;wBAAC;wBAAyB;wBAAyB;wBAAyB;qBAAc;oBACpG,gBAAgB;wBAAE,YAAY;wBAAK,cAAc;wBAAQ,eAAe;oBAAW;gBACrF;gBACA,iBAAiB;oBAAC;oBAAkB;oBAAkB;oBAAsB;iBAAkB;gBAC9F,iBAAiB;oBAAC;oBAAuB;oBAAa;iBAAc;gBACpE,cAAc;oBACZ;wBACE,IAAI;wBACJ,MAAM;wBACN,SAAS;wBACT,WAAW;4BAAE,WAAW;4BAAU,WAAW;4BAAU,WAAW;4BAAU,YAAY;4BAAU,cAAc;4BAAU,kBAAkB;wBAAS;wBACrJ,MAAM;wBACN,kBAAkB;4BAAC;4BAAiB;yBAAc;wBAClD,gBAAgB;4BAAE,cAAc;4BAAK,iBAAiB;wBAAI;oBAC5D;iBACD;gBACD,sBAAsB;oBAAC;oBAAa;oBAAa;iBAAqB;gBACtE,qBAAqB;gBACrB,aAAa;gBACb,aAAa;gBACb,QAAQ;gBACR,YAAY;gBACZ,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;IACH;IAEA,yCAAyC;IACzC,OAAO,+BAAsD;QAC3D,OAAO;YACL;gBACE,IAAI;gBACJ,SAAS;gBACT,aAAa;gBACb,cAAc;gBACd,aAAa;gBACb,gBAAgB;gBAChB,qBAAqB;gBACrB,UAAU;gBACV,cAAc;oBACZ,gBAAgB;oBAChB,eAAe;oBACf,uBAAuB;oBACvB,qBAAqB;oBACrB,kBAAkB;oBAClB,eAAe;oBACf,YAAY;gBACd;gBACA,mBAAmB;oBACjB,kBAAkB;oBAClB,iBAAiB;oBACjB,yBAAyB;oBACzB,iBAAiB;gBACnB;gBACA,mBAAmB;oBAAC;oBAAY;oBAAY;iBAAW;gBACvD,YAAY;gBACZ,QAAQ;gBACR,qBAAqB;gBACrB,gBAAgB;gBAChB,eAAe,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW;gBAC1E,OAAO;gBACP,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW;gBACvE,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;YACxE;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,aAAa;gBACb,cAAc;gBACd,aAAa;gBACb,gBAAgB;gBAChB,qBAAqB;gBACrB,UAAU;gBACV,cAAc;oBACZ,uBAAuB;oBACvB,iBAAiB;oBACjB,aAAa;oBACb,aAAa;oBACb,mBAAmB;oBACnB,mBAAmB;gBACrB;gBACA,mBAAmB;oBACjB,iBAAiB;oBACjB,qBAAqB;oBACrB,iBAAiB;oBACjB,uBAAuB;gBACzB;gBACA,mBAAmB;oBAAC;oBAAe;oBAAgB;iBAAe;gBAClE,YAAY;gBACZ,QAAQ;gBACR,qBAAqB;gBACrB,gBAAgB;gBAChB,eAAe,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW;gBAC1E,OAAO;gBACP,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;gBACtE,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;YACxE;SACD;IACH;IAEA,mCAAmC;IACnC,OAAO,6BAAkD;QACvD,OAAO;YACL;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,UAAU;gBACV,kBAAkB;oBAChB;wBAAE,MAAM;wBAAW,MAAM;wBAAU,MAAM;wBAAK,WAAW;wBAAG,WAAW;wBAAM,UAAU;wBAAM,aAAa;oBAA6B;oBACvI;wBAAE,MAAM;wBAAY,MAAM;wBAAU,MAAM;wBAAK,WAAW;wBAAG,WAAW;wBAAK,UAAU;wBAAM,aAAa;oBAAoB;oBAC9H;wBAAE,MAAM;wBAAqB,MAAM;wBAAU,MAAM;wBAAK,eAAe;wBAAG,UAAU;wBAAM,aAAa;oBAAuC;oBAC9I;wBAAE,MAAM;wBAAa,MAAM;wBAAU,SAAS;4BAAC;4BAAW;4BAAU;4BAAY;yBAAgB;wBAAE,UAAU;wBAAM,aAAa;oBAAwB;iBACxJ;gBACD,mBAAmB;oBACjB;wBAAE,MAAM;wBAAoB,MAAM;wBAAU,MAAM;wBAAO,UAAU;wBAAM,aAAa;oBAA4B;oBAClH;wBAAE,MAAM;wBAAsB,MAAM;wBAAU,MAAM;wBAAO,UAAU;wBAAM,aAAa;oBAAgC;oBACxH;wBAAE,MAAM;wBAAwB,MAAM;wBAAU,MAAM;wBAAK,UAAU;wBAAM,aAAa;oBAA0B;iBACnH;gBACD,aAAa;oBACX,oBAAoB;oBACpB,iBAAiB;gBACnB;gBACA,kBAAkB;oBAChB;wBAAE,WAAW;wBAA6C,SAAS;wBAA8B,UAAU;oBAAO;oBAClH;wBAAE,WAAW;wBAA0C,SAAS;wBAAiC,UAAU;oBAAO;oBAClH;wBAAE,WAAW;wBAAmC,SAAS;wBAAiC,UAAU;oBAAQ;iBAC7G;gBACD,kBAAkB;oBAAC;oBAAe;iBAAY;gBAC9C,cAAc;gBACd,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;IACH;IAEA,kCAAkC;IAClC,OAAO,uBAAuB,SAAiB,EAAmB;QAChE,OAAO;YACL;gBACE,IAAI;gBACJ,YAAY;gBACZ,OAAO;gBACP,SAAS;gBACT,WAAW;gBACX,gBAAgB;gBAChB,kBAAkB;gBAClB,qBAAqB;oBAAC;iBAAW;gBACjC,sBAAsB;oBAAC;iBAAc;gBACrC,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,YAAY;gBACZ,OAAO;gBACP,SAAS;gBACT,WAAW;gBACX,gBAAgB;gBAChB,kBAAkB;gBAClB,qBAAqB;oBAAC;iBAAW;gBACjC,sBAAsB;oBAAC;iBAAc;gBACrC,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;IACH;IAEA,gEAAgE;IAChE,OAAO,uBAAuB,WAAgC,EAAE,QAA8B,EAAuB;QACnH,MAAM,UAA+B,CAAC;QAEtC,8CAA8C;QAC9C,IAAI,SAAS,EAAE,KAAK,gBAAgB;YAClC,iDAAiD;YACjD,MAAM,UAAU,YAAY,cAAc,IAAI;YAC9C,MAAM,SAAS,YAAY,qBAAqB,IAAI;YAEpD,QAAQ,mBAAmB,GAAG,UAAU,GAAG,SAAS;;YACpD,QAAQ,gBAAgB,GAAG,SAAS,IAAI,aAAa;;YACrD,QAAQ,gBAAgB,GAAG,QAAQ,mBAAmB,GAAG,QAAQ,gBAAgB;YACjF,QAAQ,eAAe,GAAG,QAAQ,gBAAgB,GAAG,CAAC,MAAM,KAAK,IAAI,CAAC,KAAK,GAAG,EAAE,gBAAgB;;YAChG,QAAQ,uBAAuB,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,eAAe;QACtF;QAEA,OAAO;IACT;IAEA,uDAAuD;IACvD,OAAe,sBAAsB,OAAe,EAAU;QAC5D,MAAM,WAAW;YAAC;YAAK;YAAK;YAAG;YAAG;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAK;YAAK;YAAK;YAAK;SAAI;QACtF,MAAM,cAAc;YAAC;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;SAAI;QAE9F,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,MAAM,EAAE,IAAK;YAC3C,IAAI,WAAW,WAAW,CAAC,EAAE,EAAE;gBAC7B,OAAO,QAAQ,CAAC,EAAE;YACpB;QACF;QAEA,OAAO,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE,CAAC,kCAAkC;;IACzE;IAEA,mDAAmD;IACnD,OAAO,8BAA8B,OAA4B,EAAyB;QACxF,OAAO;YACL,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;YACxB,YAAY,QAAQ,EAAE;YACtB,kBAAkB;YAClB,oBAAoB,CAAC,IAAI,EAAE,IAAI,OAAO,WAAW,GAAG,CAAC,EAAE,OAAO,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC,IAAI;YACrF,WAAW;YACX,WAAW,GAAG,QAAQ,WAAW,CAAC,GAAG,EAAE,QAAQ,cAAc,EAAE;YAC/D,YAAY,IAAI,OAAO,WAAW;YAClC,aAAa,IAAI,KAAK,KAAK,GAAG,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM,WAAW;YACzE,mBAAmB;gBAAC;gBAAe;aAAU;YAC7C,cAAc;gBACZ,wBAAwB;gBACxB,0BAA0B;gBAC1B,6BAA6B;YAC/B;YACA,aAAa;gBAAC;gBAAmD;aAA6C;YAC9G,mBAAmB;YACnB,iBAAiB,CAAC,4BAA4B,EAAE,KAAK,GAAG,IAAI;YAC5D,WAAW;YACX,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY,IAAI,OAAO,WAAW;QACpC;IACF;AACF", "debugId": null}}, {"offset": {"line": 891, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPrice(price: number): string {\n  return new Intl.NumberFormat('fr-FR', {\n    style: 'currency',\n    currency: 'XOF',\n    minimumFractionDigits: 0,\n  }).format(price)\n}\n\nexport function formatDate(date: string | Date): string {\n  return new Intl.DateTimeFormat('fr-FR', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(new Date(date))\n}\n\nexport function getMembershipColor(level: string): string {\n  switch (level) {\n    case 'bronze':\n      return 'text-amber-600 bg-amber-50'\n    case 'silver':\n      return 'text-gray-600 bg-gray-50'\n    case 'gold':\n      return 'text-yellow-600 bg-yellow-50'\n    case 'platinum':\n      return 'text-purple-600 bg-purple-50'\n    default:\n      return 'text-gray-600 bg-gray-50'\n  }\n}\n\nexport function generateQRCode(productId: string): string {\n  // Simulation d'un QR code - à remplacer par une vraie génération\n  return `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(productId)}`\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa;IACvC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,mBAAmB,KAAa;IAC9C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,eAAe,SAAiB;IAC9C,iEAAiE;IACjE,OAAO,CAAC,8DAA8D,EAAE,mBAAmB,YAAY;AACzG;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 953, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/components/prescriptor/QuickCalculator.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { motion } from 'framer-motion'\nimport { \n  Calculator,\n  Zap,\n  AlertTriangle,\n  CheckCircle,\n  Info,\n  Download,\n  Copy,\n  RefreshCw\n} from 'lucide-react'\n\ninterface CalculationResult {\n  section_minimale: number\n  section_normalisee: number\n  chute_tension_reelle: number\n  intensite_admissible: number\n  validation_status: 'valid' | 'warning' | 'error'\n  recommendations: string[]\n  warnings: string[]\n}\n\nexport default function QuickCalculator() {\n  const [inputs, setInputs] = useState({\n    courant: 32,\n    longueur: 50,\n    chute_tension_max: 3,\n    mode_pose: 'enterré',\n    materiau: 'cuivre',\n    temperature: 30\n  })\n  \n  const [result, setResult] = useState<CalculationResult | null>(null)\n  const [isCalculating, setIsCalculating] = useState(false)\n  const [showDetails, setShowDetails] = useState(false)\n\n  // Sections normalisées en mm²\n  const sectionsNormalisees = [1.5, 2.5, 4, 6, 10, 16, 25, 35, 50, 70, 95, 120, 150, 185, 240, 300, 400, 500]\n  \n  // Intensités admissibles selon le mode de pose (simplifié)\n  const intensitesAdmissibles = {\n    'enterré': [23, 31, 42, 54, 75, 100, 133, 171, 207, 258, 319, 367, 412, 456, 542, 621, 689, 754],\n    'aérien': [18, 24, 32, 41, 57, 76, 101, 129, 157, 196, 246, 284, 319, 353, 419, 483, 540, 593],\n    'goulotte': [15, 20, 27, 35, 48, 64, 85, 108, 132, 165, 207, 239, 268, 297, 352, 406, 454, 498],\n    'chemin_cables': [20, 27, 36, 46, 64, 85, 113, 145, 176, 220, 275, 317, 356, 394, 467, 538, 601, 660]\n  }\n\n  const calculateCableSection = () => {\n    setIsCalculating(true)\n    \n    // Simulation de calcul avec délai\n    setTimeout(() => {\n      const { courant, longueur, chute_tension_max, mode_pose, materiau, temperature } = inputs\n      \n      // Résistivité du matériau (Ω.mm²/m)\n      const resistivite = materiau === 'cuivre' ? 0.0225 : 0.037 // aluminium\n      \n      // Tension nominale (V)\n      const tension = 400 // Triphasé\n      \n      // Calcul de la section minimale pour la chute de tension\n      // ΔU = ρ × L × I / S  =>  S = ρ × L × I / ΔU\n      const chute_tension_absolue = (tension * chute_tension_max) / 100\n      const section_minimale_chute = (resistivite * longueur * courant) / chute_tension_absolue\n      \n      // Facteur de correction température\n      const facteur_temperature = temperature > 30 ? 0.87 : 1.0\n      \n      // Section minimale pour l'intensité admissible\n      const intensites = intensitesAdmissibles[mode_pose as keyof typeof intensitesAdmissibles]\n      let section_minimale_intensite = sectionsNormalisees[0]\n      \n      for (let i = 0; i < intensites.length; i++) {\n        if ((intensites[i] * facteur_temperature) >= courant) {\n          section_minimale_intensite = sectionsNormalisees[i]\n          break\n        }\n      }\n      \n      // Section minimale = max des deux critères\n      const section_minimale = Math.max(section_minimale_chute, section_minimale_intensite)\n      \n      // Section normalisée supérieure\n      let section_normalisee = sectionsNormalisees[sectionsNormalisees.length - 1]\n      for (const section of sectionsNormalisees) {\n        if (section >= section_minimale) {\n          section_normalisee = section\n          break\n        }\n      }\n      \n      // Chute de tension réelle avec la section normalisée\n      const chute_tension_reelle = (resistivite * longueur * courant) / (section_normalisee * tension) * 100\n      \n      // Intensité admissible de la section choisie\n      const index_section = sectionsNormalisees.indexOf(section_normalisee)\n      const intensite_admissible = intensites[index_section] * facteur_temperature\n      \n      // Validation et recommandations\n      let validation_status: 'valid' | 'warning' | 'error' = 'valid'\n      const recommendations: string[] = []\n      const warnings: string[] = []\n      \n      if (chute_tension_reelle > chute_tension_max) {\n        validation_status = 'error'\n        warnings.push(`Chute de tension dépassée: ${chute_tension_reelle.toFixed(2)}% > ${chute_tension_max}%`)\n      } else if (chute_tension_reelle > chute_tension_max * 0.8) {\n        validation_status = 'warning'\n        warnings.push(`Chute de tension proche de la limite: ${chute_tension_reelle.toFixed(2)}%`)\n      }\n      \n      if (intensite_admissible < courant * 1.1) {\n        validation_status = 'warning'\n        warnings.push('Marge de sécurité faible sur l\\'intensité admissible')\n      }\n      \n      // Recommandations\n      if (longueur > 100) {\n        recommendations.push('Considérer un câble de section supérieure pour les grandes longueurs')\n      }\n      \n      if (mode_pose === 'enterré') {\n        recommendations.push('Prévoir une protection mécanique adaptée')\n      }\n      \n      recommendations.push(`Vérifier la compatibilité avec les bornes (section max: ${section_normalisee}mm²)`)\n      \n      setResult({\n        section_minimale: Math.round(section_minimale * 100) / 100,\n        section_normalisee,\n        chute_tension_reelle: Math.round(chute_tension_reelle * 100) / 100,\n        intensite_admissible: Math.round(intensite_admissible),\n        validation_status,\n        recommendations,\n        warnings\n      })\n      \n      setIsCalculating(false)\n    }, 1500)\n  }\n\n  const resetCalculator = () => {\n    setInputs({\n      courant: 32,\n      longueur: 50,\n      chute_tension_max: 3,\n      mode_pose: 'enterré',\n      materiau: 'cuivre',\n      temperature: 30\n    })\n    setResult(null)\n    setShowDetails(false)\n  }\n\n  const exportResult = () => {\n    if (!result) return\n    \n    const reportContent = `\nCALCUL DE SECTION DE CÂBLE - Pro Matos Afrique Ouest\n=====================================================\n\nPARAMÈTRES D'ENTRÉE:\n- Courant nominal: ${inputs.courant} A\n- Longueur: ${inputs.longueur} m\n- Chute de tension max: ${inputs.chute_tension_max} %\n- Mode de pose: ${inputs.mode_pose}\n- Matériau: ${inputs.materiau}\n- Température ambiante: ${inputs.temperature} °C\n\nRÉSULTATS:\n- Section minimale calculée: ${result.section_minimale} mm²\n- Section normalisée: ${result.section_normalisee} mm²\n- Chute de tension réelle: ${result.chute_tension_reelle} %\n- Intensité admissible: ${result.intensite_admissible} A\n\nRECOMMANDATIONS:\n${result.recommendations.map(r => `- ${r}`).join('\\n')}\n\n${result.warnings.length > 0 ? `AVERTISSEMENTS:\\n${result.warnings.map(w => `- ${w}`).join('\\n')}` : ''}\n\nCalcul effectué selon la norme NF C 15-100\nGénéré le ${new Date().toLocaleString('fr-FR')}\n    `.trim()\n    \n    const blob = new Blob([reportContent], { type: 'text/plain' })\n    const url = URL.createObjectURL(blob)\n    const a = document.createElement('a')\n    a.href = url\n    a.download = `calcul-section-cable-${Date.now()}.txt`\n    document.body.appendChild(a)\n    a.click()\n    document.body.removeChild(a)\n    URL.revokeObjectURL(url)\n  }\n\n  const copyResult = () => {\n    if (!result) return\n    \n    const text = `Section câble: ${result.section_normalisee}mm² (${inputs.courant}A, ${inputs.longueur}m, ΔU=${result.chute_tension_reelle}%)`\n    navigator.clipboard.writeText(text)\n  }\n\n  useEffect(() => {\n    // Calcul automatique quand les paramètres changent\n    if (inputs.courant > 0 && inputs.longueur > 0) {\n      const timer = setTimeout(() => {\n        calculateCableSection()\n      }, 500)\n      return () => clearTimeout(timer)\n    }\n  }, [inputs])\n\n  return (\n    <div className=\"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <h3 className=\"text-lg font-semibold text-blue-900 flex items-center space-x-2\">\n          <Calculator className=\"h-6 w-6\" />\n          <span>Calculatrice Rapide - Section de Câble</span>\n        </h3>\n        <div className=\"flex space-x-2\">\n          <button\n            onClick={() => setShowDetails(!showDetails)}\n            className=\"px-3 py-1 text-xs bg-blue-100 text-blue-800 rounded-full hover:bg-blue-200 transition-colors\"\n          >\n            {showDetails ? 'Masquer' : 'Détails'}\n          </button>\n          <button\n            onClick={resetCalculator}\n            className=\"p-1 text-blue-600 hover:text-blue-700\"\n            title=\"Réinitialiser\"\n          >\n            <RefreshCw className=\"h-4 w-4\" />\n          </button>\n        </div>\n      </div>\n\n      {/* Paramètres d'entrée */}\n      <div className=\"grid md:grid-cols-3 lg:grid-cols-6 gap-4 mb-4\">\n        <div>\n          <label className=\"block text-sm font-medium text-blue-800 mb-1\">\n            Courant (A)\n          </label>\n          <input\n            type=\"number\"\n            value={inputs.courant}\n            onChange={(e) => setInputs(prev => ({ ...prev, courant: Number(e.target.value) }))}\n            className=\"w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-400\"\n          />\n        </div>\n        <div>\n          <label className=\"block text-sm font-medium text-blue-800 mb-1\">\n            Longueur (m)\n          </label>\n          <input\n            type=\"number\"\n            value={inputs.longueur}\n            onChange={(e) => setInputs(prev => ({ ...prev, longueur: Number(e.target.value) }))}\n            className=\"w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-400\"\n          />\n        </div>\n        <div>\n          <label className=\"block text-sm font-medium text-blue-800 mb-1\">\n            ΔU max (%)\n          </label>\n          <input\n            type=\"number\"\n            step=\"0.1\"\n            value={inputs.chute_tension_max}\n            onChange={(e) => setInputs(prev => ({ ...prev, chute_tension_max: Number(e.target.value) }))}\n            className=\"w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-400\"\n          />\n        </div>\n        <div>\n          <label className=\"block text-sm font-medium text-blue-800 mb-1\">\n            Mode de pose\n          </label>\n          <select\n            value={inputs.mode_pose}\n            onChange={(e) => setInputs(prev => ({ ...prev, mode_pose: e.target.value }))}\n            className=\"w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-400\"\n          >\n            <option value=\"enterré\">Enterré</option>\n            <option value=\"aérien\">Aérien</option>\n            <option value=\"goulotte\">Goulotte</option>\n            <option value=\"chemin_cables\">Chemin câbles</option>\n          </select>\n        </div>\n        <div>\n          <label className=\"block text-sm font-medium text-blue-800 mb-1\">\n            Matériau\n          </label>\n          <select\n            value={inputs.materiau}\n            onChange={(e) => setInputs(prev => ({ ...prev, materiau: e.target.value }))}\n            className=\"w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-400\"\n          >\n            <option value=\"cuivre\">Cuivre</option>\n            <option value=\"aluminium\">Aluminium</option>\n          </select>\n        </div>\n        <div>\n          <label className=\"block text-sm font-medium text-blue-800 mb-1\">\n            Temp. (°C)\n          </label>\n          <input\n            type=\"number\"\n            value={inputs.temperature}\n            onChange={(e) => setInputs(prev => ({ ...prev, temperature: Number(e.target.value) }))}\n            className=\"w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-400\"\n          />\n        </div>\n      </div>\n\n      {/* Résultats */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        className=\"bg-white rounded-lg p-4 border border-blue-200\"\n      >\n        {isCalculating ? (\n          <div className=\"text-center py-8\">\n            <div className=\"w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4\"></div>\n            <p className=\"text-blue-700\">Calcul en cours selon NF C 15-100...</p>\n          </div>\n        ) : result ? (\n          <div>\n            <div className=\"flex items-center justify-between mb-4\">\n              <div className=\"text-center flex-1\">\n                <div className={`text-3xl font-bold ${\n                  result.validation_status === 'valid' ? 'text-green-600' :\n                  result.validation_status === 'warning' ? 'text-orange-600' :\n                  'text-red-600'\n                }`}>\n                  {result.section_normalisee} mm²\n                </div>\n                <div className=\"text-sm text-blue-700\">Section recommandée</div>\n              </div>\n              \n              <div className=\"flex items-center space-x-2\">\n                {result.validation_status === 'valid' && <CheckCircle className=\"h-6 w-6 text-green-600\" />}\n                {result.validation_status === 'warning' && <AlertTriangle className=\"h-6 w-6 text-orange-600\" />}\n                {result.validation_status === 'error' && <AlertTriangle className=\"h-6 w-6 text-red-600\" />}\n                \n                <div className=\"flex space-x-1\">\n                  <button\n                    onClick={copyResult}\n                    className=\"p-2 text-blue-600 hover:text-blue-700\"\n                    title=\"Copier\"\n                  >\n                    <Copy className=\"h-4 w-4\" />\n                  </button>\n                  <button\n                    onClick={exportResult}\n                    className=\"p-2 text-blue-600 hover:text-blue-700\"\n                    title=\"Exporter\"\n                  >\n                    <Download className=\"h-4 w-4\" />\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            {showDetails && (\n              <motion.div\n                initial={{ opacity: 0, height: 0 }}\n                animate={{ opacity: 1, height: 'auto' }}\n                className=\"space-y-3\"\n              >\n                <div className=\"grid md:grid-cols-3 gap-4 text-sm\">\n                  <div className=\"bg-blue-50 p-3 rounded\">\n                    <div className=\"font-medium text-blue-800\">Section minimale</div>\n                    <div className=\"text-blue-900\">{result.section_minimale} mm²</div>\n                  </div>\n                  <div className=\"bg-blue-50 p-3 rounded\">\n                    <div className=\"font-medium text-blue-800\">Chute tension réelle</div>\n                    <div className=\"text-blue-900\">{result.chute_tension_reelle}%</div>\n                  </div>\n                  <div className=\"bg-blue-50 p-3 rounded\">\n                    <div className=\"font-medium text-blue-800\">Intensité admissible</div>\n                    <div className=\"text-blue-900\">{result.intensite_admissible} A</div>\n                  </div>\n                </div>\n\n                {result.warnings.length > 0 && (\n                  <div className=\"bg-orange-50 border border-orange-200 rounded p-3\">\n                    <div className=\"flex items-center space-x-2 mb-2\">\n                      <AlertTriangle className=\"h-4 w-4 text-orange-600\" />\n                      <span className=\"font-medium text-orange-800\">Avertissements</span>\n                    </div>\n                    <ul className=\"text-sm text-orange-700 space-y-1\">\n                      {result.warnings.map((warning, index) => (\n                        <li key={index}>• {warning}</li>\n                      ))}\n                    </ul>\n                  </div>\n                )}\n\n                {result.recommendations.length > 0 && (\n                  <div className=\"bg-green-50 border border-green-200 rounded p-3\">\n                    <div className=\"flex items-center space-x-2 mb-2\">\n                      <Info className=\"h-4 w-4 text-green-600\" />\n                      <span className=\"font-medium text-green-800\">Recommandations</span>\n                    </div>\n                    <ul className=\"text-sm text-green-700 space-y-1\">\n                      {result.recommendations.map((rec, index) => (\n                        <li key={index}>• {rec}</li>\n                      ))}\n                    </ul>\n                  </div>\n                )}\n              </motion.div>\n            )}\n          </div>\n        ) : (\n          <div className=\"text-center py-4 text-blue-700\">\n            Modifiez les paramètres pour lancer le calcul automatique\n          </div>\n        )}\n      </motion.div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAyBe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACnC,SAAS;QACT,UAAU;QACV,mBAAmB;QACnB,WAAW;QACX,UAAU;QACV,aAAa;IACf;IAEA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B;IAC/D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,8BAA8B;IAC9B,MAAM,sBAAsB;QAAC;QAAK;QAAK;QAAG;QAAG;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;KAAI;IAE3G,2DAA2D;IAC3D,MAAM,wBAAwB;QAC5B,WAAW;YAAC;YAAI;YAAI;YAAI;YAAI;YAAI;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;SAAI;QAChG,UAAU;YAAC;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;SAAI;QAC9F,YAAY;YAAC;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;SAAI;QAC/F,iBAAiB;YAAC;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;SAAI;IACvG;IAEA,MAAM,wBAAwB;QAC5B,iBAAiB;QAEjB,kCAAkC;QAClC,WAAW;YACT,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,iBAAiB,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG;YAEnF,oCAAoC;YACpC,MAAM,cAAc,aAAa,WAAW,SAAS,MAAM,YAAY;;YAEvE,uBAAuB;YACvB,MAAM,UAAU,IAAI,WAAW;;YAE/B,yDAAyD;YACzD,6CAA6C;YAC7C,MAAM,wBAAwB,AAAC,UAAU,oBAAqB;YAC9D,MAAM,yBAAyB,AAAC,cAAc,WAAW,UAAW;YAEpE,oCAAoC;YACpC,MAAM,sBAAsB,cAAc,KAAK,OAAO;YAEtD,+CAA+C;YAC/C,MAAM,aAAa,qBAAqB,CAAC,UAAgD;YACzF,IAAI,6BAA6B,mBAAmB,CAAC,EAAE;YAEvD,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;gBAC1C,IAAI,AAAC,UAAU,CAAC,EAAE,GAAG,uBAAwB,SAAS;oBACpD,6BAA6B,mBAAmB,CAAC,EAAE;oBACnD;gBACF;YACF;YAEA,2CAA2C;YAC3C,MAAM,mBAAmB,KAAK,GAAG,CAAC,wBAAwB;YAE1D,gCAAgC;YAChC,IAAI,qBAAqB,mBAAmB,CAAC,oBAAoB,MAAM,GAAG,EAAE;YAC5E,KAAK,MAAM,WAAW,oBAAqB;gBACzC,IAAI,WAAW,kBAAkB;oBAC/B,qBAAqB;oBACrB;gBACF;YACF;YAEA,qDAAqD;YACrD,MAAM,uBAAuB,AAAC,cAAc,WAAW,UAAW,CAAC,qBAAqB,OAAO,IAAI;YAEnG,6CAA6C;YAC7C,MAAM,gBAAgB,oBAAoB,OAAO,CAAC;YAClD,MAAM,uBAAuB,UAAU,CAAC,cAAc,GAAG;YAEzD,gCAAgC;YAChC,IAAI,oBAAmD;YACvD,MAAM,kBAA4B,EAAE;YACpC,MAAM,WAAqB,EAAE;YAE7B,IAAI,uBAAuB,mBAAmB;gBAC5C,oBAAoB;gBACpB,SAAS,IAAI,CAAC,CAAC,2BAA2B,EAAE,qBAAqB,OAAO,CAAC,GAAG,IAAI,EAAE,kBAAkB,CAAC,CAAC;YACxG,OAAO,IAAI,uBAAuB,oBAAoB,KAAK;gBACzD,oBAAoB;gBACpB,SAAS,IAAI,CAAC,CAAC,sCAAsC,EAAE,qBAAqB,OAAO,CAAC,GAAG,CAAC,CAAC;YAC3F;YAEA,IAAI,uBAAuB,UAAU,KAAK;gBACxC,oBAAoB;gBACpB,SAAS,IAAI,CAAC;YAChB;YAEA,kBAAkB;YAClB,IAAI,WAAW,KAAK;gBAClB,gBAAgB,IAAI,CAAC;YACvB;YAEA,IAAI,cAAc,WAAW;gBAC3B,gBAAgB,IAAI,CAAC;YACvB;YAEA,gBAAgB,IAAI,CAAC,CAAC,wDAAwD,EAAE,mBAAmB,IAAI,CAAC;YAExG,UAAU;gBACR,kBAAkB,KAAK,KAAK,CAAC,mBAAmB,OAAO;gBACvD;gBACA,sBAAsB,KAAK,KAAK,CAAC,uBAAuB,OAAO;gBAC/D,sBAAsB,KAAK,KAAK,CAAC;gBACjC;gBACA;gBACA;YACF;YAEA,iBAAiB;QACnB,GAAG;IACL;IAEA,MAAM,kBAAkB;QACtB,UAAU;YACR,SAAS;YACT,UAAU;YACV,mBAAmB;YACnB,WAAW;YACX,UAAU;YACV,aAAa;QACf;QACA,UAAU;QACV,eAAe;IACjB;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,QAAQ;QAEb,MAAM,gBAAgB,CAAC;;;;;mBAKR,EAAE,OAAO,OAAO,CAAC;YACxB,EAAE,OAAO,QAAQ,CAAC;wBACN,EAAE,OAAO,iBAAiB,CAAC;gBACnC,EAAE,OAAO,SAAS,CAAC;YACvB,EAAE,OAAO,QAAQ,CAAC;wBACN,EAAE,OAAO,WAAW,CAAC;;;6BAGhB,EAAE,OAAO,gBAAgB,CAAC;sBACjC,EAAE,OAAO,kBAAkB,CAAC;2BACvB,EAAE,OAAO,oBAAoB,CAAC;wBACjC,EAAE,OAAO,oBAAoB,CAAC;;;AAGtD,EAAE,OAAO,eAAe,CAAC,GAAG,CAAC,CAAA,IAAK,CAAC,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,MAAM;;AAEvD,EAAE,OAAO,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,EAAE,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAA,IAAK,CAAC,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,OAAO,GAAG,GAAG;;;UAG9F,EAAE,IAAI,OAAO,cAAc,CAAC,SAAS;IAC3C,CAAC,CAAC,IAAI;QAEN,MAAM,OAAO,IAAI,KAAK;YAAC;SAAc,EAAE;YAAE,MAAM;QAAa;QAC5D,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG,CAAC,qBAAqB,EAAE,KAAK,GAAG,GAAG,IAAI,CAAC;QACrD,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,EAAE,KAAK;QACP,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;IACtB;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,QAAQ;QAEb,MAAM,OAAO,CAAC,eAAe,EAAE,OAAO,kBAAkB,CAAC,KAAK,EAAE,OAAO,OAAO,CAAC,GAAG,EAAE,OAAO,QAAQ,CAAC,MAAM,EAAE,OAAO,oBAAoB,CAAC,EAAE,CAAC;QAC3I,UAAU,SAAS,CAAC,SAAS,CAAC;IAChC;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,mDAAmD;QACnD,IAAI,OAAO,OAAO,GAAG,KAAK,OAAO,QAAQ,GAAG,GAAG;YAC7C,MAAM,QAAQ,WAAW;gBACvB;YACF,GAAG;YACH,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;KAAO;IAEX,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC,8MAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;0CACtB,8OAAC;0CAAK;;;;;;;;;;;;kCAER,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,eAAe,CAAC;gCAC/B,WAAU;0CAET,cAAc,YAAY;;;;;;0CAE7B,8OAAC;gCACC,SAAS;gCACT,WAAU;gCACV,OAAM;0CAEN,cAAA,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAM3B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,MAAK;gCACL,OAAO,OAAO,OAAO;gCACrB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,SAAS,OAAO,EAAE,MAAM,CAAC,KAAK;wCAAE,CAAC;gCAChF,WAAU;;;;;;;;;;;;kCAGd,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,MAAK;gCACL,OAAO,OAAO,QAAQ;gCACtB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,UAAU,OAAO,EAAE,MAAM,CAAC,KAAK;wCAAE,CAAC;gCACjF,WAAU;;;;;;;;;;;;kCAGd,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,MAAK;gCACL,MAAK;gCACL,OAAO,OAAO,iBAAiB;gCAC/B,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,mBAAmB,OAAO,EAAE,MAAM,CAAC,KAAK;wCAAE,CAAC;gCAC1F,WAAU;;;;;;;;;;;;kCAGd,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,OAAO,OAAO,SAAS;gCACvB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,WAAW,EAAE,MAAM,CAAC,KAAK;wCAAC,CAAC;gCAC1E,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAU;;;;;;kDACxB,8OAAC;wCAAO,OAAM;kDAAS;;;;;;kDACvB,8OAAC;wCAAO,OAAM;kDAAW;;;;;;kDACzB,8OAAC;wCAAO,OAAM;kDAAgB;;;;;;;;;;;;;;;;;;kCAGlC,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,OAAO,OAAO,QAAQ;gCACtB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wCAAC,CAAC;gCACzE,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAS;;;;;;kDACvB,8OAAC;wCAAO,OAAM;kDAAY;;;;;;;;;;;;;;;;;;kCAG9B,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,MAAK;gCACL,OAAO,OAAO,WAAW;gCACzB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,aAAa,OAAO,EAAE,MAAM,CAAC,KAAK;wCAAE,CAAC;gCACpF,WAAU;;;;;;;;;;;;;;;;;;0BAMhB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,WAAU;0BAET,8BACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;2BAE7B,uBACF,8OAAC;;sCACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAW,CAAC,mBAAmB,EAClC,OAAO,iBAAiB,KAAK,UAAU,mBACvC,OAAO,iBAAiB,KAAK,YAAY,oBACzC,gBACA;;gDACC,OAAO,kBAAkB;gDAAC;;;;;;;sDAE7B,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAGzC,8OAAC;oCAAI,WAAU;;wCACZ,OAAO,iBAAiB,KAAK,yBAAW,8OAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCAC/D,OAAO,iBAAiB,KAAK,2BAAa,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;wCACnE,OAAO,iBAAiB,KAAK,yBAAW,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDAElE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS;oDACT,WAAU;oDACV,OAAM;8DAEN,cAAA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,8OAAC;oDACC,SAAS;oDACT,WAAU;oDACV,OAAM;8DAEN,cAAA,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAM3B,6BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,QAAQ;4BAAE;4BACjC,SAAS;gCAAE,SAAS;gCAAG,QAAQ;4BAAO;4BACtC,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAA4B;;;;;;8DAC3C,8OAAC;oDAAI,WAAU;;wDAAiB,OAAO,gBAAgB;wDAAC;;;;;;;;;;;;;sDAE1D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAA4B;;;;;;8DAC3C,8OAAC;oDAAI,WAAU;;wDAAiB,OAAO,oBAAoB;wDAAC;;;;;;;;;;;;;sDAE9D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAA4B;;;;;;8DAC3C,8OAAC;oDAAI,WAAU;;wDAAiB,OAAO,oBAAoB;wDAAC;;;;;;;;;;;;;;;;;;;gCAI/D,OAAO,QAAQ,CAAC,MAAM,GAAG,mBACxB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,wNAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;8DACzB,8OAAC;oDAAK,WAAU;8DAA8B;;;;;;;;;;;;sDAEhD,8OAAC;4CAAG,WAAU;sDACX,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC7B,8OAAC;;wDAAe;wDAAG;;mDAAV;;;;;;;;;;;;;;;;gCAMhB,OAAO,eAAe,CAAC,MAAM,GAAG,mBAC/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;;;;;;;sDAE/C,8OAAC;4CAAG,WAAU;sDACX,OAAO,eAAe,CAAC,GAAG,CAAC,CAAC,KAAK,sBAChC,8OAAC;;wDAAe;wDAAG;;mDAAV;;;;;;;;;;;;;;;;;;;;;;;;;;;yCASvB,8OAAC;oBAAI,WAAU;8BAAiC;;;;;;;;;;;;;;;;;AAO1D", "debugId": null}}, {"offset": {"line": 1920, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/components/prescriptor/CertificateGenerator.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { \n  Award,\n  Download,\n  Share2,\n  Qr<PERSON>ode,\n  CheckCircle,\n  AlertTriangle,\n  FileText,\n  Calendar,\n  User,\n  Building,\n  Printer,\n  Mail\n} from 'lucide-react'\nimport { useStore } from '@/store/useStore'\nimport { PrescriptorService } from '@/services/prescriptorService'\nimport { formatDate } from '@/lib/utils'\n\ninterface CertificateGeneratorProps {\n  onClose?: () => void\n}\n\nexport default function CertificateGenerator({ onClose }: CertificateGeneratorProps) {\n  const { prescriptionProjects, addComplianceCertificate } = useStore()\n  const [selectedProject, setSelectedProject] = useState('')\n  const [certificateType, setCertificateType] = useState<'design' | 'installation' | 'testing' | 'full_compliance'>('design')\n  const [isGenerating, setIsGenerating] = useState(false)\n  const [generatedCertificate, setGeneratedCertificate] = useState<any>(null)\n  const [showPreview, setShowPreview] = useState(false)\n\n  const certificateTypes = [\n    {\n      id: 'design',\n      label: 'Certificat de Conception',\n      description: 'Validation de la conception et des calculs techniques',\n      icon: <FileText className=\"h-5 w-5\" />,\n      color: 'from-blue-500 to-blue-600',\n      validity: 12, // mois\n      price: 150000 // FCFA\n    },\n    {\n      id: 'installation',\n      label: 'Certificat d\\'Installation',\n      description: 'Conformité de l\\'installation selon les plans',\n      icon: <Building className=\"h-5 w-5\" />,\n      color: 'from-green-500 to-green-600',\n      validity: 24,\n      price: 200000\n    },\n    {\n      id: 'testing',\n      label: 'Certificat de Tests',\n      description: 'Validation des tests et essais de mise en service',\n      icon: <CheckCircle className=\"h-5 w-5\" />,\n      color: 'from-purple-500 to-purple-600',\n      validity: 12,\n      price: 100000\n    },\n    {\n      id: 'full_compliance',\n      label: 'Certificat de Conformité Complète',\n      description: 'Conformité totale : conception, installation et tests',\n      icon: <Award className=\"h-5 w-5\" />,\n      color: 'from-amber-500 to-amber-600',\n      validity: 36,\n      price: 400000\n    }\n  ]\n\n  const selectedProjectData = prescriptionProjects.find(p => p.id === selectedProject)\n  const selectedCertType = certificateTypes.find(t => t.id === certificateType)\n\n  const generateCertificate = () => {\n    if (!selectedProjectData || !selectedCertType) return\n\n    setIsGenerating(true)\n\n    setTimeout(() => {\n      const certificate = PrescriptorService.generateComplianceCertificate(selectedProjectData)\n      \n      // Personnaliser selon le type\n      const customizedCertificate = {\n        ...certificate,\n        certificate_type: certificateType,\n        certificate_number: `PMO-${certificateType.toUpperCase()}-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`,\n        valid_until: new Date(Date.now() + selectedCertType.validity * 30 * 24 * 60 * 60 * 1000).toISOString(),\n        standards_covered: getStandardsByCertificateType(certificateType),\n        test_results: getTestResultsByCertificateType(certificateType),\n        cost: selectedCertType.price\n      }\n\n      setGeneratedCertificate(customizedCertificate)\n      addComplianceCertificate(customizedCertificate)\n      setIsGenerating(false)\n      setShowPreview(true)\n    }, 3000)\n  }\n\n  const getStandardsByCertificateType = (type: string) => {\n    switch (type) {\n      case 'design':\n        return ['NF C 15-100', 'RT 2012', 'Accessibilité PMR']\n      case 'installation':\n        return ['NF C 15-100', 'Décret n°88-1056', 'Code du travail']\n      case 'testing':\n        return ['NF C 15-100', 'Guide UTE C 15-900', 'IEC 60364-6']\n      case 'full_compliance':\n        return ['NF C 15-100', 'RT 2012', 'Décret n°88-1056', 'IEC 60364', 'Code du travail']\n      default:\n        return ['NF C 15-100']\n    }\n  }\n\n  const getTestResultsByCertificateType = (type: string) => {\n    switch (type) {\n      case 'design':\n        return {\n          'Calculs de puissance': 'Conforme',\n          'Dimensionnement câbles': 'Conforme',\n          'Schémas unifilaires': 'Conforme',\n          'Protection différentielle': 'Conforme'\n        }\n      case 'installation':\n        return {\n          'Continuité des conducteurs': 'Conforme',\n          'Résistance d\\'isolement': 'Conforme',\n          'Efficacité de la protection': 'Conforme',\n          'Fonctionnement différentiel': 'Conforme'\n        }\n      case 'testing':\n        return {\n          'Tests de continuité': 'Conforme',\n          'Mesure d\\'isolement': 'Conforme',\n          'Tests différentiels': 'Conforme',\n          'Vérification fonctionnelle': 'Conforme'\n        }\n      case 'full_compliance':\n        return {\n          'Conception': 'Conforme',\n          'Installation': 'Conforme',\n          'Tests et essais': 'Conforme',\n          'Documentation': 'Conforme',\n          'Formation utilisateurs': 'Conforme'\n        }\n      default:\n        return {}\n    }\n  }\n\n  const downloadCertificate = () => {\n    if (!generatedCertificate) return\n\n    // Simulation de génération PDF\n    const certificateContent = `\nCERTIFICAT DE CONFORMITÉ\nPro Matos Afrique Ouest - Bureau d'Études Certifié\n\nCertificat N°: ${generatedCertificate.certificate_number}\nType: ${selectedCertType?.label}\nProjet: ${selectedProjectData?.project_name}\nClient: ${selectedProjectData?.client_name}\n\nÉmis le: ${formatDate(generatedCertificate.valid_from)}\nValide jusqu'au: ${formatDate(generatedCertificate.valid_until)}\n\nNORMES COUVERTES:\n${generatedCertificate.standards_covered.map((s: string) => `- ${s}`).join('\\n')}\n\nRÉSULTATS DES TESTS:\n${Object.entries(generatedCertificate.test_results).map(([test, result]) => `- ${test}: ${result}`).join('\\n')}\n\nCe certificat atteste que le projet respecte les normes en vigueur.\n\nSignature numérique: ${generatedCertificate.digital_signature}\nVérification QR: ${generatedCertificate.qr_verification}\n    `.trim()\n\n    const blob = new Blob([certificateContent], { type: 'text/plain' })\n    const url = URL.createObjectURL(blob)\n    const a = document.createElement('a')\n    a.href = url\n    a.download = `certificat-${generatedCertificate.certificate_number}.txt`\n    document.body.appendChild(a)\n    a.click()\n    document.body.removeChild(a)\n    URL.revokeObjectURL(url)\n  }\n\n  const shareCertificate = () => {\n    if (!generatedCertificate) return\n    \n    const shareText = `Certificat de conformité ${generatedCertificate.certificate_number} émis par Pro Matos Afrique Ouest. Vérification: ${generatedCertificate.qr_verification}`\n    \n    if (navigator.share) {\n      navigator.share({\n        title: 'Certificat de Conformité',\n        text: shareText,\n        url: generatedCertificate.qr_verification\n      })\n    } else {\n      navigator.clipboard.writeText(shareText)\n      alert('Lien de partage copié dans le presse-papiers')\n    }\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-lg p-6 max-w-4xl mx-auto\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between mb-6\">\n        <div className=\"flex items-center space-x-4\">\n          <div className=\"w-12 h-12 bg-gradient-to-r from-amber-500 to-amber-600 rounded-lg flex items-center justify-center text-white\">\n            <Award className=\"h-6 w-6\" />\n          </div>\n          <div>\n            <h2 className=\"text-2xl font-bold text-gray-900\">Générateur de Certificat</h2>\n            <p className=\"text-gray-600\">Certification officielle de conformité Pro Matos</p>\n          </div>\n        </div>\n        {onClose && (\n          <button onClick={onClose} className=\"text-gray-400 hover:text-gray-600\">\n            ×\n          </button>\n        )}\n      </div>\n\n      {!showPreview ? (\n        <div className=\"space-y-6\">\n          {/* Sélection du projet */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Projet à certifier\n            </label>\n            <select\n              value={selectedProject}\n              onChange={(e) => setSelectedProject(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400\"\n            >\n              <option value=\"\">Sélectionner un projet...</option>\n              {prescriptionProjects.map(project => (\n                <option key={project.id} value={project.id}>\n                  {project.project_name} - {project.client_name}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          {/* Types de certificat */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-4\">\n              Type de certificat\n            </label>\n            <div className=\"grid md:grid-cols-2 gap-4\">\n              {certificateTypes.map((type) => (\n                <button\n                  key={type.id}\n                  onClick={() => setCertificateType(type.id as any)}\n                  className={`p-4 rounded-lg border-2 transition-all text-left ${\n                    certificateType === type.id\n                      ? 'border-amber-400 bg-amber-50'\n                      : 'border-gray-200 hover:border-gray-300'\n                  }`}\n                >\n                  <div className=\"flex items-start space-x-3\">\n                    <div className={`w-10 h-10 bg-gradient-to-r ${type.color} rounded-lg flex items-center justify-center text-white`}>\n                      {type.icon}\n                    </div>\n                    <div className=\"flex-1\">\n                      <h3 className=\"font-semibold text-gray-900 mb-1\">{type.label}</h3>\n                      <p className=\"text-sm text-gray-600 mb-2\">{type.description}</p>\n                      <div className=\"flex items-center justify-between text-xs text-gray-500\">\n                        <span>Validité: {type.validity} mois</span>\n                        <span className=\"font-medium text-amber-600\">\n                          {type.price.toLocaleString()} FCFA\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* Informations du projet sélectionné */}\n          {selectedProjectData && (\n            <div className=\"bg-gray-50 rounded-lg p-4 border\">\n              <h3 className=\"font-semibold text-gray-900 mb-3\">Informations du Projet</h3>\n              <div className=\"grid md:grid-cols-2 gap-4 text-sm\">\n                <div>\n                  <span className=\"font-medium text-gray-700\">Nom:</span>\n                  <p>{selectedProjectData.project_name}</p>\n                </div>\n                <div>\n                  <span className=\"font-medium text-gray-700\">Client:</span>\n                  <p>{selectedProjectData.client_name} - {selectedProjectData.client_company}</p>\n                </div>\n                <div>\n                  <span className=\"font-medium text-gray-700\">Localisation:</span>\n                  <p>{selectedProjectData.location}</p>\n                </div>\n                <div>\n                  <span className=\"font-medium text-gray-700\">Statut:</span>\n                  <p className=\"capitalize\">{selectedProjectData.status}</p>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Bouton de génération */}\n          <div className=\"flex justify-center\">\n            <button\n              onClick={generateCertificate}\n              disabled={!selectedProject || !certificateType || isGenerating}\n              className=\"btn-premium px-8 py-3 text-lg disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {isGenerating ? (\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n                  <span>Génération du certificat...</span>\n                </div>\n              ) : (\n                <div className=\"flex items-center space-x-2\">\n                  <Award className=\"h-5 w-5\" />\n                  <span>Générer le Certificat</span>\n                  {selectedCertType && (\n                    <span className=\"ml-2 text-sm\">\n                      ({selectedCertType.price.toLocaleString()} FCFA)\n                    </span>\n                  )}\n                </div>\n              )}\n            </button>\n          </div>\n        </div>\n      ) : (\n        /* Aperçu du certificat généré */\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"space-y-6\"\n        >\n          {/* Succès */}\n          <div className=\"bg-green-50 border border-green-200 rounded-lg p-4\">\n            <div className=\"flex items-center space-x-3\">\n              <CheckCircle className=\"h-6 w-6 text-green-600\" />\n              <div>\n                <h3 className=\"font-semibold text-green-900\">Certificat généré avec succès !</h3>\n                <p className=\"text-sm text-green-700\">\n                  Certificat N° {generatedCertificate?.certificate_number} prêt à être téléchargé\n                </p>\n              </div>\n            </div>\n          </div>\n\n          {/* Aperçu du certificat */}\n          <div className=\"bg-gradient-to-br from-amber-50 to-yellow-50 border-2 border-amber-200 rounded-lg p-6\">\n            <div className=\"text-center mb-6\">\n              <div className=\"w-16 h-16 bg-gradient-to-r from-amber-500 to-amber-600 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <Award className=\"h-8 w-8 text-white\" />\n              </div>\n              <h3 className=\"text-2xl font-bold text-amber-900 mb-2\">\n                CERTIFICAT DE CONFORMITÉ\n              </h3>\n              <p className=\"text-amber-700\">Pro Matos Afrique Ouest - Bureau d'Études Certifié</p>\n            </div>\n\n            <div className=\"grid md:grid-cols-2 gap-6 mb-6\">\n              <div className=\"space-y-3\">\n                <div>\n                  <span className=\"text-sm font-medium text-amber-800\">N° Certificat:</span>\n                  <p className=\"font-mono text-amber-900\">{generatedCertificate?.certificate_number}</p>\n                </div>\n                <div>\n                  <span className=\"text-sm font-medium text-amber-800\">Type:</span>\n                  <p className=\"text-amber-900\">{selectedCertType?.label}</p>\n                </div>\n                <div>\n                  <span className=\"text-sm font-medium text-amber-800\">Projet:</span>\n                  <p className=\"text-amber-900\">{selectedProjectData?.project_name}</p>\n                </div>\n              </div>\n              <div className=\"space-y-3\">\n                <div>\n                  <span className=\"text-sm font-medium text-amber-800\">Client:</span>\n                  <p className=\"text-amber-900\">{selectedProjectData?.client_name}</p>\n                </div>\n                <div>\n                  <span className=\"text-sm font-medium text-amber-800\">Émis le:</span>\n                  <p className=\"text-amber-900\">{formatDate(generatedCertificate?.valid_from)}</p>\n                </div>\n                <div>\n                  <span className=\"text-sm font-medium text-amber-800\">Valide jusqu'au:</span>\n                  <p className=\"text-amber-900\">{formatDate(generatedCertificate?.valid_until)}</p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"text-center\">\n              <QrCode className=\"h-12 w-12 mx-auto text-amber-600 mb-2\" />\n              <p className=\"text-xs text-amber-700\">Code QR de vérification</p>\n            </div>\n          </div>\n\n          {/* Actions */}\n          <div className=\"flex justify-center space-x-4\">\n            <button\n              onClick={downloadCertificate}\n              className=\"flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              <Download className=\"h-5 w-5\" />\n              <span>Télécharger PDF</span>\n            </button>\n            <button\n              onClick={shareCertificate}\n              className=\"flex items-center space-x-2 px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n            >\n              <Share2 className=\"h-5 w-5\" />\n              <span>Partager</span>\n            </button>\n            <button className=\"flex items-center space-x-2 px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\">\n              <Printer className=\"h-5 w-5\" />\n              <span>Imprimer</span>\n            </button>\n          </div>\n\n          <div className=\"text-center\">\n            <button\n              onClick={() => {\n                setShowPreview(false)\n                setSelectedProject('')\n                setGeneratedCertificate(null)\n              }}\n              className=\"text-amber-600 hover:text-amber-700 font-medium\"\n            >\n              Générer un nouveau certificat\n            </button>\n          </div>\n        </motion.div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AACA;AACA;AApBA;;;;;;;;AA0Be,SAAS,qBAAqB,EAAE,OAAO,EAA6B;IACjF,MAAM,EAAE,oBAAoB,EAAE,wBAAwB,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAClE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6D;IAClH,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACtE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,mBAAmB;QACvB;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,OAAO;YACP,UAAU;YACV,OAAO,OAAO,OAAO;QACvB;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,OAAO;YACP,UAAU;YACV,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,2NAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;YAC7B,OAAO;YACP,UAAU;YACV,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,UAAU;YACV,OAAO;QACT;KACD;IAED,MAAM,sBAAsB,qBAAqB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACpE,MAAM,mBAAmB,iBAAiB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAE7D,MAAM,sBAAsB;QAC1B,IAAI,CAAC,uBAAuB,CAAC,kBAAkB;QAE/C,gBAAgB;QAEhB,WAAW;YACT,MAAM,cAAc,qIAAA,CAAA,qBAAkB,CAAC,6BAA6B,CAAC;YAErE,8BAA8B;YAC9B,MAAM,wBAAwB;gBAC5B,GAAG,WAAW;gBACd,kBAAkB;gBAClB,oBAAoB,CAAC,IAAI,EAAE,gBAAgB,WAAW,GAAG,CAAC,EAAE,IAAI,OAAO,WAAW,GAAG,CAAC,EAAE,OAAO,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC,IAAI;gBACtH,aAAa,IAAI,KAAK,KAAK,GAAG,KAAK,iBAAiB,QAAQ,GAAG,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW;gBACpG,mBAAmB,8BAA8B;gBACjD,cAAc,gCAAgC;gBAC9C,MAAM,iBAAiB,KAAK;YAC9B;YAEA,wBAAwB;YACxB,yBAAyB;YACzB,gBAAgB;YAChB,eAAe;QACjB,GAAG;IACL;IAEA,MAAM,gCAAgC,CAAC;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;oBAAC;oBAAe;oBAAW;iBAAoB;YACxD,KAAK;gBACH,OAAO;oBAAC;oBAAe;oBAAoB;iBAAkB;YAC/D,KAAK;gBACH,OAAO;oBAAC;oBAAe;oBAAsB;iBAAc;YAC7D,KAAK;gBACH,OAAO;oBAAC;oBAAe;oBAAW;oBAAoB;oBAAa;iBAAkB;YACvF;gBACE,OAAO;oBAAC;iBAAc;QAC1B;IACF;IAEA,MAAM,kCAAkC,CAAC;QACvC,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,wBAAwB;oBACxB,0BAA0B;oBAC1B,uBAAuB;oBACvB,6BAA6B;gBAC/B;YACF,KAAK;gBACH,OAAO;oBACL,8BAA8B;oBAC9B,2BAA2B;oBAC3B,+BAA+B;oBAC/B,+BAA+B;gBACjC;YACF,KAAK;gBACH,OAAO;oBACL,uBAAuB;oBACvB,uBAAuB;oBACvB,uBAAuB;oBACvB,8BAA8B;gBAChC;YACF,KAAK;gBACH,OAAO;oBACL,cAAc;oBACd,gBAAgB;oBAChB,mBAAmB;oBACnB,iBAAiB;oBACjB,0BAA0B;gBAC5B;YACF;gBACE,OAAO,CAAC;QACZ;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,sBAAsB;QAE3B,+BAA+B;QAC/B,MAAM,qBAAqB,CAAC;;;;eAIjB,EAAE,qBAAqB,kBAAkB,CAAC;MACnD,EAAE,kBAAkB,MAAM;QACxB,EAAE,qBAAqB,aAAa;QACpC,EAAE,qBAAqB,YAAY;;SAElC,EAAE,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,qBAAqB,UAAU,EAAE;iBACtC,EAAE,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,qBAAqB,WAAW,EAAE;;;AAGhE,EAAE,qBAAqB,iBAAiB,CAAC,GAAG,CAAC,CAAC,IAAc,CAAC,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,MAAM;;;AAGjF,EAAE,OAAO,OAAO,CAAC,qBAAqB,YAAY,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,OAAO,GAAK,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,MAAM;;;;qBAI1F,EAAE,qBAAqB,iBAAiB,CAAC;iBAC7C,EAAE,qBAAqB,eAAe,CAAC;IACpD,CAAC,CAAC,IAAI;QAEN,MAAM,OAAO,IAAI,KAAK;YAAC;SAAmB,EAAE;YAAE,MAAM;QAAa;QACjE,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG,CAAC,WAAW,EAAE,qBAAqB,kBAAkB,CAAC,IAAI,CAAC;QACxE,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,EAAE,KAAK;QACP,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;IACtB;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,sBAAsB;QAE3B,MAAM,YAAY,CAAC,yBAAyB,EAAE,qBAAqB,kBAAkB,CAAC,iDAAiD,EAAE,qBAAqB,eAAe,EAAE;QAE/K,IAAI,UAAU,KAAK,EAAE;YACnB,UAAU,KAAK,CAAC;gBACd,OAAO;gBACP,MAAM;gBACN,KAAK,qBAAqB,eAAe;YAC3C;QACF,OAAO;YACL,UAAU,SAAS,CAAC,SAAS,CAAC;YAC9B,MAAM;QACR;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAEnB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;oBAGhC,yBACC,8OAAC;wBAAO,SAAS;wBAAS,WAAU;kCAAoC;;;;;;;;;;;;YAM3E,CAAC,4BACA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;gCAClD,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAG;;;;;;oCAChB,qBAAqB,GAAG,CAAC,CAAA,wBACxB,8OAAC;4CAAwB,OAAO,QAAQ,EAAE;;gDACvC,QAAQ,YAAY;gDAAC;gDAAI,QAAQ,WAAW;;2CADlC,QAAQ,EAAE;;;;;;;;;;;;;;;;;kCAQ7B,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCAAI,WAAU;0CACZ,iBAAiB,GAAG,CAAC,CAAC,qBACrB,8OAAC;wCAEC,SAAS,IAAM,mBAAmB,KAAK,EAAE;wCACzC,WAAW,CAAC,iDAAiD,EAC3D,oBAAoB,KAAK,EAAE,GACvB,iCACA,yCACJ;kDAEF,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAW,CAAC,2BAA2B,EAAE,KAAK,KAAK,CAAC,uDAAuD,CAAC;8DAC9G,KAAK,IAAI;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAoC,KAAK,KAAK;;;;;;sEAC5D,8OAAC;4DAAE,WAAU;sEAA8B,KAAK,WAAW;;;;;;sEAC3D,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;;wEAAK;wEAAW,KAAK,QAAQ;wEAAC;;;;;;;8EAC/B,8OAAC;oEAAK,WAAU;;wEACb,KAAK,KAAK,CAAC,cAAc;wEAAG;;;;;;;;;;;;;;;;;;;;;;;;;uCAlBhC,KAAK,EAAE;;;;;;;;;;;;;;;;oBA6BnB,qCACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;0DAA4B;;;;;;0DAC5C,8OAAC;0DAAG,oBAAoB,YAAY;;;;;;;;;;;;kDAEtC,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;0DAA4B;;;;;;0DAC5C,8OAAC;;oDAAG,oBAAoB,WAAW;oDAAC;oDAAI,oBAAoB,cAAc;;;;;;;;;;;;;kDAE5E,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;0DAA4B;;;;;;0DAC5C,8OAAC;0DAAG,oBAAoB,QAAQ;;;;;;;;;;;;kDAElC,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;0DAA4B;;;;;;0DAC5C,8OAAC;gDAAE,WAAU;0DAAc,oBAAoB,MAAM;;;;;;;;;;;;;;;;;;;;;;;;kCAO7D,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,SAAS;4BACT,UAAU,CAAC,mBAAmB,CAAC,mBAAmB;4BAClD,WAAU;sCAET,6BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;kDAAK;;;;;;;;;;;qDAGR,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;kDAAK;;;;;;oCACL,kCACC,8OAAC;wCAAK,WAAU;;4CAAe;4CAC3B,iBAAiB,KAAK,CAAC,cAAc;4CAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBASxD,+BAA+B,iBAC/B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,WAAU;;kCAGV,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,2NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA+B;;;;;;sDAC7C,8OAAC;4CAAE,WAAU;;gDAAyB;gDACrB,sBAAsB;gDAAmB;;;;;;;;;;;;;;;;;;;;;;;;kCAOhE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDAGvD,8OAAC;wCAAE,WAAU;kDAAiB;;;;;;;;;;;;0CAGhC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAK,WAAU;kEAAqC;;;;;;kEACrD,8OAAC;wDAAE,WAAU;kEAA4B,sBAAsB;;;;;;;;;;;;0DAEjE,8OAAC;;kEACC,8OAAC;wDAAK,WAAU;kEAAqC;;;;;;kEACrD,8OAAC;wDAAE,WAAU;kEAAkB,kBAAkB;;;;;;;;;;;;0DAEnD,8OAAC;;kEACC,8OAAC;wDAAK,WAAU;kEAAqC;;;;;;kEACrD,8OAAC;wDAAE,WAAU;kEAAkB,qBAAqB;;;;;;;;;;;;;;;;;;kDAGxD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAK,WAAU;kEAAqC;;;;;;kEACrD,8OAAC;wDAAE,WAAU;kEAAkB,qBAAqB;;;;;;;;;;;;0DAEtD,8OAAC;;kEACC,8OAAC;wDAAK,WAAU;kEAAqC;;;;;;kEACrD,8OAAC;wDAAE,WAAU;kEAAkB,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,sBAAsB;;;;;;;;;;;;0DAElE,8OAAC;;kEACC,8OAAC;wDAAK,WAAU;kEAAqC;;;;;;kEACrD,8OAAC;wDAAE,WAAU;kEAAkB,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;0CAKtE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCAAE,WAAU;kDAAyB;;;;;;;;;;;;;;;;;;kCAK1C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;kCAIV,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,SAAS;gCACP,eAAe;gCACf,mBAAmB;gCACnB,wBAAwB;4BAC1B;4BACA,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 3007, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/components/prescriptor/ProjectWizard.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { \n  ArrowRight,\n  ArrowLeft,\n  CheckCircle,\n  FileText,\n  User,\n  MapPin,\n  Calculator,\n  Save,\n  X\n} from 'lucide-react'\nimport { useStore } from '@/store/useStore'\nimport { PrescriptorService } from '@/services/prescriptorService'\n\ninterface ProjectWizardProps {\n  onClose: () => void\n  onComplete: (project: any) => void\n}\n\nexport default function ProjectWizard({ onClose, onComplete }: ProjectWizardProps) {\n  const { prescriptionTemplates, addPrescriptionProject } = useStore()\n  const [currentStep, setCurrentStep] = useState(1)\n  const [projectData, setProjectData] = useState({\n    template_id: '',\n    project_name: '',\n    client_name: '',\n    client_company: '',\n    project_description: '',\n    location: '',\n    technical_data: {} as Record<string, any>,\n    notes: ''\n  })\n\n  const steps = [\n    {\n      id: 1,\n      title: 'Template',\n      description: 'Choisir le template de prescription',\n      icon: <FileText className=\"h-5 w-5\" />\n    },\n    {\n      id: 2,\n      title: 'Informations',\n      description: 'Détails du projet et client',\n      icon: <User className=\"h-5 w-5\" />\n    },\n    {\n      id: 3,\n      title: 'Paramètres',\n      description: 'Données techniques du projet',\n      icon: <Calculator className=\"h-5 w-5\" />\n    },\n    {\n      id: 4,\n      title: 'Validation',\n      description: 'Vérification et création',\n      icon: <CheckCircle className=\"h-5 w-5\" />\n    }\n  ]\n\n  const selectedTemplate = prescriptionTemplates.find(t => t.id === projectData.template_id)\n\n  const nextStep = () => {\n    if (currentStep < steps.length) {\n      setCurrentStep(currentStep + 1)\n    }\n  }\n\n  const prevStep = () => {\n    if (currentStep > 1) {\n      setCurrentStep(currentStep - 1)\n    }\n  }\n\n  const canProceed = () => {\n    switch (currentStep) {\n      case 1:\n        return projectData.template_id !== ''\n      case 2:\n        return projectData.project_name && projectData.client_name && projectData.location\n      case 3:\n        return selectedTemplate ? \n          selectedTemplate.required_fields.every(field => projectData.technical_data[field]) :\n          true\n      default:\n        return true\n    }\n  }\n\n  const createProject = () => {\n    if (!selectedTemplate) return\n\n    // Calculer les valeurs automatiquement\n    const calculatedValues = PrescriptorService.calculateProjectValues(\n      projectData.technical_data, \n      selectedTemplate\n    )\n\n    const newProject = {\n      id: `project_${Date.now()}`,\n      user_id: 'current_user',\n      template_id: projectData.template_id,\n      project_name: projectData.project_name,\n      client_name: projectData.client_name,\n      client_company: projectData.client_company,\n      project_description: projectData.project_description,\n      location: projectData.location,\n      project_data: projectData.technical_data,\n      calculated_values: calculatedValues,\n      selected_products: [],\n      total_cost: Math.round(calculatedValues.puissance_totale * 50000) || 1000000, // Estimation\n      status: 'draft' as const,\n      compliance_verified: false,\n      warranty_terms: 'Garantie standard 2 ans',\n      notes: projectData.notes,\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString(),\n    }\n\n    addPrescriptionProject(newProject)\n    onComplete(newProject)\n  }\n\n  const renderStepContent = () => {\n    switch (currentStep) {\n      case 1:\n        return (\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n              Sélectionner un Template de Prescription\n            </h3>\n            <div className=\"grid gap-4\">\n              {prescriptionTemplates.map((template) => (\n                <button\n                  key={template.id}\n                  onClick={() => setProjectData(prev => ({ ...prev, template_id: template.id }))}\n                  className={`p-4 rounded-lg border-2 transition-all text-left ${\n                    projectData.template_id === template.id\n                      ? 'border-amber-400 bg-amber-50'\n                      : 'border-gray-200 hover:border-gray-300'\n                  }`}\n                >\n                  <div className=\"flex items-start space-x-3\">\n                    <div className={`w-10 h-10 bg-gradient-to-r ${\n                      template.category === 'electrical' ? 'from-blue-500 to-blue-600' :\n                      template.category === 'energy' ? 'from-green-500 to-green-600' :\n                      'from-purple-500 to-purple-600'\n                    } rounded-lg flex items-center justify-center text-white`}>\n                      <FileText className=\"h-5 w-5\" />\n                    </div>\n                    <div className=\"flex-1\">\n                      <h4 className=\"font-semibold text-gray-900 mb-1\">{template.name}</h4>\n                      <p className=\"text-sm text-gray-600 mb-2\">{template.description}</p>\n                      <div className=\"flex items-center space-x-4 text-xs text-gray-500\">\n                        <span>Niveau: {template.complexity_level}</span>\n                        <span>★ {template.rating}</span>\n                        <span>{template.usage_count} utilisations</span>\n                      </div>\n                    </div>\n                  </div>\n                </button>\n              ))}\n            </div>\n          </div>\n        )\n\n      case 2:\n        return (\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n              Informations du Projet\n            </h3>\n            <div className=\"grid md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Nom du projet *\n                </label>\n                <input\n                  type=\"text\"\n                  value={projectData.project_name}\n                  onChange={(e) => setProjectData(prev => ({ ...prev, project_name: e.target.value }))}\n                  placeholder=\"Ex: Installation électrique bureau\"\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Nom du client *\n                </label>\n                <input\n                  type=\"text\"\n                  value={projectData.client_name}\n                  onChange={(e) => setProjectData(prev => ({ ...prev, client_name: e.target.value }))}\n                  placeholder=\"Ex: Jean Dupont\"\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Entreprise du client\n                </label>\n                <input\n                  type=\"text\"\n                  value={projectData.client_company}\n                  onChange={(e) => setProjectData(prev => ({ ...prev, client_company: e.target.value }))}\n                  placeholder=\"Ex: SODECI\"\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Localisation *\n                </label>\n                <input\n                  type=\"text\"\n                  value={projectData.location}\n                  onChange={(e) => setProjectData(prev => ({ ...prev, location: e.target.value }))}\n                  placeholder=\"Ex: Abidjan, Côte d'Ivoire\"\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400\"\n                />\n              </div>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Description du projet\n              </label>\n              <textarea\n                value={projectData.project_description}\n                onChange={(e) => setProjectData(prev => ({ ...prev, project_description: e.target.value }))}\n                rows={3}\n                placeholder=\"Décrivez brièvement le projet...\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400\"\n              />\n            </div>\n          </div>\n        )\n\n      case 3:\n        return (\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n              Paramètres Techniques\n            </h3>\n            {selectedTemplate && (\n              <div className=\"space-y-4\">\n                <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4\">\n                  <h4 className=\"font-medium text-blue-900 mb-2\">Template: {selectedTemplate.name}</h4>\n                  <p className=\"text-sm text-blue-700\">{selectedTemplate.description}</p>\n                </div>\n                \n                <div className=\"grid md:grid-cols-2 gap-4\">\n                  {selectedTemplate.required_fields.map((field) => (\n                    <div key={field}>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        {field.replace('_', ' ')} *\n                      </label>\n                      <input\n                        type=\"number\"\n                        value={projectData.technical_data[field] || ''}\n                        onChange={(e) => setProjectData(prev => ({\n                          ...prev,\n                          technical_data: {\n                            ...prev.technical_data,\n                            [field]: Number(e.target.value)\n                          }\n                        }))}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400\"\n                      />\n                    </div>\n                  ))}\n                </div>\n                \n                {selectedTemplate.optional_fields.length > 0 && (\n                  <>\n                    <h4 className=\"font-medium text-gray-900 mt-6 mb-3\">Paramètres optionnels</h4>\n                    <div className=\"grid md:grid-cols-2 gap-4\">\n                      {selectedTemplate.optional_fields.map((field) => (\n                        <div key={field}>\n                          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                            {field.replace('_', ' ')}\n                          </label>\n                          <input\n                            type=\"number\"\n                            value={projectData.technical_data[field] || ''}\n                            onChange={(e) => setProjectData(prev => ({\n                              ...prev,\n                              technical_data: {\n                                ...prev.technical_data,\n                                [field]: Number(e.target.value)\n                              }\n                            }))}\n                            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400\"\n                          />\n                        </div>\n                      ))}\n                    </div>\n                  </>\n                )}\n              </div>\n            )}\n          </div>\n        )\n\n      case 4:\n        return (\n          <div className=\"space-y-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n              Validation du Projet\n            </h3>\n            \n            <div className=\"bg-gray-50 rounded-lg p-4 space-y-3\">\n              <div className=\"grid md:grid-cols-2 gap-4 text-sm\">\n                <div>\n                  <span className=\"font-medium text-gray-700\">Template:</span>\n                  <p>{selectedTemplate?.name}</p>\n                </div>\n                <div>\n                  <span className=\"font-medium text-gray-700\">Projet:</span>\n                  <p>{projectData.project_name}</p>\n                </div>\n                <div>\n                  <span className=\"font-medium text-gray-700\">Client:</span>\n                  <p>{projectData.client_name} {projectData.client_company && `- ${projectData.client_company}`}</p>\n                </div>\n                <div>\n                  <span className=\"font-medium text-gray-700\">Localisation:</span>\n                  <p>{projectData.location}</p>\n                </div>\n              </div>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Notes additionnelles\n              </label>\n              <textarea\n                value={projectData.notes}\n                onChange={(e) => setProjectData(prev => ({ ...prev, notes: e.target.value }))}\n                rows={3}\n                placeholder=\"Notes ou commentaires sur le projet...\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400\"\n              />\n            </div>\n\n            <div className=\"bg-green-50 border border-green-200 rounded-lg p-4\">\n              <div className=\"flex items-center space-x-2 mb-2\">\n                <CheckCircle className=\"h-5 w-5 text-green-600\" />\n                <span className=\"font-medium text-green-800\">Prêt à créer</span>\n              </div>\n              <p className=\"text-sm text-green-700\">\n                Le projet sera créé avec les calculs automatiques selon le template sélectionné.\n              </p>\n            </div>\n          </div>\n        )\n\n      default:\n        return null\n    }\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <motion.div\n        initial={{ opacity: 0, scale: 0.9 }}\n        animate={{ opacity: 1, scale: 1 }}\n        className=\"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden\"\n      >\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n          <h2 className=\"text-2xl font-bold text-gray-900\">Nouveau Projet de Prescription</h2>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600\"\n          >\n            <X className=\"h-6 w-6\" />\n          </button>\n        </div>\n\n        {/* Progress Steps */}\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <div className=\"flex items-center justify-between\">\n            {steps.map((step, index) => (\n              <div key={step.id} className=\"flex items-center\">\n                <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${\n                  currentStep >= step.id\n                    ? 'bg-amber-500 border-amber-500 text-white'\n                    : 'border-gray-300 text-gray-500'\n                }`}>\n                  {currentStep > step.id ? (\n                    <CheckCircle className=\"h-5 w-5\" />\n                  ) : (\n                    step.icon\n                  )}\n                </div>\n                <div className=\"ml-3\">\n                  <p className={`text-sm font-medium ${\n                    currentStep >= step.id ? 'text-amber-600' : 'text-gray-500'\n                  }`}>\n                    {step.title}\n                  </p>\n                  <p className=\"text-xs text-gray-500\">{step.description}</p>\n                </div>\n                {index < steps.length - 1 && (\n                  <div className={`w-16 h-0.5 mx-4 ${\n                    currentStep > step.id ? 'bg-amber-500' : 'bg-gray-300'\n                  }`} />\n                )}\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-6 overflow-y-auto max-h-96\">\n          <AnimatePresence mode=\"wait\">\n            <motion.div\n              key={currentStep}\n              initial={{ opacity: 0, x: 20 }}\n              animate={{ opacity: 1, x: 0 }}\n              exit={{ opacity: 0, x: -20 }}\n              transition={{ duration: 0.2 }}\n            >\n              {renderStepContent()}\n            </motion.div>\n          </AnimatePresence>\n        </div>\n\n        {/* Footer */}\n        <div className=\"flex items-center justify-between p-6 border-t border-gray-200\">\n          <button\n            onClick={prevStep}\n            disabled={currentStep === 1}\n            className=\"flex items-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            <ArrowLeft className=\"h-4 w-4\" />\n            <span>Précédent</span>\n          </button>\n\n          <div className=\"text-sm text-gray-500\">\n            Étape {currentStep} sur {steps.length}\n          </div>\n\n          {currentStep < steps.length ? (\n            <button\n              onClick={nextStep}\n              disabled={!canProceed()}\n              className=\"flex items-center space-x-2 btn-premium disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              <span>Suivant</span>\n              <ArrowRight className=\"h-4 w-4\" />\n            </button>\n          ) : (\n            <button\n              onClick={createProject}\n              className=\"flex items-center space-x-2 btn-premium\"\n            >\n              <Save className=\"h-4 w-4\" />\n              <span>Créer le Projet</span>\n            </button>\n          )}\n        </div>\n      </motion.div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AAhBA;;;;;;;AAuBe,SAAS,cAAc,EAAE,OAAO,EAAE,UAAU,EAAsB;IAC/E,MAAM,EAAE,qBAAqB,EAAE,sBAAsB,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IACjE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC7C,aAAa;QACb,cAAc;QACd,aAAa;QACb,gBAAgB;QAChB,qBAAqB;QACrB,UAAU;QACV,gBAAgB,CAAC;QACjB,OAAO;IACT;IAEA,MAAM,QAAQ;QACZ;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;QAC5B;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;QACxB;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,8MAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;QAC9B;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,2NAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;QAC/B;KACD;IAED,MAAM,mBAAmB,sBAAsB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,YAAY,WAAW;IAEzF,MAAM,WAAW;QACf,IAAI,cAAc,MAAM,MAAM,EAAE;YAC9B,eAAe,cAAc;QAC/B;IACF;IAEA,MAAM,WAAW;QACf,IAAI,cAAc,GAAG;YACnB,eAAe,cAAc;QAC/B;IACF;IAEA,MAAM,aAAa;QACjB,OAAQ;YACN,KAAK;gBACH,OAAO,YAAY,WAAW,KAAK;YACrC,KAAK;gBACH,OAAO,YAAY,YAAY,IAAI,YAAY,WAAW,IAAI,YAAY,QAAQ;YACpF,KAAK;gBACH,OAAO,mBACL,iBAAiB,eAAe,CAAC,KAAK,CAAC,CAAA,QAAS,YAAY,cAAc,CAAC,MAAM,IACjF;YACJ;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,kBAAkB;QAEvB,uCAAuC;QACvC,MAAM,mBAAmB,qIAAA,CAAA,qBAAkB,CAAC,sBAAsB,CAChE,YAAY,cAAc,EAC1B;QAGF,MAAM,aAAa;YACjB,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,IAAI;YAC3B,SAAS;YACT,aAAa,YAAY,WAAW;YACpC,cAAc,YAAY,YAAY;YACtC,aAAa,YAAY,WAAW;YACpC,gBAAgB,YAAY,cAAc;YAC1C,qBAAqB,YAAY,mBAAmB;YACpD,UAAU,YAAY,QAAQ;YAC9B,cAAc,YAAY,cAAc;YACxC,mBAAmB;YACnB,mBAAmB,EAAE;YACrB,YAAY,KAAK,KAAK,CAAC,iBAAiB,gBAAgB,GAAG,UAAU;YACrE,QAAQ;YACR,qBAAqB;YACrB,gBAAgB;YAChB,OAAO,YAAY,KAAK;YACxB,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY,IAAI,OAAO,WAAW;QACpC;QAEA,uBAAuB;QACvB,WAAW;IACb;IAEA,MAAM,oBAAoB;QACxB,OAAQ;YACN,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA2C;;;;;;sCAGzD,8OAAC;4BAAI,WAAU;sCACZ,sBAAsB,GAAG,CAAC,CAAC,yBAC1B,8OAAC;oCAEC,SAAS,IAAM,eAAe,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,aAAa,SAAS,EAAE;4CAAC,CAAC;oCAC5E,WAAW,CAAC,iDAAiD,EAC3D,YAAY,WAAW,KAAK,SAAS,EAAE,GACnC,iCACA,yCACJ;8CAEF,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAW,CAAC,2BAA2B,EAC1C,SAAS,QAAQ,KAAK,eAAe,8BACrC,SAAS,QAAQ,KAAK,WAAW,gCACjC,gCACD,uDAAuD,CAAC;0DACvD,cAAA,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;0DAEtB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAoC,SAAS,IAAI;;;;;;kEAC/D,8OAAC;wDAAE,WAAU;kEAA8B,SAAS,WAAW;;;;;;kEAC/D,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;oEAAK;oEAAS,SAAS,gBAAgB;;;;;;;0EACxC,8OAAC;;oEAAK;oEAAG,SAAS,MAAM;;;;;;;0EACxB,8OAAC;;oEAAM,SAAS,WAAW;oEAAC;;;;;;;;;;;;;;;;;;;;;;;;;mCAtB7B,SAAS,EAAE;;;;;;;;;;;;;;;;YAgC5B,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA2C;;;;;;sCAGzD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,MAAK;4CACL,OAAO,YAAY,YAAY;4CAC/B,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,cAAc,EAAE,MAAM,CAAC,KAAK;oDAAC,CAAC;4CAClF,aAAY;4CACZ,WAAU;;;;;;;;;;;;8CAGd,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,MAAK;4CACL,OAAO,YAAY,WAAW;4CAC9B,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;oDAAC,CAAC;4CACjF,aAAY;4CACZ,WAAU;;;;;;;;;;;;8CAGd,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,MAAK;4CACL,OAAO,YAAY,cAAc;4CACjC,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;oDAAC,CAAC;4CACpF,aAAY;4CACZ,WAAU;;;;;;;;;;;;8CAGd,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,MAAK;4CACL,OAAO,YAAY,QAAQ;4CAC3B,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;oDAAC,CAAC;4CAC9E,aAAY;4CACZ,WAAU;;;;;;;;;;;;;;;;;;sCAIhB,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,OAAO,YAAY,mBAAmB;oCACtC,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,qBAAqB,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCACzF,MAAM;oCACN,aAAY;oCACZ,WAAU;;;;;;;;;;;;;;;;;;YAMpB,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA2C;;;;;;wBAGxD,kCACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;gDAAiC;gDAAW,iBAAiB,IAAI;;;;;;;sDAC/E,8OAAC;4CAAE,WAAU;sDAAyB,iBAAiB,WAAW;;;;;;;;;;;;8CAGpE,8OAAC;oCAAI,WAAU;8CACZ,iBAAiB,eAAe,CAAC,GAAG,CAAC,CAAC,sBACrC,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;;wDACd,MAAM,OAAO,CAAC,KAAK;wDAAK;;;;;;;8DAE3B,8OAAC;oDACC,MAAK;oDACL,OAAO,YAAY,cAAc,CAAC,MAAM,IAAI;oDAC5C,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;gEACvC,GAAG,IAAI;gEACP,gBAAgB;oEACd,GAAG,KAAK,cAAc;oEACtB,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gEAChC;4DACF,CAAC;oDACD,WAAU;;;;;;;2CAdJ;;;;;;;;;;gCAoBb,iBAAiB,eAAe,CAAC,MAAM,GAAG,mBACzC;;sDACE,8OAAC;4CAAG,WAAU;sDAAsC;;;;;;sDACpD,8OAAC;4CAAI,WAAU;sDACZ,iBAAiB,eAAe,CAAC,GAAG,CAAC,CAAC,sBACrC,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEACd,MAAM,OAAO,CAAC,KAAK;;;;;;sEAEtB,8OAAC;4DACC,MAAK;4DACL,OAAO,YAAY,cAAc,CAAC,MAAM,IAAI;4DAC5C,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;wEACvC,GAAG,IAAI;wEACP,gBAAgB;4EACd,GAAG,KAAK,cAAc;4EACtB,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wEAChC;oEACF,CAAC;4DACD,WAAU;;;;;;;mDAdJ;;;;;;;;;;;;;;;;;;;;;;;;YA0B5B,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA2C;;;;;;sCAIzD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;0DAA4B;;;;;;0DAC5C,8OAAC;0DAAG,kBAAkB;;;;;;;;;;;;kDAExB,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;0DAA4B;;;;;;0DAC5C,8OAAC;0DAAG,YAAY,YAAY;;;;;;;;;;;;kDAE9B,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;0DAA4B;;;;;;0DAC5C,8OAAC;;oDAAG,YAAY,WAAW;oDAAC;oDAAE,YAAY,cAAc,IAAI,CAAC,EAAE,EAAE,YAAY,cAAc,EAAE;;;;;;;;;;;;;kDAE/F,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;0DAA4B;;;;;;0DAC5C,8OAAC;0DAAG,YAAY,QAAQ;;;;;;;;;;;;;;;;;;;;;;;sCAK9B,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,OAAO,YAAY,KAAK;oCACxB,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCAC3E,MAAM;oCACN,aAAY;oCACZ,WAAU;;;;;;;;;;;;sCAId,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,8OAAC;4CAAK,WAAU;sDAA6B;;;;;;;;;;;;8CAE/C,8OAAC;oCAAE,WAAU;8CAAyB;;;;;;;;;;;;;;;;;;YAO9C;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAI;YAClC,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAE;YAChC,WAAU;;8BAGV,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAKjB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;gCAAkB,WAAU;;kDAC3B,8OAAC;wCAAI,WAAW,CAAC,iEAAiE,EAChF,eAAe,KAAK,EAAE,GAClB,6CACA,iCACJ;kDACC,cAAc,KAAK,EAAE,iBACpB,8OAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;mDAEvB,KAAK,IAAI;;;;;;kDAGb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAW,CAAC,oBAAoB,EACjC,eAAe,KAAK,EAAE,GAAG,mBAAmB,iBAC5C;0DACC,KAAK,KAAK;;;;;;0DAEb,8OAAC;gDAAE,WAAU;0DAAyB,KAAK,WAAW;;;;;;;;;;;;oCAEvD,QAAQ,MAAM,MAAM,GAAG,mBACtB,8OAAC;wCAAI,WAAW,CAAC,gBAAgB,EAC/B,cAAc,KAAK,EAAE,GAAG,iBAAiB,eACzC;;;;;;;+BAvBI,KAAK,EAAE;;;;;;;;;;;;;;;8BA+BvB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,yLAAA,CAAA,kBAAe;wBAAC,MAAK;kCACpB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,MAAM;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC3B,YAAY;gCAAE,UAAU;4BAAI;sCAE3B;2BANI;;;;;;;;;;;;;;;8BAYX,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS;4BACT,UAAU,gBAAgB;4BAC1B,WAAU;;8CAEV,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,8OAAC;8CAAK;;;;;;;;;;;;sCAGR,8OAAC;4BAAI,WAAU;;gCAAwB;gCAC9B;gCAAY;gCAAM,MAAM,MAAM;;;;;;;wBAGtC,cAAc,MAAM,MAAM,iBACzB,8OAAC;4BACC,SAAS;4BACT,UAAU,CAAC;4BACX,WAAU;;8CAEV,8OAAC;8CAAK;;;;;;8CACN,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;iDAGxB,8OAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB", "debugId": null}}, {"offset": {"line": 4062, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/components/common/AccessControl.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { motion } from 'framer-motion'\nimport { \n  Lock,\n  Crown,\n  Star,\n  Award,\n  Shield,\n  AlertTriangle,\n  Upgrade,\n  X,\n  CheckCircle\n} from 'lucide-react'\n\ninterface AccessControlProps {\n  requiredLevel: 'bronze' | 'silver' | 'gold' | 'platinum'\n  currentLevel?: 'bronze' | 'silver' | 'gold' | 'platinum' | null\n  feature: string\n  onUpgrade?: () => void\n  children: React.ReactNode\n}\n\nexport default function AccessControl({ \n  requiredLevel, \n  currentLevel = 'bronze', \n  feature, \n  onUpgrade,\n  children \n}: AccessControlProps) {\n  const [showUpgradeModal, setShowUpgradeModal] = useState(false)\n\n  const membershipLevels = {\n    bronze: { order: 1, icon: <Shield className=\"h-5 w-5\" />, color: 'from-amber-600 to-amber-700', name: 'Bronze' },\n    silver: { order: 2, icon: <Star className=\"h-5 w-5\" />, color: 'from-gray-400 to-gray-500', name: 'Silver' },\n    gold: { order: 3, icon: <Crown className=\"h-5 w-5\" />, color: 'from-yellow-400 to-yellow-500', name: 'Gold' },\n    platinum: { order: 4, icon: <Award className=\"h-5 w-5\" />, color: 'from-purple-400 to-purple-500', name: 'Platinum' }\n  }\n\n  const currentOrder = currentLevel ? membershipLevels[currentLevel].order : 0\n  const requiredOrder = membershipLevels[requiredLevel].order\n  const hasAccess = currentOrder >= requiredOrder\n\n  const benefits = {\n    bronze: [\n      'Accès aux templates de base',\n      'Calculatrice simple',\n      'Support par email',\n      'Documentation publique'\n    ],\n    silver: [\n      'Templates avancés',\n      'Calculs automatisés',\n      'Validation technique',\n      'Chat support',\n      'Bibliothèque étendue'\n    ],\n    gold: [\n      'Templates experts',\n      'Consultation directe',\n      'Certification officielle',\n      'Rapports personnalisés',\n      'Formation exclusive',\n      'Support téléphonique'\n    ],\n    platinum: [\n      'Accès illimité total',\n      'Templates sur mesure',\n      'Expert dédié',\n      'Certification premium',\n      'Formation VIP',\n      'Support prioritaire 24/7',\n      'API privée'\n    ]\n  }\n\n  const pricing = {\n    bronze: { monthly: 0, annual: 0 },\n    silver: { monthly: 25000, annual: 250000 },\n    gold: { monthly: 75000, annual: 750000 },\n    platinum: { monthly: 150000, annual: 1500000 }\n  }\n\n  if (hasAccess) {\n    return <>{children}</>\n  }\n\n  return (\n    <div className=\"relative\">\n      {/* Contenu flouté */}\n      <div className=\"relative\">\n        <div className=\"filter blur-sm pointer-events-none select-none\">\n          {children}\n        </div>\n        \n        {/* Overlay de restriction */}\n        <div className=\"absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent flex items-center justify-center\">\n          <motion.div\n            initial={{ opacity: 0, scale: 0.9 }}\n            animate={{ opacity: 1, scale: 1 }}\n            className=\"bg-white rounded-lg shadow-xl p-6 max-w-md mx-4 text-center\"\n          >\n            <div className={`w-16 h-16 bg-gradient-to-r ${membershipLevels[requiredLevel].color} rounded-full flex items-center justify-center mx-auto mb-4`}>\n              <Lock className=\"h-8 w-8 text-white\" />\n            </div>\n            \n            <h3 className=\"text-xl font-bold text-gray-900 mb-2\">\n              Accès Restreint\n            </h3>\n            \n            <p className=\"text-gray-600 mb-4\">\n              <strong>{feature}</strong> nécessite un niveau d'adhésion{' '}\n              <span className={`font-bold bg-gradient-to-r ${membershipLevels[requiredLevel].color} bg-clip-text text-transparent`}>\n                {membershipLevels[requiredLevel].name}\n              </span> ou supérieur.\n            </p>\n            \n            <div className=\"flex items-center justify-center space-x-2 mb-4\">\n              <span className=\"text-sm text-gray-500\">Votre niveau actuel:</span>\n              {currentLevel ? (\n                <div className={`flex items-center space-x-1 px-3 py-1 rounded-full bg-gradient-to-r ${membershipLevels[currentLevel].color} text-white text-sm font-medium`}>\n                  {membershipLevels[currentLevel].icon}\n                  <span>{membershipLevels[currentLevel].name}</span>\n                </div>\n              ) : (\n                <span className=\"text-sm text-gray-400\">Aucun</span>\n              )}\n            </div>\n            \n            <div className=\"space-y-2 mb-6\">\n              <button\n                onClick={() => setShowUpgradeModal(true)}\n                className=\"w-full btn-premium\"\n              >\n                <Upgrade className=\"h-4 w-4 mr-2\" />\n                Passer au niveau {membershipLevels[requiredLevel].name}\n              </button>\n              \n              <p className=\"text-xs text-gray-500\">\n                À partir de {pricing[requiredLevel].monthly.toLocaleString()} FCFA/mois\n              </p>\n            </div>\n            \n            <div className=\"text-xs text-gray-400\">\n              Rejoignez {Math.floor(Math.random() * 500) + 200}+ professionnels qui utilisent Pro Matos\n            </div>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Modal d'upgrade */}\n      {showUpgradeModal && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n          <motion.div\n            initial={{ opacity: 0, scale: 0.9 }}\n            animate={{ opacity: 1, scale: 1 }}\n            className=\"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto\"\n          >\n            {/* Header */}\n            <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n              <h2 className=\"text-2xl font-bold text-gray-900\">Choisissez Votre Niveau d'Adhésion</h2>\n              <button\n                onClick={() => setShowUpgradeModal(false)}\n                className=\"text-gray-400 hover:text-gray-600\"\n              >\n                <X className=\"h-6 w-6\" />\n              </button>\n            </div>\n\n            {/* Plans de pricing */}\n            <div className=\"p-6\">\n              <div className=\"grid md:grid-cols-4 gap-6\">\n                {Object.entries(membershipLevels).map(([level, config]) => {\n                  const isRecommended = level === requiredLevel\n                  const isCurrentLevel = level === currentLevel\n                  \n                  return (\n                    <div\n                      key={level}\n                      className={`relative rounded-lg border-2 p-6 ${\n                        isRecommended \n                          ? 'border-amber-400 bg-amber-50' \n                          : isCurrentLevel\n                          ? 'border-green-400 bg-green-50'\n                          : 'border-gray-200 bg-white'\n                      }`}\n                    >\n                      {isRecommended && (\n                        <div className=\"absolute -top-3 left-1/2 transform -translate-x-1/2\">\n                          <span className=\"bg-amber-500 text-white px-3 py-1 rounded-full text-xs font-medium\">\n                            Recommandé\n                          </span>\n                        </div>\n                      )}\n                      \n                      {isCurrentLevel && (\n                        <div className=\"absolute -top-3 left-1/2 transform -translate-x-1/2\">\n                          <span className=\"bg-green-500 text-white px-3 py-1 rounded-full text-xs font-medium\">\n                            Actuel\n                          </span>\n                        </div>\n                      )}\n\n                      <div className=\"text-center mb-4\">\n                        <div className={`w-12 h-12 bg-gradient-to-r ${config.color} rounded-full flex items-center justify-center mx-auto mb-3`}>\n                          {config.icon}\n                        </div>\n                        <h3 className=\"text-xl font-bold text-gray-900\">{config.name}</h3>\n                        <div className=\"mt-2\">\n                          <span className=\"text-3xl font-bold text-gray-900\">\n                            {pricing[level as keyof typeof pricing].monthly.toLocaleString()}\n                          </span>\n                          <span className=\"text-gray-600\"> FCFA/mois</span>\n                        </div>\n                        {pricing[level as keyof typeof pricing].annual > 0 && (\n                          <p className=\"text-sm text-gray-500 mt-1\">\n                            ou {pricing[level as keyof typeof pricing].annual.toLocaleString()} FCFA/an\n                          </p>\n                        )}\n                      </div>\n\n                      <ul className=\"space-y-2 mb-6\">\n                        {benefits[level as keyof typeof benefits].map((benefit, index) => (\n                          <li key={index} className=\"flex items-center space-x-2 text-sm\">\n                            <CheckCircle className=\"h-4 w-4 text-green-500 flex-shrink-0\" />\n                            <span>{benefit}</span>\n                          </li>\n                        ))}\n                      </ul>\n\n                      <button\n                        onClick={() => {\n                          setShowUpgradeModal(false)\n                          onUpgrade?.()\n                        }}\n                        disabled={isCurrentLevel}\n                        className={`w-full py-2 px-4 rounded-lg font-medium transition-colors ${\n                          isCurrentLevel\n                            ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                            : isRecommended\n                            ? 'bg-amber-500 text-white hover:bg-amber-600'\n                            : 'bg-gray-900 text-white hover:bg-gray-800'\n                        }`}\n                      >\n                        {isCurrentLevel ? 'Niveau actuel' : `Passer au ${config.name}`}\n                      </button>\n                    </div>\n                  )\n                })}\n              </div>\n\n              {/* Garanties et sécurité */}\n              <div className=\"mt-8 bg-gray-50 rounded-lg p-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4 text-center\">\n                  Pourquoi Choisir Pro Matos ?\n                </h3>\n                <div className=\"grid md:grid-cols-3 gap-6 text-center\">\n                  <div>\n                    <Shield className=\"h-8 w-8 text-green-600 mx-auto mb-2\" />\n                    <h4 className=\"font-medium text-gray-900 mb-1\">Sécurité Garantie</h4>\n                    <p className=\"text-sm text-gray-600\">Calculs certifiés selon normes internationales</p>\n                  </div>\n                  <div>\n                    <Award className=\"h-8 w-8 text-blue-600 mx-auto mb-2\" />\n                    <h4 className=\"font-medium text-gray-900 mb-1\">Expertise Reconnue</h4>\n                    <p className=\"text-sm text-gray-600\">Validation par experts certifiés</p>\n                  </div>\n                  <div>\n                    <Crown className=\"h-8 w-8 text-purple-600 mx-auto mb-2\" />\n                    <h4 className=\"font-medium text-gray-900 mb-1\">Standard du Marché</h4>\n                    <p className=\"text-sm text-gray-600\">Référence utilisée par 500+ professionnels</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAwBe,SAAS,cAAc,EACpC,aAAa,EACb,eAAe,QAAQ,EACvB,OAAO,EACP,SAAS,EACT,QAAQ,EACW;IACnB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,mBAAmB;QACvB,QAAQ;YAAE,OAAO;YAAG,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YAAc,OAAO;YAA+B,MAAM;QAAS;QAC/G,QAAQ;YAAE,OAAO;YAAG,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YAAc,OAAO;YAA6B,MAAM;QAAS;QAC3G,MAAM;YAAE,OAAO;YAAG,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YAAc,OAAO;YAAiC,MAAM;QAAO;QAC5G,UAAU;YAAE,OAAO;YAAG,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YAAc,OAAO;YAAiC,MAAM;QAAW;IACtH;IAEA,MAAM,eAAe,eAAe,gBAAgB,CAAC,aAAa,CAAC,KAAK,GAAG;IAC3E,MAAM,gBAAgB,gBAAgB,CAAC,cAAc,CAAC,KAAK;IAC3D,MAAM,YAAY,gBAAgB;IAElC,MAAM,WAAW;QACf,QAAQ;YACN;YACA;YACA;YACA;SACD;QACD,QAAQ;YACN;YACA;YACA;YACA;YACA;SACD;QACD,MAAM;YACJ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,MAAM,UAAU;QACd,QAAQ;YAAE,SAAS;YAAG,QAAQ;QAAE;QAChC,QAAQ;YAAE,SAAS;YAAO,QAAQ;QAAO;QACzC,MAAM;YAAE,SAAS;YAAO,QAAQ;QAAO;QACvC,UAAU;YAAE,SAAS;YAAQ,QAAQ;QAAQ;IAC/C;IAEA,IAAI,WAAW;QACb,qBAAO;sBAAG;;IACZ;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACZ;;;;;;kCAIH,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BAChC,WAAU;;8CAEV,8OAAC;oCAAI,WAAW,CAAC,2BAA2B,EAAE,gBAAgB,CAAC,cAAc,CAAC,KAAK,CAAC,2DAA2D,CAAC;8CAC9I,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAGlB,8OAAC;oCAAG,WAAU;8CAAuC;;;;;;8CAIrD,8OAAC;oCAAE,WAAU;;sDACX,8OAAC;sDAAQ;;;;;;wCAAiB;wCAAgC;sDAC1D,8OAAC;4CAAK,WAAW,CAAC,2BAA2B,EAAE,gBAAgB,CAAC,cAAc,CAAC,KAAK,CAAC,8BAA8B,CAAC;sDACjH,gBAAgB,CAAC,cAAc,CAAC,IAAI;;;;;;wCAChC;;;;;;;8CAGT,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAwB;;;;;;wCACvC,6BACC,8OAAC;4CAAI,WAAW,CAAC,oEAAoE,EAAE,gBAAgB,CAAC,aAAa,CAAC,KAAK,CAAC,+BAA+B,CAAC;;gDACzJ,gBAAgB,CAAC,aAAa,CAAC,IAAI;8DACpC,8OAAC;8DAAM,gBAAgB,CAAC,aAAa,CAAC,IAAI;;;;;;;;;;;iEAG5C,8OAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;;8CAI5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,oBAAoB;4CACnC,WAAU;;8DAEV,8OAAC,kLAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAAiB;gDAClB,gBAAgB,CAAC,cAAc,CAAC,IAAI;;;;;;;sDAGxD,8OAAC;4CAAE,WAAU;;gDAAwB;gDACtB,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,cAAc;gDAAG;;;;;;;;;;;;;8CAIjE,8OAAC;oCAAI,WAAU;;wCAAwB;wCAC1B,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO;wCAAI;;;;;;;;;;;;;;;;;;;;;;;;YAOxD,kCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAI;oBAClC,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAE;oBAChC,WAAU;;sCAGV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC;oCACC,SAAS,IAAM,oBAAoB;oCACnC,WAAU;8CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACZ,OAAO,OAAO,CAAC,kBAAkB,GAAG,CAAC,CAAC,CAAC,OAAO,OAAO;wCACpD,MAAM,gBAAgB,UAAU;wCAChC,MAAM,iBAAiB,UAAU;wCAEjC,qBACE,8OAAC;4CAEC,WAAW,CAAC,iCAAiC,EAC3C,gBACI,iCACA,iBACA,iCACA,4BACJ;;gDAED,+BACC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAqE;;;;;;;;;;;gDAMxF,gCACC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAqE;;;;;;;;;;;8DAMzF,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAW,CAAC,2BAA2B,EAAE,OAAO,KAAK,CAAC,2DAA2D,CAAC;sEACpH,OAAO,IAAI;;;;;;sEAEd,8OAAC;4DAAG,WAAU;sEAAmC,OAAO,IAAI;;;;;;sEAC5D,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EACb,OAAO,CAAC,MAA8B,CAAC,OAAO,CAAC,cAAc;;;;;;8EAEhE,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;;;;;;;wDAEjC,OAAO,CAAC,MAA8B,CAAC,MAAM,GAAG,mBAC/C,8OAAC;4DAAE,WAAU;;gEAA6B;gEACpC,OAAO,CAAC,MAA8B,CAAC,MAAM,CAAC,cAAc;gEAAG;;;;;;;;;;;;;8DAKzE,8OAAC;oDAAG,WAAU;8DACX,QAAQ,CAAC,MAA+B,CAAC,GAAG,CAAC,CAAC,SAAS,sBACtD,8OAAC;4DAAe,WAAU;;8EACxB,8OAAC,2NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;8EACvB,8OAAC;8EAAM;;;;;;;2DAFA;;;;;;;;;;8DAOb,8OAAC;oDACC,SAAS;wDACP,oBAAoB;wDACpB;oDACF;oDACA,UAAU;oDACV,WAAW,CAAC,0DAA0D,EACpE,iBACI,iDACA,gBACA,+CACA,4CACJ;8DAED,iBAAiB,kBAAkB,CAAC,UAAU,EAAE,OAAO,IAAI,EAAE;;;;;;;2CAlE3D;;;;;oCAsEX;;;;;;8CAIF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuD;;;;;;sDAGrE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;4DAAG,WAAU;sEAAiC;;;;;;sEAC/C,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;8DAEvC,8OAAC;;sEACC,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;4DAAG,WAAU;sEAAiC;;;;;;sEAC/C,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;8DAEvC,8OAAC;;sEACC,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;4DAAG,WAAU;sEAAiC;;;;;;sEAC/C,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzD", "debugId": null}}, {"offset": {"line": 4729, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/components/ui/AnimatedCard.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { motion, useMotionValue, useTransform } from 'framer-motion'\nimport { \n  Heart,\n  Share2,\n  Bookmark,\n  MoreHorizontal,\n  TrendingUp,\n  Eye,\n  Clock\n} from 'lucide-react'\n\ninterface AnimatedCardProps {\n  children: React.ReactNode\n  className?: string\n  hover?: boolean\n  tilt?: boolean\n  glow?: boolean\n  interactive?: boolean\n  delay?: number\n  onClick?: () => void\n}\n\nexport default function AnimatedCard({ \n  children, \n  className = '', \n  hover = true,\n  tilt = false,\n  glow = false,\n  interactive = false,\n  delay = 0,\n  onClick \n}: AnimatedCardProps) {\n  const [isHovered, setIsHovered] = useState(false)\n  const [isPressed, setIsPressed] = useState(false)\n\n  // Motion values pour l'effet de tilt\n  const x = useMotionValue(0)\n  const y = useMotionValue(0)\n  const rotateX = useTransform(y, [-100, 100], [30, -30])\n  const rotateY = useTransform(x, [-100, 100], [-30, 30])\n\n  const handleMouseMove = (event: React.MouseEvent<HTMLDivElement>) => {\n    if (!tilt) return\n    \n    const rect = event.currentTarget.getBoundingClientRect()\n    const centerX = rect.left + rect.width / 2\n    const centerY = rect.top + rect.height / 2\n    \n    x.set(event.clientX - centerX)\n    y.set(event.clientY - centerY)\n  }\n\n  const handleMouseLeave = () => {\n    setIsHovered(false)\n    if (tilt) {\n      x.set(0)\n      y.set(0)\n    }\n  }\n\n  const cardVariants = {\n    initial: { \n      opacity: 0, \n      y: 20,\n      scale: 0.95\n    },\n    animate: { \n      opacity: 1, \n      y: 0,\n      scale: 1,\n      transition: {\n        duration: 0.5,\n        delay,\n        ease: [0.25, 0.46, 0.45, 0.94]\n      }\n    },\n    hover: hover ? {\n      y: -8,\n      scale: 1.02,\n      transition: {\n        duration: 0.3,\n        ease: [0.25, 0.46, 0.45, 0.94]\n      }\n    } : {},\n    tap: {\n      scale: 0.98,\n      transition: {\n        duration: 0.1\n      }\n    }\n  }\n\n  const glowVariants = {\n    initial: { opacity: 0 },\n    hover: { \n      opacity: glow ? 0.6 : 0,\n      transition: { duration: 0.3 }\n    }\n  }\n\n  return (\n    <motion.div\n      className={`relative ${className}`}\n      variants={cardVariants}\n      initial=\"initial\"\n      animate=\"animate\"\n      whileHover=\"hover\"\n      whileTap=\"tap\"\n      onMouseEnter={() => setIsHovered(true)}\n      onMouseLeave={handleMouseLeave}\n      onMouseMove={handleMouseMove}\n      onMouseDown={() => setIsPressed(true)}\n      onMouseUp={() => setIsPressed(false)}\n      onClick={onClick}\n      style={tilt ? { rotateX, rotateY, transformStyle: \"preserve-3d\" } : {}}\n    >\n      {/* Glow effect */}\n      {glow && (\n        <motion.div\n          className=\"absolute -inset-1 bg-gradient-to-r from-amber-400 to-amber-600 rounded-lg blur opacity-0\"\n          variants={glowVariants}\n          animate={isHovered ? \"hover\" : \"initial\"}\n        />\n      )}\n\n      {/* Card content */}\n      <div className={`relative bg-white rounded-lg border border-gray-200 overflow-hidden ${\n        interactive ? 'cursor-pointer' : ''\n      }`}>\n        {children}\n\n        {/* Interactive overlay */}\n        {interactive && (\n          <motion.div\n            className=\"absolute inset-0 bg-gradient-to-t from-black/0 via-black/0 to-black/0 opacity-0\"\n            animate={{\n              opacity: isHovered ? 1 : 0,\n              background: isHovered \n                ? 'linear-gradient(to top, rgba(0,0,0,0.1), rgba(0,0,0,0), rgba(0,0,0,0))'\n                : 'linear-gradient(to top, rgba(0,0,0,0), rgba(0,0,0,0), rgba(0,0,0,0))'\n            }}\n            transition={{ duration: 0.3 }}\n          >\n            {/* Action buttons */}\n            <div className=\"absolute top-4 right-4 flex space-x-2\">\n              <motion.button\n                className=\"p-2 bg-white/90 backdrop-blur-sm rounded-full shadow-lg\"\n                initial={{ scale: 0, opacity: 0 }}\n                animate={{ \n                  scale: isHovered ? 1 : 0, \n                  opacity: isHovered ? 1 : 0 \n                }}\n                transition={{ delay: 0.1 }}\n                whileHover={{ scale: 1.1 }}\n                whileTap={{ scale: 0.9 }}\n              >\n                <Heart className=\"h-4 w-4 text-gray-600\" />\n              </motion.button>\n              \n              <motion.button\n                className=\"p-2 bg-white/90 backdrop-blur-sm rounded-full shadow-lg\"\n                initial={{ scale: 0, opacity: 0 }}\n                animate={{ \n                  scale: isHovered ? 1 : 0, \n                  opacity: isHovered ? 1 : 0 \n                }}\n                transition={{ delay: 0.2 }}\n                whileHover={{ scale: 1.1 }}\n                whileTap={{ scale: 0.9 }}\n              >\n                <Share2 className=\"h-4 w-4 text-gray-600\" />\n              </motion.button>\n              \n              <motion.button\n                className=\"p-2 bg-white/90 backdrop-blur-sm rounded-full shadow-lg\"\n                initial={{ scale: 0, opacity: 0 }}\n                animate={{ \n                  scale: isHovered ? 1 : 0, \n                  opacity: isHovered ? 1 : 0 \n                }}\n                transition={{ delay: 0.3 }}\n                whileHover={{ scale: 1.1 }}\n                whileTap={{ scale: 0.9 }}\n              >\n                <Bookmark className=\"h-4 w-4 text-gray-600\" />\n              </motion.button>\n            </div>\n\n            {/* Stats overlay */}\n            <motion.div\n              className=\"absolute bottom-4 left-4 right-4\"\n              initial={{ y: 20, opacity: 0 }}\n              animate={{ \n                y: isHovered ? 0 : 20, \n                opacity: isHovered ? 1 : 0 \n              }}\n              transition={{ delay: 0.2 }}\n            >\n              <div className=\"bg-white/90 backdrop-blur-sm rounded-lg p-3\">\n                <div className=\"flex items-center justify-between text-sm\">\n                  <div className=\"flex items-center space-x-4\">\n                    <div className=\"flex items-center space-x-1 text-gray-600\">\n                      <Eye className=\"h-4 w-4\" />\n                      <span>1.2k</span>\n                    </div>\n                    <div className=\"flex items-center space-x-1 text-gray-600\">\n                      <TrendingUp className=\"h-4 w-4\" />\n                      <span>+12%</span>\n                    </div>\n                    <div className=\"flex items-center space-x-1 text-gray-600\">\n                      <Clock className=\"h-4 w-4\" />\n                      <span>2h</span>\n                    </div>\n                  </div>\n                  <button className=\"p-1 text-gray-600 hover:text-gray-900\">\n                    <MoreHorizontal className=\"h-4 w-4\" />\n                  </button>\n                </div>\n              </div>\n            </motion.div>\n          </motion.div>\n        )}\n\n        {/* Ripple effect */}\n        {isPressed && (\n          <motion.div\n            className=\"absolute inset-0 pointer-events-none\"\n            initial={{ scale: 0, opacity: 0.5 }}\n            animate={{ scale: 2, opacity: 0 }}\n            transition={{ duration: 0.6 }}\n          >\n            <div className=\"w-full h-full bg-amber-400 rounded-full\" />\n          </motion.div>\n        )}\n      </div>\n    </motion.div>\n  )\n}\n\n// Composant de grille animée\nexport function AnimatedGrid({ \n  children, \n  className = '',\n  stagger = 0.1 \n}: { \n  children: React.ReactNode\n  className?: string\n  stagger?: number \n}) {\n  const containerVariants = {\n    initial: {},\n    animate: {\n      transition: {\n        staggerChildren: stagger\n      }\n    }\n  }\n\n  return (\n    <motion.div\n      className={`grid gap-6 ${className}`}\n      variants={containerVariants}\n      initial=\"initial\"\n      animate=\"animate\"\n    >\n      {children}\n    </motion.div>\n  )\n}\n\n// Hook pour les animations de scroll\nexport function useScrollAnimation() {\n  const scrollY = useMotionValue(0)\n  const opacity = useTransform(scrollY, [0, 300], [1, 0])\n  const scale = useTransform(scrollY, [0, 300], [1, 0.8])\n\n  return { scrollY, opacity, scale }\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAyBe,SAAS,aAAa,EACnC,QAAQ,EACR,YAAY,EAAE,EACd,QAAQ,IAAI,EACZ,OAAO,KAAK,EACZ,OAAO,KAAK,EACZ,cAAc,KAAK,EACnB,QAAQ,CAAC,EACT,OAAO,EACW;IAClB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qCAAqC;IACrC,MAAM,IAAI,CAAA,GAAA,kLAAA,CAAA,iBAAc,AAAD,EAAE;IACzB,MAAM,IAAI,CAAA,GAAA,kLAAA,CAAA,iBAAc,AAAD,EAAE;IACzB,MAAM,UAAU,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,GAAG;QAAC,CAAC;QAAK;KAAI,EAAE;QAAC;QAAI,CAAC;KAAG;IACtD,MAAM,UAAU,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,GAAG;QAAC,CAAC;QAAK;KAAI,EAAE;QAAC,CAAC;QAAI;KAAG;IAEtD,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,MAAM;QAEX,MAAM,OAAO,MAAM,aAAa,CAAC,qBAAqB;QACtD,MAAM,UAAU,KAAK,IAAI,GAAG,KAAK,KAAK,GAAG;QACzC,MAAM,UAAU,KAAK,GAAG,GAAG,KAAK,MAAM,GAAG;QAEzC,EAAE,GAAG,CAAC,MAAM,OAAO,GAAG;QACtB,EAAE,GAAG,CAAC,MAAM,OAAO,GAAG;IACxB;IAEA,MAAM,mBAAmB;QACvB,aAAa;QACb,IAAI,MAAM;YACR,EAAE,GAAG,CAAC;YACN,EAAE,GAAG,CAAC;QACR;IACF;IAEA,MAAM,eAAe;QACnB,SAAS;YACP,SAAS;YACT,GAAG;YACH,OAAO;QACT;QACA,SAAS;YACP,SAAS;YACT,GAAG;YACH,OAAO;YACP,YAAY;gBACV,UAAU;gBACV;gBACA,MAAM;oBAAC;oBAAM;oBAAM;oBAAM;iBAAK;YAChC;QACF;QACA,OAAO,QAAQ;YACb,GAAG,CAAC;YACJ,OAAO;YACP,YAAY;gBACV,UAAU;gBACV,MAAM;oBAAC;oBAAM;oBAAM;oBAAM;iBAAK;YAChC;QACF,IAAI,CAAC;QACL,KAAK;YACH,OAAO;YACP,YAAY;gBACV,UAAU;YACZ;QACF;IACF;IAEA,MAAM,eAAe;QACnB,SAAS;YAAE,SAAS;QAAE;QACtB,OAAO;YACL,SAAS,OAAO,MAAM;YACtB,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,SAAS,EAAE,WAAW;QAClC,UAAU;QACV,SAAQ;QACR,SAAQ;QACR,YAAW;QACX,UAAS;QACT,cAAc,IAAM,aAAa;QACjC,cAAc;QACd,aAAa;QACb,aAAa,IAAM,aAAa;QAChC,WAAW,IAAM,aAAa;QAC9B,SAAS;QACT,OAAO,OAAO;YAAE;YAAS;YAAS,gBAAgB;QAAc,IAAI,CAAC;;YAGpE,sBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;gBACV,SAAS,YAAY,UAAU;;;;;;0BAKnC,8OAAC;gBAAI,WAAW,CAAC,oEAAoE,EACnF,cAAc,mBAAmB,IACjC;;oBACC;oBAGA,6BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,SAAS,YAAY,IAAI;4BACzB,YAAY,YACR,2EACA;wBACN;wBACA,YAAY;4BAAE,UAAU;wBAAI;;0CAG5B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,WAAU;wCACV,SAAS;4CAAE,OAAO;4CAAG,SAAS;wCAAE;wCAChC,SAAS;4CACP,OAAO,YAAY,IAAI;4CACvB,SAAS,YAAY,IAAI;wCAC3B;wCACA,YAAY;4CAAE,OAAO;wCAAI;wCACzB,YAAY;4CAAE,OAAO;wCAAI;wCACzB,UAAU;4CAAE,OAAO;wCAAI;kDAEvB,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAGnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,WAAU;wCACV,SAAS;4CAAE,OAAO;4CAAG,SAAS;wCAAE;wCAChC,SAAS;4CACP,OAAO,YAAY,IAAI;4CACvB,SAAS,YAAY,IAAI;wCAC3B;wCACA,YAAY;4CAAE,OAAO;wCAAI;wCACzB,YAAY;4CAAE,OAAO;wCAAI;wCACzB,UAAU;4CAAE,OAAO;wCAAI;kDAEvB,cAAA,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAGpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,WAAU;wCACV,SAAS;4CAAE,OAAO;4CAAG,SAAS;wCAAE;wCAChC,SAAS;4CACP,OAAO,YAAY,IAAI;4CACvB,SAAS,YAAY,IAAI;wCAC3B;wCACA,YAAY;4CAAE,OAAO;wCAAI;wCACzB,YAAY;4CAAE,OAAO;wCAAI;wCACzB,UAAU;4CAAE,OAAO;wCAAI;kDAEvB,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAKxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,GAAG;oCAAI,SAAS;gCAAE;gCAC7B,SAAS;oCACP,GAAG,YAAY,IAAI;oCACnB,SAAS,YAAY,IAAI;gCAC3B;gCACA,YAAY;oCAAE,OAAO;gCAAI;0CAEzB,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,gMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;0EACf,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;0EACtB,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;0DAGV,8OAAC;gDAAO,WAAU;0DAChB,cAAA,8OAAC,gNAAA,CAAA,iBAAc;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBASrC,2BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,OAAO;4BAAG,SAAS;wBAAI;wBAClC,SAAS;4BAAE,OAAO;4BAAG,SAAS;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;kCAE5B,cAAA,8OAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAM3B;AAGO,SAAS,aAAa,EAC3B,QAAQ,EACR,YAAY,EAAE,EACd,UAAU,GAAG,EAKd;IACC,MAAM,oBAAoB;QACxB,SAAS,CAAC;QACV,SAAS;YACP,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,WAAW,EAAE,WAAW;QACpC,UAAU;QACV,SAAQ;QACR,SAAQ;kBAEP;;;;;;AAGP;AAGO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,kLAAA,CAAA,iBAAc,AAAD,EAAE;IAC/B,MAAM,UAAU,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,SAAS;QAAC;QAAG;KAAI,EAAE;QAAC;QAAG;KAAE;IACtD,MAAM,QAAQ,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,SAAS;QAAC;QAAG;KAAI,EAAE;QAAC;QAAG;KAAI;IAEtD,OAAO;QAAE;QAAS;QAAS;IAAM;AACnC", "debugId": null}}, {"offset": {"line": 5201, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/components/prescriptor/PrescriptorModule.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { \n  FileText, \n  Calculator, \n  Award, \n  Settings,\n  Plus,\n  Search,\n  Filter,\n  Clock,\n  CheckCircle,\n  AlertTriangle,\n  Users,\n  Building,\n  Zap,\n  Download,\n  Share2\n} from 'lucide-react'\nimport { useStore } from '@/store/useStore'\nimport { PrescriptorService } from '@/services/prescriptorService'\nimport { formatDate, getMembershipColor } from '@/lib/utils'\nimport QuickCalculator from './QuickCalculator'\nimport CertificateGenerator from './CertificateGenerator'\nimport ProjectWizard from './ProjectWizard'\nimport AccessControl from '../common/AccessControl'\nimport AnimatedCard, { AnimatedGrid } from '../ui/AnimatedCard'\nimport SmartLinks from '../integration/SmartLinks'\n\ninterface PrescriptorModuleProps {\n  className?: string\n}\n\nexport default function PrescriptorModule({ className = '' }: PrescriptorModuleProps) {\n  const {\n    prescriptionTemplates,\n    prescriptionProjects,\n    technicalNotes,\n    complianceCertificates,\n    calculationEngines,\n    activeProject,\n    selectedTemplate,\n    setPrescriptionTemplates,\n    setPrescriptionProjects,\n    setTechnicalNotes,\n    setComplianceCertificates,\n    setCalculationEngines,\n    setActiveProject,\n    setSelectedTemplate\n  } = useStore()\n\n  const [activeTab, setActiveTab] = useState<'templates' | 'projects' | 'calculations' | 'certificates'>('templates')\n  const [searchQuery, setSearchQuery] = useState('')\n  const [filterCategory, setFilterCategory] = useState<'all' | 'electrical' | 'automation' | 'energy' | 'safety'>('all')\n  const [showProjectWizard, setShowProjectWizard] = useState(false)\n  const [currentMembershipLevel, setCurrentMembershipLevel] = useState<'bronze' | 'silver' | 'gold' | 'platinum'>('bronze')\n\n  // Initialisation des données\n  useEffect(() => {\n    const loadInitialData = () => {\n      setPrescriptionTemplates(PrescriptorService.generatePrescriptionTemplates())\n      setPrescriptionProjects(PrescriptorService.generatePrescriptionProjects())\n      setCalculationEngines(PrescriptorService.generateCalculationEngines())\n      \n      // Générer des notes techniques pour les projets existants\n      const projects = PrescriptorService.generatePrescriptionProjects()\n      const allNotes = projects.flatMap(project => \n        PrescriptorService.generateTechnicalNotes(project.id)\n      )\n      setTechnicalNotes(allNotes)\n    }\n\n    loadInitialData()\n  }, [])\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'completed':\n      case 'approved':\n        return 'bg-green-100 text-green-800 border-green-200'\n      case 'in_progress':\n      case 'review':\n        return 'bg-blue-100 text-blue-800 border-blue-200'\n      case 'draft':\n        return 'bg-yellow-100 text-yellow-800 border-yellow-200'\n      default:\n        return 'bg-gray-100 text-gray-800 border-gray-200'\n    }\n  }\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'completed':\n      case 'approved':\n        return <CheckCircle className=\"h-4 w-4\" />\n      case 'in_progress':\n      case 'review':\n        return <Clock className=\"h-4 w-4\" />\n      case 'draft':\n        return <FileText className=\"h-4 w-4\" />\n      default:\n        return <AlertTriangle className=\"h-4 w-4\" />\n    }\n  }\n\n  const getCategoryIcon = (category: string) => {\n    switch (category) {\n      case 'electrical':\n        return <Zap className=\"h-5 w-5\" />\n      case 'automation':\n        return <Settings className=\"h-5 w-5\" />\n      case 'energy':\n        return <Calculator className=\"h-5 w-5\" />\n      case 'safety':\n        return <Award className=\"h-5 w-5\" />\n      default:\n        return <FileText className=\"h-5 w-5\" />\n    }\n  }\n\n  const tabs = [\n    { \n      id: 'templates', \n      label: 'Templates de Prescription', \n      icon: FileText, \n      count: prescriptionTemplates.length,\n      description: 'Modèles prêts à l\\'emploi pour vos prescriptions'\n    },\n    { \n      id: 'projects', \n      label: 'Projets en Cours', \n      icon: Building, \n      count: prescriptionProjects.length,\n      description: 'Vos projets de prescription et leur avancement'\n    },\n    { \n      id: 'calculations', \n      label: 'Outils de Calcul', \n      icon: Calculator, \n      count: calculationEngines.length,\n      description: 'Moteurs de calcul automatisés et certifiés'\n    },\n    { \n      id: 'certificates', \n      label: 'Certificats de Conformité', \n      icon: Award, \n      count: complianceCertificates.length,\n      description: 'Certificats et garanties contractuelles'\n    }\n  ]\n\n  return (\n    <div className={`bg-white rounded-lg shadow-lg ${className}`}>\n      {/* Header */}\n      <div className=\"p-6 border-b border-gray-200\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h2 className=\"text-2xl font-bold text-gray-900\">Module Prescripteur Professionnel</h2>\n            <p className=\"text-gray-600 mt-1\">Kits de prescription, calculs automatisés et certification de conformité</p>\n          </div>\n          \n          <div className=\"flex items-center space-x-4\">\n            <div className=\"text-right\">\n              <div className=\"text-sm text-gray-600\">Projets actifs</div>\n              <div className=\"text-lg font-bold text-amber-600\">\n                {prescriptionProjects.filter(p => p.status === 'in_progress').length}\n              </div>\n            </div>\n            \n            <button\n              type=\"button\"\n              onClick={() => setShowProjectWizard(true)}\n              className=\"btn-premium\"\n            >\n              <Plus className=\"h-4 w-4 mr-2\" />\n              Nouveau Projet\n            </button>\n          </div>\n        </div>\n\n        {/* Barre de recherche et filtres */}\n        <div className=\"mt-4 flex items-center space-x-4\">\n          <div className=\"flex-1 relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n            <input\n              type=\"text\"\n              placeholder=\"Rechercher templates, projets, calculs...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400 focus:border-transparent\"\n            />\n          </div>\n\n          <select\n            value={filterCategory}\n            onChange={(e) => setFilterCategory(e.target.value as any)}\n            className=\"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400 focus:border-transparent\"\n          >\n            <option value=\"all\">Toutes catégories</option>\n            <option value=\"electrical\">Électrique</option>\n            <option value=\"automation\">Automatisme</option>\n            <option value=\"energy\">Énergie</option>\n            <option value=\"safety\">Sécurité</option>\n          </select>\n\n          {/* Sélecteur de niveau pour test */}\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-sm text-gray-600\">Test niveau:</span>\n            <select\n              value={currentMembershipLevel}\n              onChange={(e) => setCurrentMembershipLevel(e.target.value as any)}\n              className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400 text-sm\"\n            >\n              <option value=\"bronze\">🥉 Bronze</option>\n              <option value=\"silver\">🥈 Silver</option>\n              <option value=\"gold\">🥇 Gold</option>\n              <option value=\"platinum\">💎 Platinum</option>\n            </select>\n          </div>\n        </div>\n      </div>\n\n      {/* Onglets */}\n      <div className=\"border-b border-gray-200\">\n        <nav className=\"flex space-x-8 px-6\">\n          {tabs.map((tab) => {\n            const Icon = tab.icon\n            const isActive = activeTab === tab.id\n            \n            return (\n              <button\n                key={tab.id}\n                onClick={() => setActiveTab(tab.id as any)}\n                className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${\n                  isActive\n                    ? 'border-amber-500 text-amber-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                <Icon className=\"h-5 w-5\" />\n                <span>{tab.label}</span>\n                {tab.count > 0 && (\n                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                    isActive ? 'bg-amber-100 text-amber-800' : 'bg-gray-100 text-gray-600'\n                  }`}>\n                    {tab.count}\n                  </span>\n                )}\n              </button>\n            )\n          })}\n        </nav>\n      </div>\n\n      {/* Description de l'onglet actif */}\n      <div className=\"px-6 py-3 bg-gradient-to-r from-amber-50 to-yellow-50 border-b border-amber-200\">\n        <p className=\"text-sm text-amber-800\">\n          {tabs.find(tab => tab.id === activeTab)?.description}\n        </p>\n      </div>\n\n      {/* Contenu des onglets */}\n      <div className=\"p-6\">\n        <AnimatePresence mode=\"wait\">\n          {activeTab === 'templates' && (\n            <motion.div\n              key=\"templates\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -20 }}\n              className=\"space-y-6\"\n            >\n              {/* Grille des templates */}\n              <AnimatedGrid className=\"md:grid-cols-2 lg:grid-cols-3\">\n                {prescriptionTemplates.map((template, index) => (\n                  <AccessControl\n                    key={template.id}\n                    requiredLevel={template.membership_required || 'bronze'}\n                    currentLevel={currentMembershipLevel}\n                    feature={`Template ${template.name}`}\n                    onUpgrade={() => {\n                      // Simulation d'upgrade - en réalité, redirection vers paiement\n                      setCurrentMembershipLevel(template.membership_required || 'bronze')\n                    }}\n                  >\n                    <AnimatedCard\n                      hover={true}\n                      tilt={true}\n                      glow={selectedTemplate?.id === template.id}\n                      interactive={true}\n                      delay={index * 0.1}\n                      onClick={() => setSelectedTemplate(template)}\n                      className={selectedTemplate?.id === template.id ? 'ring-2 ring-amber-400' : ''}\n                    >\n                    <div className=\"flex items-start justify-between mb-4\">\n                      <div className={`w-12 h-12 bg-gradient-to-r ${\n                        template.category === 'electrical' ? 'from-blue-500 to-blue-600' :\n                        template.category === 'automation' ? 'from-purple-500 to-purple-600' :\n                        template.category === 'energy' ? 'from-green-500 to-green-600' :\n                        'from-gray-500 to-gray-600'\n                      } rounded-lg flex items-center justify-center text-white`}>\n                        {getCategoryIcon(template.category)}\n                      </div>\n                      {template.is_featured && (\n                        <span className=\"px-2 py-1 bg-amber-100 text-amber-800 text-xs font-medium rounded-full\">\n                          Populaire\n                        </span>\n                      )}\n                    </div>\n                    \n                    <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">{template.name}</h3>\n                    <p className=\"text-sm text-gray-600 mb-4 line-clamp-2\">{template.description}</p>\n                    \n                    <div className=\"flex items-center justify-between mb-3\">\n                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                        template.complexity_level === 'expert' ? 'bg-red-100 text-red-800' :\n                        template.complexity_level === 'advanced' ? 'bg-orange-100 text-orange-800' :\n                        template.complexity_level === 'intermediate' ? 'bg-yellow-100 text-yellow-800' :\n                        'bg-green-100 text-green-800'\n                      }`}>\n                        {template.complexity_level}\n                      </span>\n                      <div className=\"flex items-center space-x-1 text-xs text-gray-500\">\n                        <Users className=\"h-3 w-3\" />\n                        <span>{template.usage_count}</span>\n                      </div>\n                    </div>\n                    \n                    <div className=\"flex items-center justify-between\">\n                      {template.membership_required && (\n                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getMembershipColor(template.membership_required)}`}>\n                          {template.membership_required}+ requis\n                        </span>\n                      )}\n                      <div className=\"flex items-center space-x-1 text-amber-600\">\n                        <span className=\"text-sm font-medium\">★ {template.rating}</span>\n                      </div>\n                    </div>\n                    \n                    <div className=\"mt-4 pt-4 border-t border-gray-200\">\n                      <button className=\"w-full btn-premium text-sm py-2\">\n                        Utiliser ce Template\n                      </button>\n                    </div>\n                    </AnimatedCard>\n                  </AccessControl>\n                ))}\n              </AnimatedGrid>\n            </motion.div>\n          )}\n\n          {activeTab === 'projects' && (\n            <motion.div\n              key=\"projects\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -20 }}\n              className=\"space-y-6\"\n            >\n              {/* Liste des projets */}\n              {prescriptionProjects.length === 0 ? (\n                <div className=\"text-center py-12 text-gray-500\">\n                  <Building className=\"h-16 w-16 mx-auto mb-4 text-gray-300\" />\n                  <h3 className=\"text-lg font-medium mb-2\">Aucun projet en cours</h3>\n                  <p className=\"mb-4\">Créez votre premier projet de prescription</p>\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowProjectWizard(true)}\n                    className=\"btn-premium\"\n                  >\n                    <Plus className=\"h-4 w-4 mr-2\" />\n                    Nouveau Projet\n                  </button>\n                </div>\n              ) : (\n                <div className=\"space-y-4\">\n                  {prescriptionProjects.map((project) => (\n                    <motion.div\n                      key={project.id}\n                      initial={{ opacity: 0, x: -20 }}\n                      animate={{ opacity: 1, x: 0 }}\n                      className=\"industrial-card p-6\"\n                    >\n                      <div className=\"flex items-start justify-between\">\n                        <div className=\"flex-1\">\n                          <div className=\"flex items-center space-x-3 mb-3\">\n                            <span className={`px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(project.status)}`}>\n                              {getStatusIcon(project.status)}\n                              <span className=\"ml-2\">{project.status}</span>\n                            </span>\n                            {project.compliance_verified && (\n                              <span className=\"px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full\">\n                                ✓ Conforme\n                              </span>\n                            )}\n                          </div>\n\n                          <h3 className=\"text-xl font-bold text-gray-900 mb-2\">{project.project_name}</h3>\n                          <p className=\"text-gray-600 mb-3\">{project.project_description}</p>\n\n                          <div className=\"grid md:grid-cols-3 gap-4 mb-4\">\n                            <div>\n                              <span className=\"text-sm font-medium text-gray-700\">Client:</span>\n                              <p className=\"text-sm text-gray-900\">{project.client_name}</p>\n                              <p className=\"text-xs text-gray-500\">{project.client_company}</p>\n                            </div>\n                            <div>\n                              <span className=\"text-sm font-medium text-gray-700\">Localisation:</span>\n                              <p className=\"text-sm text-gray-900\">{project.location}</p>\n                            </div>\n                            <div>\n                              <span className=\"text-sm font-medium text-gray-700\">Coût total:</span>\n                              <p className=\"text-lg font-bold text-amber-600\">\n                                {project.total_cost.toLocaleString()} FCFA\n                              </p>\n                            </div>\n                          </div>\n\n                          <div className=\"flex items-center space-x-6 text-sm text-gray-500\">\n                            <span>Créé: {formatDate(project.created_at)}</span>\n                            <span>Mis à jour: {formatDate(project.updated_at)}</span>\n                            {project.delivery_date && (\n                              <span>Livraison: {formatDate(project.delivery_date)}</span>\n                            )}\n                          </div>\n                        </div>\n\n                        <div className=\"ml-6 flex flex-col space-y-2\">\n                          <button className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\">\n                            Ouvrir\n                          </button>\n                          <button className=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\">\n                            <Download className=\"h-4 w-4 mr-2 inline\" />\n                            Export\n                          </button>\n                          <button className=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\">\n                            <Share2 className=\"h-4 w-4 mr-2 inline\" />\n                            Partager\n                          </button>\n                        </div>\n                      </div>\n                    </motion.div>\n                  ))}\n                </div>\n              )}\n            </motion.div>\n          )}\n\n          {activeTab === 'calculations' && (\n            <motion.div\n              key=\"calculations\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -20 }}\n              className=\"space-y-6\"\n            >\n              {/* Outils de calcul */}\n              <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6\">\n                {calculationEngines.map((engine) => (\n                  <motion.div\n                    key={engine.id}\n                    initial={{ opacity: 0, scale: 0.9 }}\n                    animate={{ opacity: 1, scale: 1 }}\n                    className=\"industrial-card p-6\"\n                  >\n                    <div className=\"flex items-center space-x-3 mb-4\">\n                      <div className=\"w-12 h-12 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center text-white\">\n                        <Calculator className=\"h-6 w-6\" />\n                      </div>\n                      {engine.is_certified && (\n                        <span className=\"px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full\">\n                          ✓ Certifié\n                        </span>\n                      )}\n                    </div>\n\n                    <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">{engine.name}</h3>\n                    <p className=\"text-sm text-gray-600 mb-4\">{engine.description}</p>\n\n                    <div className=\"space-y-2 mb-4\">\n                      <div className=\"flex justify-between text-sm\">\n                        <span className=\"text-gray-600\">Paramètres d'entrée:</span>\n                        <span className=\"font-medium\">{engine.input_parameters.length}</span>\n                      </div>\n                      <div className=\"flex justify-between text-sm\">\n                        <span className=\"text-gray-600\">Paramètres de sortie:</span>\n                        <span className=\"font-medium\">{engine.output_parameters.length}</span>\n                      </div>\n                      <div className=\"flex justify-between text-sm\">\n                        <span className=\"text-gray-600\">Normes:</span>\n                        <span className=\"font-medium\">{engine.safety_standards.length}</span>\n                      </div>\n                    </div>\n\n                    <button className=\"w-full btn-premium text-sm py-2\">\n                      Utiliser l'Outil\n                    </button>\n                  </motion.div>\n                ))}\n              </div>\n\n              {/* Calculatrice rapide */}\n              <QuickCalculator />\n            </motion.div>\n          )}\n\n          {activeTab === 'certificates' && (\n            <motion.div\n              key=\"certificates\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -20 }}\n            >\n              <CertificateGenerator />\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </div>\n\n      {/* Project Wizard */}\n      {showProjectWizard && (\n        <ProjectWizard\n          onClose={() => setShowProjectWizard(false)}\n          onComplete={(project) => {\n            setShowProjectWizard(false)\n            // Optionally switch to projects tab to show the new project\n            setActiveTab('projects')\n          }}\n        />\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA5BA;;;;;;;;;;;;;AAmCe,SAAS,kBAAkB,EAAE,YAAY,EAAE,EAA0B;IAClF,MAAM,EACJ,qBAAqB,EACrB,oBAAoB,EACpB,cAAc,EACd,sBAAsB,EACtB,kBAAkB,EAClB,aAAa,EACb,gBAAgB,EAChB,wBAAwB,EACxB,uBAAuB,EACvB,iBAAiB,EACjB,yBAAyB,EACzB,qBAAqB,EACrB,gBAAgB,EAChB,mBAAmB,EACpB,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAEX,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8D;IACvG,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6D;IAChH,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6C;IAEhH,6BAA6B;IAC7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB;YACtB,yBAAyB,qIAAA,CAAA,qBAAkB,CAAC,6BAA6B;YACzE,wBAAwB,qIAAA,CAAA,qBAAkB,CAAC,4BAA4B;YACvE,sBAAsB,qIAAA,CAAA,qBAAkB,CAAC,0BAA0B;YAEnE,0DAA0D;YAC1D,MAAM,WAAW,qIAAA,CAAA,qBAAkB,CAAC,4BAA4B;YAChE,MAAM,WAAW,SAAS,OAAO,CAAC,CAAA,UAChC,qIAAA,CAAA,qBAAkB,CAAC,sBAAsB,CAAC,QAAQ,EAAE;YAEtD,kBAAkB;QACpB;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;YACL,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B;gBACE,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;QACpC;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,gMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;YACxB,KAAK;gBACH,qBAAO,8OAAC,0MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK;gBACH,qBAAO,8OAAC,8MAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC/B,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B;gBACE,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;QAC/B;IACF;IAEA,MAAM,OAAO;QACX;YACE,IAAI;YACJ,OAAO;YACP,MAAM,8MAAA,CAAA,WAAQ;YACd,OAAO,sBAAsB,MAAM;YACnC,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,0MAAA,CAAA,WAAQ;YACd,OAAO,qBAAqB,MAAM;YAClC,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,8MAAA,CAAA,aAAU;YAChB,OAAO,mBAAmB,MAAM;YAChC,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO,uBAAuB,MAAM;YACpC,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAI,WAAW,CAAC,8BAA8B,EAAE,WAAW;;0BAE1D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;;0CAGpC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;0DACvC,8OAAC;gDAAI,WAAU;0DACZ,qBAAqB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,eAAe,MAAM;;;;;;;;;;;;kDAIxE,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,qBAAqB;wCACpC,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;kCAOvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;;;;;;;0CAId,8OAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;gCACjD,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAM;;;;;;kDACpB,8OAAC;wCAAO,OAAM;kDAAa;;;;;;kDAC3B,8OAAC;wCAAO,OAAM;kDAAa;;;;;;kDAC3B,8OAAC;wCAAO,OAAM;kDAAS;;;;;;kDACvB,8OAAC;wCAAO,OAAM;kDAAS;;;;;;;;;;;;0CAIzB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,0BAA0B,EAAE,MAAM,CAAC,KAAK;wCACzD,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAS;;;;;;0DACvB,8OAAC;gDAAO,OAAM;0DAAS;;;;;;0DACvB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOjC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC;wBACT,MAAM,OAAO,IAAI,IAAI;wBACrB,MAAM,WAAW,cAAc,IAAI,EAAE;wBAErC,qBACE,8OAAC;4BAEC,SAAS,IAAM,aAAa,IAAI,EAAE;4BAClC,WAAW,CAAC,uFAAuF,EACjG,WACI,oCACA,8EACJ;;8CAEF,8OAAC;oCAAK,WAAU;;;;;;8CAChB,8OAAC;8CAAM,IAAI,KAAK;;;;;;gCACf,IAAI,KAAK,GAAG,mBACX,8OAAC;oCAAK,WAAW,CAAC,2CAA2C,EAC3D,WAAW,gCAAgC,6BAC3C;8CACC,IAAI,KAAK;;;;;;;2BAdT,IAAI,EAAE;;;;;oBAmBjB;;;;;;;;;;;0BAKJ,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BACV,KAAK,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,YAAY;;;;;;;;;;;0BAK7C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,yLAAA,CAAA,kBAAe;oBAAC,MAAK;;wBACnB,cAAc,6BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,MAAM;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC3B,WAAU;sCAGV,cAAA,8OAAC,wIAAA,CAAA,eAAY;gCAAC,WAAU;0CACrB,sBAAsB,GAAG,CAAC,CAAC,UAAU,sBACpC,8OAAC,6IAAA,CAAA,UAAa;wCAEZ,eAAe,SAAS,mBAAmB,IAAI;wCAC/C,cAAc;wCACd,SAAS,CAAC,SAAS,EAAE,SAAS,IAAI,EAAE;wCACpC,WAAW;4CACT,+DAA+D;4CAC/D,0BAA0B,SAAS,mBAAmB,IAAI;wCAC5D;kDAEA,cAAA,8OAAC,wIAAA,CAAA,UAAY;4CACX,OAAO;4CACP,MAAM;4CACN,MAAM,kBAAkB,OAAO,SAAS,EAAE;4CAC1C,aAAa;4CACb,OAAO,QAAQ;4CACf,SAAS,IAAM,oBAAoB;4CACnC,WAAW,kBAAkB,OAAO,SAAS,EAAE,GAAG,0BAA0B;;8DAE9E,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAW,CAAC,2BAA2B,EAC1C,SAAS,QAAQ,KAAK,eAAe,8BACrC,SAAS,QAAQ,KAAK,eAAe,kCACrC,SAAS,QAAQ,KAAK,WAAW,gCACjC,4BACD,uDAAuD,CAAC;sEACtD,gBAAgB,SAAS,QAAQ;;;;;;wDAEnC,SAAS,WAAW,kBACnB,8OAAC;4DAAK,WAAU;sEAAyE;;;;;;;;;;;;8DAM7F,8OAAC;oDAAG,WAAU;8DAA4C,SAAS,IAAI;;;;;;8DACvE,8OAAC;oDAAE,WAAU;8DAA2C,SAAS,WAAW;;;;;;8DAE5E,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAW,CAAC,2CAA2C,EAC3D,SAAS,gBAAgB,KAAK,WAAW,4BACzC,SAAS,gBAAgB,KAAK,aAAa,kCAC3C,SAAS,gBAAgB,KAAK,iBAAiB,kCAC/C,+BACA;sEACC,SAAS,gBAAgB;;;;;;sEAE5B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,8OAAC;8EAAM,SAAS,WAAW;;;;;;;;;;;;;;;;;;8DAI/B,8OAAC;oDAAI,WAAU;;wDACZ,SAAS,mBAAmB,kBAC3B,8OAAC;4DAAK,WAAW,CAAC,2CAA2C,EAAE,CAAA,GAAA,mHAAA,CAAA,qBAAkB,AAAD,EAAE,SAAS,mBAAmB,GAAG;;gEAC9G,SAAS,mBAAmB;gEAAC;;;;;;;sEAGlC,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;;oEAAsB;oEAAG,SAAS,MAAM;;;;;;;;;;;;;;;;;;8DAI5D,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAO,WAAU;kEAAkC;;;;;;;;;;;;;;;;;uCAhEjD,SAAS,EAAE;;;;;;;;;;2BAVlB;;;;;wBAqFP,cAAc,4BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,MAAM;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC3B,WAAU;sCAGT,qBAAqB,MAAM,KAAK,kBAC/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAG,WAAU;kDAA2B;;;;;;kDACzC,8OAAC;wCAAE,WAAU;kDAAO;;;;;;kDACpB,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,qBAAqB;wCACpC,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;qDAKrC,8OAAC;gCAAI,WAAU;0CACZ,qBAAqB,GAAG,CAAC,CAAC,wBACzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAW,CAAC,kDAAkD,EAAE,eAAe,QAAQ,MAAM,GAAG;;wEACnG,cAAc,QAAQ,MAAM;sFAC7B,8OAAC;4EAAK,WAAU;sFAAQ,QAAQ,MAAM;;;;;;;;;;;;gEAEvC,QAAQ,mBAAmB,kBAC1B,8OAAC;oEAAK,WAAU;8EAAyE;;;;;;;;;;;;sEAM7F,8OAAC;4DAAG,WAAU;sEAAwC,QAAQ,YAAY;;;;;;sEAC1E,8OAAC;4DAAE,WAAU;sEAAsB,QAAQ,mBAAmB;;;;;;sEAE9D,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;;sFACC,8OAAC;4EAAK,WAAU;sFAAoC;;;;;;sFACpD,8OAAC;4EAAE,WAAU;sFAAyB,QAAQ,WAAW;;;;;;sFACzD,8OAAC;4EAAE,WAAU;sFAAyB,QAAQ,cAAc;;;;;;;;;;;;8EAE9D,8OAAC;;sFACC,8OAAC;4EAAK,WAAU;sFAAoC;;;;;;sFACpD,8OAAC;4EAAE,WAAU;sFAAyB,QAAQ,QAAQ;;;;;;;;;;;;8EAExD,8OAAC;;sFACC,8OAAC;4EAAK,WAAU;sFAAoC;;;;;;sFACpD,8OAAC;4EAAE,WAAU;;gFACV,QAAQ,UAAU,CAAC,cAAc;gFAAG;;;;;;;;;;;;;;;;;;;sEAK3C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;;wEAAK;wEAAO,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,UAAU;;;;;;;8EAC1C,8OAAC;;wEAAK;wEAAa,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,UAAU;;;;;;;gEAC/C,QAAQ,aAAa,kBACpB,8OAAC;;wEAAK;wEAAY,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,aAAa;;;;;;;;;;;;;;;;;;;8DAKxD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAO,WAAU;sEAAkF;;;;;;sEAGpG,8OAAC;4DAAO,WAAU;;8EAChB,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEAAwB;;;;;;;sEAG9C,8OAAC;4DAAO,WAAU;;8EAChB,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEAAwB;;;;;;;;;;;;;;;;;;;uCA1D3C,QAAQ,EAAE;;;;;;;;;;2BAzBnB;;;;;wBA+FP,cAAc,gCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,MAAM;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC3B,WAAU;;8CAGV,8OAAC;oCAAI,WAAU;8CACZ,mBAAmB,GAAG,CAAC,CAAC,uBACvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAI;4CAClC,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CAChC,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,8MAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;wDAEvB,OAAO,YAAY,kBAClB,8OAAC;4DAAK,WAAU;sEAAyE;;;;;;;;;;;;8DAM7F,8OAAC;oDAAG,WAAU;8DAA4C,OAAO,IAAI;;;;;;8DACrE,8OAAC;oDAAE,WAAU;8DAA8B,OAAO,WAAW;;;;;;8DAE7D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,8OAAC;oEAAK,WAAU;8EAAe,OAAO,gBAAgB,CAAC,MAAM;;;;;;;;;;;;sEAE/D,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,8OAAC;oEAAK,WAAU;8EAAe,OAAO,iBAAiB,CAAC,MAAM;;;;;;;;;;;;sEAEhE,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,8OAAC;oEAAK,WAAU;8EAAe,OAAO,gBAAgB,CAAC,MAAM;;;;;;;;;;;;;;;;;;8DAIjE,8OAAC;oDAAO,WAAU;8DAAkC;;;;;;;2CAlC/C,OAAO,EAAE;;;;;;;;;;8CA0CpB,8OAAC,oJAAA,CAAA,UAAe;;;;;;2BApDZ;;;;;wBAwDP,cAAc,gCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,MAAM;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;sCAE3B,cAAA,8OAAC,yJAAA,CAAA,UAAoB;;;;;2BALjB;;;;;;;;;;;;;;;;YAYX,mCACC,8OAAC,kJAAA,CAAA,UAAa;gBACZ,SAAS,IAAM,qBAAqB;gBACpC,YAAY,CAAC;oBACX,qBAAqB;oBACrB,4DAA4D;oBAC5D,aAAa;gBACf;;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 6482, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/components/prescriptor/StandardsControl.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { motion } from 'framer-motion'\nimport { \n  Shield,\n  CheckCircle,\n  AlertTriangle,\n  XCircle,\n  Award,\n  FileText,\n  Calculator,\n  Users,\n  TrendingUp,\n  Lock,\n  Crown\n} from 'lucide-react'\n\nexport default function StandardsControl() {\n  const [selectedStandard, setSelectedStandard] = useState('pro_matos')\n\n  const standards = {\n    pro_matos: {\n      name: 'Standards Pro Matos',\n      description: 'Méthodes et calculs certifiés Pro Matos Afrique Ouest',\n      status: 'imposed',\n      adoption: 87,\n      color: 'from-amber-500 to-amber-600',\n      icon: <Crown className=\"h-6 w-6\" />\n    },\n    nf_c15100: {\n      name: 'NF C 15-100',\n      description: 'Norme française pour installations électriques BT',\n      status: 'integrated',\n      adoption: 65,\n      color: 'from-blue-500 to-blue-600',\n      icon: <Shield className=\"h-6 w-6\" />\n    },\n    iec_60364: {\n      name: 'IEC 60364',\n      description: 'Norme internationale installations électriques',\n      status: 'referenced',\n      adoption: 45,\n      color: 'from-green-500 to-green-600',\n      icon: <FileText className=\"h-6 w-6\" />\n    },\n    local_standards: {\n      name: 'Normes Locales',\n      description: 'Standards nationaux Afrique de l\\'Ouest',\n      status: 'obsolete',\n      adoption: 23,\n      color: 'from-gray-400 to-gray-500',\n      icon: <XCircle className=\"h-6 w-6\" />\n    }\n  }\n\n  const controlMetrics = [\n    {\n      label: 'Templates Utilisés',\n      value: '2,847',\n      change: '+34%',\n      description: 'Projets créés avec nos templates',\n      icon: <FileText className=\"h-5 w-5\" />\n    },\n    {\n      label: 'Calculs Validés',\n      value: '15,692',\n      change: '+67%',\n      description: 'Calculs effectués via nos moteurs',\n      icon: <Calculator className=\"h-5 w-5\" />\n    },\n    {\n      label: 'Professionnels Formés',\n      value: '1,234',\n      change: '+89%',\n      description: 'Ingénieurs certifiés à nos méthodes',\n      icon: <Users className=\"h-5 w-5\" />\n    },\n    {\n      label: 'Certificats Émis',\n      value: '567',\n      change: '+156%',\n      description: 'Projets certifiés conformes',\n      icon: <Award className=\"h-5 w-5\" />\n    }\n  ]\n\n  const adoptionSteps = [\n    {\n      phase: 'Introduction',\n      description: 'Lancement des standards Pro Matos',\n      completion: 100,\n      status: 'complete'\n    },\n    {\n      phase: 'Adoption Précoce',\n      description: 'Premiers utilisateurs et retours',\n      completion: 100,\n      status: 'complete'\n    },\n    {\n      phase: 'Croissance',\n      description: 'Expansion et formation massive',\n      completion: 87,\n      status: 'current'\n    },\n    {\n      phase: 'Domination',\n      description: 'Standard de facto du marché',\n      completion: 45,\n      status: 'future'\n    },\n    {\n      phase: 'Monopole',\n      description: 'Contrôle total de l\\'écosystème',\n      completion: 12,\n      status: 'future'\n    }\n  ]\n\n  const competitorAnalysis = [\n    {\n      name: 'Schneider Electric',\n      marketShare: 35,\n      proMatosIntegration: 78,\n      status: 'partner',\n      trend: 'increasing'\n    },\n    {\n      name: 'ABB',\n      marketShare: 28,\n      proMatosIntegration: 65,\n      status: 'partner',\n      trend: 'increasing'\n    },\n    {\n      name: 'Legrand',\n      marketShare: 22,\n      proMatosIntegration: 82,\n      status: 'partner',\n      trend: 'stable'\n    },\n    {\n      name: 'Bureaux d\\'études locaux',\n      marketShare: 15,\n      proMatosIntegration: 34,\n      status: 'resistance',\n      trend: 'decreasing'\n    }\n  ]\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Header de contrôle */}\n      <div className=\"bg-gradient-to-r from-amber-50 to-yellow-50 rounded-lg p-6 border border-amber-200\">\n        <div className=\"flex items-center space-x-4 mb-4\">\n          <Crown className=\"h-8 w-8 text-amber-600\" />\n          <div>\n            <h2 className=\"text-2xl font-bold text-amber-900\">Contrôle des Standards du Marché</h2>\n            <p className=\"text-amber-700\">Imposer nos méthodes comme référence incontournable</p>\n          </div>\n        </div>\n        \n        <div className=\"grid md:grid-cols-4 gap-4\">\n          {controlMetrics.map((metric, index) => (\n            <motion.div\n              key={metric.label}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: index * 0.1 }}\n              className=\"bg-white rounded-lg p-4 border border-amber-200\"\n            >\n              <div className=\"flex items-center space-x-2 mb-2\">\n                <div className=\"p-2 bg-amber-100 rounded-lg text-amber-600\">\n                  {metric.icon}\n                </div>\n                <div>\n                  <div className=\"text-lg font-bold text-amber-900\">{metric.value}</div>\n                  <div className=\"text-xs text-amber-700\">{metric.label}</div>\n                </div>\n              </div>\n              <div className=\"text-xs text-green-600 font-medium\">{metric.change}</div>\n              <div className=\"text-xs text-amber-600 mt-1\">{metric.description}</div>\n            </motion.div>\n          ))}\n        </div>\n      </div>\n\n      {/* Comparaison des standards */}\n      <div className=\"grid lg:grid-cols-2 gap-8\">\n        <div>\n          <h3 className=\"text-xl font-bold text-gray-900 mb-4\">Standards en Compétition</h3>\n          <div className=\"space-y-3\">\n            {Object.entries(standards).map(([key, standard]) => (\n              <button\n                key={key}\n                onClick={() => setSelectedStandard(key)}\n                className={`w-full p-4 rounded-lg border-2 transition-all text-left ${\n                  selectedStandard === key\n                    ? 'border-amber-400 bg-amber-50'\n                    : 'border-gray-200 hover:border-gray-300'\n                }`}\n              >\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className={`w-10 h-10 bg-gradient-to-r ${standard.color} rounded-lg flex items-center justify-center text-white`}>\n                      {standard.icon}\n                    </div>\n                    <div>\n                      <h4 className=\"font-semibold text-gray-900\">{standard.name}</h4>\n                      <p className=\"text-sm text-gray-600\">{standard.description}</p>\n                    </div>\n                  </div>\n                  <div className=\"text-right\">\n                    <div className=\"text-lg font-bold text-gray-900\">{standard.adoption}%</div>\n                    <div className=\"text-xs text-gray-500\">Adoption</div>\n                  </div>\n                </div>\n                \n                <div className=\"mt-3\">\n                  <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                    <div \n                      className={`h-2 rounded-full bg-gradient-to-r ${standard.color}`}\n                      style={{ width: `${standard.adoption}%` }}\n                    ></div>\n                  </div>\n                </div>\n              </button>\n            ))}\n          </div>\n        </div>\n\n        <div>\n          <h3 className=\"text-xl font-bold text-gray-900 mb-4\">Phases de Domination</h3>\n          <div className=\"space-y-4\">\n            {adoptionSteps.map((step, index) => (\n              <div key={step.phase} className=\"flex items-center space-x-4\">\n                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${\n                  step.status === 'complete' ? 'bg-green-500 text-white' :\n                  step.status === 'current' ? 'bg-amber-500 text-white' :\n                  'bg-gray-300 text-gray-600'\n                }`}>\n                  {step.status === 'complete' ? (\n                    <CheckCircle className=\"h-4 w-4\" />\n                  ) : step.status === 'current' ? (\n                    <TrendingUp className=\"h-4 w-4\" />\n                  ) : (\n                    <Lock className=\"h-4 w-4\" />\n                  )}\n                </div>\n                \n                <div className=\"flex-1\">\n                  <div className=\"flex items-center justify-between mb-1\">\n                    <h4 className=\"font-medium text-gray-900\">{step.phase}</h4>\n                    <span className=\"text-sm font-medium text-gray-600\">{step.completion}%</span>\n                  </div>\n                  <p className=\"text-sm text-gray-600 mb-2\">{step.description}</p>\n                  <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                    <div \n                      className={`h-2 rounded-full ${\n                        step.status === 'complete' ? 'bg-green-500' :\n                        step.status === 'current' ? 'bg-amber-500' :\n                        'bg-gray-400'\n                      }`}\n                      style={{ width: `${step.completion}%` }}\n                    ></div>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Analyse concurrentielle */}\n      <div>\n        <h3 className=\"text-xl font-bold text-gray-900 mb-4\">Intégration Pro Matos par Concurrent</h3>\n        <div className=\"bg-white rounded-lg border border-gray-200 overflow-hidden\">\n          <div className=\"grid grid-cols-4 gap-4 p-4 bg-gray-50 border-b border-gray-200 font-medium text-gray-700\">\n            <div>Concurrent</div>\n            <div>Part de Marché</div>\n            <div>Intégration Pro Matos</div>\n            <div>Tendance</div>\n          </div>\n          \n          {competitorAnalysis.map((competitor, index) => (\n            <motion.div\n              key={competitor.name}\n              initial={{ opacity: 0, x: -20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ delay: index * 0.1 }}\n              className=\"grid grid-cols-4 gap-4 p-4 border-b border-gray-100 hover:bg-gray-50\"\n            >\n              <div className=\"flex items-center space-x-2\">\n                <div className={`w-3 h-3 rounded-full ${\n                  competitor.status === 'partner' ? 'bg-green-500' :\n                  competitor.status === 'resistance' ? 'bg-red-500' :\n                  'bg-yellow-500'\n                }`}></div>\n                <span className=\"font-medium\">{competitor.name}</span>\n              </div>\n              \n              <div>\n                <div className=\"text-sm font-medium\">{competitor.marketShare}%</div>\n                <div className=\"w-full bg-gray-200 rounded-full h-1 mt-1\">\n                  <div \n                    className=\"h-1 rounded-full bg-blue-500\"\n                    style={{ width: `${competitor.marketShare}%` }}\n                  ></div>\n                </div>\n              </div>\n              \n              <div>\n                <div className=\"text-sm font-medium\">{competitor.proMatosIntegration}%</div>\n                <div className=\"w-full bg-gray-200 rounded-full h-1 mt-1\">\n                  <div \n                    className=\"h-1 rounded-full bg-amber-500\"\n                    style={{ width: `${competitor.proMatosIntegration}%` }}\n                  ></div>\n                </div>\n              </div>\n              \n              <div className=\"flex items-center space-x-1\">\n                {competitor.trend === 'increasing' ? (\n                  <TrendingUp className=\"h-4 w-4 text-green-500\" />\n                ) : competitor.trend === 'decreasing' ? (\n                  <TrendingUp className=\"h-4 w-4 text-red-500 rotate-180\" />\n                ) : (\n                  <div className=\"h-4 w-4 bg-gray-400 rounded-full\"></div>\n                )}\n                <span className=\"text-sm capitalize\">{competitor.trend}</span>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n      </div>\n\n      {/* Stratégie de contrôle */}\n      <div className=\"bg-gradient-to-r from-slate-900 to-slate-800 text-white rounded-lg p-6\">\n        <h3 className=\"text-xl font-bold mb-4\">Stratégie de Contrôle Total</h3>\n        <div className=\"grid md:grid-cols-3 gap-6\">\n          <div>\n            <h4 className=\"font-semibold mb-2 flex items-center space-x-2\">\n              <FileText className=\"h-5 w-5 text-amber-400\" />\n              <span>Phase 1: Standards</span>\n            </h4>\n            <ul className=\"text-sm text-slate-300 space-y-1\">\n              <li>• Templates deviennent référence</li>\n              <li>• Calculs certifiés obligatoires</li>\n              <li>• Formation aux méthodes Pro Matos</li>\n            </ul>\n          </div>\n          <div>\n            <h4 className=\"font-semibold mb-2 flex items-center space-x-2\">\n              <Users className=\"h-5 w-5 text-amber-400\" />\n              <span>Phase 2: Réseau</span>\n            </h4>\n            <ul className=\"text-sm text-slate-300 space-y-1\">\n              <li>• Certification obligatoire</li>\n              <li>• Club membre exclusif</li>\n              <li>• Partenariats stratégiques</li>\n            </ul>\n          </div>\n          <div>\n            <h4 className=\"font-semibold mb-2 flex items-center space-x-2\">\n              <Crown className=\"h-5 w-5 text-amber-400\" />\n              <span>Phase 3: Domination</span>\n            </h4>\n            <ul className=\"text-sm text-slate-300 space-y-1\">\n              <li>• Contrôle de l'information</li>\n              <li>• Validation technique obligatoire</li>\n              <li>• Barrière à l'entrée naturelle</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAkBe,SAAS;IACtB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,YAAY;QAChB,WAAW;YACT,MAAM;YACN,aAAa;YACb,QAAQ;YACR,UAAU;YACV,OAAO;YACP,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;QACzB;QACA,WAAW;YACT,MAAM;YACN,aAAa;YACb,QAAQ;YACR,UAAU;YACV,OAAO;YACP,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;QAC1B;QACA,WAAW;YACT,MAAM;YACN,aAAa;YACb,QAAQ;YACR,UAAU;YACV,OAAO;YACP,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;QAC5B;QACA,iBAAiB;YACf,MAAM;YACN,aAAa;YACb,QAAQ;YACR,UAAU;YACV,OAAO;YACP,oBAAM,8OAAC,4MAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,iBAAiB;QACrB;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,aAAa;YACb,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;QAC5B;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,aAAa;YACb,oBAAM,8OAAC,8MAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;QAC9B;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,aAAa;YACb,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;QACzB;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,aAAa;YACb,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;QACzB;KACD;IAED,MAAM,gBAAgB;QACpB;YACE,OAAO;YACP,aAAa;YACb,YAAY;YACZ,QAAQ;QACV;QACA;YACE,OAAO;YACP,aAAa;YACb,YAAY;YACZ,QAAQ;QACV;QACA;YACE,OAAO;YACP,aAAa;YACb,YAAY;YACZ,QAAQ;QACV;QACA;YACE,OAAO;YACP,aAAa;YACb,YAAY;YACZ,QAAQ;QACV;QACA;YACE,OAAO;YACP,aAAa;YACb,YAAY;YACZ,QAAQ;QACV;KACD;IAED,MAAM,qBAAqB;QACzB;YACE,MAAM;YACN,aAAa;YACb,qBAAqB;YACrB,QAAQ;YACR,OAAO;QACT;QACA;YACE,MAAM;YACN,aAAa;YACb,qBAAqB;YACrB,QAAQ;YACR,OAAO;QACT;QACA;YACE,MAAM;YACN,aAAa;YACb,qBAAqB;YACrB,QAAQ;YACR,OAAO;QACT;QACA;YACE,MAAM;YACN,aAAa;YACb,qBAAqB;YACrB,QAAQ;YACR,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAoC;;;;;;kDAClD,8OAAC;wCAAE,WAAU;kDAAiB;;;;;;;;;;;;;;;;;;kCAIlC,8OAAC;wBAAI,WAAU;kCACZ,eAAe,GAAG,CAAC,CAAC,QAAQ,sBAC3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO,QAAQ;gCAAI;gCACjC,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,OAAO,IAAI;;;;;;0DAEd,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEAAoC,OAAO,KAAK;;;;;;kEAC/D,8OAAC;wDAAI,WAAU;kEAA0B,OAAO,KAAK;;;;;;;;;;;;;;;;;;kDAGzD,8OAAC;wCAAI,WAAU;kDAAsC,OAAO,MAAM;;;;;;kDAClE,8OAAC;wCAAI,WAAU;kDAA+B,OAAO,WAAW;;;;;;;+BAhB3D,OAAO,KAAK;;;;;;;;;;;;;;;;0BAuBzB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,8OAAC;gCAAI,WAAU;0CACZ,OAAO,OAAO,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,KAAK,SAAS,iBAC7C,8OAAC;wCAEC,SAAS,IAAM,oBAAoB;wCACnC,WAAW,CAAC,wDAAwD,EAClE,qBAAqB,MACjB,iCACA,yCACJ;;0DAEF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAW,CAAC,2BAA2B,EAAE,SAAS,KAAK,CAAC,uDAAuD,CAAC;0EAClH,SAAS,IAAI;;;;;;0EAEhB,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAA+B,SAAS,IAAI;;;;;;kFAC1D,8OAAC;wEAAE,WAAU;kFAAyB,SAAS,WAAW;;;;;;;;;;;;;;;;;;kEAG9D,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;oEAAmC,SAAS,QAAQ;oEAAC;;;;;;;0EACpE,8OAAC;gEAAI,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAI3C,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,WAAW,CAAC,kCAAkC,EAAE,SAAS,KAAK,EAAE;wDAChE,OAAO;4DAAE,OAAO,GAAG,SAAS,QAAQ,CAAC,CAAC,CAAC;wDAAC;;;;;;;;;;;;;;;;;uCA5BzC;;;;;;;;;;;;;;;;kCAqCb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,8OAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAC,MAAM,sBACxB,8OAAC;wCAAqB,WAAU;;0DAC9B,8OAAC;gDAAI,WAAW,CAAC,sDAAsD,EACrE,KAAK,MAAM,KAAK,aAAa,4BAC7B,KAAK,MAAM,KAAK,YAAY,4BAC5B,6BACA;0DACC,KAAK,MAAM,KAAK,2BACf,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;2DACrB,KAAK,MAAM,KAAK,0BAClB,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;yEAEtB,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAIpB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAA6B,KAAK,KAAK;;;;;;0EACrD,8OAAC;gEAAK,WAAU;;oEAAqC,KAAK,UAAU;oEAAC;;;;;;;;;;;;;kEAEvE,8OAAC;wDAAE,WAAU;kEAA8B,KAAK,WAAW;;;;;;kEAC3D,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DACC,WAAW,CAAC,iBAAiB,EAC3B,KAAK,MAAM,KAAK,aAAa,iBAC7B,KAAK,MAAM,KAAK,YAAY,iBAC5B,eACA;4DACF,OAAO;gEAAE,OAAO,GAAG,KAAK,UAAU,CAAC,CAAC,CAAC;4DAAC;;;;;;;;;;;;;;;;;;uCA5BpC,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;0BAuC5B,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAuC;;;;;;kCACrD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAI;;;;;;kDACL,8OAAC;kDAAI;;;;;;kDACL,8OAAC;kDAAI;;;;;;kDACL,8OAAC;kDAAI;;;;;;;;;;;;4BAGN,mBAAmB,GAAG,CAAC,CAAC,YAAY,sBACnC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO,QAAQ;oCAAI;oCACjC,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAW,CAAC,qBAAqB,EACpC,WAAW,MAAM,KAAK,YAAY,iBAClC,WAAW,MAAM,KAAK,eAAe,eACrC,iBACA;;;;;;8DACF,8OAAC;oDAAK,WAAU;8DAAe,WAAW,IAAI;;;;;;;;;;;;sDAGhD,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;;wDAAuB,WAAW,WAAW;wDAAC;;;;;;;8DAC7D,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,OAAO,GAAG,WAAW,WAAW,CAAC,CAAC,CAAC;wDAAC;;;;;;;;;;;;;;;;;sDAKnD,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;;wDAAuB,WAAW,mBAAmB;wDAAC;;;;;;;8DACrE,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,OAAO,GAAG,WAAW,mBAAmB,CAAC,CAAC,CAAC;wDAAC;;;;;;;;;;;;;;;;;sDAK3D,8OAAC;4CAAI,WAAU;;gDACZ,WAAW,KAAK,KAAK,6BACpB,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;2DACpB,WAAW,KAAK,KAAK,6BACvB,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;yEAEtB,8OAAC;oDAAI,WAAU;;;;;;8DAEjB,8OAAC;oDAAK,WAAU;8DAAsB,WAAW,KAAK;;;;;;;;;;;;;mCA3CnD,WAAW,IAAI;;;;;;;;;;;;;;;;;0BAmD5B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyB;;;;;;kCACvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAGR,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAGR,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB", "debugId": null}}, {"offset": {"line": 7515, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/components/navigation/GlobalNavigation.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { \n  Home,\n  Database,\n  Users,\n  FileText,\n  Award,\n  Settings,\n  Menu,\n  X,\n  ChevronDown,\n  Bell,\n  Search,\n  User,\n  Crown,\n  Zap\n} from 'lucide-react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\n\ninterface GlobalNavigationProps {\n  className?: string\n}\n\nexport default function GlobalNavigation({ className = '' }: GlobalNavigationProps) {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)\n  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false)\n  const [notifications, setNotifications] = useState(3)\n  const pathname = usePathname()\n\n  const navigationItems = [\n    {\n      name: 'Accueil',\n      href: '/',\n      icon: <Home className=\"h-5 w-5\" />,\n      description: 'Tableau de bord principal'\n    },\n    {\n      name: 'Hub Information',\n      href: '/hub',\n      icon: <Database className=\"h-5 w-5\" />,\n      description: 'Centre d\\'information temps réel',\n      badge: 'LIVE'\n    },\n    {\n      name: 'Expert Conseil',\n      href: '/expert',\n      icon: <Users className=\"h-5 w-5\" />,\n      description: 'Consultation et validation technique',\n      badge: 'PRO'\n    },\n    {\n      name: 'Module Prescripteur',\n      href: '/prescriptor',\n      icon: <FileText className=\"h-5 w-5\" />,\n      description: 'Templates et certification',\n      badge: 'PREMIUM'\n    },\n    {\n      name: 'Club Membre',\n      href: '/club',\n      icon: <Crown className=\"h-5 w-5\" />,\n      description: 'Réseau exclusif professionnel',\n      badge: 'VIP'\n    }\n  ]\n\n  const userProfile = {\n    name: 'Jean Kouassi',\n    role: 'Ingénieur Électricien',\n    company: 'SODECI',\n    level: 'Gold',\n    avatar: '/api/placeholder/40/40'\n  }\n\n  const quickActions = [\n    { name: 'Nouveau Projet', href: '/prescriptor?action=new', icon: <FileText className=\"h-4 w-4\" /> },\n    { name: 'Consultation Express', href: '/expert?mode=express', icon: <Zap className=\"h-4 w-4\" /> },\n    { name: 'Alertes Hub', href: '/hub?tab=alerts', icon: <Bell className=\"h-4 w-4\" /> }\n  ]\n\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (isProfileMenuOpen) {\n        setIsProfileMenuOpen(false)\n      }\n    }\n\n    document.addEventListener('click', handleClickOutside)\n    return () => document.removeEventListener('click', handleClickOutside)\n  }, [isProfileMenuOpen])\n\n  const isActive = (href: string) => {\n    if (href === '/') return pathname === '/'\n    return pathname.startsWith(href)\n  }\n\n  return (\n    <nav className={`bg-white/95 backdrop-blur-md border-b border-gray-200 sticky top-0 z-50 ${className}`}>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo et branding */}\n          <div className=\"flex items-center space-x-4\">\n            <Link href=\"/\" className=\"flex items-center space-x-3\">\n              <div className=\"w-10 h-10 bg-gradient-to-r from-amber-400 to-amber-500 rounded-lg flex items-center justify-center\">\n                <Zap className=\"h-6 w-6 text-slate-900\" />\n              </div>\n              <div className=\"hidden sm:block\">\n                <h1 className=\"text-xl font-bold text-slate-900\">Pro Matos</h1>\n                <p className=\"text-xs text-slate-600\">Afrique Ouest</p>\n              </div>\n            </Link>\n          </div>\n\n          {/* Navigation principale - Desktop */}\n          <div className=\"hidden lg:flex items-center space-x-1\">\n            {navigationItems.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className={`relative px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 group ${\n                  isActive(item.href)\n                    ? 'bg-amber-100 text-amber-900'\n                    : 'text-slate-700 hover:bg-slate-100 hover:text-slate-900'\n                }`}\n              >\n                <div className=\"flex items-center space-x-2\">\n                  {item.icon}\n                  <span>{item.name}</span>\n                  {item.badge && (\n                    <span className={`px-2 py-0.5 text-xs font-bold rounded-full ${\n                      item.badge === 'LIVE' ? 'bg-red-100 text-red-700' :\n                      item.badge === 'PRO' ? 'bg-blue-100 text-blue-700' :\n                      item.badge === 'PREMIUM' ? 'bg-purple-100 text-purple-700' :\n                      'bg-amber-100 text-amber-700'\n                    }`}>\n                      {item.badge}\n                    </span>\n                  )}\n                </div>\n                \n                {/* Tooltip */}\n                <div className=\"absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-3 py-2 bg-slate-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap\">\n                  {item.description}\n                  <div className=\"absolute bottom-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-b-slate-900\"></div>\n                </div>\n              </Link>\n            ))}\n          </div>\n\n          {/* Actions rapides et profil */}\n          <div className=\"flex items-center space-x-4\">\n            {/* Barre de recherche rapide */}\n            <div className=\"hidden md:block relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400\" />\n              <input\n                type=\"text\"\n                placeholder=\"Recherche rapide...\"\n                className=\"pl-10 pr-4 py-2 w-64 border border-slate-300 rounded-lg focus:ring-2 focus:ring-amber-400 focus:border-transparent text-sm\"\n              />\n            </div>\n\n            {/* Actions rapides */}\n            <div className=\"hidden lg:flex items-center space-x-2\">\n              {quickActions.map((action) => (\n                <Link\n                  key={action.name}\n                  href={action.href}\n                  className=\"p-2 text-slate-600 hover:text-amber-600 hover:bg-amber-50 rounded-lg transition-colors\"\n                  title={action.name}\n                >\n                  {action.icon}\n                </Link>\n              ))}\n            </div>\n\n            {/* Notifications */}\n            <button className=\"relative p-2 text-slate-600 hover:text-amber-600 hover:bg-amber-50 rounded-lg transition-colors\">\n              <Bell className=\"h-5 w-5\" />\n              {notifications > 0 && (\n                <span className=\"absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\">\n                  {notifications}\n                </span>\n              )}\n            </button>\n\n            {/* Profil utilisateur */}\n            <div className=\"relative\">\n              <button\n                onClick={(e) => {\n                  e.stopPropagation()\n                  setIsProfileMenuOpen(!isProfileMenuOpen)\n                }}\n                className=\"flex items-center space-x-3 p-2 rounded-lg hover:bg-slate-100 transition-colors\"\n              >\n                <div className=\"w-8 h-8 bg-gradient-to-r from-amber-400 to-amber-500 rounded-full flex items-center justify-center\">\n                  <User className=\"h-4 w-4 text-slate-900\" />\n                </div>\n                <div className=\"hidden sm:block text-left\">\n                  <div className=\"text-sm font-medium text-slate-900\">{userProfile.name}</div>\n                  <div className=\"text-xs text-slate-600\">{userProfile.level}</div>\n                </div>\n                <ChevronDown className=\"h-4 w-4 text-slate-600\" />\n              </button>\n\n              {/* Menu profil */}\n              <AnimatePresence>\n                {isProfileMenuOpen && (\n                  <motion.div\n                    initial={{ opacity: 0, y: -10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    exit={{ opacity: 0, y: -10 }}\n                    className=\"absolute right-0 top-full mt-2 w-64 bg-white rounded-lg shadow-lg border border-slate-200 py-2\"\n                  >\n                    <div className=\"px-4 py-3 border-b border-slate-200\">\n                      <div className=\"font-medium text-slate-900\">{userProfile.name}</div>\n                      <div className=\"text-sm text-slate-600\">{userProfile.role}</div>\n                      <div className=\"text-sm text-slate-600\">{userProfile.company}</div>\n                      <div className=\"mt-2\">\n                        <span className=\"px-2 py-1 bg-amber-100 text-amber-800 text-xs font-medium rounded-full\">\n                          Niveau {userProfile.level}\n                        </span>\n                      </div>\n                    </div>\n                    \n                    <div className=\"py-2\">\n                      <Link href=\"/profile\" className=\"block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100\">\n                        Mon Profil\n                      </Link>\n                      <Link href=\"/settings\" className=\"block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100\">\n                        Paramètres\n                      </Link>\n                      <Link href=\"/billing\" className=\"block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100\">\n                        Facturation\n                      </Link>\n                      <div className=\"border-t border-slate-200 mt-2 pt-2\">\n                        <button className=\"block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50\">\n                          Déconnexion\n                        </button>\n                      </div>\n                    </div>\n                  </motion.div>\n                )}\n              </AnimatePresence>\n            </div>\n\n            {/* Menu mobile */}\n            <button\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n              className=\"lg:hidden p-2 text-slate-600 hover:text-slate-900\"\n            >\n              {isMobileMenuOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\n            </button>\n          </div>\n        </div>\n\n        {/* Menu mobile */}\n        <AnimatePresence>\n          {isMobileMenuOpen && (\n            <motion.div\n              initial={{ opacity: 0, height: 0 }}\n              animate={{ opacity: 1, height: 'auto' }}\n              exit={{ opacity: 0, height: 0 }}\n              className=\"lg:hidden border-t border-slate-200 py-4\"\n            >\n              <div className=\"space-y-2\">\n                {navigationItems.map((item) => (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    onClick={() => setIsMobileMenuOpen(false)}\n                    className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors ${\n                      isActive(item.href)\n                        ? 'bg-amber-100 text-amber-900'\n                        : 'text-slate-700 hover:bg-slate-100'\n                    }`}\n                  >\n                    {item.icon}\n                    <div className=\"flex-1\">\n                      <div className=\"font-medium\">{item.name}</div>\n                      <div className=\"text-sm text-slate-600\">{item.description}</div>\n                    </div>\n                    {item.badge && (\n                      <span className={`px-2 py-1 text-xs font-bold rounded-full ${\n                        item.badge === 'LIVE' ? 'bg-red-100 text-red-700' :\n                        item.badge === 'PRO' ? 'bg-blue-100 text-blue-700' :\n                        item.badge === 'PREMIUM' ? 'bg-purple-100 text-purple-700' :\n                        'bg-amber-100 text-amber-700'\n                      }`}>\n                        {item.badge}\n                      </span>\n                    )}\n                  </Link>\n                ))}\n              </div>\n\n              {/* Actions rapides mobile */}\n              <div className=\"mt-4 pt-4 border-t border-slate-200\">\n                <div className=\"px-4 mb-3\">\n                  <div className=\"text-sm font-medium text-slate-900\">Actions Rapides</div>\n                </div>\n                <div className=\"space-y-2\">\n                  {quickActions.map((action) => (\n                    <Link\n                      key={action.name}\n                      href={action.href}\n                      onClick={() => setIsMobileMenuOpen(false)}\n                      className=\"flex items-center space-x-3 px-4 py-2 text-slate-700 hover:bg-slate-100 rounded-lg\"\n                    >\n                      {action.icon}\n                      <span>{action.name}</span>\n                    </Link>\n                  ))}\n                </div>\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AACA;AArBA;;;;;;;AA2Be,SAAS,iBAAiB,EAAE,YAAY,EAAE,EAAyB;IAChF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,kBAAkB;QACtB;YACE,MAAM;YACN,MAAM;YACN,oBAAM,8OAAC,mMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,aAAa;YACb,OAAO;QACT;KACD;IAED,MAAM,cAAc;QAClB,MAAM;QACN,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IAEA,MAAM,eAAe;QACnB;YAAE,MAAM;YAAkB,MAAM;YAA2B,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;QAAa;QAClG;YAAE,MAAM;YAAwB,MAAM;YAAwB,oBAAM,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;QAAa;QAChG;YAAE,MAAM;YAAe,MAAM;YAAmB,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;QAAa;KACpF;IAED,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,mBAAmB;gBACrB,qBAAqB;YACvB;QACF;QAEA,SAAS,gBAAgB,CAAC,SAAS;QACnC,OAAO,IAAM,SAAS,mBAAmB,CAAC,SAAS;IACrD,GAAG;QAAC;KAAkB;IAEtB,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,KAAK,OAAO,aAAa;QACtC,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,wEAAwE,EAAE,WAAW;kBACpG,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;;;;;;kDAEjB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAyB;;;;;;;;;;;;;;;;;;;;;;;sCAM5C,8OAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC,oFAAoF,EAC9F,SAAS,KAAK,IAAI,IACd,gCACA,0DACJ;;sDAEF,8OAAC;4CAAI,WAAU;;gDACZ,KAAK,IAAI;8DACV,8OAAC;8DAAM,KAAK,IAAI;;;;;;gDACf,KAAK,KAAK,kBACT,8OAAC;oDAAK,WAAW,CAAC,2CAA2C,EAC3D,KAAK,KAAK,KAAK,SAAS,4BACxB,KAAK,KAAK,KAAK,QAAQ,8BACvB,KAAK,KAAK,KAAK,YAAY,kCAC3B,+BACA;8DACC,KAAK,KAAK;;;;;;;;;;;;sDAMjB,8OAAC;4CAAI,WAAU;;gDACZ,KAAK,WAAW;8DACjB,8OAAC;oDAAI,WAAU;;;;;;;;;;;;;mCA1BZ,KAAK,IAAI;;;;;;;;;;sCAiCpB,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;;;;;;;8CAKd,8OAAC;oCAAI,WAAU;8CACZ,aAAa,GAAG,CAAC,CAAC,uBACjB,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,OAAO,IAAI;4CACjB,WAAU;4CACV,OAAO,OAAO,IAAI;sDAEjB,OAAO,IAAI;2CALP,OAAO,IAAI;;;;;;;;;;8CAWtB,8OAAC;oCAAO,WAAU;;sDAChB,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCACf,gBAAgB,mBACf,8OAAC;4CAAK,WAAU;sDACb;;;;;;;;;;;;8CAMP,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,CAAC;gDACR,EAAE,eAAe;gDACjB,qBAAqB,CAAC;4CACxB;4CACA,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAsC,YAAY,IAAI;;;;;;sEACrE,8OAAC;4DAAI,WAAU;sEAA0B,YAAY,KAAK;;;;;;;;;;;;8DAE5D,8OAAC,oNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;;sDAIzB,8OAAC,yLAAA,CAAA,kBAAe;sDACb,mCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC9B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,MAAM;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC3B,WAAU;;kEAEV,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAA8B,YAAY,IAAI;;;;;;0EAC7D,8OAAC;gEAAI,WAAU;0EAA0B,YAAY,IAAI;;;;;;0EACzD,8OAAC;gEAAI,WAAU;0EAA0B,YAAY,OAAO;;;;;;0EAC5D,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAK,WAAU;;wEAAyE;wEAC/E,YAAY,KAAK;;;;;;;;;;;;;;;;;;kEAK/B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAK;gEAAW,WAAU;0EAA4D;;;;;;0EAG5F,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAK;gEAAY,WAAU;0EAA4D;;;;;;0EAG7F,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAK;gEAAW,WAAU;0EAA4D;;;;;;0EAG5F,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAO,WAAU;8EAAwE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAWtG,8OAAC;oCACC,SAAS,IAAM,oBAAoB,CAAC;oCACpC,WAAU;8CAET,iCAAmB,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;6DAAe,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAMtE,8OAAC,yLAAA,CAAA,kBAAe;8BACb,kCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBACjC,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAO;wBACtC,MAAM;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBAC9B,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;0CACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,SAAS,IAAM,oBAAoB;wCACnC,WAAW,CAAC,mEAAmE,EAC7E,SAAS,KAAK,IAAI,IACd,gCACA,qCACJ;;4CAED,KAAK,IAAI;0DACV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAe,KAAK,IAAI;;;;;;kEACvC,8OAAC;wDAAI,WAAU;kEAA0B,KAAK,WAAW;;;;;;;;;;;;4CAE1D,KAAK,KAAK,kBACT,8OAAC;gDAAK,WAAW,CAAC,yCAAyC,EACzD,KAAK,KAAK,KAAK,SAAS,4BACxB,KAAK,KAAK,KAAK,QAAQ,8BACvB,KAAK,KAAK,KAAK,YAAY,kCAC3B,+BACA;0DACC,KAAK,KAAK;;;;;;;uCArBV,KAAK,IAAI;;;;;;;;;;0CA6BpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDAAqC;;;;;;;;;;;kDAEtD,8OAAC;wCAAI,WAAU;kDACZ,aAAa,GAAG,CAAC,CAAC,uBACjB,8OAAC,4JAAA,CAAA,UAAI;gDAEH,MAAM,OAAO,IAAI;gDACjB,SAAS,IAAM,oBAAoB;gDACnC,WAAU;;oDAET,OAAO,IAAI;kEACZ,8OAAC;kEAAM,OAAO,IAAI;;;;;;;+CANb,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBtC", "debugId": null}}, {"offset": {"line": 8240, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/components/layout/ResponsiveLayout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { motion } from 'framer-motion'\nimport { \n  ChevronLeft,\n  ChevronRight,\n  Grid,\n  List,\n  Maximize2,\n  Minimize2,\n  Smartphone,\n  Tablet,\n  Monitor\n} from 'lucide-react'\n\ninterface ResponsiveLayoutProps {\n  children: React.ReactNode\n  title?: string\n  subtitle?: string\n  sidebar?: React.ReactNode\n  actions?: React.ReactNode\n  className?: string\n}\n\nexport default function ResponsiveLayout({ \n  children, \n  title, \n  subtitle, \n  sidebar, \n  actions,\n  className = '' \n}: ResponsiveLayoutProps) {\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)\n  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')\n  const [isFullscreen, setIsFullscreen] = useState(false)\n  const [screenSize, setScreenSize] = useState<'mobile' | 'tablet' | 'desktop'>('desktop')\n\n  useEffect(() => {\n    const handleResize = () => {\n      const width = window.innerWidth\n      if (width < 768) {\n        setScreenSize('mobile')\n        setSidebarCollapsed(true)\n      } else if (width < 1024) {\n        setScreenSize('tablet')\n        setSidebarCollapsed(false)\n      } else {\n        setScreenSize('desktop')\n        setSidebarCollapsed(false)\n      }\n    }\n\n    handleResize()\n    window.addEventListener('resize', handleResize)\n    return () => window.removeEventListener('resize', handleResize)\n  }, [])\n\n  const toggleFullscreen = () => {\n    if (!document.fullscreenElement) {\n      document.documentElement.requestFullscreen()\n      setIsFullscreen(true)\n    } else {\n      document.exitFullscreen()\n      setIsFullscreen(false)\n    }\n  }\n\n  return (\n    <div className={`min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 ${className}`}>\n      {/* Header responsive */}\n      <div className=\"bg-white/80 backdrop-blur-md border-b border-slate-200 sticky top-16 z-40\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-16\">\n            {/* Titre et contrôles */}\n            <div className=\"flex items-center space-x-4\">\n              {sidebar && (\n                <button\n                  onClick={() => setSidebarCollapsed(!sidebarCollapsed)}\n                  className=\"p-2 text-slate-600 hover:text-slate-900 hover:bg-slate-100 rounded-lg transition-colors lg:hidden\"\n                >\n                  {sidebarCollapsed ? <ChevronRight className=\"h-5 w-5\" /> : <ChevronLeft className=\"h-5 w-5\" />}\n                </button>\n              )}\n              \n              <div>\n                {title && (\n                  <h1 className=\"text-xl font-bold text-slate-900 truncate max-w-xs sm:max-w-none\">\n                    {title}\n                  </h1>\n                )}\n                {subtitle && (\n                  <p className=\"text-sm text-slate-600 hidden sm:block\">\n                    {subtitle}\n                  </p>\n                )}\n              </div>\n            </div>\n\n            {/* Contrôles d'affichage */}\n            <div className=\"flex items-center space-x-2\">\n              {/* Indicateur de taille d'écran */}\n              <div className=\"hidden md:flex items-center space-x-1 px-3 py-1 bg-slate-100 rounded-lg\">\n                {screenSize === 'mobile' && <Smartphone className=\"h-4 w-4 text-slate-600\" />}\n                {screenSize === 'tablet' && <Tablet className=\"h-4 w-4 text-slate-600\" />}\n                {screenSize === 'desktop' && <Monitor className=\"h-4 w-4 text-slate-600\" />}\n                <span className=\"text-xs text-slate-600 capitalize\">{screenSize}</span>\n              </div>\n\n              {/* Mode d'affichage */}\n              <div className=\"hidden sm:flex items-center bg-slate-100 rounded-lg p-1\">\n                <button\n                  onClick={() => setViewMode('grid')}\n                  className={`p-2 rounded transition-colors ${\n                    viewMode === 'grid' \n                      ? 'bg-white text-slate-900 shadow-sm' \n                      : 'text-slate-600 hover:text-slate-900'\n                  }`}\n                  title=\"Vue grille\"\n                >\n                  <Grid className=\"h-4 w-4\" />\n                </button>\n                <button\n                  onClick={() => setViewMode('list')}\n                  className={`p-2 rounded transition-colors ${\n                    viewMode === 'list' \n                      ? 'bg-white text-slate-900 shadow-sm' \n                      : 'text-slate-600 hover:text-slate-900'\n                  }`}\n                  title=\"Vue liste\"\n                >\n                  <List className=\"h-4 w-4\" />\n                </button>\n              </div>\n\n              {/* Plein écran */}\n              <button\n                onClick={toggleFullscreen}\n                className=\"p-2 text-slate-600 hover:text-slate-900 hover:bg-slate-100 rounded-lg transition-colors hidden lg:block\"\n                title={isFullscreen ? 'Quitter le plein écran' : 'Plein écran'}\n              >\n                {isFullscreen ? <Minimize2 className=\"h-4 w-4\" /> : <Maximize2 className=\"h-4 w-4\" />}\n              </button>\n\n              {/* Actions personnalisées */}\n              {actions && (\n                <div className=\"flex items-center space-x-2\">\n                  {actions}\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Contenu principal */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n        <div className=\"flex gap-6\">\n          {/* Sidebar */}\n          {sidebar && (\n            <motion.aside\n              initial={false}\n              animate={{\n                width: sidebarCollapsed ? 0 : screenSize === 'mobile' ? '100%' : '320px',\n                opacity: sidebarCollapsed ? 0 : 1\n              }}\n              transition={{ duration: 0.3 }}\n              className={`${\n                screenSize === 'mobile' \n                  ? 'fixed inset-0 z-50 bg-white' \n                  : 'relative'\n              } overflow-hidden`}\n            >\n              {!sidebarCollapsed && (\n                <div className=\"h-full\">\n                  {screenSize === 'mobile' && (\n                    <div className=\"flex items-center justify-between p-4 border-b border-slate-200\">\n                      <h2 className=\"text-lg font-semibold text-slate-900\">Menu</h2>\n                      <button\n                        onClick={() => setSidebarCollapsed(true)}\n                        className=\"p-2 text-slate-600 hover:text-slate-900\"\n                      >\n                        <ChevronLeft className=\"h-5 w-5\" />\n                      </button>\n                    </div>\n                  )}\n                  <div className={screenSize === 'mobile' ? 'p-4' : ''}>\n                    {sidebar}\n                  </div>\n                </div>\n              )}\n            </motion.aside>\n          )}\n\n          {/* Contenu principal */}\n          <main className=\"flex-1 min-w-0\">\n            <div className={`${viewMode === 'grid' ? 'space-y-6' : 'space-y-4'}`}>\n              {children}\n            </div>\n          </main>\n        </div>\n      </div>\n\n      {/* Overlay mobile pour sidebar */}\n      {screenSize === 'mobile' && !sidebarCollapsed && (\n        <div\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-40\"\n          onClick={() => setSidebarCollapsed(true)}\n        />\n      )}\n\n      {/* Indicateurs de responsive design */}\n      <div className=\"fixed bottom-4 right-4 z-50\">\n        <div className=\"bg-slate-900 text-white px-3 py-2 rounded-lg text-xs font-mono\">\n          <div className=\"flex items-center space-x-2\">\n            {screenSize === 'mobile' && <Smartphone className=\"h-3 w-3\" />}\n            {screenSize === 'tablet' && <Tablet className=\"h-3 w-3\" />}\n            {screenSize === 'desktop' && <Monitor className=\"h-3 w-3\" />}\n            <span>{window.innerWidth}px</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Breakpoints indicator (dev only) */}\n      <div className=\"fixed bottom-4 left-4 z-50 bg-slate-900 text-white px-3 py-2 rounded-lg text-xs font-mono\">\n        <div className=\"sm:hidden\">XS (&lt;640px)</div>\n        <div className=\"hidden sm:block md:hidden\">SM (640px+)</div>\n        <div className=\"hidden md:block lg:hidden\">MD (768px+)</div>\n        <div className=\"hidden lg:block xl:hidden\">LG (1024px+)</div>\n        <div className=\"hidden xl:block 2xl:hidden\">XL (1280px+)</div>\n        <div className=\"hidden 2xl:block\">2XL (1536px+)</div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAyBe,SAAS,iBAAiB,EACvC,QAAQ,EACR,KAAK,EACL,QAAQ,EACR,OAAO,EACP,OAAO,EACP,YAAY,EAAE,EACQ;IACtB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmC;IAE9E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,MAAM,QAAQ,OAAO,UAAU;YAC/B,IAAI,QAAQ,KAAK;gBACf,cAAc;gBACd,oBAAoB;YACtB,OAAO,IAAI,QAAQ,MAAM;gBACvB,cAAc;gBACd,oBAAoB;YACtB,OAAO;gBACL,cAAc;gBACd,oBAAoB;YACtB;QACF;QAEA;QACA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,mBAAmB;QACvB,IAAI,CAAC,SAAS,iBAAiB,EAAE;YAC/B,SAAS,eAAe,CAAC,iBAAiB;YAC1C,gBAAgB;QAClB,OAAO;YACL,SAAS,cAAc;YACvB,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,0DAA0D,EAAE,WAAW;;0BAEtF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;oCACZ,yBACC,8OAAC;wCACC,SAAS,IAAM,oBAAoB,CAAC;wCACpC,WAAU;kDAET,iCAAmB,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;iEAAe,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;kDAItF,8OAAC;;4CACE,uBACC,8OAAC;gDAAG,WAAU;0DACX;;;;;;4CAGJ,0BACC,8OAAC;gDAAE,WAAU;0DACV;;;;;;;;;;;;;;;;;;0CAOT,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;4CACZ,eAAe,0BAAY,8OAAC,8MAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CACjD,eAAe,0BAAY,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAC7C,eAAe,2BAAa,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DAChD,8OAAC;gDAAK,WAAU;0DAAqC;;;;;;;;;;;;kDAIvD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS,IAAM,YAAY;gDAC3B,WAAW,CAAC,8BAA8B,EACxC,aAAa,SACT,sCACA,uCACJ;gDACF,OAAM;0DAEN,cAAA,8OAAC,yMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,8OAAC;gDACC,SAAS,IAAM,YAAY;gDAC3B,WAAW,CAAC,8BAA8B,EACxC,aAAa,SACT,sCACA,uCACJ;gDACF,OAAM;0DAEN,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAKpB,8OAAC;wCACC,SAAS;wCACT,WAAU;wCACV,OAAO,eAAe,2BAA2B;kDAEhD,6BAAe,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;iEAAe,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;oCAI1E,yBACC,8OAAC;wCAAI,WAAU;kDACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;wBAEZ,yBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,KAAK;4BACX,SAAS;4BACT,SAAS;gCACP,OAAO,mBAAmB,IAAI,eAAe,WAAW,SAAS;gCACjE,SAAS,mBAAmB,IAAI;4BAClC;4BACA,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAW,GACT,eAAe,WACX,gCACA,WACL,gBAAgB,CAAC;sCAEjB,CAAC,kCACA,8OAAC;gCAAI,WAAU;;oCACZ,eAAe,0BACd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAuC;;;;;;0DACrD,8OAAC;gDACC,SAAS,IAAM,oBAAoB;gDACnC,WAAU;0DAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAI7B,8OAAC;wCAAI,WAAW,eAAe,WAAW,QAAQ;kDAC/C;;;;;;;;;;;;;;;;;sCAQX,8OAAC;4BAAK,WAAU;sCACd,cAAA,8OAAC;gCAAI,WAAW,GAAG,aAAa,SAAS,cAAc,aAAa;0CACjE;;;;;;;;;;;;;;;;;;;;;;YAOR,eAAe,YAAY,CAAC,kCAC3B,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,oBAAoB;;;;;;0BAKvC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,eAAe,0BAAY,8OAAC,8MAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;4BACjD,eAAe,0BAAY,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAC7C,eAAe,2BAAa,8OAAC,wMAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CAChD,8OAAC;;oCAAM,OAAO,UAAU;oCAAC;;;;;;;;;;;;;;;;;;;;;;;0BAM/B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAY;;;;;;kCAC3B,8OAAC;wBAAI,WAAU;kCAA4B;;;;;;kCAC3C,8OAAC;wBAAI,WAAU;kCAA4B;;;;;;kCAC3C,8OAAC;wBAAI,WAAU;kCAA4B;;;;;;kCAC3C,8OAAC;wBAAI,WAAU;kCAA6B;;;;;;kCAC5C,8OAAC;wBAAI,WAAU;kCAAmB;;;;;;;;;;;;;;;;;;AAI1C", "debugId": null}}, {"offset": {"line": 8717, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/components/integration/EcosystemHub.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { \n  ArrowRight,\n  Database,\n  Users,\n  FileText,\n  Crown,\n  Zap,\n  TrendingUp,\n  AlertTriangle,\n  CheckCircle,\n  Clock,\n  Star,\n  Link as LinkIcon,\n  ExternalLink,\n  RefreshCw\n} from 'lucide-react'\nimport Link from 'next/link'\n\ninterface EcosystemHubProps {\n  currentModule: 'hub' | 'expert' | 'prescriptor' | 'club'\n  className?: string\n}\n\nexport default function EcosystemHub({ currentModule, className = '' }: EcosystemHubProps) {\n  const [crossModuleData, setCrossModuleData] = useState<any>(null)\n  const [isLoading, setIsLoading] = useState(true)\n\n  const modules = {\n    hub: {\n      name: 'Hub Information',\n      icon: <Database className=\"h-6 w-6\" />,\n      color: 'from-blue-500 to-blue-600',\n      description: 'Centre d\\'information temps réel',\n      status: 'active',\n      lastUpdate: '2 min',\n      metrics: { alerts: 12, updates: 47, trends: 8 }\n    },\n    expert: {\n      name: 'Expert Conseil',\n      icon: <Users className=\"h-6 w-6\" />,\n      color: 'from-green-500 to-green-600',\n      description: 'Consultation et validation technique',\n      status: 'active',\n      lastUpdate: '5 min',\n      metrics: { consultations: 23, validations: 15, experts: 8 }\n    },\n    prescriptor: {\n      name: 'Module Prescripteur',\n      icon: <FileText className=\"h-6 w-6\" />,\n      color: 'from-purple-500 to-purple-600',\n      description: 'Templates et certification',\n      status: 'active',\n      lastUpdate: '1 min',\n      metrics: { templates: 47, projects: 23, certificates: 89 }\n    },\n    club: {\n      name: 'Club Membre',\n      icon: <Crown className=\"h-6 w-6\" />,\n      color: 'from-amber-500 to-amber-600',\n      description: 'Réseau exclusif professionnel',\n      status: 'premium',\n      lastUpdate: '3 min',\n      metrics: { members: 1234, events: 12, benefits: 25 }\n    }\n  }\n\n  const integrationFlows = [\n    {\n      from: 'hub',\n      to: 'expert',\n      title: 'Alerte → Consultation',\n      description: 'Alertes techniques redirigées vers experts',\n      count: 15,\n      trend: '+23%',\n      examples: [\n        'Panne transformateur → Expert électrique',\n        'Nouveau produit → Validation technique',\n        'Norme mise à jour → Conseil réglementaire'\n      ]\n    },\n    {\n      from: 'expert',\n      to: 'prescriptor',\n      title: 'Validation → Template',\n      description: 'Conseils d\\'experts intégrés aux templates',\n      count: 28,\n      trend: '+45%',\n      examples: [\n        'Conseil technique → Template personnalisé',\n        'Validation produit → Calcul automatisé',\n        'Recommandation → Standard imposé'\n      ]\n    },\n    {\n      from: 'prescriptor',\n      to: 'club',\n      title: 'Certification → Réseau',\n      description: 'Projets certifiés partagés au club',\n      count: 34,\n      trend: '+67%',\n      examples: [\n        'Projet certifié → Showcase membre',\n        'Template validé → Formation exclusive',\n        'Innovation → Partage réseau'\n      ]\n    },\n    {\n      from: 'club',\n      to: 'hub',\n      title: 'Réseau → Information',\n      description: 'Insights membres alimentent le hub',\n      count: 42,\n      trend: '+89%',\n      examples: [\n        'Retour terrain → Alerte précoce',\n        'Tendance marché → Information exclusive',\n        'Innovation membre → Veille technologique'\n      ]\n    }\n  ]\n\n  const crossModuleActions = {\n    hub: [\n      {\n        title: 'Consulter un Expert',\n        description: 'Obtenir une validation technique sur cette alerte',\n        action: '/expert?source=hub&alert=',\n        icon: <Users className=\"h-4 w-4\" />,\n        color: 'bg-green-100 text-green-700'\n      },\n      {\n        title: 'Créer Template',\n        description: 'Transformer cette info en template de prescription',\n        action: '/prescriptor?action=create&source=hub',\n        icon: <FileText className=\"h-4 w-4\" />,\n        color: 'bg-purple-100 text-purple-700'\n      }\n    ],\n    expert: [\n      {\n        title: 'Voir Alertes Hub',\n        description: 'Consulter les dernières alertes techniques',\n        action: '/hub?filter=technical',\n        icon: <Database className=\"h-4 w-4\" />,\n        color: 'bg-blue-100 text-blue-700'\n      },\n      {\n        title: 'Générer Template',\n        description: 'Créer un template basé sur cette consultation',\n        action: '/prescriptor?action=generate&consultation=',\n        icon: <FileText className=\"h-4 w-4\" />,\n        color: 'bg-purple-100 text-purple-700'\n      }\n    ],\n    prescriptor: [\n      {\n        title: 'Consulter Expert',\n        description: 'Valider ce template avec un expert',\n        action: '/expert?mode=validation&template=',\n        icon: <Users className=\"h-4 w-4\" />,\n        color: 'bg-green-100 text-green-700'\n      },\n      {\n        title: 'Partager au Club',\n        description: 'Présenter ce projet au réseau membre',\n        action: '/club?action=share&project=',\n        icon: <Crown className=\"h-4 w-4\" />,\n        color: 'bg-amber-100 text-amber-700'\n      }\n    ],\n    club: [\n      {\n        title: 'Alertes Exclusives',\n        description: 'Accéder aux informations réservées aux membres',\n        action: '/hub?level=premium',\n        icon: <Database className=\"h-4 w-4\" />,\n        color: 'bg-blue-100 text-blue-700'\n      },\n      {\n        title: 'Expert Dédié',\n        description: 'Consultation prioritaire avec expert membre',\n        action: '/expert?priority=member',\n        icon: <Users className=\"h-4 w-4\" />,\n        color: 'bg-green-100 text-green-700'\n      }\n    ]\n  }\n\n  const recentIntegrations = [\n    {\n      type: 'hub_to_expert',\n      title: 'Alerte transformateur → Consultation urgente',\n      time: '5 min',\n      status: 'completed',\n      impact: 'Panne évitée chez 3 clients'\n    },\n    {\n      type: 'expert_to_prescriptor',\n      title: 'Validation LED → Template automatisé',\n      time: '12 min',\n      status: 'in_progress',\n      impact: 'Nouveau standard créé'\n    },\n    {\n      type: 'prescriptor_to_club',\n      title: 'Projet solaire → Showcase membre',\n      time: '1h',\n      status: 'completed',\n      impact: '15 nouveaux projets générés'\n    },\n    {\n      type: 'club_to_hub',\n      title: 'Retour terrain → Alerte préventive',\n      time: '2h',\n      status: 'completed',\n      impact: 'Tendance détectée en avance'\n    }\n  ]\n\n  useEffect(() => {\n    // Simulation de chargement des données cross-module\n    setIsLoading(true)\n    setTimeout(() => {\n      setCrossModuleData({\n        totalIntegrations: 147,\n        activeFlows: 23,\n        efficiency: 94,\n        userSatisfaction: 4.8\n      })\n      setIsLoading(false)\n    }, 1500)\n  }, [])\n\n  const getCurrentModuleActions = () => {\n    return crossModuleActions[currentModule] || []\n  }\n\n  const getIntegrationStatus = (from: string, to: string) => {\n    if (from === currentModule || to === currentModule) {\n      return 'active'\n    }\n    return 'available'\n  }\n\n  return (\n    <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>\n      {/* Header */}\n      <div className=\"flex items-center justify-between mb-6\">\n        <div className=\"flex items-center space-x-4\">\n          <div className=\"w-12 h-12 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center text-white\">\n            <LinkIcon className=\"h-6 w-6\" />\n          </div>\n          <div>\n            <h2 className=\"text-2xl font-bold text-gray-900\">Intégration Écosystème</h2>\n            <p className=\"text-gray-600\">Flux de données et actions cross-module</p>\n          </div>\n        </div>\n        \n        <div className=\"flex items-center space-x-4\">\n          {isLoading ? (\n            <div className=\"flex items-center space-x-2 text-gray-500\">\n              <RefreshCw className=\"h-4 w-4 animate-spin\" />\n              <span className=\"text-sm\">Synchronisation...</span>\n            </div>\n          ) : (\n            <div className=\"text-right\">\n              <div className=\"text-lg font-bold text-indigo-600\">{crossModuleData?.efficiency}%</div>\n              <div className=\"text-sm text-gray-600\">Efficacité</div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Vue d'ensemble des modules */}\n      <div className=\"grid md:grid-cols-4 gap-4 mb-8\">\n        {Object.entries(modules).map(([key, module]) => (\n          <motion.div\n            key={key}\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            className={`p-4 rounded-lg border-2 transition-all ${\n              currentModule === key\n                ? 'border-indigo-400 bg-indigo-50'\n                : 'border-gray-200 hover:border-gray-300'\n            }`}\n          >\n            <div className=\"flex items-center space-x-3 mb-3\">\n              <div className={`w-10 h-10 bg-gradient-to-r ${module.color} rounded-lg flex items-center justify-center text-white`}>\n                {module.icon}\n              </div>\n              <div>\n                <h3 className=\"font-semibold text-gray-900\">{module.name}</h3>\n                <p className=\"text-xs text-gray-600\">{module.description}</p>\n              </div>\n            </div>\n            \n            <div className=\"flex items-center justify-between text-sm\">\n              <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                module.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-amber-100 text-amber-800'\n              }`}>\n                {module.status}\n              </span>\n              <span className=\"text-gray-500\">Màj: {module.lastUpdate}</span>\n            </div>\n          </motion.div>\n        ))}\n      </div>\n\n      {/* Flux d'intégration */}\n      <div className=\"mb-8\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Flux d'Intégration Actifs</h3>\n        <div className=\"grid md:grid-cols-2 gap-6\">\n          {integrationFlows.map((flow, index) => (\n            <motion.div\n              key={`${flow.from}-${flow.to}`}\n              initial={{ opacity: 0, x: -20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ delay: index * 0.1 }}\n              className={`p-4 rounded-lg border ${\n                getIntegrationStatus(flow.from, flow.to) === 'active'\n                  ? 'border-indigo-200 bg-indigo-50'\n                  : 'border-gray-200 bg-gray-50'\n              }`}\n            >\n              <div className=\"flex items-center justify-between mb-3\">\n                <div className=\"flex items-center space-x-2\">\n                  <div className={`w-6 h-6 bg-gradient-to-r ${modules[flow.from as keyof typeof modules].color} rounded flex items-center justify-center`}>\n                    <div className=\"w-2 h-2 bg-white rounded-full\"></div>\n                  </div>\n                  <ArrowRight className=\"h-4 w-4 text-gray-400\" />\n                  <div className={`w-6 h-6 bg-gradient-to-r ${modules[flow.to as keyof typeof modules].color} rounded flex items-center justify-center`}>\n                    <div className=\"w-2 h-2 bg-white rounded-full\"></div>\n                  </div>\n                </div>\n                <div className=\"text-right\">\n                  <div className=\"text-lg font-bold text-gray-900\">{flow.count}</div>\n                  <div className=\"text-xs text-green-600 font-medium\">{flow.trend}</div>\n                </div>\n              </div>\n              \n              <h4 className=\"font-semibold text-gray-900 mb-1\">{flow.title}</h4>\n              <p className=\"text-sm text-gray-600 mb-3\">{flow.description}</p>\n              \n              <div className=\"space-y-1\">\n                {flow.examples.slice(0, 2).map((example, i) => (\n                  <div key={i} className=\"text-xs text-gray-500 flex items-center space-x-1\">\n                    <div className=\"w-1 h-1 bg-gray-400 rounded-full\"></div>\n                    <span>{example}</span>\n                  </div>\n                ))}\n              </div>\n            </motion.div>\n          ))}\n        </div>\n      </div>\n\n      {/* Actions cross-module pour le module actuel */}\n      <div className=\"mb-8\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n          Actions Disponibles depuis {modules[currentModule].name}\n        </h3>\n        <div className=\"grid md:grid-cols-2 gap-4\">\n          {getCurrentModuleActions().map((action, index) => (\n            <Link\n              key={index}\n              href={action.action}\n              className=\"p-4 rounded-lg border border-gray-200 hover:border-gray-300 transition-all group\"\n            >\n              <div className=\"flex items-start space-x-3\">\n                <div className={`p-2 rounded-lg ${action.color}`}>\n                  {action.icon}\n                </div>\n                <div className=\"flex-1\">\n                  <h4 className=\"font-semibold text-gray-900 group-hover:text-indigo-600 transition-colors\">\n                    {action.title}\n                  </h4>\n                  <p className=\"text-sm text-gray-600\">{action.description}</p>\n                </div>\n                <ExternalLink className=\"h-4 w-4 text-gray-400 group-hover:text-indigo-600 transition-colors\" />\n              </div>\n            </Link>\n          ))}\n        </div>\n      </div>\n\n      {/* Intégrations récentes */}\n      <div>\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Intégrations Récentes</h3>\n        <div className=\"space-y-3\">\n          {recentIntegrations.map((integration, index) => (\n            <motion.div\n              key={index}\n              initial={{ opacity: 0, y: 10 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: index * 0.05 }}\n              className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\"\n            >\n              <div className=\"flex items-center space-x-3\">\n                <div className={`w-2 h-2 rounded-full ${\n                  integration.status === 'completed' ? 'bg-green-500' :\n                  integration.status === 'in_progress' ? 'bg-blue-500' :\n                  'bg-gray-400'\n                }`}></div>\n                <div>\n                  <h4 className=\"font-medium text-gray-900\">{integration.title}</h4>\n                  <p className=\"text-sm text-gray-600\">{integration.impact}</p>\n                </div>\n              </div>\n              <div className=\"text-right\">\n                <div className=\"text-sm text-gray-500\">{integration.time}</div>\n                <div className={`text-xs font-medium ${\n                  integration.status === 'completed' ? 'text-green-600' :\n                  integration.status === 'in_progress' ? 'text-blue-600' :\n                  'text-gray-600'\n                }`}>\n                  {integration.status}\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AApBA;;;;;;AA2Be,SAAS,aAAa,EAAE,aAAa,EAAE,YAAY,EAAE,EAAqB;IACvF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAC5D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,UAAU;QACd,KAAK;YACH,MAAM;YACN,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,OAAO;YACP,aAAa;YACb,QAAQ;YACR,YAAY;YACZ,SAAS;gBAAE,QAAQ;gBAAI,SAAS;gBAAI,QAAQ;YAAE;QAChD;QACA,QAAQ;YACN,MAAM;YACN,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,aAAa;YACb,QAAQ;YACR,YAAY;YACZ,SAAS;gBAAE,eAAe;gBAAI,aAAa;gBAAI,SAAS;YAAE;QAC5D;QACA,aAAa;YACX,MAAM;YACN,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,OAAO;YACP,aAAa;YACb,QAAQ;YACR,YAAY;YACZ,SAAS;gBAAE,WAAW;gBAAI,UAAU;gBAAI,cAAc;YAAG;QAC3D;QACA,MAAM;YACJ,MAAM;YACN,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,aAAa;YACb,QAAQ;YACR,YAAY;YACZ,SAAS;gBAAE,SAAS;gBAAM,QAAQ;gBAAI,UAAU;YAAG;QACrD;IACF;IAEA,MAAM,mBAAmB;QACvB;YACE,MAAM;YACN,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;aACD;QACH;QACA;YACE,MAAM;YACN,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;aACD;QACH;QACA;YACE,MAAM;YACN,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;aACD;QACH;QACA;YACE,MAAM;YACN,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;aACD;QACH;KACD;IAED,MAAM,qBAAqB;QACzB,KAAK;YACH;gBACE,OAAO;gBACP,aAAa;gBACb,QAAQ;gBACR,oBAAM,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;gBACvB,OAAO;YACT;YACA;gBACE,OAAO;gBACP,aAAa;gBACb,QAAQ;gBACR,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;gBAC1B,OAAO;YACT;SACD;QACD,QAAQ;YACN;gBACE,OAAO;gBACP,aAAa;gBACb,QAAQ;gBACR,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;gBAC1B,OAAO;YACT;YACA;gBACE,OAAO;gBACP,aAAa;gBACb,QAAQ;gBACR,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;gBAC1B,OAAO;YACT;SACD;QACD,aAAa;YACX;gBACE,OAAO;gBACP,aAAa;gBACb,QAAQ;gBACR,oBAAM,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;gBACvB,OAAO;YACT;YACA;gBACE,OAAO;gBACP,aAAa;gBACb,QAAQ;gBACR,oBAAM,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;gBACvB,OAAO;YACT;SACD;QACD,MAAM;YACJ;gBACE,OAAO;gBACP,aAAa;gBACb,QAAQ;gBACR,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;gBAC1B,OAAO;YACT;YACA;gBACE,OAAO;gBACP,aAAa;gBACb,QAAQ;gBACR,oBAAM,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;gBACvB,OAAO;YACT;SACD;IACH;IAEA,MAAM,qBAAqB;QACzB;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;KACD;IAED,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oDAAoD;QACpD,aAAa;QACb,WAAW;YACT,mBAAmB;gBACjB,mBAAmB;gBACnB,aAAa;gBACb,YAAY;gBACZ,kBAAkB;YACpB;YACA,aAAa;QACf,GAAG;IACL,GAAG,EAAE;IAEL,MAAM,0BAA0B;QAC9B,OAAO,kBAAkB,CAAC,cAAc,IAAI,EAAE;IAChD;IAEA,MAAM,uBAAuB,CAAC,MAAc;QAC1C,IAAI,SAAS,iBAAiB,OAAO,eAAe;YAClD,OAAO;QACT;QACA,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,kCAAkC,EAAE,WAAW;;0BAE9D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kMAAA,CAAA,OAAQ;oCAAC,WAAU;;;;;;;;;;;0CAEtB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;kCAIjC,8OAAC;wBAAI,WAAU;kCACZ,0BACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,8OAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;iDAG5B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;wCAAqC,iBAAiB;wCAAW;;;;;;;8CAChF,8OAAC;oCAAI,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;0BAO/C,8OAAC;gBAAI,WAAU;0BACZ,OAAO,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,KAAK,OAAO,iBACzC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,WAAW,CAAC,uCAAuC,EACjD,kBAAkB,MACd,mCACA,yCACJ;;0CAEF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAW,CAAC,2BAA2B,EAAE,OAAO,KAAK,CAAC,uDAAuD,CAAC;kDAChH,OAAO,IAAI;;;;;;kDAEd,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA+B,OAAO,IAAI;;;;;;0DACxD,8OAAC;gDAAE,WAAU;0DAAyB,OAAO,WAAW;;;;;;;;;;;;;;;;;;0CAI5D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAW,CAAC,2CAA2C,EAC3D,OAAO,MAAM,KAAK,WAAW,gCAAgC,+BAC7D;kDACC,OAAO,MAAM;;;;;;kDAEhB,8OAAC;wCAAK,WAAU;;4CAAgB;4CAAM,OAAO,UAAU;;;;;;;;;;;;;;uBAzBpD;;;;;;;;;;0BAgCX,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,8OAAC;wBAAI,WAAU;kCACZ,iBAAiB,GAAG,CAAC,CAAC,MAAM,sBAC3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO,QAAQ;gCAAI;gCACjC,WAAW,CAAC,sBAAsB,EAChC,qBAAqB,KAAK,IAAI,EAAE,KAAK,EAAE,MAAM,WACzC,mCACA,8BACJ;;kDAEF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,yBAAyB,EAAE,OAAO,CAAC,KAAK,IAAI,CAAyB,CAAC,KAAK,CAAC,yCAAyC,CAAC;kEACrI,cAAA,8OAAC;4DAAI,WAAU;;;;;;;;;;;kEAEjB,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;kEACtB,8OAAC;wDAAI,WAAW,CAAC,yBAAyB,EAAE,OAAO,CAAC,KAAK,EAAE,CAAyB,CAAC,KAAK,CAAC,yCAAyC,CAAC;kEACnI,cAAA,8OAAC;4DAAI,WAAU;;;;;;;;;;;;;;;;;0DAGnB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAmC,KAAK,KAAK;;;;;;kEAC5D,8OAAC;wDAAI,WAAU;kEAAsC,KAAK,KAAK;;;;;;;;;;;;;;;;;;kDAInE,8OAAC;wCAAG,WAAU;kDAAoC,KAAK,KAAK;;;;;;kDAC5D,8OAAC;wCAAE,WAAU;kDAA8B,KAAK,WAAW;;;;;;kDAE3D,8OAAC;wCAAI,WAAU;kDACZ,KAAK,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,kBACvC,8OAAC;gDAAY,WAAU;;kEACrB,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;kEAAM;;;;;;;+CAFC;;;;;;;;;;;+BA/BT,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;;;;;;;;;;;;;;;;0BA2CtC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;4BAA2C;4BAC3B,OAAO,CAAC,cAAc,CAAC,IAAI;;;;;;;kCAEzD,8OAAC;wBAAI,WAAU;kCACZ,0BAA0B,GAAG,CAAC,CAAC,QAAQ,sBACtC,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,OAAO,MAAM;gCACnB,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAW,CAAC,eAAe,EAAE,OAAO,KAAK,EAAE;sDAC7C,OAAO,IAAI;;;;;;sDAEd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DACX,OAAO,KAAK;;;;;;8DAEf,8OAAC;oDAAE,WAAU;8DAAyB,OAAO,WAAW;;;;;;;;;;;;sDAE1D,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;+BAdrB;;;;;;;;;;;;;;;;0BAsBb,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,8OAAC;wBAAI,WAAU;kCACZ,mBAAmB,GAAG,CAAC,CAAC,aAAa,sBACpC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO,QAAQ;gCAAK;gCAClC,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAW,CAAC,qBAAqB,EACpC,YAAY,MAAM,KAAK,cAAc,iBACrC,YAAY,MAAM,KAAK,gBAAgB,gBACvC,eACA;;;;;;0DACF,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6B,YAAY,KAAK;;;;;;kEAC5D,8OAAC;wDAAE,WAAU;kEAAyB,YAAY,MAAM;;;;;;;;;;;;;;;;;;kDAG5D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAyB,YAAY,IAAI;;;;;;0DACxD,8OAAC;gDAAI,WAAW,CAAC,oBAAoB,EACnC,YAAY,MAAM,KAAK,cAAc,mBACrC,YAAY,MAAM,KAAK,gBAAgB,kBACvC,iBACA;0DACC,YAAY,MAAM;;;;;;;;;;;;;+BAxBlB;;;;;;;;;;;;;;;;;;;;;;AAiCnB", "debugId": null}}, {"offset": {"line": 9622, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/components/integration/SmartLinks.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { motion } from 'framer-motion'\nimport { \n  ArrowRight,\n  ExternalLink,\n  Zap,\n  Database,\n  Users,\n  FileText,\n  Crown,\n  TrendingUp,\n  AlertTriangle,\n  CheckCircle,\n  Clock,\n  Star\n} from 'lucide-react'\nimport Link from 'next/link'\n\ninterface SmartLinksProps {\n  context: {\n    module: 'hub' | 'expert' | 'prescriptor' | 'club'\n    data?: any\n    action?: string\n  }\n  className?: string\n}\n\nexport default function SmartLinks({ context, className = '' }: SmartLinksProps) {\n  const [hoveredLink, setHoveredLink] = useState<string | null>(null)\n\n  const generateSmartLinks = () => {\n    const { module, data, action } = context\n\n    switch (module) {\n      case 'hub':\n        return [\n          {\n            id: 'hub-to-expert',\n            title: 'Consulter un Expert',\n            description: 'Obtenir une validation technique sur cette alerte',\n            href: `/expert?source=hub&alert=${data?.alertId || 'current'}`,\n            icon: <Users className=\"h-5 w-5\" />,\n            color: 'from-green-500 to-green-600',\n            priority: 'high',\n            estimatedTime: '15 min',\n            cost: '25,000 FCFA'\n          },\n          {\n            id: 'hub-to-prescriptor',\n            title: 'Créer Template',\n            description: 'Transformer cette information en template de prescription',\n            href: `/prescriptor?action=create&source=hub&data=${data?.type || 'alert'}`,\n            icon: <FileText className=\"h-5 w-5\" />,\n            color: 'from-purple-500 to-purple-600',\n            priority: 'medium',\n            estimatedTime: '30 min',\n            cost: 'Inclus'\n          },\n          {\n            id: 'hub-to-club',\n            title: 'Partager au Réseau',\n            description: 'Diffuser cette information aux membres du club',\n            href: `/club?action=share&content=${data?.id || 'current'}`,\n            icon: <Crown className=\"h-5 w-5\" />,\n            color: 'from-amber-500 to-amber-600',\n            priority: 'low',\n            estimatedTime: '5 min',\n            cost: 'Membre requis'\n          }\n        ]\n\n      case 'expert':\n        return [\n          {\n            id: 'expert-to-hub',\n            title: 'Voir Alertes Techniques',\n            description: 'Consulter les dernières alertes nécessitant expertise',\n            href: `/hub?filter=technical&priority=expert`,\n            icon: <Database className=\"h-5 w-5\" />,\n            color: 'from-blue-500 to-blue-600',\n            priority: 'high',\n            estimatedTime: '10 min',\n            cost: 'Gratuit'\n          },\n          {\n            id: 'expert-to-prescriptor',\n            title: 'Générer Template',\n            description: 'Créer un template basé sur cette consultation',\n            href: `/prescriptor?action=generate&consultation=${data?.consultationId || 'current'}`,\n            icon: <FileText className=\"h-5 w-5\" />,\n            color: 'from-purple-500 to-purple-600',\n            priority: 'high',\n            estimatedTime: '20 min',\n            cost: 'Inclus'\n          },\n          {\n            id: 'expert-to-club',\n            title: 'Présenter au Club',\n            description: 'Partager cette expertise avec le réseau professionnel',\n            href: `/club?action=present&expertise=${data?.expertiseId || 'current'}`,\n            icon: <Crown className=\"h-5 w-5\" />,\n            color: 'from-amber-500 to-amber-600',\n            priority: 'medium',\n            estimatedTime: '15 min',\n            cost: 'Membre requis'\n          }\n        ]\n\n      case 'prescriptor':\n        return [\n          {\n            id: 'prescriptor-to-expert',\n            title: 'Valider avec Expert',\n            description: 'Faire valider ce template par un expert certifié',\n            href: `/expert?mode=validation&template=${data?.templateId || 'current'}`,\n            icon: <Users className=\"h-5 w-5\" />,\n            color: 'from-green-500 to-green-600',\n            priority: 'high',\n            estimatedTime: '30 min',\n            cost: '35,000 FCFA'\n          },\n          {\n            id: 'prescriptor-to-hub',\n            title: 'Surveiller Tendances',\n            description: 'Suivre les tendances liées à ce type de projet',\n            href: `/hub?track=${data?.category || 'electrical'}&project=${data?.projectId || 'current'}`,\n            icon: <Database className=\"h-5 w-5\" />,\n            color: 'from-blue-500 to-blue-600',\n            priority: 'medium',\n            estimatedTime: '5 min',\n            cost: 'Gratuit'\n          },\n          {\n            id: 'prescriptor-to-club',\n            title: 'Showcase Projet',\n            description: 'Présenter ce projet certifié au réseau membre',\n            href: `/club?action=showcase&project=${data?.projectId || 'current'}`,\n            icon: <Crown className=\"h-5 w-5\" />,\n            color: 'from-amber-500 to-amber-600',\n            priority: 'high',\n            estimatedTime: '10 min',\n            cost: 'Membre requis'\n          }\n        ]\n\n      case 'club':\n        return [\n          {\n            id: 'club-to-hub',\n            title: 'Alertes Exclusives',\n            description: 'Accéder aux informations réservées aux membres',\n            href: `/hub?level=premium&member=true`,\n            icon: <Database className=\"h-5 w-5\" />,\n            color: 'from-blue-500 to-blue-600',\n            priority: 'high',\n            estimatedTime: '5 min',\n            cost: 'Inclus membre'\n          },\n          {\n            id: 'club-to-expert',\n            title: 'Expert Dédié',\n            description: 'Consultation prioritaire avec expert membre',\n            href: `/expert?priority=member&dedicated=true`,\n            icon: <Users className=\"h-5 w-5\" />,\n            color: 'from-green-500 to-green-600',\n            priority: 'high',\n            estimatedTime: '15 min',\n            cost: 'Tarif préférentiel'\n          },\n          {\n            id: 'club-to-prescriptor',\n            title: 'Templates VIP',\n            description: 'Accéder aux templates exclusifs membres',\n            href: `/prescriptor?level=vip&member=true`,\n            icon: <FileText className=\"h-5 w-5\" />,\n            color: 'from-purple-500 to-purple-600',\n            priority: 'medium',\n            estimatedTime: '10 min',\n            cost: 'Inclus membre'\n          }\n        ]\n\n      default:\n        return []\n    }\n  }\n\n  const smartLinks = generateSmartLinks()\n\n  const getPriorityColor = (priority: string) => {\n    switch (priority) {\n      case 'high':\n        return 'border-red-200 bg-red-50'\n      case 'medium':\n        return 'border-yellow-200 bg-yellow-50'\n      case 'low':\n        return 'border-green-200 bg-green-50'\n      default:\n        return 'border-gray-200 bg-gray-50'\n    }\n  }\n\n  const getPriorityIcon = (priority: string) => {\n    switch (priority) {\n      case 'high':\n        return <AlertTriangle className=\"h-4 w-4 text-red-600\" />\n      case 'medium':\n        return <Clock className=\"h-4 w-4 text-yellow-600\" />\n      case 'low':\n        return <CheckCircle className=\"h-4 w-4 text-green-600\" />\n      default:\n        return <Star className=\"h-4 w-4 text-gray-600\" />\n    }\n  }\n\n  if (smartLinks.length === 0) return null\n\n  return (\n    <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>\n      <div className=\"flex items-center space-x-3 mb-6\">\n        <div className=\"w-10 h-10 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center text-white\">\n          <Zap className=\"h-5 w-5\" />\n        </div>\n        <div>\n          <h3 className=\"text-lg font-semibold text-gray-900\">Actions Intelligentes</h3>\n          <p className=\"text-sm text-gray-600\">Suggestions basées sur votre contexte actuel</p>\n        </div>\n      </div>\n\n      <div className=\"space-y-4\">\n        {smartLinks.map((link, index) => (\n          <motion.div\n            key={link.id}\n            initial={{ opacity: 0, x: -20 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ delay: index * 0.1 }}\n            onMouseEnter={() => setHoveredLink(link.id)}\n            onMouseLeave={() => setHoveredLink(null)}\n          >\n            <Link\n              href={link.href}\n              className={`block p-4 rounded-lg border-2 transition-all duration-300 hover:shadow-md ${\n                hoveredLink === link.id \n                  ? 'border-indigo-400 bg-indigo-50 transform scale-[1.02]' \n                  : getPriorityColor(link.priority)\n              }`}\n            >\n              <div className=\"flex items-start justify-between\">\n                <div className=\"flex items-start space-x-4 flex-1\">\n                  <div className={`w-12 h-12 bg-gradient-to-r ${link.color} rounded-lg flex items-center justify-center text-white flex-shrink-0`}>\n                    {link.icon}\n                  </div>\n                  \n                  <div className=\"flex-1 min-w-0\">\n                    <div className=\"flex items-center space-x-2 mb-1\">\n                      <h4 className=\"font-semibold text-gray-900 truncate\">{link.title}</h4>\n                      {getPriorityIcon(link.priority)}\n                    </div>\n                    <p className=\"text-sm text-gray-600 mb-3 line-clamp-2\">{link.description}</p>\n                    \n                    <div className=\"flex items-center space-x-4 text-xs text-gray-500\">\n                      <div className=\"flex items-center space-x-1\">\n                        <Clock className=\"h-3 w-3\" />\n                        <span>{link.estimatedTime}</span>\n                      </div>\n                      <div className=\"flex items-center space-x-1\">\n                        <span className=\"font-medium\">Coût:</span>\n                        <span>{link.cost}</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                \n                <div className=\"flex items-center space-x-2 ml-4\">\n                  <motion.div\n                    animate={{\n                      x: hoveredLink === link.id ? 5 : 0\n                    }}\n                    transition={{ duration: 0.2 }}\n                  >\n                    <ArrowRight className=\"h-5 w-5 text-gray-400\" />\n                  </motion.div>\n                  <ExternalLink className=\"h-4 w-4 text-gray-400\" />\n                </div>\n              </div>\n            </Link>\n          </motion.div>\n        ))}\n      </div>\n\n      {/* Statistiques d'utilisation */}\n      <div className=\"mt-6 pt-6 border-t border-gray-200\">\n        <div className=\"flex items-center justify-between text-sm text-gray-500\">\n          <span>Actions suggérées aujourd'hui</span>\n          <div className=\"flex items-center space-x-4\">\n            <span className=\"flex items-center space-x-1\">\n              <TrendingUp className=\"h-4 w-4 text-green-500\" />\n              <span className=\"text-green-600 font-medium\">+23%</span>\n            </span>\n            <span>47 utilisées</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AAlBA;;;;;;AA6Be,SAAS,WAAW,EAAE,OAAO,EAAE,YAAY,EAAE,EAAmB;IAC7E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE9D,MAAM,qBAAqB;QACzB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG;QAEjC,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL;wBACE,IAAI;wBACJ,OAAO;wBACP,aAAa;wBACb,MAAM,CAAC,yBAAyB,EAAE,MAAM,WAAW,WAAW;wBAC9D,oBAAM,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBACvB,OAAO;wBACP,UAAU;wBACV,eAAe;wBACf,MAAM;oBACR;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,aAAa;wBACb,MAAM,CAAC,2CAA2C,EAAE,MAAM,QAAQ,SAAS;wBAC3E,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAC1B,OAAO;wBACP,UAAU;wBACV,eAAe;wBACf,MAAM;oBACR;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,aAAa;wBACb,MAAM,CAAC,2BAA2B,EAAE,MAAM,MAAM,WAAW;wBAC3D,oBAAM,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBACvB,OAAO;wBACP,UAAU;wBACV,eAAe;wBACf,MAAM;oBACR;iBACD;YAEH,KAAK;gBACH,OAAO;oBACL;wBACE,IAAI;wBACJ,OAAO;wBACP,aAAa;wBACb,MAAM,CAAC,qCAAqC,CAAC;wBAC7C,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAC1B,OAAO;wBACP,UAAU;wBACV,eAAe;wBACf,MAAM;oBACR;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,aAAa;wBACb,MAAM,CAAC,0CAA0C,EAAE,MAAM,kBAAkB,WAAW;wBACtF,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAC1B,OAAO;wBACP,UAAU;wBACV,eAAe;wBACf,MAAM;oBACR;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,aAAa;wBACb,MAAM,CAAC,+BAA+B,EAAE,MAAM,eAAe,WAAW;wBACxE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBACvB,OAAO;wBACP,UAAU;wBACV,eAAe;wBACf,MAAM;oBACR;iBACD;YAEH,KAAK;gBACH,OAAO;oBACL;wBACE,IAAI;wBACJ,OAAO;wBACP,aAAa;wBACb,MAAM,CAAC,iCAAiC,EAAE,MAAM,cAAc,WAAW;wBACzE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBACvB,OAAO;wBACP,UAAU;wBACV,eAAe;wBACf,MAAM;oBACR;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,aAAa;wBACb,MAAM,CAAC,WAAW,EAAE,MAAM,YAAY,aAAa,SAAS,EAAE,MAAM,aAAa,WAAW;wBAC5F,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAC1B,OAAO;wBACP,UAAU;wBACV,eAAe;wBACf,MAAM;oBACR;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,aAAa;wBACb,MAAM,CAAC,8BAA8B,EAAE,MAAM,aAAa,WAAW;wBACrE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBACvB,OAAO;wBACP,UAAU;wBACV,eAAe;wBACf,MAAM;oBACR;iBACD;YAEH,KAAK;gBACH,OAAO;oBACL;wBACE,IAAI;wBACJ,OAAO;wBACP,aAAa;wBACb,MAAM,CAAC,8BAA8B,CAAC;wBACtC,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAC1B,OAAO;wBACP,UAAU;wBACV,eAAe;wBACf,MAAM;oBACR;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,aAAa;wBACb,MAAM,CAAC,sCAAsC,CAAC;wBAC9C,oBAAM,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBACvB,OAAO;wBACP,UAAU;wBACV,eAAe;wBACf,MAAM;oBACR;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,aAAa;wBACb,MAAM,CAAC,kCAAkC,CAAC;wBAC1C,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAC1B,OAAO;wBACP,UAAU;wBACV,eAAe;wBACf,MAAM;oBACR;iBACD;YAEH;gBACE,OAAO,EAAE;QACb;IACF;IAEA,MAAM,aAAa;IAEnB,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,IAAI,WAAW,MAAM,KAAK,GAAG,OAAO;IAEpC,qBACE,8OAAC;QAAI,WAAW,CAAC,kCAAkC,EAAE,WAAW;;0BAC9D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;;;;;;kCAEjB,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;0BAIzC,8OAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO,QAAQ;wBAAI;wBACjC,cAAc,IAAM,eAAe,KAAK,EAAE;wBAC1C,cAAc,IAAM,eAAe;kCAEnC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAM,KAAK,IAAI;4BACf,WAAW,CAAC,0EAA0E,EACpF,gBAAgB,KAAK,EAAE,GACnB,0DACA,iBAAiB,KAAK,QAAQ,GAClC;sCAEF,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAW,CAAC,2BAA2B,EAAE,KAAK,KAAK,CAAC,qEAAqE,CAAC;0DAC5H,KAAK,IAAI;;;;;;0DAGZ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAwC,KAAK,KAAK;;;;;;4DAC/D,gBAAgB,KAAK,QAAQ;;;;;;;kEAEhC,8OAAC;wDAAE,WAAU;kEAA2C,KAAK,WAAW;;;;;;kEAExE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,8OAAC;kFAAM,KAAK,aAAa;;;;;;;;;;;;0EAE3B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAc;;;;;;kFAC9B,8OAAC;kFAAM,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAMxB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDACP,GAAG,gBAAgB,KAAK,EAAE,GAAG,IAAI;gDACnC;gDACA,YAAY;oDAAE,UAAU;gDAAI;0DAE5B,cAAA,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;0DAExB,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;uBAlDzB,KAAK,EAAE;;;;;;;;;;0BA2DlB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;sCAAK;;;;;;sCACN,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;;sDACd,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,8OAAC;4CAAK,WAAU;sDAA6B;;;;;;;;;;;;8CAE/C,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlB", "debugId": null}}, {"offset": {"line": 10234, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/app/prescriptor/page.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { \n  ArrowLeft,\n  FileText,\n  Calculator,\n  Award,\n  Building,\n  Users,\n  TrendingUp,\n  CheckCircle,\n  Clock\n} from 'lucide-react'\nimport Link from 'next/link'\nimport PrescriptorModule from '@/components/prescriptor/PrescriptorModule'\nimport StandardsControl from '@/components/prescriptor/StandardsControl'\nimport GlobalNavigation from '@/components/navigation/GlobalNavigation'\nimport ResponsiveLayout from '@/components/layout/ResponsiveLayout'\nimport { AnimatedGrid } from '@/components/ui/AnimatedCard'\nimport EcosystemHub from '@/components/integration/EcosystemHub'\nimport SmartLinks from '@/components/integration/SmartLinks'\n\nexport default function PrescriptorPage() {\n  const stats = [\n    {\n      label: 'Templates Disponibles',\n      value: '47',\n      change: '+5',\n      changeType: 'increase',\n      icon: <FileText className=\"h-6 w-6\" />\n    },\n    {\n      label: 'Projets Actifs',\n      value: '23',\n      change: '+8',\n      changeType: 'increase',\n      icon: <Building className=\"h-6 w-6\" />\n    },\n    {\n      label: 'Calculs Automatisés',\n      value: '156',\n      change: '+12',\n      changeType: 'increase',\n      icon: <Calculator className=\"h-6 w-6\" />\n    },\n    {\n      label: 'Certificats Émis',\n      value: '89',\n      change: '+15',\n      changeType: 'increase',\n      icon: <Award className=\"h-6 w-6\" />\n    }\n  ]\n\n  const prescriptionFlow = [\n    {\n      step: 1,\n      title: 'Sélection Template',\n      description: 'Choisissez un template adapté à votre projet',\n      icon: <FileText className=\"h-6 w-6\" />,\n      color: 'from-blue-500 to-blue-600'\n    },\n    {\n      step: 2,\n      title: 'Calculs Automatiques',\n      description: 'Les calculs se font automatiquement selon les normes',\n      icon: <Calculator className=\"h-6 w-6\" />,\n      color: 'from-green-500 to-green-600'\n    },\n    {\n      step: 3,\n      title: 'Validation Technique',\n      description: 'Vérification de conformité et recommandations',\n      icon: <CheckCircle className=\"h-6 w-6\" />,\n      color: 'from-purple-500 to-purple-600'\n    },\n    {\n      step: 4,\n      title: 'Certification',\n      description: 'Génération du certificat de conformité officiel',\n      icon: <Award className=\"h-6 w-6\" />,\n      color: 'from-amber-500 to-amber-600'\n    }\n  ]\n\n  return (\n    <>\n      <GlobalNavigation />\n      <ResponsiveLayout\n        title=\"Module Prescripteur Professionnel\"\n        subtitle=\"Kits de prescription, calculs automatisés et certification de conformité pour imposer vos standards\"\n        sidebar={\n          <SmartLinks\n            context={{\n              module: 'prescriptor',\n              data: { templateId: 'current', category: 'electrical' },\n              action: 'view'\n            }}\n          />\n        }\n        actions={\n          <Link\n            href=\"/\"\n            className=\"flex items-center space-x-2 text-slate-700 hover:text-amber-600 transition-colors\"\n          >\n            <ArrowLeft className=\"h-5 w-5\" />\n            <span className=\"hidden sm:inline\">Retour à l'accueil</span>\n          </Link>\n        }\n      >\n        {/* Statistiques rapides */}\n        <AnimatedGrid className=\"grid-cols-1 md:grid-cols-2 lg:grid-cols-4 mb-8\">\n          {stats.map((stat, index) => (\n            <motion.div\n              key={stat.label}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: index * 0.05 }}\n              className=\"industrial-card p-6\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-slate-600\">{stat.label}</p>\n                  <div className=\"flex items-baseline space-x-2\">\n                    <p className=\"text-2xl font-bold text-slate-900\">{stat.value}</p>\n                    <span className={`text-sm font-medium ${\n                      stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'\n                    }`}>\n                      {stat.change}\n                    </span>\n                  </div>\n                </div>\n                <div className=\"p-3 bg-amber-100 rounded-lg text-amber-600\">\n                  {stat.icon}\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </AnimatedGrid>\n\n        {/* Processus de prescription */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.2 }}\n          className=\"mb-8\"\n        >\n          <h2 className=\"text-2xl font-bold text-slate-900 mb-6\">Processus de Prescription Automatisé</h2>\n          <AnimatedGrid className=\"md:grid-cols-4\">\n            {prescriptionFlow.map((step, index) => (\n              <motion.div\n                key={step.step}\n                initial={{ opacity: 0, x: -20 }}\n                animate={{ opacity: 1, x: 0 }}\n                transition={{ delay: 0.3 + index * 0.1 }}\n                className=\"relative\"\n              >\n                <div className=\"industrial-card p-6 text-center\">\n                  <div className={`w-16 h-16 bg-gradient-to-r ${step.color} rounded-full flex items-center justify-center text-white mx-auto mb-4`}>\n                    {step.icon}\n                  </div>\n                  <div className=\"absolute -top-2 -left-2 w-8 h-8 bg-amber-500 text-white rounded-full flex items-center justify-center text-sm font-bold\">\n                    {step.step}\n                  </div>\n                  <h3 className=\"text-lg font-bold text-slate-900 mb-2\">{step.title}</h3>\n                  <p className=\"text-sm text-slate-600\">{step.description}</p>\n                </div>\n\n                {/* Flèche de connexion */}\n                {index < prescriptionFlow.length - 1 && (\n                  <div className=\"hidden md:block absolute top-1/2 -right-3 transform -translate-y-1/2\">\n                    <div className=\"w-6 h-0.5 bg-amber-400\"></div>\n                    <div className=\"absolute -right-1 -top-1 w-2 h-2 bg-amber-400 rotate-45\"></div>\n                  </div>\n                )}\n              </motion.div>\n            ))}\n          </AnimatedGrid>\n        </motion.div>\n\n        {/* Intégration écosystème */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.4 }}\n        >\n          <EcosystemHub currentModule=\"prescriptor\" />\n        </motion.div>\n\n        {/* Contrôle des standards */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.5 }}\n        >\n          <StandardsControl />\n        </motion.div>\n\n        {/* Module Prescripteur principal */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.6 }}\n        >\n          <PrescriptorModule />\n        </motion.div>\n\n        {/* Section avantages stratégiques */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.7 }}\n          className=\"mt-8 bg-gradient-to-r from-slate-900 to-slate-800 text-white rounded-lg p-8\"\n        >\n          <h3 className=\"text-2xl font-bold mb-6 text-center\">\n            Contrôlez les Prescriptions = Contrôlez le Marché\n          </h3>\n          <div className=\"grid md:grid-cols-3 gap-8\">\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-gradient-to-r from-amber-400 to-amber-500 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                <FileText className=\"h-8 w-8 text-slate-900\" />\n              </div>\n              <h4 className=\"text-xl font-bold mb-3\">Templates Standardisés</h4>\n              <p className=\"text-slate-300\">\n                Vos templates deviennent la référence. Les architectes et ingénieurs utilisent VOS standards.\n              </p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-gradient-to-r from-amber-400 to-amber-500 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                <Calculator className=\"h-8 w-8 text-slate-900\" />\n              </div>\n              <h4 className=\"text-xl font-bold mb-3\">Calculs Certifiés</h4>\n              <p className=\"text-slate-300\">\n                Vos calculs font autorité. Impossible de prescrire sans passer par votre validation technique.\n              </p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-gradient-to-r from-amber-400 to-amber-500 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                <Award className=\"h-8 w-8 text-slate-900\" />\n              </div>\n              <h4 className=\"text-xl font-bold mb-3\">Certification Obligatoire</h4>\n              <p className=\"text-slate-300\">\n                Vos certificats deviennent indispensables. Aucun projet sérieux sans votre validation.\n              </p>\n            </div>\n          </div>\n        </motion.div>\n      </ResponsiveLayout>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AArBA;;;;;;;;;;;;AAuBe,SAAS;IACtB,MAAM,QAAQ;QACZ;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;QAC5B;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;QAC5B;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,oBAAM,8OAAC,8MAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;QAC9B;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;QACzB;KACD;IAED,MAAM,mBAAmB;QACvB;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,OAAO;QACT;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,8MAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;YAC5B,OAAO;QACT;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,2NAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;YAC7B,OAAO;QACT;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;QACT;KACD;IAED,qBACE;;0BACE,8OAAC,oJAAA,CAAA,UAAgB;;;;;0BACjB,8OAAC,gJAAA,CAAA,UAAgB;gBACf,OAAM;gBACN,UAAS;gBACT,uBACE,8OAAC,+IAAA,CAAA,UAAU;oBACT,SAAS;wBACP,QAAQ;wBACR,MAAM;4BAAE,YAAY;4BAAW,UAAU;wBAAa;wBACtD,QAAQ;oBACV;;;;;;gBAGJ,uBACE,8OAAC,4JAAA,CAAA,UAAI;oBACH,MAAK;oBACL,WAAU;;sCAEV,8OAAC,gNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,8OAAC;4BAAK,WAAU;sCAAmB;;;;;;;;;;;;;kCAKvC,8OAAC,wIAAA,CAAA,eAAY;wBAAC,WAAU;kCACrB,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO,QAAQ;gCAAK;gCAClC,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAsC,KAAK,KAAK;;;;;;8DAC7D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAqC,KAAK,KAAK;;;;;;sEAC5D,8OAAC;4DAAK,WAAW,CAAC,oBAAoB,EACpC,KAAK,UAAU,KAAK,aAAa,mBAAmB,gBACpD;sEACC,KAAK,MAAM;;;;;;;;;;;;;;;;;;sDAIlB,8OAAC;4CAAI,WAAU;sDACZ,KAAK,IAAI;;;;;;;;;;;;+BAnBT,KAAK,KAAK;;;;;;;;;;kCA2BrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;wBACzB,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC,wIAAA,CAAA,eAAY;gCAAC,WAAU;0CACrB,iBAAiB,GAAG,CAAC,CAAC,MAAM,sBAC3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO,MAAM,QAAQ;wCAAI;wCACvC,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,2BAA2B,EAAE,KAAK,KAAK,CAAC,sEAAsE,CAAC;kEAC7H,KAAK,IAAI;;;;;;kEAEZ,8OAAC;wDAAI,WAAU;kEACZ,KAAK,IAAI;;;;;;kEAEZ,8OAAC;wDAAG,WAAU;kEAAyC,KAAK,KAAK;;;;;;kEACjE,8OAAC;wDAAE,WAAU;kEAA0B,KAAK,WAAW;;;;;;;;;;;;4CAIxD,QAAQ,iBAAiB,MAAM,GAAG,mBACjC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;;;;;;;;uCArBd,KAAK,IAAI;;;;;;;;;;;;;;;;kCA8BtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;kCAEzB,cAAA,8OAAC,iJAAA,CAAA,UAAY;4BAAC,eAAc;;;;;;;;;;;kCAI9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;kCAEzB,cAAA,8OAAC,qJAAA,CAAA,UAAgB;;;;;;;;;;kCAInB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;kCAEzB,cAAA,8OAAC,sJAAA,CAAA,UAAiB;;;;;;;;;;kCAIpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;wBACzB,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;0DAEtB,8OAAC;gDAAG,WAAU;0DAAyB;;;;;;0DACvC,8OAAC;gDAAE,WAAU;0DAAiB;;;;;;;;;;;;kDAIhC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,8MAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;0DAExB,8OAAC;gDAAG,WAAU;0DAAyB;;;;;;0DACvC,8OAAC;gDAAE,WAAU;0DAAiB;;;;;;;;;;;;kDAIhC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAEnB,8OAAC;gDAAG,WAAU;0DAAyB;;;;;;0DACvC,8OAAC;gDAAE,WAAU;0DAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5C", "debugId": null}}]}