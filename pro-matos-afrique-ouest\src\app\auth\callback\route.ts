import { createClient } from '@supabase/supabase-js'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url)
  const code = requestUrl.searchParams.get('code')

  if (code) {
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    )

    try {
      await supabase.auth.exchangeCodeForSession(code)
    } catch (error) {
      console.error('Erreur lors de l\'échange du code:', error)
      return NextResponse.redirect(`${requestUrl.origin}/auth/signin?error=auth_error`)
    }
  }

  // Rediriger vers le dashboard après connexion réussie
  return NextResponse.redirect(`${requestUrl.origin}/hub`)
}
