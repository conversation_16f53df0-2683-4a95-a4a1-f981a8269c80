import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url)
  const code = requestUrl.searchParams.get('code')
  const error = requestUrl.searchParams.get('error')

  console.log('=== AUTH CALLBACK DEBUG ===')
  console.log('URL complète:', requestUrl.toString())
  console.log('Code reçu:', code)
  console.log('Erreur reçue:', error)
  console.log('Tous les paramètres:', Object.fromEntries(requestUrl.searchParams))

  if (error) {
    console.error('Erreur dans l\'URL:', error)
    return NextResponse.redirect(`${requestUrl.origin}/auth/signin?error=${error}`)
  }

  if (code) {
    const cookieStore = cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })

    try {
      console.log('Tentative d\'échange du code...')
      const { data, error } = await supabase.auth.exchangeCodeForSession(code)

      if (error) {
        console.error('Erreur lors de l\'échange du code:', error)
        return NextResponse.redirect(`${requestUrl.origin}/auth/signin?error=auth_error`)
      }

      if (data.session) {
        console.log('✅ Session créée avec succès pour:', data.session.user.email)
        console.log('User ID:', data.session.user.id)
        // Rediriger vers le hub après connexion réussie
        return NextResponse.redirect(`${requestUrl.origin}/hub`)
      } else {
        console.log('❌ Pas de session créée')
        return NextResponse.redirect(`${requestUrl.origin}/auth/signin?error=no_session`)
      }
    } catch (error) {
      console.error('❌ Exception lors de l\'échange du code:', error)
      return NextResponse.redirect(`${requestUrl.origin}/auth/signin?error=auth_error`)
    }
  }

  // Si pas de code, rediriger vers la connexion
  console.log('❌ Aucun code reçu dans l\'URL')
  return NextResponse.redirect(`${requestUrl.origin}/auth/signin?error=no_code`)
}
