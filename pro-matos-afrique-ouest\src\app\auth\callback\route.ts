import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url)
  const code = requestUrl.searchParams.get('code')

  if (code) {
    const cookieStore = cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })

    try {
      const { data, error } = await supabase.auth.exchangeCodeForSession(code)

      if (error) {
        console.error('Erreur lors de l\'échange du code:', error)
        return NextResponse.redirect(`${requestUrl.origin}/auth/signin?error=auth_error`)
      }

      if (data.session) {
        console.log('Session créée avec succès pour:', data.session.user.email)
        // Rediriger vers le hub après connexion réussie
        return NextResponse.redirect(`${requestUrl.origin}/hub`)
      }
    } catch (error) {
      console.error('Erreur lors de l\'échange du code:', error)
      return NextResponse.redirect(`${requestUrl.origin}/auth/signin?error=auth_error`)
    }
  }

  // Si pas de code, rediriger vers la connexion
  return NextResponse.redirect(`${requestUrl.origin}/auth/signin?error=no_code`)
}
