import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url)

  // Lire les paramètres de la query string
  const token_hash = requestUrl.searchParams.get('token_hash')
  const type = requestUrl.searchParams.get('type')
  const code = requestUrl.searchParams.get('code')
  const error = requestUrl.searchParams.get('error')

  // Lire aussi les paramètres du fragment (hash) si présents
  const hash = requestUrl.hash
  const hashParams = new URLSearchParams(hash.substring(1)) // Enlever le #
  const hashTokenHash = hashParams.get('token_hash')
  const hashType = hashParams.get('type')
  const hashError = hashParams.get('error')
  const hashErrorCode = hashParams.get('error_code')
  const hashErrorDescription = hashParams.get('error_description')

  console.log('=== AUTH CALLBACK DEBUG ===')
  console.log('URL complète:', requestUrl.toString())
  console.log('Query params - Code:', code, 'Token:', token_hash, 'Type:', type, 'Error:', error)
  console.log('Hash params - Token:', hashTokenHash, 'Type:', hashType, 'Error:', hashError)
  console.log('Hash error details:', hashErrorCode, hashErrorDescription)
  console.log('Tous les paramètres query:', Object.fromEntries(requestUrl.searchParams))
  console.log('Tous les paramètres hash:', Object.fromEntries(hashParams))

  // Gérer les erreurs (priorité au hash)
  const finalError = hashError || error
  const finalErrorCode = hashErrorCode
  const finalErrorDescription = hashErrorDescription

  if (finalError) {
    console.error('Erreur dans l\'URL:', finalError, finalErrorCode, finalErrorDescription)
    if (finalErrorCode === 'otp_expired') {
      return NextResponse.redirect(`${requestUrl.origin}/auth/signin?error=link_expired&message=Le lien de connexion a expiré. Demandez un nouveau lien.`)
    }
    return NextResponse.redirect(`${requestUrl.origin}/auth/signin?error=${finalError}`)
  }

  const cookieStore = cookies()
  const supabase = createRouteHandlerClient({ cookies: () => cookieStore })

  // Utiliser les paramètres du hash en priorité, puis ceux de la query
  const finalTokenHash = hashTokenHash || token_hash
  const finalType = hashType || type
  const finalCode = code

  // Gestion du magic link avec token_hash
  if (finalTokenHash && finalType) {
    try {
      console.log('Tentative de vérification du token hash...', finalTokenHash, finalType)
      const { data, error } = await supabase.auth.verifyOtp({
        token_hash: finalTokenHash,
        type: finalType as any
      })

      if (error) {
        console.error('Erreur lors de la vérification du token:', error)
        return NextResponse.redirect(`${requestUrl.origin}/auth/signin?error=token_error`)
      }

      if (data.session) {
        console.log('✅ Session créée avec succès pour:', data.session.user.email)
        console.log('User ID:', data.session.user.id)
        return NextResponse.redirect(`${requestUrl.origin}/hub`)
      }
    } catch (error) {
      console.error('❌ Exception lors de la vérification du token:', error)
      return NextResponse.redirect(`${requestUrl.origin}/auth/signin?error=token_error`)
    }
  }

  // Gestion du code d'autorisation (OAuth)
  if (code) {
    try {
      console.log('Tentative d\'échange du code...')
      const { data, error } = await supabase.auth.exchangeCodeForSession(code)

      if (error) {
        console.error('Erreur lors de l\'échange du code:', error)
        return NextResponse.redirect(`${requestUrl.origin}/auth/signin?error=auth_error`)
      }

      if (data.session) {
        console.log('✅ Session créée avec succès pour:', data.session.user.email)
        console.log('User ID:', data.session.user.id)
        return NextResponse.redirect(`${requestUrl.origin}/hub`)
      }
    } catch (error) {
      console.error('❌ Exception lors de l\'échange du code:', error)
      return NextResponse.redirect(`${requestUrl.origin}/auth/signin?error=auth_error`)
    }
  }

  // Si aucun paramètre valide, rediriger vers la connexion
  console.log('❌ Aucun paramètre d\'authentification valide reçu')
  return NextResponse.redirect(`${requestUrl.origin}/auth/signin?error=no_auth_params`)
}
