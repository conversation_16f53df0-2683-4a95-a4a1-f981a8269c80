import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url)
  const token_hash = requestUrl.searchParams.get('token_hash')
  const type = requestUrl.searchParams.get('type')
  const code = requestUrl.searchParams.get('code')
  const error = requestUrl.searchParams.get('error')

  console.log('=== AUTH CALLBACK DEBUG ===')
  console.log('URL complète:', requestUrl.toString())
  console.log('Code reçu:', code)
  console.log('Token hash reçu:', token_hash)
  console.log('Type reçu:', type)
  console.log('Erreur reçue:', error)
  console.log('Tous les paramètres:', Object.fromEntries(requestUrl.searchParams))

  if (error) {
    console.error('Erreur dans l\'URL:', error)
    return NextResponse.redirect(`${requestUrl.origin}/auth/signin?error=${error}`)
  }

  const cookieStore = cookies()
  const supabase = createRouteHandlerClient({ cookies: () => cookieStore })

  // Gestion du magic link avec token_hash
  if (token_hash && type) {
    try {
      console.log('Tentative de vérification du token hash...')
      const { data, error } = await supabase.auth.verifyOtp({
        token_hash,
        type: type as any
      })

      if (error) {
        console.error('Erreur lors de la vérification du token:', error)
        return NextResponse.redirect(`${requestUrl.origin}/auth/signin?error=token_error`)
      }

      if (data.session) {
        console.log('✅ Session créée avec succès pour:', data.session.user.email)
        console.log('User ID:', data.session.user.id)
        return NextResponse.redirect(`${requestUrl.origin}/hub`)
      }
    } catch (error) {
      console.error('❌ Exception lors de la vérification du token:', error)
      return NextResponse.redirect(`${requestUrl.origin}/auth/signin?error=token_error`)
    }
  }

  // Gestion du code d'autorisation (OAuth)
  if (code) {
    try {
      console.log('Tentative d\'échange du code...')
      const { data, error } = await supabase.auth.exchangeCodeForSession(code)

      if (error) {
        console.error('Erreur lors de l\'échange du code:', error)
        return NextResponse.redirect(`${requestUrl.origin}/auth/signin?error=auth_error`)
      }

      if (data.session) {
        console.log('✅ Session créée avec succès pour:', data.session.user.email)
        console.log('User ID:', data.session.user.id)
        return NextResponse.redirect(`${requestUrl.origin}/hub`)
      }
    } catch (error) {
      console.error('❌ Exception lors de l\'échange du code:', error)
      return NextResponse.redirect(`${requestUrl.origin}/auth/signin?error=auth_error`)
    }
  }

  // Si aucun paramètre valide, rediriger vers la connexion
  console.log('❌ Aucun paramètre d\'authentification valide reçu')
  return NextResponse.redirect(`${requestUrl.origin}/auth/signin?error=no_auth_params`)
}
