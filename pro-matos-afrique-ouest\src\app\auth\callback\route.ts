import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url)

  console.log('=== AUTH CALLBACK REDIRECT ===')
  console.log('URL complète:', requestUrl.toString())

  // Les paramètres sont dans le fragment (#), donc on redirige vers une page client
  // qui peut les lire avec JavaScript
  return NextResponse.redirect(`${requestUrl.origin}/auth/callback-client`)
}

