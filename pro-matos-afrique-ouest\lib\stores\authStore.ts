import { create } from 'zustand'
import { User as SupabaseUser } from '@supabase/auth-helpers-nextjs'
import { User } from '@/lib/types/database'
import { supabase } from '@/lib/supabase/client'

interface AuthState {
  user: SupabaseUser | null
  profile: User | null
  loading: boolean
  signIn: (email: string) => Promise<{ error: Error | null }>
  signOut: () => Promise<void>
  updateProfile: (updates: Partial<User>) => Promise<{ error: Error | null }>
  fetchProfile: () => Promise<void>
  initialize: () => Promise<void>
}

export const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  profile: null,
  loading: true,

  signIn: async (email: string) => {
    try {
      const { error } = await supabase.auth.signInWithOtp({
        email,
        options: {
          emailRedirectTo: `${window.location.origin}/auth/callback`
        }
      })

      if (error) throw error

      return { error: null }
    } catch (error) {
      return { error: error as Error }
    }
  },

  signOut: async () => {
    await supabase.auth.signOut()
    set({ user: null, profile: null })
  },

  updateProfile: async (updates: Partial<User>) => {
    try {
      const { user } = get()
      if (!user) throw new Error('Non authentifié')

      const { error } = await supabase
        .from('users')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', user.id)

      if (error) throw error

      // Mettre à jour le profil local
      const { profile } = get()
      if (profile) {
        set({ profile: { ...profile, ...updates } })
      }

      return { error: null }
    } catch (error) {
      return { error: error as Error }
    }
  },

  fetchProfile: async () => {
    try {
      const { user } = get()
      if (!user) return

      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', user.id)
        .single()

      if (error) throw error

      set({ profile: data })
    } catch (error) {
      console.error('Erreur lors du chargement du profil:', error)
    }
  },

  initialize: async () => {
    try {
      set({ loading: true })

      // Récupérer la session actuelle
      const { data: { session } } = await supabase.auth.getSession()
      
      if (session?.user) {
        set({ user: session.user })
        await get().fetchProfile()
      }

      // Écouter les changements d'authentification
      supabase.auth.onAuthStateChange(async (event, session) => {
        if (session?.user) {
          set({ user: session.user })
          await get().fetchProfile()
        } else {
          set({ user: null, profile: null })
        }
      })

    } catch (error) {
      console.error('Erreur lors de l\'initialisation:', error)
    } finally {
      set({ loading: false })
    }
  }
}))

// Hook pour vérifier les permissions
export const usePermissions = () => {
  const { profile } = useAuthStore()
  
  return {
    isGuest: !profile || profile.role === 'guest',
    isMember: profile?.role === 'member' || profile?.role === 'vip' || profile?.role === 'admin',
    isVip: profile?.role === 'vip' || profile?.role === 'admin',
    isAdmin: profile?.role === 'admin',
    canAccessValidation: profile && (profile.statut !== 'grey' || profile.devis_demandes <= 3),
    canAccessKits: profile?.role === 'member' || profile?.role === 'vip' || profile?.role === 'admin',
    canAccessClub: profile?.role === 'vip' || profile?.role === 'admin',
    canAccessCRM: profile?.role === 'admin'
  }
}
