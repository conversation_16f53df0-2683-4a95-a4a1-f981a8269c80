'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Crown, 
  CreditCard, 
  CheckCircle, 
  Star,
  Zap,
  Shield,
  Download,
  Users,
  Calendar,
  Gift
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { toast } from 'sonner'
import { useAuthStore } from '@/lib/stores/authStore'
import { supabase } from '@/lib/supabase/client'

interface VIPUpgradeProps {
  className?: string
}

export default function VIPUpgrade({ className = '' }: VIPUpgradeProps) {
  const { user } = useAuthStore()
  const [isProcessing, setIsProcessing] = useState(false)

  const vipBenefits = [
    {
      icon: <Download className="h-5 w-5" />,
      title: 'Kits Premium',
      description: 'Accès à tous les kits de prescription avancés'
    },
    {
      icon: <Users className="h-5 w-5" />,
      title: 'Réseau Exclusif',
      description: 'Connexion avec les experts et leaders du secteur'
    },
    {
      icon: <Calendar className="h-5 w-5" />,
      title: 'Événements VIP',
      description: 'Invitations aux webinaires et formations exclusives'
    },
    {
      icon: <Shield className="h-5 w-5" />,
      title: 'Support Prioritaire',
      description: 'Assistance technique prioritaire 24/7'
    },
    {
      icon: <Star className="h-5 w-5" />,
      title: 'Badge Personnalisé',
      description: 'Badge professionnel téléchargeable pour vos documents'
    },
    {
      icon: <Gift className="h-5 w-5" />,
      title: 'Avantages Partenaires',
      description: 'Remises exclusives chez nos partenaires fabricants'
    }
  ]

  const pricingPlans = [
    {
      name: 'VIP Mensuel',
      price: '15,000',
      period: '/mois',
      description: 'Parfait pour tester les avantages VIP',
      features: ['Tous les avantages VIP', 'Résiliation à tout moment', 'Support prioritaire'],
      popular: false
    },
    {
      name: 'VIP Annuel',
      price: '150,000',
      period: '/an',
      description: 'Le meilleur rapport qualité-prix',
      features: ['Tous les avantages VIP', '2 mois gratuits', 'Badge premium', 'Formation exclusive'],
      popular: true,
      savings: '30,000 FCFA économisés'
    }
  ]

  const handleUpgrade = async (planType: 'monthly' | 'annual') => {
    if (!user) {
      toast.error('Vous devez être connecté pour effectuer un upgrade')
      return
    }

    setIsProcessing(true)
    toast.loading('Traitement de votre demande...', { id: 'upgrade' })

    try {
      // Simuler le processus de paiement Stripe (en mode test)
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Mettre à jour le rôle utilisateur en base
      const { error } = await supabase
        .from('users')
        .update({ 
          role: 'vip',
          updated_at: new Date().toISOString()
        })
        .eq('id', user.id)

      if (error) throw error

      // Enregistrer la transaction (simulation)
      await supabase
        .from('transactions')
        .insert({
          user_id: user.id,
          type: 'upgrade_vip',
          plan: planType,
          amount: planType === 'monthly' ? 15000 : 150000,
          status: 'completed',
          created_at: new Date().toISOString()
        })

      toast.success('Félicitations ! Vous êtes maintenant membre VIP !', { 
        id: 'upgrade',
        description: 'Profitez de tous vos nouveaux avantages'
      })

      // Recharger la page pour mettre à jour l'interface
      window.location.reload()

    } catch (error: any) {
      console.error('Erreur lors de l\'upgrade:', error)
      toast.error('Erreur lors du traitement', { 
        id: 'upgrade',
        description: 'Veuillez réessayer ou contacter le support'
      })
    } finally {
      setIsProcessing(false)
    }
  }

  // Si l'utilisateur est déjà VIP ou admin
  if (user?.role === 'vip' || user?.role === 'admin') {
    return (
      <Card className={`border-amber-200 bg-amber-50 ${className}`}>
        <CardContent className="p-6 text-center">
          <Crown className="h-12 w-12 text-amber-600 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-amber-900 mb-2">
            Vous êtes déjà membre VIP !
          </h3>
          <p className="text-amber-800">
            Profitez de tous vos avantages exclusifs dans l'application.
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={`space-y-8 ${className}`}>
      {/* Header */}
      <div className="text-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="inline-flex items-center space-x-2 bg-amber-100 text-amber-800 px-4 py-2 rounded-full mb-4"
        >
          <Crown className="h-5 w-5" />
          <span className="font-medium">Upgrade VIP</span>
        </motion.div>
        <h2 className="text-3xl font-bold text-gray-900 mb-4">
          Rejoignez l'Élite Professionnelle
        </h2>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          Débloquez tous les outils avancés et rejoignez notre réseau exclusif d'experts électriciens
        </p>
      </div>

      {/* Avantages VIP */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {vipBenefits.map((benefit, index) => (
          <motion.div
            key={benefit.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className="h-full hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start space-x-3">
                  <div className="w-10 h-10 bg-amber-100 rounded-lg flex items-center justify-center text-amber-600">
                    {benefit.icon}
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-1">{benefit.title}</h3>
                    <p className="text-gray-600 text-sm">{benefit.description}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Plans tarifaires */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
        {pricingPlans.map((plan, index) => (
          <motion.div
            key={plan.name}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 + index * 0.1 }}
          >
            <Card className={`relative h-full ${plan.popular ? 'border-amber-400 shadow-lg' : ''}`}>
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-amber-500 text-white px-4 py-1">
                    <Star className="h-3 w-3 mr-1" />
                    Populaire
                  </Badge>
                </div>
              )}
              <CardHeader className="text-center pb-4">
                <CardTitle className="text-xl">{plan.name}</CardTitle>
                <div className="flex items-baseline justify-center space-x-1">
                  <span className="text-3xl font-bold text-gray-900">{plan.price}</span>
                  <span className="text-gray-600">{plan.period}</span>
                </div>
                <CardDescription>{plan.description}</CardDescription>
                {plan.savings && (
                  <div className="text-green-600 font-medium text-sm">
                    {plan.savings}
                  </div>
                )}
              </CardHeader>
              <CardContent className="space-y-4">
                <ul className="space-y-3">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>
                <Button
                  onClick={() => handleUpgrade(plan.name.includes('Mensuel') ? 'monthly' : 'annual')}
                  disabled={isProcessing}
                  className={`w-full ${plan.popular ? 'bg-amber-600 hover:bg-amber-700' : ''}`}
                >
                  {isProcessing ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                      Traitement...
                    </>
                  ) : (
                    <>
                      <CreditCard className="h-4 w-4 mr-2" />
                      Choisir ce plan
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Garantie */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-6 text-center">
          <Shield className="h-8 w-8 text-blue-600 mx-auto mb-3" />
          <h3 className="font-semibold text-blue-900 mb-2">Garantie Satisfait ou Remboursé</h3>
          <p className="text-blue-800 text-sm">
            Essayez sans risque pendant 30 jours. Si vous n'êtes pas satisfait, nous vous remboursons intégralement.
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
