{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_59dee874.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_59dee874-module__9CtR0q__className\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_59dee874.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22]}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/app/layout.tsx"], "sourcesContent": ["import type { Metadata } from 'next'\nimport { Inter } from 'next/font/google'\nimport './globals.css'\nimport { Toaster } from 'sonner'\nimport AuthProvider from '@/components/providers/AuthProvider'\n\nconst inter = Inter({ subsets: ['latin'] })\n\nexport const metadata: Metadata = {\n  title: 'Pro Matos Afrique Ouest - Écosystème Professionnel Électrique',\n  description: 'La plateforme de référence pour les professionnels de l\\'électrique en Afrique de l\\'Ouest. Hub d\\'information, validation technique, kits de prescription et club exclusif.',\n  keywords: 'électrique, Afrique Ouest, professionnel, validation, prescription, club, réseau',\n  authors: [{ name: 'Pro Matos Afrique Ouest' }],\n  openGraph: {\n    title: 'Pro Matos Afrique Ouest',\n    description: 'L\\'écosystème professionnel de l\\'électrique en Afrique de l\\'Ouest',\n    type: 'website',\n    locale: 'fr_FR',\n  },\n  robots: {\n    index: true,\n    follow: true,\n  },\n  viewport: {\n    width: 'device-width',\n    initialScale: 1,\n  },\n}\n\nexport default function RootLayout({\n  children,\n}: {\n  children: React.ReactNode\n}) {\n  return (\n    <html lang=\"fr\" className=\"h-full\">\n      <body className={`${inter.className} h-full antialiased`}>\n        <AuthProvider>\n          {children}\n        </AuthProvider>\n        <Toaster \n          position=\"top-right\"\n          toastOptions={{\n            duration: 4000,\n            style: {\n              background: 'white',\n              border: '1px solid #e5e7eb',\n              color: '#374151',\n            },\n          }}\n        />\n      </body>\n    </html>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAQO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;IACV,SAAS;QAAC;YAAE,MAAM;QAA0B;KAAE;IAC9C,WAAW;QACT,OAAO;QACP,aAAa;QACb,MAAM;QACN,QAAQ;IACV;IACA,QAAQ;QACN,OAAO;QACP,QAAQ;IACV;IACA,UAAU;QACR,OAAO;QACP,cAAc;IAChB;AACF;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGT;IACC,qBACE,8OAAC;QAAK,MAAK;QAAK,WAAU;kBACxB,cAAA,8OAAC;YAAK,WAAW,GAAG,yIAAA,CAAA,UAAK,CAAC,SAAS,CAAC,mBAAmB,CAAC;;8BACtD,8OAAC;8BACE;;;;;;8BAEH,8OAAC;oBACC,UAAS;oBACT,cAAc;wBACZ,UAAU;wBACV,OAAO;4BACL,YAAY;4BACZ,QAAQ;4BACR,OAAO;wBACT;oBACF;;;;;;;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}