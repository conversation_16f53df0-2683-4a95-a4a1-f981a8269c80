"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/hub/page",{

/***/ "(app-pages-browser)/./src/components/hub/InformationHub.tsx":
/*!***********************************************!*\
  !*** ./src/components/hub/InformationHub.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ InformationHub; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Calendar,Clock,Newspaper,Package,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Calendar,Clock,Newspaper,Package,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Calendar,Clock,Newspaper,Package,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Calendar,Clock,Newspaper,Package,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Calendar,Clock,Newspaper,Package,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Calendar,Clock,Newspaper,Package,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/newspaper.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Calendar,Clock,Newspaper,Package,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Calendar,Clock,Newspaper,Package,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Calendar,Clock,Newspaper,Package,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _store_useStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/useStore */ \"(app-pages-browser)/./src/store/useStore.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction InformationHub(param) {\n    let { className = \"\" } = param;\n    _s();\n    const { marketAlerts, stockUpdates, trainingEvents, newsUpdates, realTimeConnected, lastUpdateTime, setMarketAlerts, addMarketAlert, setStockUpdates, addStockUpdate, setTrainingEvents, setNewsUpdates, addNewsUpdate, setRealTimeConnected, updateLastUpdateTime } = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_2__.useStore)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"alerts\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filterSeverity, setFilterSeverity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialisation des données\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Marquer que nous sommes côté client\n        setIsClient(true);\n        const loadInitialData = ()=>{\n            // Données statiques d'exemple pour démonstration\n            setMarketAlerts([\n                {\n                    id: \"1\",\n                    title: \"Nouvelle norme NF C 15-100 - Amendement A6\",\n                    message: \"Mise \\xe0 jour importante des r\\xe8gles d'installation \\xe9lectrique\",\n                    severity: \"medium\",\n                    type: \"regulation\",\n                    is_active: true,\n                    created_at: \"2024-01-15T10:00:00Z\",\n                    source: \"Veille r\\xe9glementaire\",\n                    affected_regions: [\n                        \"C\\xf4te d'Ivoire\",\n                        \"S\\xe9n\\xe9gal\",\n                        \"Mali\"\n                    ],\n                    category: \"R\\xe9glementation\"\n                },\n                {\n                    id: \"2\",\n                    title: \"Formation Schneider Electric - Nouveaux produits\",\n                    message: \"Session de formation sur la gamme Acti9 nouvelle g\\xe9n\\xe9ration\",\n                    severity: \"low\",\n                    type: \"training\",\n                    is_active: true,\n                    created_at: \"2024-01-14T14:30:00Z\",\n                    source: \"Partenaire\",\n                    affected_regions: [\n                        \"Abidjan\",\n                        \"Dakar\"\n                    ],\n                    category: \"Formation\"\n                }\n            ]);\n            // Pas de données temps réel - interface statique\n            setStockUpdates([]);\n            setTrainingEvents([]);\n            setNewsUpdates([]);\n        };\n        loadInitialData();\n        // Pas de connexion temps réel - données statiques\n        setRealTimeConnected(false);\n    // Pas d'abonnement temps réel\n    // const unsubscribe = HubService.subscribeToRealTimeUpdates(...)\n    // return () => {\n    //   unsubscribe()\n    //   setRealTimeConnected(false)\n    // }\n    }, []);\n    // Filtrage des alertes\n    const filteredAlerts = marketAlerts.filter((alert)=>{\n        const matchesSearch = searchQuery === \"\" || alert.title.toLowerCase().includes(searchQuery.toLowerCase()) || alert.message.toLowerCase().includes(searchQuery.toLowerCase());\n        const matchesSeverity = filterSeverity === \"all\" || alert.severity === filterSeverity;\n        return matchesSearch && matchesSeverity && alert.is_active;\n    });\n    const getSeverityColor = (severity)=>{\n        switch(severity){\n            case \"critical\":\n                return \"bg-red-100 text-red-800 border-red-200\";\n            case \"high\":\n                return \"bg-orange-100 text-orange-800 border-orange-200\";\n            case \"medium\":\n                return \"bg-yellow-100 text-yellow-800 border-yellow-200\";\n            case \"low\":\n                return \"bg-blue-100 text-blue-800 border-blue-200\";\n            default:\n                return \"bg-gray-100 text-gray-800 border-gray-200\";\n        }\n    };\n    const getUpdateTypeIcon = (type)=>{\n        switch(type){\n            case \"restock\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 30\n                }, this);\n            case \"decrease\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4 text-orange-600 rotate-180\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 31\n                }, this);\n            case \"out_of_stock\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 35\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 text-blue-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const tabs = [\n        {\n            id: \"alerts\",\n            label: \"Alertes March\\xe9\",\n            icon: _barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            count: filteredAlerts.length\n        },\n        {\n            id: \"stocks\",\n            label: \"Stocks\",\n            icon: _barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            count: stockUpdates.length\n        },\n        {\n            id: \"training\",\n            label: \"Formations\",\n            icon: _barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            count: trainingEvents.length\n        },\n        {\n            id: \"news\",\n            label: \"Actualit\\xe9s\",\n            icon: _barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            count: newsUpdates.length\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"Hub d'Information Pro Matos\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-1\",\n                                        children: \"Centre de veille technologique et r\\xe9glementaire\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 rounded-full bg-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-blue-600 font-medium\",\n                                                children: \"Mode D\\xe9monstration\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-sm text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"Mis \\xe0 jour \",\n                                                    isClient ? (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatDate)(lastUpdateTime) : \"--\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: updateLastUpdateTime,\n                                        className: \"p-2 rounded-lg hover:bg-gray-100 transition-colors\",\n                                        title: \"Actualiser les donn\\xe9es\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Rechercher dans le hub...\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                        className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400 focus:border-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 11\n                            }, this),\n                            activeTab === \"alerts\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: filterSeverity,\n                                onChange: (e)=>setFilterSeverity(e.target.value),\n                                className: \"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400 focus:border-transparent\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"all\",\n                                        children: \"Toutes les priorit\\xe9s\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"critical\",\n                                        children: \"Critique\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"high\",\n                                        children: \"\\xc9lev\\xe9e\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"medium\",\n                                        children: \"Moyenne\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"low\",\n                                        children: \"Faible\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                lineNumber: 151,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"flex space-x-8 px-6\",\n                    children: tabs.map((tab)=>{\n                        const Icon = tab.icon;\n                        const isActive = activeTab === tab.id;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: ()=>setActiveTab(tab.id),\n                            className: \"flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors \".concat(isActive ? \"border-amber-500 text-amber-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: tab.label\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 17\n                                }, this),\n                                tab.count > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(isActive ? \"bg-amber-100 text-amber-800\" : \"bg-gray-100 text-gray-600\"),\n                                    children: tab.count\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, tab.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.AnimatePresence, {\n                    mode: \"wait\",\n                    children: [\n                        activeTab === \"alerts\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            className: \"space-y-4\",\n                            children: filteredAlerts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8 text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Aucune alerte correspondant aux crit\\xe8res\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 17\n                            }, this) : filteredAlerts.map((alert)=>{\n                                var _alert_affected_regions;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: -20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    className: \"p-4 rounded-lg border \".concat(getSeverityColor(alert.severity)),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-medium uppercase tracking-wide\",\n                                                            children: alert.type.replace(\"_\", \" \")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                            lineNumber: 272,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatDate)(alert.created_at)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                            lineNumber: 275,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold mb-1\",\n                                                    children: alert.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm mb-2\",\n                                                    children: alert.message\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4 text-xs text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"Cat\\xe9gorie: \",\n                                                                alert.category || alert.type\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"R\\xe9gions: \",\n                                                                ((_alert_affected_regions = alert.affected_regions) === null || _alert_affected_regions === void 0 ? void 0 : _alert_affected_regions.join(\", \")) || \"Non sp\\xe9cifi\\xe9\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 21\n                                    }, this)\n                                }, alert.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, \"alerts\", false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 13\n                        }, this),\n                        activeTab === \"stocks\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            className: \"space-y-4\",\n                            children: stockUpdates.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8 text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Aucune mise \\xe0 jour de stock r\\xe9cente\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 17\n                            }, this) : stockUpdates.map((update)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: -20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    className: \"p-4 rounded-lg border border-gray-200 bg-gray-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    getUpdateTypeIcon(update.update_type),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-gray-900\",\n                                                                children: [\n                                                                    \"Produit #\",\n                                                                    update.product_id\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: update.location\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                lineNumber: 321,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-lg font-bold text-gray-900\",\n                                                        children: [\n                                                            update.previous_quantity,\n                                                            \" → \",\n                                                            update.current_quantity\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatDate)(update.created_at)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 21\n                                    }, this)\n                                }, update.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 19\n                                }, this))\n                        }, \"stocks\", false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 13\n                        }, this),\n                        activeTab === \"training\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            className: \"space-y-4\",\n                            children: trainingEvents.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8 text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Aucun \\xe9v\\xe9nement de formation programm\\xe9\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 17\n                            }, this) : trainingEvents.map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: -20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    className: \"p-6 rounded-lg border border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2 mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(event.type === \"certification\" ? \"bg-purple-100 text-purple-800\" : event.type === \"webinar\" ? \"bg-green-100 text-green-800\" : event.type === \"workshop\" ? \"bg-blue-100 text-blue-800\" : \"bg-gray-100 text-gray-800\"),\n                                                                children: event.type\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                lineNumber: 363,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            event.membership_required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800\",\n                                                                children: [\n                                                                    event.membership_required,\n                                                                    \"+\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                lineNumber: 372,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                        lineNumber: 362,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                                        children: event.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 mb-3\",\n                                                        children: event.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-4 text-sm text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"Instructeur:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                        lineNumber: 383,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    \" \",\n                                                                    event.instructor\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                lineNumber: 382,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"Lieu:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                        lineNumber: 386,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    \" \",\n                                                                    event.location\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                lineNumber: 385,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"Date:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                        lineNumber: 389,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    \" \",\n                                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatDate)(event.start_date)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                lineNumber: 388,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"Participants:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                        lineNumber: 392,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    \" \",\n                                                                    event.current_participants,\n                                                                    \"/\",\n                                                                    event.max_participants\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                lineNumber: 391,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right ml-4\",\n                                                children: [\n                                                    event.registration_fee > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-lg font-bold text-gray-900\",\n                                                        children: [\n                                                            event.registration_fee.toLocaleString(),\n                                                            \" FCFA\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                        lineNumber: 398,\n                                                        columnNumber: 27\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-lg font-bold text-green-600\",\n                                                        children: \"Gratuit\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        className: \"mt-2 px-4 py-2 bg-amber-500 text-white rounded-lg hover:bg-amber-600 transition-colors\",\n                                                        children: \"S'inscrire\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 21\n                                    }, this)\n                                }, event.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 19\n                                }, this))\n                        }, \"training\", false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                            lineNumber: 340,\n                            columnNumber: 13\n                        }, this),\n                        activeTab === \"news\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            className: \"space-y-6\",\n                            children: newsUpdates.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8 text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Aucune actualit\\xe9 r\\xe9cente\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                lineNumber: 427,\n                                columnNumber: 17\n                            }, this) : newsUpdates.map((news)=>{\n                                var _news_tags;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: -20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    className: \"p-6 rounded-lg border border-gray-200 \".concat(news.is_featured ? \"bg-gradient-to-r from-amber-50 to-yellow-50 border-amber-200\" : \"bg-white\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start space-x-4\",\n                                        children: [\n                                            news.image_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-24 h-24 bg-gray-200 rounded-lg flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2 mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(news.category === \"regulation\" ? \"bg-red-100 text-red-800\" : news.category === \"technology\" ? \"bg-blue-100 text-blue-800\" : news.category === \"company\" ? \"bg-green-100 text-green-800\" : news.category === \"market\" ? \"bg-purple-100 text-purple-800\" : \"bg-gray-100 text-gray-800\"),\n                                                                children: news.category\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                lineNumber: 447,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            news.is_featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800\",\n                                                                children: \"\\xc0 la une\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                lineNumber: 457,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatDate)(news.published_at)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                        lineNumber: 446,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                                        children: news.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                        lineNumber: 465,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 mb-3\",\n                                                        children: news.summary\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                        lineNumber: 468,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-4 text-sm text-gray-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"Par \",\n                                                                            news.author\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                        lineNumber: 471,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex space-x-1\",\n                                                                        children: ((_news_tags = news.tags) === null || _news_tags === void 0 ? void 0 : _news_tags.slice(0, 3).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"px-2 py-1 bg-gray-100 rounded text-xs\",\n                                                                                children: [\n                                                                                    \"#\",\n                                                                                    tag\n                                                                                ]\n                                                                            }, tag, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                                lineNumber: 474,\n                                                                                columnNumber: 33\n                                                                            }, this))) || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-400\",\n                                                                            children: \"Aucun tag\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                            lineNumber: 477,\n                                                                            columnNumber: 37\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                        lineNumber: 472,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                lineNumber: 470,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            news.source_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: news.source_url,\n                                                                target: \"_blank\",\n                                                                rel: \"noopener noreferrer\",\n                                                                className: \"text-amber-600 hover:text-amber-700 text-sm font-medium\",\n                                                                children: \"Lire la suite →\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                lineNumber: 481,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 21\n                                    }, this)\n                                }, news.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, \"news\", false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                            lineNumber: 419,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                lineNumber: 246,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n        lineNumber: 149,\n        columnNumber: 5\n    }, this);\n}\n_s(InformationHub, \"loBCZCVo073DPZacugQqkhsCJZE=\", false, function() {\n    return [\n        _store_useStore__WEBPACK_IMPORTED_MODULE_2__.useStore\n    ];\n});\n_c = InformationHub;\nvar _c;\n$RefreshReg$(_c, \"InformationHub\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/hub/InformationHub.tsx\n"));

/***/ })

});