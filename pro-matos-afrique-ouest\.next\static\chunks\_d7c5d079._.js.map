{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuthStore } from '@/lib/stores/authStore'\n\nexport default function HomePage() {\n  const { user, loading } = useAuthStore()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading) {\n      if (user) {\n        router.push('/hub')\n      } else {\n        router.push('/auth/signin')\n      }\n    }\n  }, [user, loading, router])\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-slate-100\">\n      <div className=\"text-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-amber-500 mx-auto mb-4\"></div>\n        <p className=\"text-gray-600\">Chargement...</p>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD;IACrC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,CAAC,SAAS;gBACZ,IAAI,MAAM;oBACR,OAAO,IAAI,CAAC;gBACd,OAAO;oBACL,OAAO,IAAI,CAAC;gBACd;YACF;QACF;6BAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;;;;;;AAIrC;GAtBwB;;QACI,oIAAA,CAAA,eAAY;QACvB,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}