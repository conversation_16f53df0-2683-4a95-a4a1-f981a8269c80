-- Script SQL simple pour initialiser les données d'exemple
-- À exécuter dans l'éditeur SQL de Supabase

-- Insérer des alertes d'exemple
INSERT INTO public.alerts (title, body, type, category, is_active) VALUES
('Nouvelle norme NF C 15-100 - Amendement A6', 'Mise à jour importante des règles d''installation électrique pour les bâtiments résidentiels et tertiaires.', 'info', 'Réglementation', true),
('Rupture de stock - Disjoncteurs Schneider', 'Stock épuisé sur les disjoncteurs C60N 32A chez plusieurs fournisseurs d''Abidjan.', 'warning', 'Stock', true),
('Formation technique Legrand', 'Session de formation sur les nouveaux produits de la gamme Mosaic disponible.', 'info', 'Formation', true),
('Alerte sécurité - Rappel produit', 'Rappel de sécurité sur certains modèles de prises électriques défectueuses.', 'critical', 'Sécurité', true),
('Promotion spéciale - Câbles électriques', 'Remise de 20% sur tous les câbles électriques ce mois-ci.', 'promo', 'Promotion', true),
('Mise à jour stock - Tableaux électriques', 'Réapprovisionnement des tableaux électriques Hager disponibles.', 'info', 'Stock', true),
('Formation Schneider Electric', 'Nouvelle session de formation sur les produits Acti9 le mois prochain.', 'info', 'Formation', true),
('Alerte prix - Augmentation cuivre', 'Augmentation prévue des prix du cuivre, impact sur les câbles électriques.', 'warning', 'Prix', true)
ON CONFLICT (id) DO NOTHING;
