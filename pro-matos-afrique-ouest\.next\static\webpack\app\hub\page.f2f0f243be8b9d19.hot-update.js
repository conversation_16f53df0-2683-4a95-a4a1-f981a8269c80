"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/hub/page",{

/***/ "(app-pages-browser)/./src/lib/stores/alertStore.ts":
/*!**************************************!*\
  !*** ./src/lib/stores/alertStore.ts ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAlertActions: function() { return /* binding */ useAlertActions; },\n/* harmony export */   useAlertStore: function() { return /* binding */ useAlertStore; }\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n\n\nconst useAlertStore = (0,zustand__WEBPACK_IMPORTED_MODULE_1__.create)((set, get)=>({\n        alerts: [],\n        userAlerts: [],\n        loading: false,\n        fetchAlerts: async ()=>{\n            try {\n                set({\n                    loading: true\n                });\n                const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"alerts\").select(\"*\").eq(\"is_active\", true).order(\"created_at\", {\n                    ascending: false\n                });\n                if (error) {\n                    console.error(\"Erreur Supabase, utilisation des donn\\xe9es d'exemple:\", error);\n                    // Données d'exemple si Supabase échoue\n                    const exampleAlerts = [\n                        {\n                            id: 1,\n                            title: \"Nouvelle norme NF C 15-100 - Amendement A6\",\n                            body: \"Mise \\xe0 jour importante des r\\xe8gles d'installation \\xe9lectrique pour les b\\xe2timents r\\xe9sidentiels et tertiaires.\",\n                            type: \"info\",\n                            category: \"R\\xe9glementation\",\n                            is_active: true,\n                            created_at: new Date().toISOString()\n                        },\n                        {\n                            id: 2,\n                            title: \"Rupture de stock - Disjoncteurs Schneider\",\n                            body: \"Stock \\xe9puis\\xe9 sur les disjoncteurs C60N 32A chez plusieurs fournisseurs d'Abidjan.\",\n                            type: \"warning\",\n                            category: \"Stock\",\n                            is_active: true,\n                            created_at: new Date(Date.now() - 3600000).toISOString()\n                        },\n                        {\n                            id: 3,\n                            title: \"Formation technique Legrand\",\n                            body: \"Session de formation sur les nouveaux produits de la gamme Mosaic disponible.\",\n                            type: \"info\",\n                            category: \"Formation\",\n                            is_active: true,\n                            created_at: new Date(Date.now() - 7200000).toISOString()\n                        },\n                        {\n                            id: 4,\n                            title: \"Alerte s\\xe9curit\\xe9 - Rappel produit\",\n                            body: \"Rappel de s\\xe9curit\\xe9 sur certains mod\\xe8les de prises \\xe9lectriques d\\xe9fectueuses.\",\n                            type: \"critical\",\n                            category: \"S\\xe9curit\\xe9\",\n                            is_active: true,\n                            created_at: new Date(Date.now() - 10800000).toISOString()\n                        }\n                    ];\n                    set({\n                        alerts: exampleAlerts\n                    });\n                } else {\n                    set({\n                        alerts: data || []\n                    });\n                }\n            } catch (error) {\n                console.error(\"Erreur lors du chargement des alertes:\", error);\n                // Données d'exemple en cas d'erreur\n                const exampleAlerts = [\n                    {\n                        id: 1,\n                        title: \"Nouvelle norme NF C 15-100 - Amendement A6\",\n                        body: \"Mise \\xe0 jour importante des r\\xe8gles d'installation \\xe9lectrique pour les b\\xe2timents r\\xe9sidentiels et tertiaires.\",\n                        type: \"info\",\n                        category: \"R\\xe9glementation\",\n                        is_active: true,\n                        created_at: new Date().toISOString()\n                    },\n                    {\n                        id: 2,\n                        title: \"Rupture de stock - Disjoncteurs Schneider\",\n                        body: \"Stock \\xe9puis\\xe9 sur les disjoncteurs C60N 32A chez plusieurs fournisseurs d'Abidjan.\",\n                        type: \"warning\",\n                        category: \"Stock\",\n                        is_active: true,\n                        created_at: new Date(Date.now() - 3600000).toISOString()\n                    }\n                ];\n                set({\n                    alerts: exampleAlerts\n                });\n            } finally{\n                set({\n                    loading: false\n                });\n            }\n        },\n        fetchUserAlerts: async (userId)=>{\n            try {\n                const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"user_alerts\").select(\"*\").eq(\"user_id\", userId);\n                if (error) throw error;\n                set({\n                    userAlerts: data || []\n                });\n            } catch (error) {\n                console.error(\"Erreur lors du chargement des abonnements:\", error);\n            }\n        },\n        subscribeToAlert: async (alertId)=>{\n            try {\n                const { data: { user } } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n                if (!user) throw new Error(\"Non authentifi\\xe9\");\n                const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"user_alerts\").insert({\n                    user_id: user.id,\n                    alert_id: alertId\n                });\n                if (error) throw error;\n                // Mettre à jour l'état local\n                const { userAlerts } = get();\n                set({\n                    userAlerts: [\n                        ...userAlerts,\n                        {\n                            user_id: user.id,\n                            alert_id: alertId,\n                            subscribed_at: new Date().toISOString()\n                        }\n                    ]\n                });\n                return {\n                    error: null\n                };\n            } catch (error) {\n                return {\n                    error: error\n                };\n            }\n        },\n        unsubscribeFromAlert: async (alertId)=>{\n            try {\n                const { data: { user } } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n                if (!user) throw new Error(\"Non authentifi\\xe9\");\n                const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"user_alerts\").delete().eq(\"user_id\", user.id).eq(\"alert_id\", alertId);\n                if (error) throw error;\n                // Mettre à jour l'état local\n                const { userAlerts } = get();\n                set({\n                    userAlerts: userAlerts.filter((ua)=>ua.alert_id !== alertId)\n                });\n                return {\n                    error: null\n                };\n            } catch (error) {\n                return {\n                    error: error\n                };\n            }\n        },\n        isSubscribed: (alertId)=>{\n            const { userAlerts } = get();\n            return userAlerts.some((ua)=>ua.alert_id === alertId);\n        }\n    }));\n// Hook pour les notifications toast\nconst useAlertActions = ()=>{\n    const { subscribeToAlert, unsubscribeFromAlert, isSubscribed } = useAlertStore();\n    const handleSubscribe = async (alertId, onSuccess, onError)=>{\n        const { error } = await subscribeToAlert(alertId);\n        if (error) {\n            onError === null || onError === void 0 ? void 0 : onError(error.message);\n        } else {\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess();\n        }\n    };\n    const handleUnsubscribe = async (alertId, onSuccess, onError)=>{\n        const { error } = await unsubscribeFromAlert(alertId);\n        if (error) {\n            onError === null || onError === void 0 ? void 0 : onError(error.message);\n        } else {\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess();\n        }\n    };\n    return {\n        handleSubscribe,\n        handleUnsubscribe,\n        isSubscribed\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/stores/alertStore.ts\n"));

/***/ })

});