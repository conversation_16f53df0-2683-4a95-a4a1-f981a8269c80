'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Calendar, 
  Clock, 
  MapPin, 
  Users, 
  Star,
  Filter,
  Search,
  ChevronLeft,
  ChevronRight,
  Plus,
  BookOpen,
  Award,
  Zap,
  Building
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { toast } from 'sonner'
import { formatDate } from '@/lib/utils'

interface TrainingEvent {
  id: string
  title: string
  description: string
  instructor: string
  instructor_company: string
  date: string
  start_time: string
  end_time: string
  location: string
  location_type: 'online' | 'physical' | 'hybrid'
  category: string
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert'
  max_participants: number
  current_participants: number
  price: number
  certification: boolean
  rating: number
  tags: string[]
  requirements: string[]
  agenda: string[]
  materials_included: string[]
  status: 'upcoming' | 'ongoing' | 'completed' | 'cancelled'
}

interface TrainingCalendarProps {
  className?: string
}

export default function TrainingCalendar({ className = '' }: TrainingCalendarProps) {
  const [events, setEvents] = useState<TrainingEvent[]>([])
  const [filteredEvents, setFilteredEvents] = useState<TrainingEvent[]>([])
  const [currentDate, setCurrentDate] = useState(new Date())
  const [viewMode, setViewMode] = useState<'calendar' | 'list'>('list')
  const [searchQuery, setSearchQuery] = useState('')
  const [filterCategory, setFilterCategory] = useState<string>('all')
  const [filterLevel, setFilterLevel] = useState<string>('all')
  const [filterLocation, setFilterLocation] = useState<string>('all')
  const [selectedEvent, setSelectedEvent] = useState<TrainingEvent | null>(null)
  const [loading, setLoading] = useState(false)

  // Données d'exemple pour les formations
  useEffect(() => {
    const sampleEvents: TrainingEvent[] = [
      {
        id: '1',
        title: 'Formation NF C 15-100 - Installations Électriques',
        description: 'Formation complète sur la norme NF C 15-100 pour les installations électriques basse tension.',
        instructor: 'Jean-Claude MARTIN',
        instructor_company: 'Schneider Electric',
        date: '2024-01-15',
        start_time: '09:00',
        end_time: '17:00',
        location: 'Abidjan, Côte d\'Ivoire',
        location_type: 'physical',
        category: 'Réglementation',
        level: 'intermediate',
        max_participants: 25,
        current_participants: 18,
        price: 150000,
        certification: true,
        rating: 4.8,
        tags: ['NF C 15-100', 'Installation', 'Sécurité'],
        requirements: ['Notions de base en électricité', 'Expérience terrain 2 ans'],
        agenda: ['Introduction à la norme', 'Règles de sécurité', 'Cas pratiques', 'Certification'],
        materials_included: ['Manuel NF C 15-100', 'Outils de mesure', 'Certificat'],
        status: 'upcoming'
      },
      {
        id: '2',
        title: 'Maîtrise des Disjoncteurs Différentiels',
        description: 'Formation technique approfondie sur les disjoncteurs différentiels et leur mise en œuvre.',
        instructor: 'Marie KOUASSI',
        instructor_company: 'Legrand',
        date: '2024-01-20',
        start_time: '14:00',
        end_time: '18:00',
        location: 'Formation en ligne',
        location_type: 'online',
        category: 'Technique',
        level: 'advanced',
        max_participants: 50,
        current_participants: 32,
        price: 75000,
        certification: true,
        rating: 4.9,
        tags: ['Disjoncteurs', 'Protection', 'Différentiel'],
        requirements: ['Connaissance des installations électriques'],
        agenda: ['Types de disjoncteurs', 'Calculs de seuils', 'Installation pratique'],
        materials_included: ['Guide technique', 'Logiciel de calcul', 'Certificat'],
        status: 'upcoming'
      },
      {
        id: '3',
        title: 'Éclairage LED Professionnel',
        description: 'Conception et installation d\'éclairages LED pour applications professionnelles.',
        instructor: 'Amadou DIALLO',
        instructor_company: 'Philips Lighting',
        date: '2024-01-25',
        start_time: '09:00',
        end_time: '16:00',
        location: 'Dakar, Sénégal',
        location_type: 'hybrid',
        category: 'Éclairage',
        level: 'beginner',
        max_participants: 30,
        current_participants: 22,
        price: 120000,
        certification: false,
        rating: 4.6,
        tags: ['LED', 'Éclairage', 'Efficacité énergétique'],
        requirements: ['Aucun prérequis'],
        agenda: ['Technologie LED', 'Calculs photométriques', 'Installation'],
        materials_included: ['Kit LED', 'Luxmètre', 'Documentation'],
        status: 'upcoming'
      }
    ]

    setEvents(sampleEvents)
    setFilteredEvents(sampleEvents)
  }, [])

  // Filtrage des événements
  useEffect(() => {
    let filtered = events

    if (searchQuery) {
      filtered = filtered.filter(event =>
        event.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        event.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        event.instructor.toLowerCase().includes(searchQuery.toLowerCase()) ||
        event.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      )
    }

    if (filterCategory !== 'all') {
      filtered = filtered.filter(event => event.category === filterCategory)
    }

    if (filterLevel !== 'all') {
      filtered = filtered.filter(event => event.level === filterLevel)
    }

    if (filterLocation !== 'all') {
      filtered = filtered.filter(event => event.location_type === filterLocation)
    }

    setFilteredEvents(filtered)
  }, [events, searchQuery, filterCategory, filterLevel, filterLocation])

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'beginner': return 'bg-green-100 text-green-800 border-green-200'
      case 'intermediate': return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'advanced': return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'expert': return 'bg-red-100 text-red-800 border-red-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getLocationIcon = (type: string) => {
    switch (type) {
      case 'online': return <Zap className="h-4 w-4" />
      case 'physical': return <Building className="h-4 w-4" />
      case 'hybrid': return <Users className="h-4 w-4" />
      default: return <MapPin className="h-4 w-4" />
    }
  }

  const handleRegister = async (eventId: string) => {
    setLoading(true)
    try {
      // Simulation d'inscription
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setEvents(prev => prev.map(event => 
        event.id === eventId 
          ? { ...event, current_participants: event.current_participants + 1 }
          : event
      ))
      
      toast.success('Inscription réussie ! Vous recevrez un email de confirmation.')
    } catch (error) {
      toast.error('Erreur lors de l\'inscription')
    } finally {
      setLoading(false)
    }
  }

  const categories = ['all', 'Réglementation', 'Technique', 'Éclairage', 'Sécurité', 'Installation']
  const levels = ['all', 'beginner', 'intermediate', 'advanced', 'expert']
  const locationTypes = ['all', 'online', 'physical', 'hybrid']

  return (
    <div className={`bg-white rounded-lg shadow-lg ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Calendrier des Formations</h2>
            <p className="text-gray-600">Formations techniques et certifications professionnelles</p>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              Liste
            </Button>
            <Button
              variant={viewMode === 'calendar' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('calendar')}
            >
              Calendrier
            </Button>
          </div>
        </div>

        {/* Filtres et recherche */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Rechercher une formation..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <Select value={filterCategory} onValueChange={setFilterCategory}>
            <SelectTrigger>
              <SelectValue placeholder="Catégorie" />
            </SelectTrigger>
            <SelectContent>
              {categories.map(category => (
                <SelectItem key={category} value={category}>
                  {category === 'all' ? 'Toutes catégories' : category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={filterLevel} onValueChange={setFilterLevel}>
            <SelectTrigger>
              <SelectValue placeholder="Niveau" />
            </SelectTrigger>
            <SelectContent>
              {levels.map(level => (
                <SelectItem key={level} value={level}>
                  {level === 'all' ? 'Tous niveaux' : level}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={filterLocation} onValueChange={setFilterLocation}>
            <SelectTrigger>
              <SelectValue placeholder="Format" />
            </SelectTrigger>
            <SelectContent>
              {locationTypes.map(type => (
                <SelectItem key={type} value={type}>
                  {type === 'all' ? 'Tous formats' : 
                   type === 'online' ? 'En ligne' :
                   type === 'physical' ? 'Présentiel' : 'Hybride'}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Button className="bg-amber-500 hover:bg-amber-600">
            <Plus className="h-4 w-4 mr-2" />
            Proposer Formation
          </Button>
        </div>
      </div>

      {/* Contenu */}
      <div className="p-6">
        {viewMode === 'list' ? (
          <div className="space-y-4">
            {filteredEvents.map((event) => (
              <motion.div
                key={event.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">{event.title}</h3>
                      <Badge className={getLevelColor(event.level)}>
                        {event.level}
                      </Badge>
                      {event.certification && (
                        <Badge className="bg-amber-100 text-amber-800 border-amber-200">
                          <Award className="h-3 w-3 mr-1" />
                          Certifiant
                        </Badge>
                      )}
                    </div>
                    
                    <p className="text-gray-600 mb-3">{event.description}</p>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-500">
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-4 w-4" />
                        <span>{formatDate(event.date)}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Clock className="h-4 w-4" />
                        <span>{event.start_time} - {event.end_time}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        {getLocationIcon(event.location_type)}
                        <span>{event.location}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Users className="h-4 w-4" />
                        <span>{event.current_participants}/{event.max_participants}</span>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4 mt-3">
                      <div className="flex items-center space-x-1">
                        <Star className="h-4 w-4 text-yellow-400 fill-current" />
                        <span className="text-sm text-gray-600">{event.rating}</span>
                      </div>
                      <span className="text-sm text-gray-500">par {event.instructor} ({event.instructor_company})</span>
                    </div>
                  </div>

                  <div className="text-right">
                    <div className="text-2xl font-bold text-gray-900 mb-2">
                      {event.price.toLocaleString()} FCFA
                    </div>
                    <Button
                      onClick={() => handleRegister(event.id)}
                      disabled={loading || event.current_participants >= event.max_participants}
                      className="bg-amber-500 hover:bg-amber-600"
                    >
                      {event.current_participants >= event.max_participants ? 'Complet' : 'S\'inscrire'}
                    </Button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <Calendar className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Vue Calendrier</h3>
            <p className="text-gray-600">La vue calendrier sera implémentée prochainement</p>
          </div>
        )}
      </div>
    </div>
  )
}
