import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { access_token, refresh_token } = await request.json()
    
    console.log('=== API SESSION CREATE ===')
    console.log('Access token reçu:', access_token ? 'présent' : 'absent')
    console.log('Refresh token reçu:', refresh_token ? 'présent' : 'absent')

    if (!access_token || !refresh_token) {
      return NextResponse.json(
        { error: 'Tokens manquants' },
        { status: 400 }
      )
    }

    const cookieStore = cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })

    // Créer la session côté serveur
    const { data, error } = await supabase.auth.setSession({
      access_token,
      refresh_token
    })

    if (error) {
      console.error('Erreur création session serveur:', error)
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      )
    }

    if (data.session) {
      console.log('✅ Session serveur créée pour:', data.session.user.email)
      
      // Vérifier que la session est bien établie
      const { data: { session: verifySession } } = await supabase.auth.getSession()
      
      if (verifySession) {
        console.log('✅ Session serveur vérifiée')
        return NextResponse.json({
          success: true,
          user: data.session.user
        })
      } else {
        console.log('❌ Session serveur non vérifiée')
        return NextResponse.json(
          { error: 'Session non établie côté serveur' },
          { status: 500 }
        )
      }
    }

    return NextResponse.json(
      { error: 'Aucune session créée' },
      { status: 500 }
    )

  } catch (error) {
    console.error('Exception API session:', error)
    return NextResponse.json(
      { error: 'Erreur serveur' },
      { status: 500 }
    )
  }
}
