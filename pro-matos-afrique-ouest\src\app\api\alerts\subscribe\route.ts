import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { AlertService } from '@/lib/services/alertService'

export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })

    // Vérifier l'authentification
    const { data: { session }, error: authError } = await supabase.auth.getSession()
    
    if (authError || !session) {
      return NextResponse.json(
        { error: 'Non authentifié' },
        { status: 401 }
      )
    }

    const { alertId } = await request.json()

    if (!alertId) {
      return NextResponse.json(
        { error: 'ID de l\'alerte requis' },
        { status: 400 }
      )
    }

    // S'abonner à l'alerte
    const result = await AlertService.subscribeToAlert(session.user.id, alertId)

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Abonnement créé avec succès'
    })

  } catch (error) {
    console.error('Erreur API subscribe:', error)
    return NextResponse.json(
      { error: 'Erreur serveur' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })

    // Vérifier l'authentification
    const { data: { session }, error: authError } = await supabase.auth.getSession()
    
    if (authError || !session) {
      return NextResponse.json(
        { error: 'Non authentifié' },
        { status: 401 }
      )
    }

    const { alertId } = await request.json()

    if (!alertId) {
      return NextResponse.json(
        { error: 'ID de l\'alerte requis' },
        { status: 400 }
      )
    }

    // Se désabonner de l'alerte
    const result = await AlertService.unsubscribeFromAlert(session.user.id, alertId)

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Désabonnement effectué avec succès'
    })

  } catch (error) {
    console.error('Erreur API unsubscribe:', error)
    return NextResponse.json(
      { error: 'Erreur serveur' },
      { status: 500 }
    )
  }
}
