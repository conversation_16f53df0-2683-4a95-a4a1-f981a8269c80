import { create } from 'zustand'
import { Alert, UserAlert } from '@/lib/types/database'
import { supabase } from '@/lib/supabase/client'

interface AlertState {
  alerts: Alert[]
  userAlerts: UserAlert[]
  loading: boolean
  fetchAlerts: () => Promise<void>
  fetchUserAlerts: (userId: string) => Promise<void>
  subscribeToAlert: (alertId: number) => Promise<{ error: Error | null }>
  unsubscribeFromAlert: (alertId: number) => Promise<{ error: Error | null }>
  isSubscribed: (alertId: number) => boolean
}

export const useAlertStore = create<AlertState>((set, get) => ({
  alerts: [],
  userAlerts: [],
  loading: false,

  fetchAlerts: async () => {
    try {
      set({ loading: true })

      const { data, error } = await supabase
        .from('alerts')
        .select('*')
        .eq('is_active', true)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Erreur Supabase, utilisation des données d\'exemple:', error)
        // Données d'exemple si Supabase échoue
        const exampleAlerts: Alert[] = [
          {
            id: 1,
            title: 'Nouvelle norme NF C 15-100 - Amendement A6',
            body: 'Mise à jour importante des règles d\'installation électrique pour les bâtiments résidentiels et tertiaires.',
            type: 'info',
            category: 'Réglementation',
            is_active: true,
            created_at: new Date().toISOString()
          },
          {
            id: 2,
            title: 'Rupture de stock - Disjoncteurs Schneider',
            body: 'Stock épuisé sur les disjoncteurs C60N 32A chez plusieurs fournisseurs d\'Abidjan.',
            type: 'warning',
            category: 'Stock',
            is_active: true,
            created_at: new Date(Date.now() - 3600000).toISOString()
          },
          {
            id: 3,
            title: 'Formation technique Legrand',
            body: 'Session de formation sur les nouveaux produits de la gamme Mosaic disponible.',
            type: 'info',
            category: 'Formation',
            is_active: true,
            created_at: new Date(Date.now() - 7200000).toISOString()
          },
          {
            id: 4,
            title: 'Alerte sécurité - Rappel produit',
            body: 'Rappel de sécurité sur certains modèles de prises électriques défectueuses.',
            type: 'critical',
            category: 'Sécurité',
            is_active: true,
            created_at: new Date(Date.now() - 10800000).toISOString()
          }
        ]
        set({ alerts: exampleAlerts })
      } else {
        set({ alerts: data || [] })
      }
    } catch (error) {
      console.error('Erreur lors du chargement des alertes:', error)
      // Données d'exemple en cas d'erreur
      const exampleAlerts: Alert[] = [
        {
          id: 1,
          title: 'Nouvelle norme NF C 15-100 - Amendement A6',
          body: 'Mise à jour importante des règles d\'installation électrique pour les bâtiments résidentiels et tertiaires.',
          type: 'info',
          category: 'Réglementation',
          is_active: true,
          created_at: new Date().toISOString()
        },
        {
          id: 2,
          title: 'Rupture de stock - Disjoncteurs Schneider',
          body: 'Stock épuisé sur les disjoncteurs C60N 32A chez plusieurs fournisseurs d\'Abidjan.',
          type: 'warning',
          category: 'Stock',
          is_active: true,
          created_at: new Date(Date.now() - 3600000).toISOString()
        }
      ]
      set({ alerts: exampleAlerts })
    } finally {
      set({ loading: false })
    }
  },

  fetchUserAlerts: async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('user_alerts')
        .select('*')
        .eq('user_id', userId)

      if (error) throw error

      set({ userAlerts: data || [] })
    } catch (error) {
      console.error('Erreur lors du chargement des abonnements:', error)
    }
  },

  subscribeToAlert: async (alertId: number) => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('Non authentifié')

      const { error } = await supabase
        .from('user_alerts')
        .insert({
          user_id: user.id,
          alert_id: alertId
        })

      if (error) throw error

      // Mettre à jour l'état local
      const { userAlerts } = get()
      set({
        userAlerts: [
          ...userAlerts,
          {
            user_id: user.id,
            alert_id: alertId,
            subscribed_at: new Date().toISOString()
          }
        ]
      })

      return { error: null }
    } catch (error) {
      return { error: error as Error }
    }
  },

  unsubscribeFromAlert: async (alertId: number) => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('Non authentifié')

      const { error } = await supabase
        .from('user_alerts')
        .delete()
        .eq('user_id', user.id)
        .eq('alert_id', alertId)

      if (error) throw error

      // Mettre à jour l'état local
      const { userAlerts } = get()
      set({
        userAlerts: userAlerts.filter(ua => ua.alert_id !== alertId)
      })

      return { error: null }
    } catch (error) {
      return { error: error as Error }
    }
  },

  isSubscribed: (alertId: number) => {
    const { userAlerts } = get()
    return userAlerts.some(ua => ua.alert_id === alertId)
  }
}))

// Hook pour les notifications toast
export const useAlertActions = () => {
  const { subscribeToAlert, unsubscribeFromAlert, isSubscribed } = useAlertStore()

  const handleSubscribe = async (alertId: number, onSuccess?: () => void, onError?: (error: string) => void) => {
    const { error } = await subscribeToAlert(alertId)
    
    if (error) {
      onError?.(error.message)
    } else {
      onSuccess?.()
    }
  }

  const handleUnsubscribe = async (alertId: number, onSuccess?: () => void, onError?: (error: string) => void) => {
    const { error } = await unsubscribeFromAlert(alertId)
    
    if (error) {
      onError?.(error.message)
    } else {
      onSuccess?.()
    }
  }

  return {
    handleSubscribe,
    handleUnsubscribe,
    isSubscribed
  }
}
