import { create } from 'zustand'
import { <PERSON><PERSON>, UserAlert } from '@/lib/types/database'
import { supabase } from '@/lib/supabase/client'

// Données d'exemple par défaut
const DEFAULT_ALERTS: Alert[] = [
  {
    id: 1,
    title: 'Nouvelle norme NF C 15-100 - Amendement A6',
    body: 'Mise à jour importante des règles d\'installation électrique pour les bâtiments résidentiels et tertiaires.',
    type: 'info',
    category: 'Réglementation',
    is_active: true,
    created_at: new Date().toISOString()
  },
  {
    id: 2,
    title: 'Rupture de stock - Disjoncteurs Schneider',
    body: 'Stock épuisé sur les disjoncteurs C60N 32A chez plusieurs fournisseurs d\'Abidjan.',
    type: 'warning',
    category: 'Stock',
    is_active: true,
    created_at: new Date(Date.now() - 3600000).toISOString()
  },
  {
    id: 3,
    title: 'Formation technique Legrand',
    body: 'Session de formation sur les nouveaux produits de la gamme Mosaic disponible.',
    type: 'info',
    category: 'Formation',
    is_active: true,
    created_at: new Date(Date.now() - 7200000).toISOString()
  },
  {
    id: 4,
    title: 'Alerte sécurité - Rappel produit',
    body: 'Rappel de sécurité sur certains modèles de prises électriques défectueuses.',
    type: 'critical',
    category: 'Sécurité',
    is_active: true,
    created_at: new Date(Date.now() - 10800000).toISOString()
  },
  {
    id: 5,
    title: 'Promotion spéciale - Câbles électriques',
    body: 'Remise de 20% sur tous les câbles électriques ce mois-ci.',
    type: 'promo',
    category: 'Promotion',
    is_active: true,
    created_at: new Date(Date.now() - 14400000).toISOString()
  }
]

interface AlertState {
  alerts: Alert[]
  userAlerts: UserAlert[]
  loading: boolean
  fetchAlerts: () => Promise<void>
  fetchUserAlerts: (userId: string) => Promise<void>
  subscribeToAlert: (alertId: number) => Promise<{ error: Error | null }>
  unsubscribeFromAlert: (alertId: number) => Promise<{ error: Error | null }>
  isSubscribed: (alertId: number) => boolean
  seedDatabase: () => Promise<void>
}

export const useAlertStore = create<AlertState>((set, get) => ({
  alerts: DEFAULT_ALERTS, // Commencer avec les données par défaut
  userAlerts: [],
  loading: false,

  fetchAlerts: async () => {
    try {
      set({ loading: true })

      const { data, error } = await supabase
        .from('alerts')
        .select('*')
        .eq('is_active', true)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Erreur Supabase, utilisation des données par défaut:', error)
        set({ alerts: DEFAULT_ALERTS })
      } else {
        set({ alerts: data && data.length > 0 ? data : DEFAULT_ALERTS })
      }
    } catch (error) {
      console.error('Erreur lors du chargement des alertes:', error)
      set({ alerts: DEFAULT_ALERTS })
    } finally {
      set({ loading: false })
    }
  },

  fetchUserAlerts: async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('user_alerts')
        .select('*')
        .eq('user_id', userId)

      if (error) throw error

      set({ userAlerts: data || [] })
    } catch (error) {
      console.error('Erreur lors du chargement des abonnements:', error)
    }
  },

  subscribeToAlert: async (alertId: number) => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('Non authentifié')

      const { error } = await supabase
        .from('user_alerts')
        .insert({
          user_id: user.id,
          alert_id: alertId
        })

      if (error) throw error

      // Mettre à jour l'état local
      const { userAlerts } = get()
      set({
        userAlerts: [
          ...userAlerts,
          {
            user_id: user.id,
            alert_id: alertId,
            subscribed_at: new Date().toISOString()
          }
        ]
      })

      return { error: null }
    } catch (error) {
      return { error: error as Error }
    }
  },

  unsubscribeFromAlert: async (alertId: number) => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('Non authentifié')

      const { error } = await supabase
        .from('user_alerts')
        .delete()
        .eq('user_id', user.id)
        .eq('alert_id', alertId)

      if (error) throw error

      // Mettre à jour l'état local
      const { userAlerts } = get()
      set({
        userAlerts: userAlerts.filter(ua => ua.alert_id !== alertId)
      })

      return { error: null }
    } catch (error) {
      return { error: error as Error }
    }
  },

  isSubscribed: (alertId: number) => {
    const { userAlerts } = get()
    return userAlerts.some(ua => ua.alert_id === alertId)
  },

  seedDatabase: async () => {
    try {
      console.log('🌱 Initialisation des données d\'exemple...')

      // Essayer d'insérer les alertes d'exemple dans Supabase
      const { data, error } = await supabase
        .from('alerts')
        .insert(DEFAULT_ALERTS.map(alert => ({
          title: alert.title,
          body: alert.body,
          type: alert.type,
          category: alert.category,
          is_active: alert.is_active
        })))
        .select()

      if (error) {
        console.warn('⚠️ Impossible d\'insérer dans Supabase, utilisation des données locales:', error.message)
      } else {
        console.log(`✅ ${data.length} alertes insérées dans Supabase`)
        set({ alerts: data })
      }
    } catch (error) {
      console.warn('⚠️ Erreur lors de l\'initialisation, utilisation des données locales:', error)
    }
  }
}))

// Hook pour les notifications toast
export const useAlertActions = () => {
  const { subscribeToAlert, unsubscribeFromAlert, isSubscribed } = useAlertStore()

  const handleSubscribe = async (alertId: number, onSuccess?: () => void, onError?: (error: string) => void) => {
    const { error } = await subscribeToAlert(alertId)
    
    if (error) {
      onError?.(error.message)
    } else {
      onSuccess?.()
    }
  }

  const handleUnsubscribe = async (alertId: number, onSuccess?: () => void, onError?: (error: string) => void) => {
    const { error } = await unsubscribeFromAlert(alertId)
    
    if (error) {
      onError?.(error.message)
    } else {
      onSuccess?.()
    }
  }

  return {
    handleSubscribe,
    handleUnsubscribe,
    isSubscribed
  }
}
