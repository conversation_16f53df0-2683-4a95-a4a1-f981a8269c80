// Service Stripe pour les upgrades VIP
// Note: En mode développement, nous simulons Stripe pour éviter les complications de configuration

export interface StripeProduct {
  id: string
  name: string
  description: string
  price: number
  currency: string
  interval: 'month' | 'year'
  features: string[]
}

export interface StripeSession {
  id: string
  url: string
  status: 'open' | 'complete' | 'expired'
}

export interface PaymentResult {
  success: boolean
  sessionId?: string
  checkoutUrl?: string
  error?: string
}

export class StripeService {
  // Produits VIP disponibles (simulation)
  static readonly VIP_PRODUCTS: StripeProduct[] = [
    {
      id: 'vip_monthly',
      name: 'Pro Matos VIP - Mensuel',
      description: 'Accès VIP mensuel à tous les services premium',
      price: 29.99,
      currency: 'EUR',
      interval: 'month',
      features: [
        'Accès à tous les kits premium',
        'Support prioritaire 24/7',
        'Webinaires exclusifs',
        'Badge VIP personnalisé',
        'Réseau professionnel étendu',
        'Alertes en temps réel'
      ]
    },
    {
      id: 'vip_yearly',
      name: 'Pro Matos VIP - Annuel',
      description: 'Accès VIP annuel avec 2 mois gratuits',
      price: 299.99,
      currency: 'EUR',
      interval: 'year',
      features: [
        'Accès à tous les kits premium',
        'Support prioritaire 24/7',
        'Webinaires exclusifs',
        'Badge VIP personnalisé',
        'Réseau professionnel étendu',
        'Alertes en temps réel',
        '2 mois gratuits',
        'Consultation technique mensuelle'
      ]
    }
  ]

  /**
   * Crée une session de checkout Stripe (simulation)
   */
  static async createCheckoutSession(
    productId: string,
    userId: string,
    userEmail: string,
    successUrl: string,
    cancelUrl: string
  ): Promise<PaymentResult> {
    try {
      // Vérifier que le produit existe
      const product = StripeService.VIP_PRODUCTS.find(p => p.id === productId)
      if (!product) {
        return { success: false, error: 'Produit non trouvé' }
      }

      // En mode développement, simuler Stripe
      if (process.env.NODE_ENV === 'development' || !process.env.STRIPE_SECRET_KEY) {
        console.log('🔄 Mode simulation Stripe activé')
        
        // Générer un ID de session simulé
        const sessionId = `cs_test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        
        // URL de checkout simulée qui redirige vers notre page de test
        const checkoutUrl = `/club/checkout-simulation?session_id=${sessionId}&product_id=${productId}&user_id=${userId}`
        
        console.log('💳 Session Stripe simulée créée:', {
          sessionId,
          productId,
          userId,
          price: product.price,
          currency: product.currency
        })

        return {
          success: true,
          sessionId,
          checkoutUrl
        }
      }

      // En production, utiliser le vrai Stripe
      // const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY)
      // 
      // const session = await stripe.checkout.sessions.create({
      //   payment_method_types: ['card'],
      //   line_items: [{
      //     price_data: {
      //       currency: product.currency.toLowerCase(),
      //       product_data: {
      //         name: product.name,
      //         description: product.description,
      //       },
      //       unit_amount: Math.round(product.price * 100),
      //       recurring: {
      //         interval: product.interval,
      //       },
      //     },
      //     quantity: 1,
      //   }],
      //   mode: 'subscription',
      //   success_url: successUrl,
      //   cancel_url: cancelUrl,
      //   customer_email: userEmail,
      //   metadata: {
      //     userId,
      //     productId,
      //   },
      // })
      //
      // return {
      //   success: true,
      //   sessionId: session.id,
      //   checkoutUrl: session.url
      // }

      // Pour l'instant, retourner la simulation même en production
      return StripeService.createCheckoutSession(productId, userId, userEmail, successUrl, cancelUrl)

    } catch (error) {
      console.error('Erreur création session Stripe:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erreur inconnue'
      }
    }
  }

  /**
   * Vérifie le statut d'une session de checkout (simulation)
   */
  static async verifyCheckoutSession(sessionId: string): Promise<{
    success: boolean
    status?: 'open' | 'complete' | 'expired'
    customerEmail?: string
    metadata?: Record<string, string>
    error?: string
  }> {
    try {
      // En mode développement, simuler la vérification
      if (process.env.NODE_ENV === 'development' || !process.env.STRIPE_SECRET_KEY) {
        console.log('🔍 Vérification session Stripe simulée:', sessionId)
        
        // Simuler une session complétée
        return {
          success: true,
          status: 'complete',
          customerEmail: '<EMAIL>',
          metadata: {
            userId: 'user_123',
            productId: 'vip_monthly'
          }
        }
      }

      // En production, utiliser le vrai Stripe
      // const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY)
      // const session = await stripe.checkout.sessions.retrieve(sessionId)
      //
      // return {
      //   success: true,
      //   status: session.status,
      //   customerEmail: session.customer_email,
      //   metadata: session.metadata
      // }

      // Pour l'instant, retourner la simulation
      return StripeService.verifyCheckoutSession(sessionId)

    } catch (error) {
      console.error('Erreur vérification session Stripe:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erreur inconnue'
      }
    }
  }

  /**
   * Récupère les informations d'un produit
   */
  static getProduct(productId: string): StripeProduct | null {
    return StripeService.VIP_PRODUCTS.find(p => p.id === productId) || null
  }

  /**
   * Récupère tous les produits VIP
   */
  static getAllProducts(): StripeProduct[] {
    return StripeService.VIP_PRODUCTS
  }

  /**
   * Calcule les économies pour l'abonnement annuel
   */
  static calculateYearlySavings(): number {
    const monthly = StripeService.VIP_PRODUCTS.find(p => p.id === 'vip_monthly')
    const yearly = StripeService.VIP_PRODUCTS.find(p => p.id === 'vip_yearly')
    
    if (!monthly || !yearly) return 0
    
    const monthlyTotal = monthly.price * 12
    const savings = monthlyTotal - yearly.price
    
    return Math.round(savings * 100) / 100
  }

  /**
   * Formate le prix pour l'affichage
   */
  static formatPrice(price: number, currency: string = 'EUR'): string {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: currency.toUpperCase()
    }).format(price)
  }

  /**
   * Génère une URL de webhook pour Stripe (production)
   */
  static getWebhookUrl(): string {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'
    return `${baseUrl}/api/webhooks/stripe`
  }
}

// Types pour les webhooks Stripe
export interface StripeWebhookEvent {
  id: string
  type: string
  data: {
    object: any
  }
  created: number
}

// Utilitaires pour les tests
export const StripeTestUtils = {
  /**
   * Simule un paiement réussi
   */
  simulateSuccessfulPayment: (sessionId: string, userId: string, productId: string) => {
    console.log('✅ Simulation paiement réussi:', {
      sessionId,
      userId,
      productId,
      timestamp: new Date().toISOString()
    })
    
    return {
      success: true,
      sessionId,
      status: 'complete' as const,
      metadata: { userId, productId }
    }
  },

  /**
   * Simule un paiement échoué
   */
  simulateFailedPayment: (sessionId: string, reason: string = 'card_declined') => {
    console.log('❌ Simulation paiement échoué:', {
      sessionId,
      reason,
      timestamp: new Date().toISOString()
    })
    
    return {
      success: false,
      error: reason
    }
  }
}
