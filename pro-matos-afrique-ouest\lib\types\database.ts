export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          role: 'guest' | 'member' | 'vip' | 'admin'
          full_name: string | null
          company: string | null
          phone: string | null
          devis_demandes: number
          achats: number
          statut: 'white' | 'grey' | 'black'
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          role?: 'guest' | 'member' | 'vip' | 'admin'
          full_name?: string | null
          company?: string | null
          phone?: string | null
          devis_demandes?: number
          achats?: number
          statut?: 'white' | 'grey' | 'black'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          role?: 'guest' | 'member' | 'vip' | 'admin'
          full_name?: string | null
          company?: string | null
          phone?: string | null
          devis_demandes?: number
          achats?: number
          statut?: 'white' | 'grey' | 'black'
          created_at?: string
          updated_at?: string
        }
      }
      alerts: {
        Row: {
          id: number
          title: string
          body: string
          type: 'info' | 'warning' | 'critical' | 'promo'
          category: string
          is_active: boolean
          created_at: string
        }
        Insert: {
          id?: number
          title: string
          body: string
          type?: 'info' | 'warning' | 'critical' | 'promo'
          category?: string
          is_active?: boolean
          created_at?: string
        }
        Update: {
          id?: number
          title?: string
          body?: string
          type?: 'info' | 'warning' | 'critical' | 'promo'
          category?: string
          is_active?: boolean
          created_at?: string
        }
      }
      user_alerts: {
        Row: {
          user_id: string
          alert_id: number
          subscribed_at: string
        }
        Insert: {
          user_id: string
          alert_id: number
          subscribed_at?: string
        }
        Update: {
          user_id?: string
          alert_id?: number
          subscribed_at?: string
        }
      }
      validations: {
        Row: {
          id: number
          user_id: string
          title: string
          description: string | null
          file_url: string | null
          file_name: string | null
          status: 'En cours' | 'Validé' | 'Refusé'
          admin_notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: number
          user_id: string
          title: string
          description?: string | null
          file_url?: string | null
          file_name?: string | null
          status?: 'En cours' | 'Validé' | 'Refusé'
          admin_notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: number
          user_id?: string
          title?: string
          description?: string | null
          file_url?: string | null
          file_name?: string | null
          status?: 'En cours' | 'Validé' | 'Refusé'
          admin_notes?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      kits: {
        Row: {
          id: number
          title: string
          description: string | null
          file_url: string
          file_name: string | null
          category: string
          downloads: number
          is_premium: boolean
          created_at: string
        }
        Insert: {
          id?: number
          title: string
          description?: string | null
          file_url: string
          file_name?: string | null
          category?: string
          downloads?: number
          is_premium?: boolean
          created_at?: string
        }
        Update: {
          id?: number
          title?: string
          description?: string | null
          file_url?: string
          file_name?: string | null
          category?: string
          downloads?: number
          is_premium?: boolean
          created_at?: string
        }
      }
      kit_downloads: {
        Row: {
          id: number
          user_id: string
          kit_id: number
          downloaded_at: string
        }
        Insert: {
          id?: number
          user_id: string
          kit_id: number
          downloaded_at?: string
        }
        Update: {
          id?: number
          user_id?: string
          kit_id?: number
          downloaded_at?: string
        }
      }
      club_events: {
        Row: {
          id: number
          title: string
          description: string | null
          event_date: string
          location: string | null
          max_participants: number
          current_participants: number
          is_vip_only: boolean
          created_at: string
        }
        Insert: {
          id?: number
          title: string
          description?: string | null
          event_date: string
          location?: string | null
          max_participants?: number
          current_participants?: number
          is_vip_only?: boolean
          created_at?: string
        }
        Update: {
          id?: number
          title?: string
          description?: string | null
          event_date?: string
          location?: string | null
          max_participants?: number
          current_participants?: number
          is_vip_only?: boolean
          created_at?: string
        }
      }
      event_registrations: {
        Row: {
          id: number
          user_id: string
          event_id: number
          registered_at: string
        }
        Insert: {
          id?: number
          user_id: string
          event_id: number
          registered_at?: string
        }
        Update: {
          id?: number
          user_id?: string
          event_id?: number
          registered_at?: string
        }
      }
    }
  }
}

// Types utilitaires
export type User = Database['public']['Tables']['users']['Row']
export type Alert = Database['public']['Tables']['alerts']['Row']
export type UserAlert = Database['public']['Tables']['user_alerts']['Row']
export type Validation = Database['public']['Tables']['validations']['Row']
export type Kit = Database['public']['Tables']['kits']['Row']
export type KitDownload = Database['public']['Tables']['kit_downloads']['Row']
export type ClubEvent = Database['public']['Tables']['club_events']['Row']
export type EventRegistration = Database['public']['Tables']['event_registrations']['Row']
