'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Wifi,
  WifiOff,
  Download,
  Upload,
  Database,
  Clock,
  CheckCircle,
  AlertTriangle,
  RefreshCw,
  HardDrive,
  Cloud,
  Settings
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Switch } from '@/components/ui/switch'
import { toast } from 'sonner'

interface OfflineData {
  id: string
  type: 'products' | 'alerts' | 'documents' | 'calculations' | 'reports'
  name: string
  size: number // en bytes
  lastSync: string
  status: 'synced' | 'pending' | 'error' | 'downloading'
  priority: 'low' | 'medium' | 'high' | 'critical'
  data?: any
}

interface SyncStatus {
  isOnline: boolean
  lastSync: string
  pendingUploads: number
  pendingDownloads: number
  totalSize: number
  availableStorage: number
  autoSync: boolean
}

interface OfflineManagerProps {
  className?: string
}

export default function OfflineManager({ className = '' }: OfflineManagerProps) {
  const [syncStatus, setSyncStatus] = useState<SyncStatus>({
    isOnline: navigator.onLine,
    lastSync: new Date().toISOString(),
    pendingUploads: 0,
    pendingDownloads: 0,
    totalSize: 0,
    availableStorage: 0,
    autoSync: true
  })

  const [offlineData, setOfflineData] = useState<OfflineData[]>([
    {
      id: '1',
      type: 'products',
      name: 'Catalogue Produits Schneider',
      size: 15728640, // 15MB
      lastSync: new Date(Date.now() - 3600000).toISOString(),
      status: 'synced',
      priority: 'high'
    },
    {
      id: '2',
      type: 'alerts',
      name: 'Alertes et Notifications',
      size: 2097152, // 2MB
      lastSync: new Date(Date.now() - 1800000).toISOString(),
      status: 'pending',
      priority: 'medium'
    },
    {
      id: '3',
      type: 'documents',
      name: 'Fiches Techniques',
      size: 52428800, // 50MB
      lastSync: new Date(Date.now() - 7200000).toISOString(),
      status: 'synced',
      priority: 'high'
    },
    {
      id: '4',
      type: 'calculations',
      name: 'Calculs Sauvegardés',
      size: 1048576, // 1MB
      lastSync: new Date(Date.now() - 900000).toISOString(),
      status: 'error',
      priority: 'low'
    },
    {
      id: '5',
      type: 'reports',
      name: 'Rapports Techniques',
      size: 10485760, // 10MB
      lastSync: new Date().toISOString(),
      status: 'downloading',
      priority: 'medium'
    }
  ])

  const [isSyncing, setIsSyncing] = useState(false)
  const [downloadProgress, setDownloadProgress] = useState<Record<string, number>>({})
  const [showSettings, setShowSettings] = useState(false)

  // Surveiller le statut de connexion
  useEffect(() => {
    const handleOnline = () => {
      setSyncStatus(prev => ({ ...prev, isOnline: true }))
      toast.success('Connexion rétablie - Synchronisation automatique')
      if (syncStatus.autoSync) {
        handleAutoSync()
      }
    }

    const handleOffline = () => {
      setSyncStatus(prev => ({ ...prev, isOnline: false }))
      toast.warning('Mode hors ligne activé')
    }

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [syncStatus.autoSync])

  // Calculer l'espace de stockage
  useEffect(() => {
    const calculateStorage = async () => {
      try {
        if ('storage' in navigator && 'estimate' in navigator.storage) {
          const estimate = await navigator.storage.estimate()
          const totalSize = offlineData.reduce((sum, item) => sum + item.size, 0)
          
          setSyncStatus(prev => ({
            ...prev,
            totalSize,
            availableStorage: estimate.quota || 0
          }))
        }
      } catch (error) {
        console.error('Erreur calcul stockage:', error)
      }
    }

    calculateStorage()
  }, [offlineData])

  // Synchronisation automatique
  const handleAutoSync = async () => {
    if (!syncStatus.isOnline || isSyncing) return

    setIsSyncing(true)
    
    try {
      // Simuler la synchronisation
      const pendingItems = offlineData.filter(item => item.status === 'pending' || item.status === 'error')
      
      for (const item of pendingItems) {
        await simulateSync(item.id)
        await new Promise(resolve => setTimeout(resolve, 1000))
      }

      setSyncStatus(prev => ({
        ...prev,
        lastSync: new Date().toISOString(),
        pendingUploads: 0,
        pendingDownloads: 0
      }))

      toast.success('Synchronisation terminée')
    } catch (error) {
      toast.error('Erreur lors de la synchronisation')
    } finally {
      setIsSyncing(false)
    }
  }

  // Simuler la synchronisation d'un élément
  const simulateSync = async (itemId: string) => {
    setOfflineData(prev => prev.map(item => 
      item.id === itemId ? { ...item, status: 'downloading' as const } : item
    ))

    // Simuler le progrès de téléchargement
    for (let progress = 0; progress <= 100; progress += 10) {
      setDownloadProgress(prev => ({ ...prev, [itemId]: progress }))
      await new Promise(resolve => setTimeout(resolve, 100))
    }

    setOfflineData(prev => prev.map(item => 
      item.id === itemId 
        ? { ...item, status: 'synced' as const, lastSync: new Date().toISOString() }
        : item
    ))

    setDownloadProgress(prev => {
      const newProgress = { ...prev }
      delete newProgress[itemId]
      return newProgress
    })
  }

  // Télécharger un élément spécifique
  const downloadItem = async (itemId: string) => {
    if (!syncStatus.isOnline) {
      toast.error('Connexion requise pour télécharger')
      return
    }

    await simulateSync(itemId)
    toast.success('Téléchargement terminé')
  }

  // Supprimer un élément du cache
  const removeFromCache = (itemId: string) => {
    setOfflineData(prev => prev.filter(item => item.id !== itemId))
    toast.success('Élément supprimé du cache')
  }

  // Vider tout le cache
  const clearAllCache = () => {
    setOfflineData([])
    setSyncStatus(prev => ({ ...prev, totalSize: 0 }))
    toast.success('Cache vidé')
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'synced': return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'pending': return <Clock className="h-4 w-4 text-yellow-500" />
      case 'error': return <AlertTriangle className="h-4 w-4 text-red-500" />
      case 'downloading': return <Download className="h-4 w-4 text-blue-500 animate-pulse" />
      default: return <Database className="h-4 w-4 text-gray-500" />
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'products': return <Database className="h-4 w-4" />
      case 'alerts': return <AlertTriangle className="h-4 w-4" />
      case 'documents': return <Download className="h-4 w-4" />
      case 'calculations': return <RefreshCw className="h-4 w-4" />
      case 'reports': return <Upload className="h-4 w-4" />
      default: return <HardDrive className="h-4 w-4" />
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200'
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'medium': return 'bg-blue-100 text-blue-800 border-blue-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const storageUsagePercent = syncStatus.availableStorage > 0 
    ? (syncStatus.totalSize / syncStatus.availableStorage) * 100 
    : 0

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header avec statut */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                syncStatus.isOnline 
                  ? 'bg-gradient-to-r from-green-400 to-green-500' 
                  : 'bg-gradient-to-r from-gray-400 to-gray-500'
              }`}>
                {syncStatus.isOnline ? 
                  <Wifi className="h-6 w-6 text-white" /> : 
                  <WifiOff className="h-6 w-6 text-white" />
                }
              </div>
              <div>
                <CardTitle className="flex items-center space-x-2">
                  <span>Gestionnaire Hors Ligne</span>
                  <Badge className={syncStatus.isOnline ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                    {syncStatus.isOnline ? 'En ligne' : 'Hors ligne'}
                  </Badge>
                </CardTitle>
                <CardDescription>
                  Dernière sync: {new Date(syncStatus.lastSync).toLocaleString('fr-FR')}
                </CardDescription>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowSettings(!showSettings)}
              >
                <Settings className="h-4 w-4" />
              </Button>
              
              <Button
                onClick={handleAutoSync}
                disabled={!syncStatus.isOnline || isSyncing}
                className="bg-blue-500 hover:bg-blue-600"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isSyncing ? 'animate-spin' : ''}`} />
                {isSyncing ? 'Synchronisation...' : 'Synchroniser'}
              </Button>
            </div>
          </div>
        </CardHeader>

        {/* Paramètres */}
        {showSettings && (
          <CardContent className="border-t border-gray-200 bg-gray-50">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">Synchronisation automatique</h4>
                  <p className="text-sm text-gray-600">Synchroniser automatiquement quand la connexion est rétablie</p>
                </div>
                <Switch
                  checked={syncStatus.autoSync}
                  onCheckedChange={(checked) => 
                    setSyncStatus(prev => ({ ...prev, autoSync: checked }))
                  }
                />
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-900">Vider tout le cache</span>
                <Button variant="destructive" size="sm" onClick={clearAllCache}>
                  Vider
                </Button>
              </div>
            </div>
          </CardContent>
        )}
      </Card>

      {/* Statistiques de stockage */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <HardDrive className="h-8 w-8 text-blue-500" />
              <div>
                <p className="text-sm text-gray-600">Espace utilisé</p>
                <p className="text-lg font-bold">{formatFileSize(syncStatus.totalSize)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <Cloud className="h-8 w-8 text-green-500" />
              <div>
                <p className="text-sm text-gray-600">Éléments synchronisés</p>
                <p className="text-lg font-bold">
                  {offlineData.filter(item => item.status === 'synced').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <Clock className="h-8 w-8 text-yellow-500" />
              <div>
                <p className="text-sm text-gray-600">En attente</p>
                <p className="text-lg font-bold">
                  {offlineData.filter(item => item.status === 'pending' || item.status === 'error').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Utilisation du stockage */}
      {syncStatus.availableStorage > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Utilisation du Stockage</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Utilisé: {formatFileSize(syncStatus.totalSize)}</span>
                <span>Disponible: {formatFileSize(syncStatus.availableStorage)}</span>
              </div>
              <Progress value={storageUsagePercent} className="h-2" />
              <p className="text-xs text-gray-600">
                {storageUsagePercent.toFixed(1)}% de l'espace de stockage utilisé
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Liste des données hors ligne */}
      <Card>
        <CardHeader>
          <CardTitle>Données Hors Ligne</CardTitle>
          <CardDescription>
            Gérez vos données disponibles en mode hors ligne
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {offlineData.map((item) => (
              <motion.div
                key={item.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
              >
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    {getTypeIcon(item.type)}
                    {getStatusIcon(item.status)}
                  </div>

                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">{item.name}</h4>
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <span>{formatFileSize(item.size)}</span>
                      <span>•</span>
                      <span>Sync: {new Date(item.lastSync).toLocaleString('fr-FR')}</span>
                      <Badge className={getPriorityColor(item.priority)}>
                        {item.priority}
                      </Badge>
                    </div>

                    {/* Barre de progression pour les téléchargements */}
                    {item.status === 'downloading' && downloadProgress[item.id] !== undefined && (
                      <div className="mt-2">
                        <Progress value={downloadProgress[item.id]} className="h-1" />
                        <p className="text-xs text-gray-500 mt-1">
                          Téléchargement: {downloadProgress[item.id]}%
                        </p>
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  {item.status === 'error' || item.status === 'pending' ? (
                    <Button
                      size="sm"
                      onClick={() => downloadItem(item.id)}
                      disabled={!syncStatus.isOnline}
                      className="bg-blue-500 hover:bg-blue-600"
                    >
                      <Download className="h-3 w-3 mr-1" />
                      Télécharger
                    </Button>
                  ) : item.status === 'synced' ? (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => downloadItem(item.id)}
                      disabled={!syncStatus.isOnline}
                    >
                      <RefreshCw className="h-3 w-3 mr-1" />
                      Actualiser
                    </Button>
                  ) : null}

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeFromCache(item.id)}
                  >
                    Supprimer
                  </Button>
                </div>
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Indicateur de statut flottant */}
      <AnimatePresence>
        {!syncStatus.isOnline && (
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 50 }}
            className="fixed bottom-4 right-4 z-50"
          >
            <Card className="bg-yellow-50 border-yellow-200">
              <CardContent className="p-4">
                <div className="flex items-center space-x-3">
                  <WifiOff className="h-5 w-5 text-yellow-600" />
                  <div>
                    <p className="font-medium text-yellow-900">Mode Hors Ligne</p>
                    <p className="text-sm text-yellow-700">
                      Utilisation des données en cache
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
