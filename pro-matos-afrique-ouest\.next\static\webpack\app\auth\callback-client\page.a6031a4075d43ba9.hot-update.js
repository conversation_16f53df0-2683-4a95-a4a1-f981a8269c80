"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/callback-client/page",{

/***/ "(app-pages-browser)/./src/app/auth/callback-client/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/auth/callback-client/page.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AuthCallbackClient; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction AuthCallbackClient() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Traitement de la connexion...\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleAuthCallback = async ()=>{\n            try {\n                console.log(\"=== CLIENT CALLBACK DEBUG ===\");\n                console.log(\"URL compl\\xe8te:\", window.location.href);\n                console.log(\"Hash:\", window.location.hash);\n                console.log(\"Search:\", window.location.search);\n                // Lire les paramètres du fragment (#)\n                const hashParams = new URLSearchParams(window.location.hash.substring(1));\n                const accessToken = hashParams.get(\"access_token\");\n                const refreshToken = hashParams.get(\"refresh_token\");\n                const tokenType = hashParams.get(\"token_type\");\n                const type = hashParams.get(\"type\");\n                const error = hashParams.get(\"error\");\n                console.log(\"Param\\xe8tres extraits:\", {\n                    accessToken: accessToken ? \"pr\\xe9sent\" : \"absent\",\n                    refreshToken: refreshToken ? \"pr\\xe9sent\" : \"absent\",\n                    tokenType,\n                    type,\n                    error\n                });\n                console.log(\"AccessToken (d\\xe9but):\", accessToken === null || accessToken === void 0 ? void 0 : accessToken.substring(0, 50));\n                console.log(\"RefreshToken:\", refreshToken);\n                if (error) {\n                    console.error(\"Erreur dans l'URL:\", error);\n                    setStatus(\"Erreur: \".concat(error));\n                    setTimeout(()=>{\n                        router.push(\"/auth/signin?error=\" + error);\n                    }, 2000);\n                    return;\n                }\n                if (accessToken && refreshToken) {\n                    setStatus(\"Cr\\xe9ation de la session...\");\n                    // Créer la session avec les tokens\n                    const { data, error: sessionError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.setSession({\n                        access_token: accessToken,\n                        refresh_token: refreshToken\n                    });\n                    if (sessionError) {\n                        console.error(\"Erreur cr\\xe9ation session:\", sessionError);\n                        setStatus(\"Erreur de session: \".concat(sessionError.message));\n                        setTimeout(()=>{\n                            router.push(\"/auth/signin?error=session_error\");\n                        }, 2000);\n                        return;\n                    }\n                    if (data.session) {\n                        console.log(\"✅ Session cr\\xe9\\xe9e avec succ\\xe8s pour:\", data.session.user.email);\n                        setStatus(\"Connexion r\\xe9ussie ! Finalisation...\");\n                        // Nettoyer l'URL\n                        window.history.replaceState({}, document.title, \"/auth/callback-client\");\n                        // Attendre que la session soit propagée côté serveur\n                        setStatus(\"Session \\xe9tablie, redirection vers le hub...\");\n                        setTimeout(()=>{\n                            // Forcer un rechargement de page pour que le middleware reconnaisse la session\n                            window.location.href = \"/hub\";\n                        }, 2000);\n                    } else {\n                        console.error(\"Pas de session cr\\xe9\\xe9e\");\n                        setStatus(\"Erreur: Impossible de cr\\xe9er la session\");\n                        setTimeout(()=>{\n                            router.push(\"/auth/signin?error=no_session\");\n                        }, 2000);\n                    }\n                } else {\n                    console.error(\"Tokens manquants\");\n                    setStatus(\"Erreur: Tokens d'authentification manquants\");\n                    setTimeout(()=>{\n                        router.push(\"/auth/signin?error=missing_tokens\");\n                    }, 2000);\n                }\n            } catch (error) {\n                console.error(\"Exception dans le callback:\", error);\n                setStatus(\"Exception: \".concat(error));\n                setTimeout(()=>{\n                    router.push(\"/auth/signin?error=callback_exception\");\n                }, 2000);\n            }\n        };\n        handleAuthCallback();\n    }, [\n        router\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full bg-white rounded-lg shadow-md p-6 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\app\\\\auth\\\\callback-client\\\\page.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-xl font-semibold text-gray-900 mb-2\",\n                    children: \"Connexion en cours\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\app\\\\auth\\\\callback-client\\\\page.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600\",\n                    children: status\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\app\\\\auth\\\\callback-client\\\\page.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\app\\\\auth\\\\callback-client\\\\page.tsx\",\n            lineNumber: 106,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\app\\\\auth\\\\callback-client\\\\page.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthCallbackClient, \"FweN7jrWa9mpZOYAHNyYrO0QKqg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AuthCallbackClient;\nvar _c;\n$RefreshReg$(_c, \"AuthCallbackClient\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/auth/callback-client/page.tsx\n"));

/***/ })

});