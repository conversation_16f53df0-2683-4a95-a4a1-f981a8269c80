{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/store/useStore.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { persist } from 'zustand/middleware'\nimport {\n  User,\n  Product,\n  Alert,\n  MarketAlert,\n  StockUpdate,\n  TrainingEvent,\n  NewsUpdate,\n  TechnicalDocument,\n  CompatibilityCheck,\n  ExpertConsultation,\n  ExpertProfile,\n  TechnicalResource,\n  TechnicalReport,\n  PrescriptionTemplate,\n  PrescriptionProject,\n  TechnicalNote,\n  ComplianceCertificate,\n  CalculationEngine\n} from '@/lib/supabase'\n\ninterface AppState {\n  // User state\n  user: User | null\n  isAuthenticated: boolean\n  \n  // Products state\n  products: Product[]\n  filteredProducts: Product[]\n  searchQuery: string\n  selectedCategory: string\n  \n  // Alerts state\n  alerts: Alert[]\n  unreadAlertsCount: number\n\n  // Hub d'Information state\n  marketAlerts: MarketAlert[]\n  stockUpdates: StockUpdate[]\n  trainingEvents: TrainingEvent[]\n  newsUpdates: NewsUpdate[]\n  realTimeConnected: boolean\n  lastUpdateTime: string\n\n  // Espace Conseil Technique state\n  technicalDocuments: TechnicalDocument[]\n  compatibilityChecks: CompatibilityCheck[]\n  expertConsultations: ExpertConsultation[]\n  expertProfiles: ExpertProfile[]\n  technicalResources: TechnicalResource[]\n  technicalReports: TechnicalReport[]\n  selectedExpert: ExpertProfile | null\n  activeConsultation: ExpertConsultation | null\n\n  // Module Prescripteur Professionnel state\n  prescriptionTemplates: PrescriptionTemplate[]\n  prescriptionProjects: PrescriptionProject[]\n  technicalNotes: TechnicalNote[]\n  complianceCertificates: ComplianceCertificate[]\n  calculationEngines: CalculationEngine[]\n  activeProject: PrescriptionProject | null\n  selectedTemplate: PrescriptionTemplate | null\n\n  // UI state\n  isLoading: boolean\n  isDarkMode: boolean\n  sidebarOpen: boolean\n  \n  // Actions\n  setUser: (user: User | null) => void\n  setProducts: (products: Product[]) => void\n  setSearchQuery: (query: string) => void\n  setSelectedCategory: (category: string) => void\n  setAlerts: (alerts: Alert[]) => void\n  markAlertAsRead: (alertId: string) => void\n\n  // Hub d'Information actions\n  setMarketAlerts: (alerts: MarketAlert[]) => void\n  addMarketAlert: (alert: MarketAlert) => void\n  setStockUpdates: (updates: StockUpdate[]) => void\n  addStockUpdate: (update: StockUpdate) => void\n  setTrainingEvents: (events: TrainingEvent[]) => void\n  setNewsUpdates: (news: NewsUpdate[]) => void\n  addNewsUpdate: (news: NewsUpdate) => void\n  setRealTimeConnected: (connected: boolean) => void\n  updateLastUpdateTime: () => void\n\n  // Espace Conseil Technique actions\n  setTechnicalDocuments: (documents: TechnicalDocument[]) => void\n  addTechnicalDocument: (document: TechnicalDocument) => void\n  updateTechnicalDocument: (id: string, updates: Partial<TechnicalDocument>) => void\n  setCompatibilityChecks: (checks: CompatibilityCheck[]) => void\n  addCompatibilityCheck: (check: CompatibilityCheck) => void\n  setExpertConsultations: (consultations: ExpertConsultation[]) => void\n  addExpertConsultation: (consultation: ExpertConsultation) => void\n  updateExpertConsultation: (id: string, updates: Partial<ExpertConsultation>) => void\n  setExpertProfiles: (profiles: ExpertProfile[]) => void\n  setTechnicalResources: (resources: TechnicalResource[]) => void\n  setTechnicalReports: (reports: TechnicalReport[]) => void\n  addTechnicalReport: (report: TechnicalReport) => void\n  setSelectedExpert: (expert: ExpertProfile | null) => void\n  setActiveConsultation: (consultation: ExpertConsultation | null) => void\n\n  // Module Prescripteur Professionnel actions\n  setPrescriptionTemplates: (templates: PrescriptionTemplate[]) => void\n  setPrescriptionProjects: (projects: PrescriptionProject[]) => void\n  addPrescriptionProject: (project: PrescriptionProject) => void\n  updatePrescriptionProject: (id: string, updates: Partial<PrescriptionProject>) => void\n  setTechnicalNotes: (notes: TechnicalNote[]) => void\n  addTechnicalNote: (note: TechnicalNote) => void\n  setComplianceCertificates: (certificates: ComplianceCertificate[]) => void\n  addComplianceCertificate: (certificate: ComplianceCertificate) => void\n  setCalculationEngines: (engines: CalculationEngine[]) => void\n  setActiveProject: (project: PrescriptionProject | null) => void\n  setSelectedTemplate: (template: PrescriptionTemplate | null) => void\n\n  setLoading: (loading: boolean) => void\n  toggleDarkMode: () => void\n  toggleSidebar: () => void\n  filterProducts: () => void\n}\n\nexport const useStore = create<AppState>()(\n  persist(\n    (set, get) => ({\n      // Initial state\n      user: null,\n      isAuthenticated: false,\n      products: [],\n      filteredProducts: [],\n      searchQuery: '',\n      selectedCategory: '',\n      alerts: [],\n      unreadAlertsCount: 0,\n\n      // Hub d'Information initial state\n      marketAlerts: [],\n      stockUpdates: [],\n      trainingEvents: [],\n      newsUpdates: [],\n      realTimeConnected: false,\n      lastUpdateTime: new Date().toISOString(),\n\n      // Espace Conseil Technique initial state\n      technicalDocuments: [],\n      compatibilityChecks: [],\n      expertConsultations: [],\n      expertProfiles: [],\n      technicalResources: [],\n      technicalReports: [],\n      selectedExpert: null,\n      activeConsultation: null,\n\n      // Module Prescripteur Professionnel initial state\n      prescriptionTemplates: [],\n      prescriptionProjects: [],\n      technicalNotes: [],\n      complianceCertificates: [],\n      calculationEngines: [],\n      activeProject: null,\n      selectedTemplate: null,\n\n      isLoading: false,\n      isDarkMode: false,\n      sidebarOpen: false,\n\n      // Actions\n      setUser: (user) => set({ \n        user, \n        isAuthenticated: !!user \n      }),\n\n      setProducts: (products) => {\n        set({ products })\n        get().filterProducts()\n      },\n\n      setSearchQuery: (searchQuery) => {\n        set({ searchQuery })\n        get().filterProducts()\n      },\n\n      setSelectedCategory: (selectedCategory) => {\n        set({ selectedCategory })\n        get().filterProducts()\n      },\n\n      setAlerts: (alerts) => {\n        const unreadAlertsCount = alerts.filter(alert => !alert.is_read).length\n        set({ alerts, unreadAlertsCount })\n      },\n\n      markAlertAsRead: (alertId) => {\n        const alerts = get().alerts.map(alert =>\n          alert.id === alertId ? { ...alert, is_read: true } : alert\n        )\n        const unreadAlertsCount = alerts.filter(alert => !alert.is_read).length\n        set({ alerts, unreadAlertsCount })\n      },\n\n      // Hub d'Information actions\n      setMarketAlerts: (marketAlerts) => {\n        set({ marketAlerts })\n        get().updateLastUpdateTime()\n      },\n\n      addMarketAlert: (alert) => {\n        const marketAlerts = [alert, ...get().marketAlerts].slice(0, 50) // Garder les 50 plus récentes\n        set({ marketAlerts })\n        get().updateLastUpdateTime()\n      },\n\n      setStockUpdates: (stockUpdates) => {\n        set({ stockUpdates })\n        get().updateLastUpdateTime()\n      },\n\n      addStockUpdate: (update) => {\n        const stockUpdates = [update, ...get().stockUpdates].slice(0, 100) // Garder les 100 plus récentes\n        set({ stockUpdates })\n        get().updateLastUpdateTime()\n      },\n\n      setTrainingEvents: (trainingEvents) => set({ trainingEvents }),\n\n      setNewsUpdates: (newsUpdates) => set({ newsUpdates }),\n\n      addNewsUpdate: (news) => {\n        const newsUpdates = [news, ...get().newsUpdates].slice(0, 20) // Garder les 20 plus récentes\n        set({ newsUpdates })\n        get().updateLastUpdateTime()\n      },\n\n      setRealTimeConnected: (realTimeConnected) => set({ realTimeConnected }),\n\n      updateLastUpdateTime: () => set({ lastUpdateTime: new Date().toISOString() }),\n\n      // Espace Conseil Technique actions\n      setTechnicalDocuments: (technicalDocuments) => set({ technicalDocuments }),\n\n      addTechnicalDocument: (document) => {\n        const technicalDocuments = [document, ...get().technicalDocuments]\n        set({ technicalDocuments })\n      },\n\n      updateTechnicalDocument: (id, updates) => {\n        const technicalDocuments = get().technicalDocuments.map(doc =>\n          doc.id === id ? { ...doc, ...updates } : doc\n        )\n        set({ technicalDocuments })\n      },\n\n      setCompatibilityChecks: (compatibilityChecks) => set({ compatibilityChecks }),\n\n      addCompatibilityCheck: (check) => {\n        const compatibilityChecks = [check, ...get().compatibilityChecks]\n        set({ compatibilityChecks })\n      },\n\n      setExpertConsultations: (expertConsultations) => set({ expertConsultations }),\n\n      addExpertConsultation: (consultation) => {\n        const expertConsultations = [consultation, ...get().expertConsultations]\n        set({ expertConsultations })\n      },\n\n      updateExpertConsultation: (id, updates) => {\n        const expertConsultations = get().expertConsultations.map(consult =>\n          consult.id === id ? { ...consult, ...updates } : consult\n        )\n        set({ expertConsultations })\n      },\n\n      setExpertProfiles: (expertProfiles) => set({ expertProfiles }),\n\n      setTechnicalResources: (technicalResources) => set({ technicalResources }),\n\n      setTechnicalReports: (technicalReports) => set({ technicalReports }),\n\n      addTechnicalReport: (report) => {\n        const technicalReports = [report, ...get().technicalReports]\n        set({ technicalReports })\n      },\n\n      setSelectedExpert: (selectedExpert) => set({ selectedExpert }),\n\n      setActiveConsultation: (activeConsultation) => set({ activeConsultation }),\n\n      // Module Prescripteur Professionnel actions\n      setPrescriptionTemplates: (prescriptionTemplates) => set({ prescriptionTemplates }),\n\n      setPrescriptionProjects: (prescriptionProjects) => set({ prescriptionProjects }),\n\n      addPrescriptionProject: (project) => {\n        const prescriptionProjects = [project, ...get().prescriptionProjects]\n        set({ prescriptionProjects })\n      },\n\n      updatePrescriptionProject: (id, updates) => {\n        const prescriptionProjects = get().prescriptionProjects.map(project =>\n          project.id === id ? { ...project, ...updates } : project\n        )\n        set({ prescriptionProjects })\n      },\n\n      setTechnicalNotes: (technicalNotes) => set({ technicalNotes }),\n\n      addTechnicalNote: (note) => {\n        const technicalNotes = [note, ...get().technicalNotes]\n        set({ technicalNotes })\n      },\n\n      setComplianceCertificates: (complianceCertificates) => set({ complianceCertificates }),\n\n      addComplianceCertificate: (certificate) => {\n        const complianceCertificates = [certificate, ...get().complianceCertificates]\n        set({ complianceCertificates })\n      },\n\n      setCalculationEngines: (calculationEngines) => set({ calculationEngines }),\n\n      setActiveProject: (activeProject) => set({ activeProject }),\n\n      setSelectedTemplate: (selectedTemplate) => set({ selectedTemplate }),\n\n      setLoading: (isLoading) => set({ isLoading }),\n\n      toggleDarkMode: () => set((state) => ({ \n        isDarkMode: !state.isDarkMode \n      })),\n\n      toggleSidebar: () => set((state) => ({ \n        sidebarOpen: !state.sidebarOpen \n      })),\n\n      filterProducts: () => {\n        const { products, searchQuery, selectedCategory } = get()\n        \n        let filtered = products\n\n        if (selectedCategory) {\n          filtered = filtered.filter(product => \n            product.category === selectedCategory\n          )\n        }\n\n        if (searchQuery) {\n          const query = searchQuery.toLowerCase()\n          filtered = filtered.filter(product =>\n            product.name.toLowerCase().includes(query) ||\n            product.description.toLowerCase().includes(query) ||\n            product.brand.toLowerCase().includes(query) ||\n            product.model.toLowerCase().includes(query)\n          )\n        }\n\n        set({ filteredProducts: filtered })\n      },\n    }),\n    {\n      name: 'pro-matos-storage',\n      partialize: (state) => ({\n        user: state.user,\n        isAuthenticated: state.isAuthenticated,\n        isDarkMode: state.isDarkMode,\n      }),\n    }\n  )\n)\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AA2HO,MAAM,WAAW,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC3B,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,MAAM;QACN,iBAAiB;QACjB,UAAU,EAAE;QACZ,kBAAkB,EAAE;QACpB,aAAa;QACb,kBAAkB;QAClB,QAAQ,EAAE;QACV,mBAAmB;QAEnB,kCAAkC;QAClC,cAAc,EAAE;QAChB,cAAc,EAAE;QAChB,gBAAgB,EAAE;QAClB,aAAa,EAAE;QACf,mBAAmB;QACnB,gBAAgB,IAAI,OAAO,WAAW;QAEtC,yCAAyC;QACzC,oBAAoB,EAAE;QACtB,qBAAqB,EAAE;QACvB,qBAAqB,EAAE;QACvB,gBAAgB,EAAE;QAClB,oBAAoB,EAAE;QACtB,kBAAkB,EAAE;QACpB,gBAAgB;QAChB,oBAAoB;QAEpB,kDAAkD;QAClD,uBAAuB,EAAE;QACzB,sBAAsB,EAAE;QACxB,gBAAgB,EAAE;QAClB,wBAAwB,EAAE;QAC1B,oBAAoB,EAAE;QACtB,eAAe;QACf,kBAAkB;QAElB,WAAW;QACX,YAAY;QACZ,aAAa;QAEb,UAAU;QACV,SAAS,CAAC,OAAS,IAAI;gBACrB;gBACA,iBAAiB,CAAC,CAAC;YACrB;QAEA,aAAa,CAAC;YACZ,IAAI;gBAAE;YAAS;YACf,MAAM,cAAc;QACtB;QAEA,gBAAgB,CAAC;YACf,IAAI;gBAAE;YAAY;YAClB,MAAM,cAAc;QACtB;QAEA,qBAAqB,CAAC;YACpB,IAAI;gBAAE;YAAiB;YACvB,MAAM,cAAc;QACtB;QAEA,WAAW,CAAC;YACV,MAAM,oBAAoB,OAAO,MAAM,CAAC,CAAA,QAAS,CAAC,MAAM,OAAO,EAAE,MAAM;YACvE,IAAI;gBAAE;gBAAQ;YAAkB;QAClC;QAEA,iBAAiB,CAAC;YAChB,MAAM,SAAS,MAAM,MAAM,CAAC,GAAG,CAAC,CAAA,QAC9B,MAAM,EAAE,KAAK,UAAU;oBAAE,GAAG,KAAK;oBAAE,SAAS;gBAAK,IAAI;YAEvD,MAAM,oBAAoB,OAAO,MAAM,CAAC,CAAA,QAAS,CAAC,MAAM,OAAO,EAAE,MAAM;YACvE,IAAI;gBAAE;gBAAQ;YAAkB;QAClC;QAEA,4BAA4B;QAC5B,iBAAiB,CAAC;YAChB,IAAI;gBAAE;YAAa;YACnB,MAAM,oBAAoB;QAC5B;QAEA,gBAAgB,CAAC;YACf,MAAM,eAAe;gBAAC;mBAAU,MAAM,YAAY;aAAC,CAAC,KAAK,CAAC,GAAG,IAAI,8BAA8B;;YAC/F,IAAI;gBAAE;YAAa;YACnB,MAAM,oBAAoB;QAC5B;QAEA,iBAAiB,CAAC;YAChB,IAAI;gBAAE;YAAa;YACnB,MAAM,oBAAoB;QAC5B;QAEA,gBAAgB,CAAC;YACf,MAAM,eAAe;gBAAC;mBAAW,MAAM,YAAY;aAAC,CAAC,KAAK,CAAC,GAAG,KAAK,+BAA+B;;YAClG,IAAI;gBAAE;YAAa;YACnB,MAAM,oBAAoB;QAC5B;QAEA,mBAAmB,CAAC,iBAAmB,IAAI;gBAAE;YAAe;QAE5D,gBAAgB,CAAC,cAAgB,IAAI;gBAAE;YAAY;QAEnD,eAAe,CAAC;YACd,MAAM,cAAc;gBAAC;mBAAS,MAAM,WAAW;aAAC,CAAC,KAAK,CAAC,GAAG,IAAI,8BAA8B;;YAC5F,IAAI;gBAAE;YAAY;YAClB,MAAM,oBAAoB;QAC5B;QAEA,sBAAsB,CAAC,oBAAsB,IAAI;gBAAE;YAAkB;QAErE,sBAAsB,IAAM,IAAI;gBAAE,gBAAgB,IAAI,OAAO,WAAW;YAAG;QAE3E,mCAAmC;QACnC,uBAAuB,CAAC,qBAAuB,IAAI;gBAAE;YAAmB;QAExE,sBAAsB,CAAC;YACrB,MAAM,qBAAqB;gBAAC;mBAAa,MAAM,kBAAkB;aAAC;YAClE,IAAI;gBAAE;YAAmB;QAC3B;QAEA,yBAAyB,CAAC,IAAI;YAC5B,MAAM,qBAAqB,MAAM,kBAAkB,CAAC,GAAG,CAAC,CAAA,MACtD,IAAI,EAAE,KAAK,KAAK;oBAAE,GAAG,GAAG;oBAAE,GAAG,OAAO;gBAAC,IAAI;YAE3C,IAAI;gBAAE;YAAmB;QAC3B;QAEA,wBAAwB,CAAC,sBAAwB,IAAI;gBAAE;YAAoB;QAE3E,uBAAuB,CAAC;YACtB,MAAM,sBAAsB;gBAAC;mBAAU,MAAM,mBAAmB;aAAC;YACjE,IAAI;gBAAE;YAAoB;QAC5B;QAEA,wBAAwB,CAAC,sBAAwB,IAAI;gBAAE;YAAoB;QAE3E,uBAAuB,CAAC;YACtB,MAAM,sBAAsB;gBAAC;mBAAiB,MAAM,mBAAmB;aAAC;YACxE,IAAI;gBAAE;YAAoB;QAC5B;QAEA,0BAA0B,CAAC,IAAI;YAC7B,MAAM,sBAAsB,MAAM,mBAAmB,CAAC,GAAG,CAAC,CAAA,UACxD,QAAQ,EAAE,KAAK,KAAK;oBAAE,GAAG,OAAO;oBAAE,GAAG,OAAO;gBAAC,IAAI;YAEnD,IAAI;gBAAE;YAAoB;QAC5B;QAEA,mBAAmB,CAAC,iBAAmB,IAAI;gBAAE;YAAe;QAE5D,uBAAuB,CAAC,qBAAuB,IAAI;gBAAE;YAAmB;QAExE,qBAAqB,CAAC,mBAAqB,IAAI;gBAAE;YAAiB;QAElE,oBAAoB,CAAC;YACnB,MAAM,mBAAmB;gBAAC;mBAAW,MAAM,gBAAgB;aAAC;YAC5D,IAAI;gBAAE;YAAiB;QACzB;QAEA,mBAAmB,CAAC,iBAAmB,IAAI;gBAAE;YAAe;QAE5D,uBAAuB,CAAC,qBAAuB,IAAI;gBAAE;YAAmB;QAExE,4CAA4C;QAC5C,0BAA0B,CAAC,wBAA0B,IAAI;gBAAE;YAAsB;QAEjF,yBAAyB,CAAC,uBAAyB,IAAI;gBAAE;YAAqB;QAE9E,wBAAwB,CAAC;YACvB,MAAM,uBAAuB;gBAAC;mBAAY,MAAM,oBAAoB;aAAC;YACrE,IAAI;gBAAE;YAAqB;QAC7B;QAEA,2BAA2B,CAAC,IAAI;YAC9B,MAAM,uBAAuB,MAAM,oBAAoB,CAAC,GAAG,CAAC,CAAA,UAC1D,QAAQ,EAAE,KAAK,KAAK;oBAAE,GAAG,OAAO;oBAAE,GAAG,OAAO;gBAAC,IAAI;YAEnD,IAAI;gBAAE;YAAqB;QAC7B;QAEA,mBAAmB,CAAC,iBAAmB,IAAI;gBAAE;YAAe;QAE5D,kBAAkB,CAAC;YACjB,MAAM,iBAAiB;gBAAC;mBAAS,MAAM,cAAc;aAAC;YACtD,IAAI;gBAAE;YAAe;QACvB;QAEA,2BAA2B,CAAC,yBAA2B,IAAI;gBAAE;YAAuB;QAEpF,0BAA0B,CAAC;YACzB,MAAM,yBAAyB;gBAAC;mBAAgB,MAAM,sBAAsB;aAAC;YAC7E,IAAI;gBAAE;YAAuB;QAC/B;QAEA,uBAAuB,CAAC,qBAAuB,IAAI;gBAAE;YAAmB;QAExE,kBAAkB,CAAC,gBAAkB,IAAI;gBAAE;YAAc;QAEzD,qBAAqB,CAAC,mBAAqB,IAAI;gBAAE;YAAiB;QAElE,YAAY,CAAC,YAAc,IAAI;gBAAE;YAAU;QAE3C,gBAAgB,IAAM,IAAI,CAAC,QAAU,CAAC;oBACpC,YAAY,CAAC,MAAM,UAAU;gBAC/B,CAAC;QAED,eAAe,IAAM,IAAI,CAAC,QAAU,CAAC;oBACnC,aAAa,CAAC,MAAM,WAAW;gBACjC,CAAC;QAED,gBAAgB;YACd,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,gBAAgB,EAAE,GAAG;YAEpD,IAAI,WAAW;YAEf,IAAI,kBAAkB;gBACpB,WAAW,SAAS,MAAM,CAAC,CAAA,UACzB,QAAQ,QAAQ,KAAK;YAEzB;YAEA,IAAI,aAAa;gBACf,MAAM,QAAQ,YAAY,WAAW;gBACrC,WAAW,SAAS,MAAM,CAAC,CAAA,UACzB,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,UACpC,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,UAC3C,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,UACrC,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC;YAEzC;YAEA,IAAI;gBAAE,kBAAkB;YAAS;QACnC;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,iBAAiB,MAAM,eAAe;YACtC,YAAY,MAAM,UAAU;QAC9B,CAAC;AACH", "debugId": null}}, {"offset": {"line": 356, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/services/prescriptorService.ts"], "sourcesContent": ["import { \n  PrescriptionTemplate, \n  PrescriptionProject, \n  TechnicalNote, \n  ComplianceCertificate, \n  CalculationEngine,\n  CalculationParameter,\n  ValidationRule,\n  WarrantyContract,\n  Product \n} from '@/lib/supabase'\n\n// Service pour le Module Prescripteur Professionnel\nexport class PrescriptorService {\n  \n  // Simulation des templates de prescription\n  static generatePrescriptionTemplates(): PrescriptionTemplate[] {\n    return [\n      {\n        id: 'template_001',\n        name: 'Installation Électrique Tertiaire',\n        description: 'Template complet pour installations électriques de bâtiments tertiaires selon NF C 15-100',\n        category: 'electrical',\n        target_audience: 'engineer',\n        complexity_level: 'intermediate',\n        template_data: {\n          sections: ['Analyse des besoins', 'Calculs de puissance', 'Schémas unifilaires', 'Liste du matériel'],\n          default_values: { tension: 400, frequence: 50, facteur_puissance: 0.8 }\n        },\n        required_fields: ['surface_totale', 'nombre_postes_travail', 'puissance_eclairage', 'puissance_prises'],\n        optional_fields: ['climatisation', 'ascenseurs', 'parking'],\n        calculations: [\n          {\n            id: 'calc_001',\n            name: 'Puissance totale installée',\n            formula: 'P_total = P_eclairage + P_prises + P_force + P_climatisation',\n            variables: { P_eclairage: 'number', P_prises: 'number', P_force: 'number', P_climatisation: 'number' },\n            unit: 'kW',\n            validation_rules: ['P_total > 0', 'P_total < 1000'],\n            safety_factors: { simultaneite: 0.8, reserve: 1.2 }\n          }\n        ],\n        compliance_standards: ['NF C 15-100', 'RT 2012', 'Accessibilité PMR'],\n        membership_required: 'silver',\n        is_featured: true,\n        usage_count: 247,\n        rating: 4.8,\n        created_by: 'Pro Matos Team',\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString(),\n      },\n      {\n        id: 'template_002',\n        name: 'Installation Photovoltaïque Résidentielle',\n        description: 'Dimensionnement et prescription pour installations PV résidentielles avec stockage',\n        category: 'energy',\n        target_audience: 'engineer',\n        complexity_level: 'advanced',\n        template_data: {\n          sections: ['Étude d\\'ensoleillement', 'Dimensionnement PV', 'Stockage batterie', 'Raccordement réseau'],\n          default_values: { irradiation_annuelle: 1800, rendement_onduleur: 0.95, degradation_annuelle: 0.005 }\n        },\n        required_fields: ['consommation_annuelle', 'surface_toiture', 'orientation', 'inclinaison'],\n        optional_fields: ['masques_solaires', 'stockage_souhaite', 'injection_reseau'],\n        calculations: [\n          {\n            id: 'calc_002',\n            name: 'Production annuelle estimée',\n            formula: 'E_prod = P_crete * irradiation * rendement_systeme * (1 - degradation)^annees',\n            variables: { P_crete: 'number', irradiation: 'number', rendement_systeme: 'number', degradation: 'number', annees: 'number' },\n            unit: 'kWh/an',\n            validation_rules: ['P_crete > 0', 'irradiation > 800', 'rendement_systeme < 1'],\n            safety_factors: { meteo: 0.9, vieillissement: 0.95 }\n          }\n        ],\n        compliance_standards: ['NF C 15-100', 'UTE C 15-712-1', 'Arrêté tarifaire'],\n        membership_required: 'gold',\n        is_featured: true,\n        usage_count: 189,\n        rating: 4.9,\n        created_by: 'Dr. Aminata Traoré',\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString(),\n      },\n      {\n        id: 'template_003',\n        name: 'Automatisme Industriel',\n        description: 'Prescription d\\'automatismes pour lignes de production industrielle',\n        category: 'automation',\n        target_audience: 'engineer',\n        complexity_level: 'expert',\n        template_data: {\n          sections: ['Analyse fonctionnelle', 'Architecture automate', 'Réseaux communication', 'Supervision'],\n          default_values: { cycle_time: 100, safety_level: 'SIL2', communication: 'Ethernet' }\n        },\n        required_fields: ['nombre_entrees', 'nombre_sorties', 'vitesse_production', 'niveau_securite'],\n        optional_fields: ['vision_industrielle', 'robotique', 'traçabilite'],\n        calculations: [\n          {\n            id: 'calc_003',\n            name: 'Temps de cycle automate',\n            formula: 'T_cycle = (N_entrees * T_lecture + N_sorties * T_ecriture + T_traitement) * facteur_securite',\n            variables: { N_entrees: 'number', T_lecture: 'number', N_sorties: 'number', T_ecriture: 'number', T_traitement: 'number', facteur_securite: 'number' },\n            unit: 'ms',\n            validation_rules: ['T_cycle < 100', 'T_cycle > 1'],\n            safety_factors: { marge_calcul: 1.5, reserve_memoire: 2.0 }\n          }\n        ],\n        compliance_standards: ['IEC 61131', 'IEC 61508', 'Directive Machines'],\n        membership_required: 'platinum',\n        is_featured: false,\n        usage_count: 67,\n        rating: 4.7,\n        created_by: 'Ing. Jean-Baptiste Kone',\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString(),\n      }\n    ]\n  }\n\n  // Simulation des projets de prescription\n  static generatePrescriptionProjects(): PrescriptionProject[] {\n    return [\n      {\n        id: 'project_001',\n        user_id: 'user_001',\n        template_id: 'template_001',\n        project_name: 'Immeuble de bureaux Plateau - Abidjan',\n        client_name: 'SODECI Immobilier',\n        client_company: 'SODECI',\n        project_description: 'Installation électrique complète pour immeuble de bureaux 8 étages',\n        location: 'Plateau, Abidjan, Côte d\\'Ivoire',\n        project_data: {\n          surface_totale: 4500,\n          nombre_etages: 8,\n          nombre_postes_travail: 180,\n          puissance_eclairage: 45,\n          puissance_prises: 72,\n          climatisation: true,\n          ascenseurs: 2\n        },\n        calculated_values: {\n          puissance_totale: 285.6,\n          courant_nominal: 410,\n          section_cable_principal: 185,\n          nombre_tableaux: 9\n        },\n        selected_products: ['prod_001', 'prod_002', 'prod_003'],\n        total_cost: 45000000, // 45M FCFA\n        status: 'in_progress',\n        compliance_verified: true,\n        warranty_terms: 'Garantie 2 ans pièces et main d\\'œuvre',\n        delivery_date: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString(), // Dans 90 jours\n        notes: 'Projet prioritaire - Livraison Q2 2024',\n        created_at: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(), // Il y a 15 jours\n        updated_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // Il y a 2 jours\n      },\n      {\n        id: 'project_002',\n        user_id: 'user_002',\n        template_id: 'template_002',\n        project_name: 'Villa solaire Almadies - Dakar',\n        client_name: 'Famille Diop',\n        client_company: 'Particulier',\n        project_description: 'Installation photovoltaïque 15kWc avec stockage batterie lithium',\n        location: 'Almadies, Dakar, Sénégal',\n        project_data: {\n          consommation_annuelle: 8500,\n          surface_toiture: 120,\n          orientation: 'Sud',\n          inclinaison: 15,\n          stockage_souhaite: true,\n          capacite_batterie: 30\n        },\n        calculated_values: {\n          puissance_crete: 15.2,\n          production_annuelle: 24680,\n          autonomie_jours: 3.5,\n          taux_autoconsommation: 85\n        },\n        selected_products: ['prod_pv_001', 'prod_bat_001', 'prod_ond_001'],\n        total_cost: 18500000, // 18.5M FCFA\n        status: 'approved',\n        compliance_verified: true,\n        warranty_terms: 'Garantie 10 ans panneaux, 5 ans onduleur, 8 ans batteries',\n        delivery_date: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000).toISOString(), // Dans 45 jours\n        notes: 'Installation avec monitoring IoT inclus',\n        created_at: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000).toISOString(), // Il y a 8 jours\n        updated_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), // Il y a 1 jour\n      }\n    ]\n  }\n\n  // Simulation des moteurs de calcul\n  static generateCalculationEngines(): CalculationEngine[] {\n    return [\n      {\n        id: 'calc_engine_001',\n        name: 'Dimensionnement Câbles BT',\n        description: 'Calcul automatique des sections de câbles basse tension selon NF C 15-100',\n        category: 'cable_sizing',\n        input_parameters: [\n          { name: 'courant', type: 'number', unit: 'A', min_value: 1, max_value: 1000, required: true, description: 'Courant nominal du circuit' },\n          { name: 'longueur', type: 'number', unit: 'm', min_value: 1, max_value: 500, required: true, description: 'Longueur du câble' },\n          { name: 'chute_tension_max', type: 'number', unit: '%', default_value: 3, required: true, description: 'Chute de tension maximale admissible' },\n          { name: 'mode_pose', type: 'select', options: ['enterré', 'aérien', 'goulotte', 'chemin_cables'], required: true, description: 'Mode de pose du câble' }\n        ],\n        output_parameters: [\n          { name: 'section_minimale', type: 'number', unit: 'mm²', required: true, description: 'Section minimale calculée' },\n          { name: 'section_normalisee', type: 'number', unit: 'mm²', required: true, description: 'Section normalisée supérieure' },\n          { name: 'chute_tension_reelle', type: 'number', unit: '%', required: true, description: 'Chute de tension réelle' }\n        ],\n        formula_set: {\n          'section_minimale': 'S = (ρ * L * I) / (U * ΔU_max)',\n          'chute_tension': 'ΔU = (ρ * L * I) / (S * U)'\n        },\n        validation_rules: [\n          { condition: 'chute_tension_reelle <= chute_tension_max', message: 'Chute de tension respectée', severity: 'info' },\n          { condition: 'section_normalisee >= section_minimale', message: 'Section normalisée suffisante', severity: 'info' },\n          { condition: 'courant <= intensite_admissible', message: 'Intensité admissible dépassée', severity: 'error' }\n        ],\n        safety_standards: ['NF C 15-100', 'IEC 60364'],\n        is_certified: true,\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString(),\n      }\n    ]\n  }\n\n  // Simulation des notes techniques\n  static generateTechnicalNotes(projectId: string): TechnicalNote[] {\n    return [\n      {\n        id: 'note_001',\n        project_id: projectId,\n        title: 'Calcul de la puissance totale installée',\n        content: 'La puissance totale installée a été calculée en tenant compte des facteurs de simultanéité et des coefficients de sécurité selon la norme NF C 15-100.',\n        note_type: 'calculation',\n        auto_generated: true,\n        template_section: 'Calculs de puissance',\n        products_referenced: ['prod_001'],\n        standards_referenced: ['NF C 15-100'],\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString(),\n      },\n      {\n        id: 'note_002',\n        project_id: projectId,\n        title: 'Recommandations de mise en œuvre',\n        content: 'Il est recommandé d\\'installer une protection différentielle 30mA sur tous les circuits prises et éclairage selon l\\'article 411.3.3 de la NF C 15-100.',\n        note_type: 'recommendation',\n        auto_generated: false,\n        template_section: 'Spécifications techniques',\n        products_referenced: ['prod_002'],\n        standards_referenced: ['NF C 15-100'],\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString(),\n      }\n    ]\n  }\n\n  // Méthode pour calculer automatiquement les valeurs d'un projet\n  static calculateProjectValues(projectData: Record<string, any>, template: PrescriptionTemplate): Record<string, any> {\n    const results: Record<string, any> = {}\n    \n    // Simulation de calculs basés sur le template\n    if (template.id === 'template_001') {\n      // Calculs pour installation électrique tertiaire\n      const surface = projectData.surface_totale || 0\n      const postes = projectData.nombre_postes_travail || 0\n      \n      results.puissance_eclairage = surface * 10 // 10W/m²\n      results.puissance_prises = postes * 400 // 400W/poste\n      results.puissance_totale = results.puissance_eclairage + results.puissance_prises\n      results.courant_nominal = results.puissance_totale / (400 * Math.sqrt(3) * 0.8) // Triphasé 400V\n      results.section_cable_principal = this.calculateCableSection(results.courant_nominal)\n    }\n    \n    return results\n  }\n\n  // Méthode utilitaire pour calculer la section de câble\n  private static calculateCableSection(current: number): number {\n    const sections = [1.5, 2.5, 4, 6, 10, 16, 25, 35, 50, 70, 95, 120, 150, 185, 240, 300]\n    const intensities = [16, 24, 32, 41, 57, 76, 101, 129, 157, 196, 246, 284, 319, 353, 419, 483]\n    \n    for (let i = 0; i < intensities.length; i++) {\n      if (current <= intensities[i]) {\n        return sections[i]\n      }\n    }\n    \n    return sections[sections.length - 1] // Section maximale si dépassement\n  }\n\n  // Méthode pour générer un certificat de conformité\n  static generateComplianceCertificate(project: PrescriptionProject): ComplianceCertificate {\n    return {\n      id: `cert_${Date.now()}`,\n      project_id: project.id,\n      certificate_type: 'design',\n      certificate_number: `PMO-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`,\n      issued_by: 'Pro Matos Afrique Ouest - Bureau d\\'Études Certifié',\n      issued_to: `${project.client_name} - ${project.client_company}`,\n      valid_from: new Date().toISOString(),\n      valid_until: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(), // 1 an\n      standards_covered: ['NF C 15-100', 'RT 2012'],\n      test_results: {\n        'Calculs de puissance': 'Conforme',\n        'Dimensionnement câbles': 'Conforme',\n        'Protection différentielle': 'Conforme'\n      },\n      limitations: ['Valable uniquement pour la conception présentée', 'Révision nécessaire en cas de modification'],\n      digital_signature: 'SHA256:a1b2c3d4e5f6...',\n      qr_verification: `https://promatos.com/verify/${Date.now()}`,\n      is_active: true,\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString(),\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAaO,MAAM;IAEX,2CAA2C;IAC3C,OAAO,gCAAwD;QAC7D,OAAO;YACL;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,UAAU;gBACV,iBAAiB;gBACjB,kBAAkB;gBAClB,eAAe;oBACb,UAAU;wBAAC;wBAAuB;wBAAwB;wBAAuB;qBAAoB;oBACrG,gBAAgB;wBAAE,SAAS;wBAAK,WAAW;wBAAI,mBAAmB;oBAAI;gBACxE;gBACA,iBAAiB;oBAAC;oBAAkB;oBAAyB;oBAAuB;iBAAmB;gBACvG,iBAAiB;oBAAC;oBAAiB;oBAAc;iBAAU;gBAC3D,cAAc;oBACZ;wBACE,IAAI;wBACJ,MAAM;wBACN,SAAS;wBACT,WAAW;4BAAE,aAAa;4BAAU,UAAU;4BAAU,SAAS;4BAAU,iBAAiB;wBAAS;wBACrG,MAAM;wBACN,kBAAkB;4BAAC;4BAAe;yBAAiB;wBACnD,gBAAgB;4BAAE,cAAc;4BAAK,SAAS;wBAAI;oBACpD;iBACD;gBACD,sBAAsB;oBAAC;oBAAe;oBAAW;iBAAoB;gBACrE,qBAAqB;gBACrB,aAAa;gBACb,aAAa;gBACb,QAAQ;gBACR,YAAY;gBACZ,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,UAAU;gBACV,iBAAiB;gBACjB,kBAAkB;gBAClB,eAAe;oBACb,UAAU;wBAAC;wBAA2B;wBAAsB;wBAAqB;qBAAsB;oBACvG,gBAAgB;wBAAE,sBAAsB;wBAAM,oBAAoB;wBAAM,sBAAsB;oBAAM;gBACtG;gBACA,iBAAiB;oBAAC;oBAAyB;oBAAmB;oBAAe;iBAAc;gBAC3F,iBAAiB;oBAAC;oBAAoB;oBAAqB;iBAAmB;gBAC9E,cAAc;oBACZ;wBACE,IAAI;wBACJ,MAAM;wBACN,SAAS;wBACT,WAAW;4BAAE,SAAS;4BAAU,aAAa;4BAAU,mBAAmB;4BAAU,aAAa;4BAAU,QAAQ;wBAAS;wBAC5H,MAAM;wBACN,kBAAkB;4BAAC;4BAAe;4BAAqB;yBAAwB;wBAC/E,gBAAgB;4BAAE,OAAO;4BAAK,gBAAgB;wBAAK;oBACrD;iBACD;gBACD,sBAAsB;oBAAC;oBAAe;oBAAkB;iBAAmB;gBAC3E,qBAAqB;gBACrB,aAAa;gBACb,aAAa;gBACb,QAAQ;gBACR,YAAY;gBACZ,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,UAAU;gBACV,iBAAiB;gBACjB,kBAAkB;gBAClB,eAAe;oBACb,UAAU;wBAAC;wBAAyB;wBAAyB;wBAAyB;qBAAc;oBACpG,gBAAgB;wBAAE,YAAY;wBAAK,cAAc;wBAAQ,eAAe;oBAAW;gBACrF;gBACA,iBAAiB;oBAAC;oBAAkB;oBAAkB;oBAAsB;iBAAkB;gBAC9F,iBAAiB;oBAAC;oBAAuB;oBAAa;iBAAc;gBACpE,cAAc;oBACZ;wBACE,IAAI;wBACJ,MAAM;wBACN,SAAS;wBACT,WAAW;4BAAE,WAAW;4BAAU,WAAW;4BAAU,WAAW;4BAAU,YAAY;4BAAU,cAAc;4BAAU,kBAAkB;wBAAS;wBACrJ,MAAM;wBACN,kBAAkB;4BAAC;4BAAiB;yBAAc;wBAClD,gBAAgB;4BAAE,cAAc;4BAAK,iBAAiB;wBAAI;oBAC5D;iBACD;gBACD,sBAAsB;oBAAC;oBAAa;oBAAa;iBAAqB;gBACtE,qBAAqB;gBACrB,aAAa;gBACb,aAAa;gBACb,QAAQ;gBACR,YAAY;gBACZ,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;IACH;IAEA,yCAAyC;IACzC,OAAO,+BAAsD;QAC3D,OAAO;YACL;gBACE,IAAI;gBACJ,SAAS;gBACT,aAAa;gBACb,cAAc;gBACd,aAAa;gBACb,gBAAgB;gBAChB,qBAAqB;gBACrB,UAAU;gBACV,cAAc;oBACZ,gBAAgB;oBAChB,eAAe;oBACf,uBAAuB;oBACvB,qBAAqB;oBACrB,kBAAkB;oBAClB,eAAe;oBACf,YAAY;gBACd;gBACA,mBAAmB;oBACjB,kBAAkB;oBAClB,iBAAiB;oBACjB,yBAAyB;oBACzB,iBAAiB;gBACnB;gBACA,mBAAmB;oBAAC;oBAAY;oBAAY;iBAAW;gBACvD,YAAY;gBACZ,QAAQ;gBACR,qBAAqB;gBACrB,gBAAgB;gBAChB,eAAe,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW;gBAC1E,OAAO;gBACP,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW;gBACvE,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;YACxE;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,aAAa;gBACb,cAAc;gBACd,aAAa;gBACb,gBAAgB;gBAChB,qBAAqB;gBACrB,UAAU;gBACV,cAAc;oBACZ,uBAAuB;oBACvB,iBAAiB;oBACjB,aAAa;oBACb,aAAa;oBACb,mBAAmB;oBACnB,mBAAmB;gBACrB;gBACA,mBAAmB;oBACjB,iBAAiB;oBACjB,qBAAqB;oBACrB,iBAAiB;oBACjB,uBAAuB;gBACzB;gBACA,mBAAmB;oBAAC;oBAAe;oBAAgB;iBAAe;gBAClE,YAAY;gBACZ,QAAQ;gBACR,qBAAqB;gBACrB,gBAAgB;gBAChB,eAAe,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW;gBAC1E,OAAO;gBACP,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;gBACtE,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;YACxE;SACD;IACH;IAEA,mCAAmC;IACnC,OAAO,6BAAkD;QACvD,OAAO;YACL;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,UAAU;gBACV,kBAAkB;oBAChB;wBAAE,MAAM;wBAAW,MAAM;wBAAU,MAAM;wBAAK,WAAW;wBAAG,WAAW;wBAAM,UAAU;wBAAM,aAAa;oBAA6B;oBACvI;wBAAE,MAAM;wBAAY,MAAM;wBAAU,MAAM;wBAAK,WAAW;wBAAG,WAAW;wBAAK,UAAU;wBAAM,aAAa;oBAAoB;oBAC9H;wBAAE,MAAM;wBAAqB,MAAM;wBAAU,MAAM;wBAAK,eAAe;wBAAG,UAAU;wBAAM,aAAa;oBAAuC;oBAC9I;wBAAE,MAAM;wBAAa,MAAM;wBAAU,SAAS;4BAAC;4BAAW;4BAAU;4BAAY;yBAAgB;wBAAE,UAAU;wBAAM,aAAa;oBAAwB;iBACxJ;gBACD,mBAAmB;oBACjB;wBAAE,MAAM;wBAAoB,MAAM;wBAAU,MAAM;wBAAO,UAAU;wBAAM,aAAa;oBAA4B;oBAClH;wBAAE,MAAM;wBAAsB,MAAM;wBAAU,MAAM;wBAAO,UAAU;wBAAM,aAAa;oBAAgC;oBACxH;wBAAE,MAAM;wBAAwB,MAAM;wBAAU,MAAM;wBAAK,UAAU;wBAAM,aAAa;oBAA0B;iBACnH;gBACD,aAAa;oBACX,oBAAoB;oBACpB,iBAAiB;gBACnB;gBACA,kBAAkB;oBAChB;wBAAE,WAAW;wBAA6C,SAAS;wBAA8B,UAAU;oBAAO;oBAClH;wBAAE,WAAW;wBAA0C,SAAS;wBAAiC,UAAU;oBAAO;oBAClH;wBAAE,WAAW;wBAAmC,SAAS;wBAAiC,UAAU;oBAAQ;iBAC7G;gBACD,kBAAkB;oBAAC;oBAAe;iBAAY;gBAC9C,cAAc;gBACd,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;IACH;IAEA,kCAAkC;IAClC,OAAO,uBAAuB,SAAiB,EAAmB;QAChE,OAAO;YACL;gBACE,IAAI;gBACJ,YAAY;gBACZ,OAAO;gBACP,SAAS;gBACT,WAAW;gBACX,gBAAgB;gBAChB,kBAAkB;gBAClB,qBAAqB;oBAAC;iBAAW;gBACjC,sBAAsB;oBAAC;iBAAc;gBACrC,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,YAAY;gBACZ,OAAO;gBACP,SAAS;gBACT,WAAW;gBACX,gBAAgB;gBAChB,kBAAkB;gBAClB,qBAAqB;oBAAC;iBAAW;gBACjC,sBAAsB;oBAAC;iBAAc;gBACrC,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;IACH;IAEA,gEAAgE;IAChE,OAAO,uBAAuB,WAAgC,EAAE,QAA8B,EAAuB;QACnH,MAAM,UAA+B,CAAC;QAEtC,8CAA8C;QAC9C,IAAI,SAAS,EAAE,KAAK,gBAAgB;YAClC,iDAAiD;YACjD,MAAM,UAAU,YAAY,cAAc,IAAI;YAC9C,MAAM,SAAS,YAAY,qBAAqB,IAAI;YAEpD,QAAQ,mBAAmB,GAAG,UAAU,GAAG,SAAS;;YACpD,QAAQ,gBAAgB,GAAG,SAAS,IAAI,aAAa;;YACrD,QAAQ,gBAAgB,GAAG,QAAQ,mBAAmB,GAAG,QAAQ,gBAAgB;YACjF,QAAQ,eAAe,GAAG,QAAQ,gBAAgB,GAAG,CAAC,MAAM,KAAK,IAAI,CAAC,KAAK,GAAG,EAAE,gBAAgB;;YAChG,QAAQ,uBAAuB,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,eAAe;QACtF;QAEA,OAAO;IACT;IAEA,uDAAuD;IACvD,OAAe,sBAAsB,OAAe,EAAU;QAC5D,MAAM,WAAW;YAAC;YAAK;YAAK;YAAG;YAAG;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAK;YAAK;YAAK;YAAK;SAAI;QACtF,MAAM,cAAc;YAAC;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;SAAI;QAE9F,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,MAAM,EAAE,IAAK;YAC3C,IAAI,WAAW,WAAW,CAAC,EAAE,EAAE;gBAC7B,OAAO,QAAQ,CAAC,EAAE;YACpB;QACF;QAEA,OAAO,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE,CAAC,kCAAkC;;IACzE;IAEA,mDAAmD;IACnD,OAAO,8BAA8B,OAA4B,EAAyB;QACxF,OAAO;YACL,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;YACxB,YAAY,QAAQ,EAAE;YACtB,kBAAkB;YAClB,oBAAoB,CAAC,IAAI,EAAE,IAAI,OAAO,WAAW,GAAG,CAAC,EAAE,OAAO,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC,IAAI;YACrF,WAAW;YACX,WAAW,GAAG,QAAQ,WAAW,CAAC,GAAG,EAAE,QAAQ,cAAc,EAAE;YAC/D,YAAY,IAAI,OAAO,WAAW;YAClC,aAAa,IAAI,KAAK,KAAK,GAAG,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM,WAAW;YACzE,mBAAmB;gBAAC;gBAAe;aAAU;YAC7C,cAAc;gBACZ,wBAAwB;gBACxB,0BAA0B;gBAC1B,6BAA6B;YAC/B;YACA,aAAa;gBAAC;gBAAmD;aAA6C;YAC9G,mBAAmB;YACnB,iBAAiB,CAAC,4BAA4B,EAAE,KAAK,GAAG,IAAI;YAC5D,WAAW;YACX,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY,IAAI,OAAO,WAAW;QACpC;IACF;AACF", "debugId": null}}, {"offset": {"line": 891, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPrice(price: number): string {\n  return new Intl.NumberFormat('fr-FR', {\n    style: 'currency',\n    currency: 'XOF',\n    minimumFractionDigits: 0,\n  }).format(price)\n}\n\nexport function formatDate(date: string | Date): string {\n  return new Intl.DateTimeFormat('fr-FR', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(new Date(date))\n}\n\nexport function getMembershipColor(level: string): string {\n  switch (level) {\n    case 'bronze':\n      return 'text-amber-600 bg-amber-50'\n    case 'silver':\n      return 'text-gray-600 bg-gray-50'\n    case 'gold':\n      return 'text-yellow-600 bg-yellow-50'\n    case 'platinum':\n      return 'text-purple-600 bg-purple-50'\n    default:\n      return 'text-gray-600 bg-gray-50'\n  }\n}\n\nexport function generateQRCode(productId: string): string {\n  // Simulation d'un QR code - à remplacer par une vraie génération\n  return `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(productId)}`\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa;IACvC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,mBAAmB,KAAa;IAC9C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,eAAe,SAAiB;IAC9C,iEAAiE;IACjE,OAAO,CAAC,8DAA8D,EAAE,mBAAmB,YAAY;AACzG;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 953, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/components/prescriptor/QuickCalculator.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { motion } from 'framer-motion'\nimport { \n  Calculator,\n  Zap,\n  AlertTriangle,\n  CheckCircle,\n  Info,\n  Download,\n  Copy,\n  RefreshCw\n} from 'lucide-react'\n\ninterface CalculationResult {\n  section_minimale: number\n  section_normalisee: number\n  chute_tension_reelle: number\n  intensite_admissible: number\n  validation_status: 'valid' | 'warning' | 'error'\n  recommendations: string[]\n  warnings: string[]\n}\n\nexport default function QuickCalculator() {\n  const [inputs, setInputs] = useState({\n    courant: 32,\n    longueur: 50,\n    chute_tension_max: 3,\n    mode_pose: 'enterré',\n    materiau: 'cuivre',\n    temperature: 30\n  })\n  \n  const [result, setResult] = useState<CalculationResult | null>(null)\n  const [isCalculating, setIsCalculating] = useState(false)\n  const [showDetails, setShowDetails] = useState(false)\n\n  // Sections normalisées en mm²\n  const sectionsNormalisees = [1.5, 2.5, 4, 6, 10, 16, 25, 35, 50, 70, 95, 120, 150, 185, 240, 300, 400, 500]\n  \n  // Intensités admissibles selon le mode de pose (simplifié)\n  const intensitesAdmissibles = {\n    'enterré': [23, 31, 42, 54, 75, 100, 133, 171, 207, 258, 319, 367, 412, 456, 542, 621, 689, 754],\n    'aérien': [18, 24, 32, 41, 57, 76, 101, 129, 157, 196, 246, 284, 319, 353, 419, 483, 540, 593],\n    'goulotte': [15, 20, 27, 35, 48, 64, 85, 108, 132, 165, 207, 239, 268, 297, 352, 406, 454, 498],\n    'chemin_cables': [20, 27, 36, 46, 64, 85, 113, 145, 176, 220, 275, 317, 356, 394, 467, 538, 601, 660]\n  }\n\n  const calculateCableSection = () => {\n    setIsCalculating(true)\n    \n    // Simulation de calcul avec délai\n    setTimeout(() => {\n      const { courant, longueur, chute_tension_max, mode_pose, materiau, temperature } = inputs\n      \n      // Résistivité du matériau (Ω.mm²/m)\n      const resistivite = materiau === 'cuivre' ? 0.0225 : 0.037 // aluminium\n      \n      // Tension nominale (V)\n      const tension = 400 // Triphasé\n      \n      // Calcul de la section minimale pour la chute de tension\n      // ΔU = ρ × L × I / S  =>  S = ρ × L × I / ΔU\n      const chute_tension_absolue = (tension * chute_tension_max) / 100\n      const section_minimale_chute = (resistivite * longueur * courant) / chute_tension_absolue\n      \n      // Facteur de correction température\n      const facteur_temperature = temperature > 30 ? 0.87 : 1.0\n      \n      // Section minimale pour l'intensité admissible\n      const intensites = intensitesAdmissibles[mode_pose as keyof typeof intensitesAdmissibles]\n      let section_minimale_intensite = sectionsNormalisees[0]\n      \n      for (let i = 0; i < intensites.length; i++) {\n        if ((intensites[i] * facteur_temperature) >= courant) {\n          section_minimale_intensite = sectionsNormalisees[i]\n          break\n        }\n      }\n      \n      // Section minimale = max des deux critères\n      const section_minimale = Math.max(section_minimale_chute, section_minimale_intensite)\n      \n      // Section normalisée supérieure\n      let section_normalisee = sectionsNormalisees[sectionsNormalisees.length - 1]\n      for (const section of sectionsNormalisees) {\n        if (section >= section_minimale) {\n          section_normalisee = section\n          break\n        }\n      }\n      \n      // Chute de tension réelle avec la section normalisée\n      const chute_tension_reelle = (resistivite * longueur * courant) / (section_normalisee * tension) * 100\n      \n      // Intensité admissible de la section choisie\n      const index_section = sectionsNormalisees.indexOf(section_normalisee)\n      const intensite_admissible = intensites[index_section] * facteur_temperature\n      \n      // Validation et recommandations\n      let validation_status: 'valid' | 'warning' | 'error' = 'valid'\n      const recommendations: string[] = []\n      const warnings: string[] = []\n      \n      if (chute_tension_reelle > chute_tension_max) {\n        validation_status = 'error'\n        warnings.push(`Chute de tension dépassée: ${chute_tension_reelle.toFixed(2)}% > ${chute_tension_max}%`)\n      } else if (chute_tension_reelle > chute_tension_max * 0.8) {\n        validation_status = 'warning'\n        warnings.push(`Chute de tension proche de la limite: ${chute_tension_reelle.toFixed(2)}%`)\n      }\n      \n      if (intensite_admissible < courant * 1.1) {\n        validation_status = 'warning'\n        warnings.push('Marge de sécurité faible sur l\\'intensité admissible')\n      }\n      \n      // Recommandations\n      if (longueur > 100) {\n        recommendations.push('Considérer un câble de section supérieure pour les grandes longueurs')\n      }\n      \n      if (mode_pose === 'enterré') {\n        recommendations.push('Prévoir une protection mécanique adaptée')\n      }\n      \n      recommendations.push(`Vérifier la compatibilité avec les bornes (section max: ${section_normalisee}mm²)`)\n      \n      setResult({\n        section_minimale: Math.round(section_minimale * 100) / 100,\n        section_normalisee,\n        chute_tension_reelle: Math.round(chute_tension_reelle * 100) / 100,\n        intensite_admissible: Math.round(intensite_admissible),\n        validation_status,\n        recommendations,\n        warnings\n      })\n      \n      setIsCalculating(false)\n    }, 1500)\n  }\n\n  const resetCalculator = () => {\n    setInputs({\n      courant: 32,\n      longueur: 50,\n      chute_tension_max: 3,\n      mode_pose: 'enterré',\n      materiau: 'cuivre',\n      temperature: 30\n    })\n    setResult(null)\n    setShowDetails(false)\n  }\n\n  const exportResult = () => {\n    if (!result) return\n    \n    const reportContent = `\nCALCUL DE SECTION DE CÂBLE - Pro Matos Afrique Ouest\n=====================================================\n\nPARAMÈTRES D'ENTRÉE:\n- Courant nominal: ${inputs.courant} A\n- Longueur: ${inputs.longueur} m\n- Chute de tension max: ${inputs.chute_tension_max} %\n- Mode de pose: ${inputs.mode_pose}\n- Matériau: ${inputs.materiau}\n- Température ambiante: ${inputs.temperature} °C\n\nRÉSULTATS:\n- Section minimale calculée: ${result.section_minimale} mm²\n- Section normalisée: ${result.section_normalisee} mm²\n- Chute de tension réelle: ${result.chute_tension_reelle} %\n- Intensité admissible: ${result.intensite_admissible} A\n\nRECOMMANDATIONS:\n${result.recommendations.map(r => `- ${r}`).join('\\n')}\n\n${result.warnings.length > 0 ? `AVERTISSEMENTS:\\n${result.warnings.map(w => `- ${w}`).join('\\n')}` : ''}\n\nCalcul effectué selon la norme NF C 15-100\nGénéré le ${new Date().toLocaleString('fr-FR')}\n    `.trim()\n    \n    const blob = new Blob([reportContent], { type: 'text/plain' })\n    const url = URL.createObjectURL(blob)\n    const a = document.createElement('a')\n    a.href = url\n    a.download = `calcul-section-cable-${Date.now()}.txt`\n    document.body.appendChild(a)\n    a.click()\n    document.body.removeChild(a)\n    URL.revokeObjectURL(url)\n  }\n\n  const copyResult = () => {\n    if (!result) return\n    \n    const text = `Section câble: ${result.section_normalisee}mm² (${inputs.courant}A, ${inputs.longueur}m, ΔU=${result.chute_tension_reelle}%)`\n    navigator.clipboard.writeText(text)\n  }\n\n  useEffect(() => {\n    // Calcul automatique quand les paramètres changent\n    if (inputs.courant > 0 && inputs.longueur > 0) {\n      const timer = setTimeout(() => {\n        calculateCableSection()\n      }, 500)\n      return () => clearTimeout(timer)\n    }\n  }, [inputs])\n\n  return (\n    <div className=\"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <h3 className=\"text-lg font-semibold text-blue-900 flex items-center space-x-2\">\n          <Calculator className=\"h-6 w-6\" />\n          <span>Calculatrice Rapide - Section de Câble</span>\n        </h3>\n        <div className=\"flex space-x-2\">\n          <button\n            onClick={() => setShowDetails(!showDetails)}\n            className=\"px-3 py-1 text-xs bg-blue-100 text-blue-800 rounded-full hover:bg-blue-200 transition-colors\"\n          >\n            {showDetails ? 'Masquer' : 'Détails'}\n          </button>\n          <button\n            onClick={resetCalculator}\n            className=\"p-1 text-blue-600 hover:text-blue-700\"\n            title=\"Réinitialiser\"\n          >\n            <RefreshCw className=\"h-4 w-4\" />\n          </button>\n        </div>\n      </div>\n\n      {/* Paramètres d'entrée */}\n      <div className=\"grid md:grid-cols-3 lg:grid-cols-6 gap-4 mb-4\">\n        <div>\n          <label className=\"block text-sm font-medium text-blue-800 mb-1\">\n            Courant (A)\n          </label>\n          <input\n            type=\"number\"\n            value={inputs.courant}\n            onChange={(e) => setInputs(prev => ({ ...prev, courant: Number(e.target.value) }))}\n            className=\"w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-400\"\n          />\n        </div>\n        <div>\n          <label className=\"block text-sm font-medium text-blue-800 mb-1\">\n            Longueur (m)\n          </label>\n          <input\n            type=\"number\"\n            value={inputs.longueur}\n            onChange={(e) => setInputs(prev => ({ ...prev, longueur: Number(e.target.value) }))}\n            className=\"w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-400\"\n          />\n        </div>\n        <div>\n          <label className=\"block text-sm font-medium text-blue-800 mb-1\">\n            ΔU max (%)\n          </label>\n          <input\n            type=\"number\"\n            step=\"0.1\"\n            value={inputs.chute_tension_max}\n            onChange={(e) => setInputs(prev => ({ ...prev, chute_tension_max: Number(e.target.value) }))}\n            className=\"w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-400\"\n          />\n        </div>\n        <div>\n          <label className=\"block text-sm font-medium text-blue-800 mb-1\">\n            Mode de pose\n          </label>\n          <select\n            value={inputs.mode_pose}\n            onChange={(e) => setInputs(prev => ({ ...prev, mode_pose: e.target.value }))}\n            className=\"w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-400\"\n          >\n            <option value=\"enterré\">Enterré</option>\n            <option value=\"aérien\">Aérien</option>\n            <option value=\"goulotte\">Goulotte</option>\n            <option value=\"chemin_cables\">Chemin câbles</option>\n          </select>\n        </div>\n        <div>\n          <label className=\"block text-sm font-medium text-blue-800 mb-1\">\n            Matériau\n          </label>\n          <select\n            value={inputs.materiau}\n            onChange={(e) => setInputs(prev => ({ ...prev, materiau: e.target.value }))}\n            className=\"w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-400\"\n          >\n            <option value=\"cuivre\">Cuivre</option>\n            <option value=\"aluminium\">Aluminium</option>\n          </select>\n        </div>\n        <div>\n          <label className=\"block text-sm font-medium text-blue-800 mb-1\">\n            Temp. (°C)\n          </label>\n          <input\n            type=\"number\"\n            value={inputs.temperature}\n            onChange={(e) => setInputs(prev => ({ ...prev, temperature: Number(e.target.value) }))}\n            className=\"w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-400\"\n          />\n        </div>\n      </div>\n\n      {/* Résultats */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        className=\"bg-white rounded-lg p-4 border border-blue-200\"\n      >\n        {isCalculating ? (\n          <div className=\"text-center py-8\">\n            <div className=\"w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4\"></div>\n            <p className=\"text-blue-700\">Calcul en cours selon NF C 15-100...</p>\n          </div>\n        ) : result ? (\n          <div>\n            <div className=\"flex items-center justify-between mb-4\">\n              <div className=\"text-center flex-1\">\n                <div className={`text-3xl font-bold ${\n                  result.validation_status === 'valid' ? 'text-green-600' :\n                  result.validation_status === 'warning' ? 'text-orange-600' :\n                  'text-red-600'\n                }`}>\n                  {result.section_normalisee} mm²\n                </div>\n                <div className=\"text-sm text-blue-700\">Section recommandée</div>\n              </div>\n              \n              <div className=\"flex items-center space-x-2\">\n                {result.validation_status === 'valid' && <CheckCircle className=\"h-6 w-6 text-green-600\" />}\n                {result.validation_status === 'warning' && <AlertTriangle className=\"h-6 w-6 text-orange-600\" />}\n                {result.validation_status === 'error' && <AlertTriangle className=\"h-6 w-6 text-red-600\" />}\n                \n                <div className=\"flex space-x-1\">\n                  <button\n                    onClick={copyResult}\n                    className=\"p-2 text-blue-600 hover:text-blue-700\"\n                    title=\"Copier\"\n                  >\n                    <Copy className=\"h-4 w-4\" />\n                  </button>\n                  <button\n                    onClick={exportResult}\n                    className=\"p-2 text-blue-600 hover:text-blue-700\"\n                    title=\"Exporter\"\n                  >\n                    <Download className=\"h-4 w-4\" />\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            {showDetails && (\n              <motion.div\n                initial={{ opacity: 0, height: 0 }}\n                animate={{ opacity: 1, height: 'auto' }}\n                className=\"space-y-3\"\n              >\n                <div className=\"grid md:grid-cols-3 gap-4 text-sm\">\n                  <div className=\"bg-blue-50 p-3 rounded\">\n                    <div className=\"font-medium text-blue-800\">Section minimale</div>\n                    <div className=\"text-blue-900\">{result.section_minimale} mm²</div>\n                  </div>\n                  <div className=\"bg-blue-50 p-3 rounded\">\n                    <div className=\"font-medium text-blue-800\">Chute tension réelle</div>\n                    <div className=\"text-blue-900\">{result.chute_tension_reelle}%</div>\n                  </div>\n                  <div className=\"bg-blue-50 p-3 rounded\">\n                    <div className=\"font-medium text-blue-800\">Intensité admissible</div>\n                    <div className=\"text-blue-900\">{result.intensite_admissible} A</div>\n                  </div>\n                </div>\n\n                {result.warnings.length > 0 && (\n                  <div className=\"bg-orange-50 border border-orange-200 rounded p-3\">\n                    <div className=\"flex items-center space-x-2 mb-2\">\n                      <AlertTriangle className=\"h-4 w-4 text-orange-600\" />\n                      <span className=\"font-medium text-orange-800\">Avertissements</span>\n                    </div>\n                    <ul className=\"text-sm text-orange-700 space-y-1\">\n                      {result.warnings.map((warning, index) => (\n                        <li key={index}>• {warning}</li>\n                      ))}\n                    </ul>\n                  </div>\n                )}\n\n                {result.recommendations.length > 0 && (\n                  <div className=\"bg-green-50 border border-green-200 rounded p-3\">\n                    <div className=\"flex items-center space-x-2 mb-2\">\n                      <Info className=\"h-4 w-4 text-green-600\" />\n                      <span className=\"font-medium text-green-800\">Recommandations</span>\n                    </div>\n                    <ul className=\"text-sm text-green-700 space-y-1\">\n                      {result.recommendations.map((rec, index) => (\n                        <li key={index}>• {rec}</li>\n                      ))}\n                    </ul>\n                  </div>\n                )}\n              </motion.div>\n            )}\n          </div>\n        ) : (\n          <div className=\"text-center py-4 text-blue-700\">\n            Modifiez les paramètres pour lancer le calcul automatique\n          </div>\n        )}\n      </motion.div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAyBe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACnC,SAAS;QACT,UAAU;QACV,mBAAmB;QACnB,WAAW;QACX,UAAU;QACV,aAAa;IACf;IAEA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B;IAC/D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,8BAA8B;IAC9B,MAAM,sBAAsB;QAAC;QAAK;QAAK;QAAG;QAAG;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;KAAI;IAE3G,2DAA2D;IAC3D,MAAM,wBAAwB;QAC5B,WAAW;YAAC;YAAI;YAAI;YAAI;YAAI;YAAI;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;SAAI;QAChG,UAAU;YAAC;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;SAAI;QAC9F,YAAY;YAAC;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;SAAI;QAC/F,iBAAiB;YAAC;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;SAAI;IACvG;IAEA,MAAM,wBAAwB;QAC5B,iBAAiB;QAEjB,kCAAkC;QAClC,WAAW;YACT,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,iBAAiB,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG;YAEnF,oCAAoC;YACpC,MAAM,cAAc,aAAa,WAAW,SAAS,MAAM,YAAY;;YAEvE,uBAAuB;YACvB,MAAM,UAAU,IAAI,WAAW;;YAE/B,yDAAyD;YACzD,6CAA6C;YAC7C,MAAM,wBAAwB,AAAC,UAAU,oBAAqB;YAC9D,MAAM,yBAAyB,AAAC,cAAc,WAAW,UAAW;YAEpE,oCAAoC;YACpC,MAAM,sBAAsB,cAAc,KAAK,OAAO;YAEtD,+CAA+C;YAC/C,MAAM,aAAa,qBAAqB,CAAC,UAAgD;YACzF,IAAI,6BAA6B,mBAAmB,CAAC,EAAE;YAEvD,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;gBAC1C,IAAI,AAAC,UAAU,CAAC,EAAE,GAAG,uBAAwB,SAAS;oBACpD,6BAA6B,mBAAmB,CAAC,EAAE;oBACnD;gBACF;YACF;YAEA,2CAA2C;YAC3C,MAAM,mBAAmB,KAAK,GAAG,CAAC,wBAAwB;YAE1D,gCAAgC;YAChC,IAAI,qBAAqB,mBAAmB,CAAC,oBAAoB,MAAM,GAAG,EAAE;YAC5E,KAAK,MAAM,WAAW,oBAAqB;gBACzC,IAAI,WAAW,kBAAkB;oBAC/B,qBAAqB;oBACrB;gBACF;YACF;YAEA,qDAAqD;YACrD,MAAM,uBAAuB,AAAC,cAAc,WAAW,UAAW,CAAC,qBAAqB,OAAO,IAAI;YAEnG,6CAA6C;YAC7C,MAAM,gBAAgB,oBAAoB,OAAO,CAAC;YAClD,MAAM,uBAAuB,UAAU,CAAC,cAAc,GAAG;YAEzD,gCAAgC;YAChC,IAAI,oBAAmD;YACvD,MAAM,kBAA4B,EAAE;YACpC,MAAM,WAAqB,EAAE;YAE7B,IAAI,uBAAuB,mBAAmB;gBAC5C,oBAAoB;gBACpB,SAAS,IAAI,CAAC,CAAC,2BAA2B,EAAE,qBAAqB,OAAO,CAAC,GAAG,IAAI,EAAE,kBAAkB,CAAC,CAAC;YACxG,OAAO,IAAI,uBAAuB,oBAAoB,KAAK;gBACzD,oBAAoB;gBACpB,SAAS,IAAI,CAAC,CAAC,sCAAsC,EAAE,qBAAqB,OAAO,CAAC,GAAG,CAAC,CAAC;YAC3F;YAEA,IAAI,uBAAuB,UAAU,KAAK;gBACxC,oBAAoB;gBACpB,SAAS,IAAI,CAAC;YAChB;YAEA,kBAAkB;YAClB,IAAI,WAAW,KAAK;gBAClB,gBAAgB,IAAI,CAAC;YACvB;YAEA,IAAI,cAAc,WAAW;gBAC3B,gBAAgB,IAAI,CAAC;YACvB;YAEA,gBAAgB,IAAI,CAAC,CAAC,wDAAwD,EAAE,mBAAmB,IAAI,CAAC;YAExG,UAAU;gBACR,kBAAkB,KAAK,KAAK,CAAC,mBAAmB,OAAO;gBACvD;gBACA,sBAAsB,KAAK,KAAK,CAAC,uBAAuB,OAAO;gBAC/D,sBAAsB,KAAK,KAAK,CAAC;gBACjC;gBACA;gBACA;YACF;YAEA,iBAAiB;QACnB,GAAG;IACL;IAEA,MAAM,kBAAkB;QACtB,UAAU;YACR,SAAS;YACT,UAAU;YACV,mBAAmB;YACnB,WAAW;YACX,UAAU;YACV,aAAa;QACf;QACA,UAAU;QACV,eAAe;IACjB;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,QAAQ;QAEb,MAAM,gBAAgB,CAAC;;;;;mBAKR,EAAE,OAAO,OAAO,CAAC;YACxB,EAAE,OAAO,QAAQ,CAAC;wBACN,EAAE,OAAO,iBAAiB,CAAC;gBACnC,EAAE,OAAO,SAAS,CAAC;YACvB,EAAE,OAAO,QAAQ,CAAC;wBACN,EAAE,OAAO,WAAW,CAAC;;;6BAGhB,EAAE,OAAO,gBAAgB,CAAC;sBACjC,EAAE,OAAO,kBAAkB,CAAC;2BACvB,EAAE,OAAO,oBAAoB,CAAC;wBACjC,EAAE,OAAO,oBAAoB,CAAC;;;AAGtD,EAAE,OAAO,eAAe,CAAC,GAAG,CAAC,CAAA,IAAK,CAAC,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,MAAM;;AAEvD,EAAE,OAAO,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,EAAE,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAA,IAAK,CAAC,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,OAAO,GAAG,GAAG;;;UAG9F,EAAE,IAAI,OAAO,cAAc,CAAC,SAAS;IAC3C,CAAC,CAAC,IAAI;QAEN,MAAM,OAAO,IAAI,KAAK;YAAC;SAAc,EAAE;YAAE,MAAM;QAAa;QAC5D,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG,CAAC,qBAAqB,EAAE,KAAK,GAAG,GAAG,IAAI,CAAC;QACrD,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,EAAE,KAAK;QACP,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;IACtB;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,QAAQ;QAEb,MAAM,OAAO,CAAC,eAAe,EAAE,OAAO,kBAAkB,CAAC,KAAK,EAAE,OAAO,OAAO,CAAC,GAAG,EAAE,OAAO,QAAQ,CAAC,MAAM,EAAE,OAAO,oBAAoB,CAAC,EAAE,CAAC;QAC3I,UAAU,SAAS,CAAC,SAAS,CAAC;IAChC;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,mDAAmD;QACnD,IAAI,OAAO,OAAO,GAAG,KAAK,OAAO,QAAQ,GAAG,GAAG;YAC7C,MAAM,QAAQ,WAAW;gBACvB;YACF,GAAG;YACH,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;KAAO;IAEX,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC,8MAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;0CACtB,8OAAC;0CAAK;;;;;;;;;;;;kCAER,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,eAAe,CAAC;gCAC/B,WAAU;0CAET,cAAc,YAAY;;;;;;0CAE7B,8OAAC;gCACC,SAAS;gCACT,WAAU;gCACV,OAAM;0CAEN,cAAA,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAM3B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,MAAK;gCACL,OAAO,OAAO,OAAO;gCACrB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,SAAS,OAAO,EAAE,MAAM,CAAC,KAAK;wCAAE,CAAC;gCAChF,WAAU;;;;;;;;;;;;kCAGd,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,MAAK;gCACL,OAAO,OAAO,QAAQ;gCACtB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,UAAU,OAAO,EAAE,MAAM,CAAC,KAAK;wCAAE,CAAC;gCACjF,WAAU;;;;;;;;;;;;kCAGd,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,MAAK;gCACL,MAAK;gCACL,OAAO,OAAO,iBAAiB;gCAC/B,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,mBAAmB,OAAO,EAAE,MAAM,CAAC,KAAK;wCAAE,CAAC;gCAC1F,WAAU;;;;;;;;;;;;kCAGd,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,OAAO,OAAO,SAAS;gCACvB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,WAAW,EAAE,MAAM,CAAC,KAAK;wCAAC,CAAC;gCAC1E,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAU;;;;;;kDACxB,8OAAC;wCAAO,OAAM;kDAAS;;;;;;kDACvB,8OAAC;wCAAO,OAAM;kDAAW;;;;;;kDACzB,8OAAC;wCAAO,OAAM;kDAAgB;;;;;;;;;;;;;;;;;;kCAGlC,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,OAAO,OAAO,QAAQ;gCACtB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wCAAC,CAAC;gCACzE,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAS;;;;;;kDACvB,8OAAC;wCAAO,OAAM;kDAAY;;;;;;;;;;;;;;;;;;kCAG9B,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,MAAK;gCACL,OAAO,OAAO,WAAW;gCACzB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,aAAa,OAAO,EAAE,MAAM,CAAC,KAAK;wCAAE,CAAC;gCACpF,WAAU;;;;;;;;;;;;;;;;;;0BAMhB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,WAAU;0BAET,8BACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;2BAE7B,uBACF,8OAAC;;sCACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAW,CAAC,mBAAmB,EAClC,OAAO,iBAAiB,KAAK,UAAU,mBACvC,OAAO,iBAAiB,KAAK,YAAY,oBACzC,gBACA;;gDACC,OAAO,kBAAkB;gDAAC;;;;;;;sDAE7B,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAGzC,8OAAC;oCAAI,WAAU;;wCACZ,OAAO,iBAAiB,KAAK,yBAAW,8OAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCAC/D,OAAO,iBAAiB,KAAK,2BAAa,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;wCACnE,OAAO,iBAAiB,KAAK,yBAAW,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDAElE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS;oDACT,WAAU;oDACV,OAAM;8DAEN,cAAA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,8OAAC;oDACC,SAAS;oDACT,WAAU;oDACV,OAAM;8DAEN,cAAA,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAM3B,6BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,QAAQ;4BAAE;4BACjC,SAAS;gCAAE,SAAS;gCAAG,QAAQ;4BAAO;4BACtC,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAA4B;;;;;;8DAC3C,8OAAC;oDAAI,WAAU;;wDAAiB,OAAO,gBAAgB;wDAAC;;;;;;;;;;;;;sDAE1D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAA4B;;;;;;8DAC3C,8OAAC;oDAAI,WAAU;;wDAAiB,OAAO,oBAAoB;wDAAC;;;;;;;;;;;;;sDAE9D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAA4B;;;;;;8DAC3C,8OAAC;oDAAI,WAAU;;wDAAiB,OAAO,oBAAoB;wDAAC;;;;;;;;;;;;;;;;;;;gCAI/D,OAAO,QAAQ,CAAC,MAAM,GAAG,mBACxB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,wNAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;8DACzB,8OAAC;oDAAK,WAAU;8DAA8B;;;;;;;;;;;;sDAEhD,8OAAC;4CAAG,WAAU;sDACX,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC7B,8OAAC;;wDAAe;wDAAG;;mDAAV;;;;;;;;;;;;;;;;gCAMhB,OAAO,eAAe,CAAC,MAAM,GAAG,mBAC/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;;;;;;;sDAE/C,8OAAC;4CAAG,WAAU;sDACX,OAAO,eAAe,CAAC,GAAG,CAAC,CAAC,KAAK,sBAChC,8OAAC;;wDAAe;wDAAG;;mDAAV;;;;;;;;;;;;;;;;;;;;;;;;;;;yCASvB,8OAAC;oBAAI,WAAU;8BAAiC;;;;;;;;;;;;;;;;;AAO1D", "debugId": null}}, {"offset": {"line": 1920, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/components/prescriptor/PrescriptorModule.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { \n  FileText, \n  Calculator, \n  Award, \n  Settings,\n  Plus,\n  Search,\n  Filter,\n  Clock,\n  CheckCircle,\n  AlertTriangle,\n  Users,\n  Building,\n  Zap,\n  Download,\n  Share2\n} from 'lucide-react'\nimport { useStore } from '@/store/useStore'\nimport { PrescriptorService } from '@/services/prescriptorService'\nimport { formatDate, getMembershipColor } from '@/lib/utils'\nimport QuickCalculator from './QuickCalculator'\nimport CertificateGenerator from './CertificateGenerator'\n\ninterface PrescriptorModuleProps {\n  className?: string\n}\n\nexport default function PrescriptorModule({ className = '' }: PrescriptorModuleProps) {\n  const {\n    prescriptionTemplates,\n    prescriptionProjects,\n    technicalNotes,\n    complianceCertificates,\n    calculationEngines,\n    activeProject,\n    selectedTemplate,\n    setPrescriptionTemplates,\n    setPrescriptionProjects,\n    setTechnicalNotes,\n    setComplianceCertificates,\n    setCalculationEngines,\n    setActiveProject,\n    setSelectedTemplate\n  } = useStore()\n\n  const [activeTab, setActiveTab] = useState<'templates' | 'projects' | 'calculations' | 'certificates'>('templates')\n  const [searchQuery, setSearchQuery] = useState('')\n  const [filterCategory, setFilterCategory] = useState<'all' | 'electrical' | 'automation' | 'energy' | 'safety'>('all')\n\n  // Initialisation des données\n  useEffect(() => {\n    const loadInitialData = () => {\n      setPrescriptionTemplates(PrescriptorService.generatePrescriptionTemplates())\n      setPrescriptionProjects(PrescriptorService.generatePrescriptionProjects())\n      setCalculationEngines(PrescriptorService.generateCalculationEngines())\n      \n      // Générer des notes techniques pour les projets existants\n      const projects = PrescriptorService.generatePrescriptionProjects()\n      const allNotes = projects.flatMap(project => \n        PrescriptorService.generateTechnicalNotes(project.id)\n      )\n      setTechnicalNotes(allNotes)\n    }\n\n    loadInitialData()\n  }, [])\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'completed':\n      case 'approved':\n        return 'bg-green-100 text-green-800 border-green-200'\n      case 'in_progress':\n      case 'review':\n        return 'bg-blue-100 text-blue-800 border-blue-200'\n      case 'draft':\n        return 'bg-yellow-100 text-yellow-800 border-yellow-200'\n      default:\n        return 'bg-gray-100 text-gray-800 border-gray-200'\n    }\n  }\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'completed':\n      case 'approved':\n        return <CheckCircle className=\"h-4 w-4\" />\n      case 'in_progress':\n      case 'review':\n        return <Clock className=\"h-4 w-4\" />\n      case 'draft':\n        return <FileText className=\"h-4 w-4\" />\n      default:\n        return <AlertTriangle className=\"h-4 w-4\" />\n    }\n  }\n\n  const getCategoryIcon = (category: string) => {\n    switch (category) {\n      case 'electrical':\n        return <Zap className=\"h-5 w-5\" />\n      case 'automation':\n        return <Settings className=\"h-5 w-5\" />\n      case 'energy':\n        return <Calculator className=\"h-5 w-5\" />\n      case 'safety':\n        return <Award className=\"h-5 w-5\" />\n      default:\n        return <FileText className=\"h-5 w-5\" />\n    }\n  }\n\n  const tabs = [\n    { \n      id: 'templates', \n      label: 'Templates de Prescription', \n      icon: FileText, \n      count: prescriptionTemplates.length,\n      description: 'Modèles prêts à l\\'emploi pour vos prescriptions'\n    },\n    { \n      id: 'projects', \n      label: 'Projets en Cours', \n      icon: Building, \n      count: prescriptionProjects.length,\n      description: 'Vos projets de prescription et leur avancement'\n    },\n    { \n      id: 'calculations', \n      label: 'Outils de Calcul', \n      icon: Calculator, \n      count: calculationEngines.length,\n      description: 'Moteurs de calcul automatisés et certifiés'\n    },\n    { \n      id: 'certificates', \n      label: 'Certificats de Conformité', \n      icon: Award, \n      count: complianceCertificates.length,\n      description: 'Certificats et garanties contractuelles'\n    }\n  ]\n\n  return (\n    <div className={`bg-white rounded-lg shadow-lg ${className}`}>\n      {/* Header */}\n      <div className=\"p-6 border-b border-gray-200\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h2 className=\"text-2xl font-bold text-gray-900\">Module Prescripteur Professionnel</h2>\n            <p className=\"text-gray-600 mt-1\">Kits de prescription, calculs automatisés et certification de conformité</p>\n          </div>\n          \n          <div className=\"flex items-center space-x-4\">\n            <div className=\"text-right\">\n              <div className=\"text-sm text-gray-600\">Projets actifs</div>\n              <div className=\"text-lg font-bold text-amber-600\">\n                {prescriptionProjects.filter(p => p.status === 'in_progress').length}\n              </div>\n            </div>\n            \n            <button className=\"btn-premium\">\n              <Plus className=\"h-4 w-4 mr-2\" />\n              Nouveau Projet\n            </button>\n          </div>\n        </div>\n\n        {/* Barre de recherche et filtres */}\n        <div className=\"mt-4 flex items-center space-x-4\">\n          <div className=\"flex-1 relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n            <input\n              type=\"text\"\n              placeholder=\"Rechercher templates, projets, calculs...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400 focus:border-transparent\"\n            />\n          </div>\n          \n          <select\n            value={filterCategory}\n            onChange={(e) => setFilterCategory(e.target.value as any)}\n            className=\"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400 focus:border-transparent\"\n          >\n            <option value=\"all\">Toutes catégories</option>\n            <option value=\"electrical\">Électrique</option>\n            <option value=\"automation\">Automatisme</option>\n            <option value=\"energy\">Énergie</option>\n            <option value=\"safety\">Sécurité</option>\n          </select>\n        </div>\n      </div>\n\n      {/* Onglets */}\n      <div className=\"border-b border-gray-200\">\n        <nav className=\"flex space-x-8 px-6\">\n          {tabs.map((tab) => {\n            const Icon = tab.icon\n            const isActive = activeTab === tab.id\n            \n            return (\n              <button\n                key={tab.id}\n                onClick={() => setActiveTab(tab.id as any)}\n                className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${\n                  isActive\n                    ? 'border-amber-500 text-amber-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                <Icon className=\"h-5 w-5\" />\n                <span>{tab.label}</span>\n                {tab.count > 0 && (\n                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                    isActive ? 'bg-amber-100 text-amber-800' : 'bg-gray-100 text-gray-600'\n                  }`}>\n                    {tab.count}\n                  </span>\n                )}\n              </button>\n            )\n          })}\n        </nav>\n      </div>\n\n      {/* Description de l'onglet actif */}\n      <div className=\"px-6 py-3 bg-gradient-to-r from-amber-50 to-yellow-50 border-b border-amber-200\">\n        <p className=\"text-sm text-amber-800\">\n          {tabs.find(tab => tab.id === activeTab)?.description}\n        </p>\n      </div>\n\n      {/* Contenu des onglets */}\n      <div className=\"p-6\">\n        <AnimatePresence mode=\"wait\">\n          {activeTab === 'templates' && (\n            <motion.div\n              key=\"templates\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -20 }}\n              className=\"space-y-6\"\n            >\n              {/* Grille des templates */}\n              <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6\">\n                {prescriptionTemplates.map((template) => (\n                  <motion.div\n                    key={template.id}\n                    initial={{ opacity: 0, scale: 0.9 }}\n                    animate={{ opacity: 1, scale: 1 }}\n                    className={`industrial-card p-6 cursor-pointer transition-all hover:shadow-lg ${\n                      selectedTemplate?.id === template.id ? 'ring-2 ring-amber-400' : ''\n                    }`}\n                    onClick={() => setSelectedTemplate(template)}\n                  >\n                    <div className=\"flex items-start justify-between mb-4\">\n                      <div className={`w-12 h-12 bg-gradient-to-r ${\n                        template.category === 'electrical' ? 'from-blue-500 to-blue-600' :\n                        template.category === 'automation' ? 'from-purple-500 to-purple-600' :\n                        template.category === 'energy' ? 'from-green-500 to-green-600' :\n                        'from-gray-500 to-gray-600'\n                      } rounded-lg flex items-center justify-center text-white`}>\n                        {getCategoryIcon(template.category)}\n                      </div>\n                      {template.is_featured && (\n                        <span className=\"px-2 py-1 bg-amber-100 text-amber-800 text-xs font-medium rounded-full\">\n                          Populaire\n                        </span>\n                      )}\n                    </div>\n                    \n                    <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">{template.name}</h3>\n                    <p className=\"text-sm text-gray-600 mb-4 line-clamp-2\">{template.description}</p>\n                    \n                    <div className=\"flex items-center justify-between mb-3\">\n                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                        template.complexity_level === 'expert' ? 'bg-red-100 text-red-800' :\n                        template.complexity_level === 'advanced' ? 'bg-orange-100 text-orange-800' :\n                        template.complexity_level === 'intermediate' ? 'bg-yellow-100 text-yellow-800' :\n                        'bg-green-100 text-green-800'\n                      }`}>\n                        {template.complexity_level}\n                      </span>\n                      <div className=\"flex items-center space-x-1 text-xs text-gray-500\">\n                        <Users className=\"h-3 w-3\" />\n                        <span>{template.usage_count}</span>\n                      </div>\n                    </div>\n                    \n                    <div className=\"flex items-center justify-between\">\n                      {template.membership_required && (\n                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getMembershipColor(template.membership_required)}`}>\n                          {template.membership_required}+ requis\n                        </span>\n                      )}\n                      <div className=\"flex items-center space-x-1 text-amber-600\">\n                        <span className=\"text-sm font-medium\">★ {template.rating}</span>\n                      </div>\n                    </div>\n                    \n                    <div className=\"mt-4 pt-4 border-t border-gray-200\">\n                      <button className=\"w-full btn-premium text-sm py-2\">\n                        Utiliser ce Template\n                      </button>\n                    </div>\n                  </motion.div>\n                ))}\n              </div>\n            </motion.div>\n          )}\n\n          {activeTab === 'projects' && (\n            <motion.div\n              key=\"projects\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -20 }}\n              className=\"space-y-6\"\n            >\n              {/* Liste des projets */}\n              {prescriptionProjects.length === 0 ? (\n                <div className=\"text-center py-12 text-gray-500\">\n                  <Building className=\"h-16 w-16 mx-auto mb-4 text-gray-300\" />\n                  <h3 className=\"text-lg font-medium mb-2\">Aucun projet en cours</h3>\n                  <p className=\"mb-4\">Créez votre premier projet de prescription</p>\n                  <button className=\"btn-premium\">\n                    <Plus className=\"h-4 w-4 mr-2\" />\n                    Nouveau Projet\n                  </button>\n                </div>\n              ) : (\n                <div className=\"space-y-4\">\n                  {prescriptionProjects.map((project) => (\n                    <motion.div\n                      key={project.id}\n                      initial={{ opacity: 0, x: -20 }}\n                      animate={{ opacity: 1, x: 0 }}\n                      className=\"industrial-card p-6\"\n                    >\n                      <div className=\"flex items-start justify-between\">\n                        <div className=\"flex-1\">\n                          <div className=\"flex items-center space-x-3 mb-3\">\n                            <span className={`px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(project.status)}`}>\n                              {getStatusIcon(project.status)}\n                              <span className=\"ml-2\">{project.status}</span>\n                            </span>\n                            {project.compliance_verified && (\n                              <span className=\"px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full\">\n                                ✓ Conforme\n                              </span>\n                            )}\n                          </div>\n\n                          <h3 className=\"text-xl font-bold text-gray-900 mb-2\">{project.project_name}</h3>\n                          <p className=\"text-gray-600 mb-3\">{project.project_description}</p>\n\n                          <div className=\"grid md:grid-cols-3 gap-4 mb-4\">\n                            <div>\n                              <span className=\"text-sm font-medium text-gray-700\">Client:</span>\n                              <p className=\"text-sm text-gray-900\">{project.client_name}</p>\n                              <p className=\"text-xs text-gray-500\">{project.client_company}</p>\n                            </div>\n                            <div>\n                              <span className=\"text-sm font-medium text-gray-700\">Localisation:</span>\n                              <p className=\"text-sm text-gray-900\">{project.location}</p>\n                            </div>\n                            <div>\n                              <span className=\"text-sm font-medium text-gray-700\">Coût total:</span>\n                              <p className=\"text-lg font-bold text-amber-600\">\n                                {project.total_cost.toLocaleString()} FCFA\n                              </p>\n                            </div>\n                          </div>\n\n                          <div className=\"flex items-center space-x-6 text-sm text-gray-500\">\n                            <span>Créé: {formatDate(project.created_at)}</span>\n                            <span>Mis à jour: {formatDate(project.updated_at)}</span>\n                            {project.delivery_date && (\n                              <span>Livraison: {formatDate(project.delivery_date)}</span>\n                            )}\n                          </div>\n                        </div>\n\n                        <div className=\"ml-6 flex flex-col space-y-2\">\n                          <button className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\">\n                            Ouvrir\n                          </button>\n                          <button className=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\">\n                            <Download className=\"h-4 w-4 mr-2 inline\" />\n                            Export\n                          </button>\n                          <button className=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\">\n                            <Share2 className=\"h-4 w-4 mr-2 inline\" />\n                            Partager\n                          </button>\n                        </div>\n                      </div>\n                    </motion.div>\n                  ))}\n                </div>\n              )}\n            </motion.div>\n          )}\n\n          {activeTab === 'calculations' && (\n            <motion.div\n              key=\"calculations\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -20 }}\n              className=\"space-y-6\"\n            >\n              {/* Outils de calcul */}\n              <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6\">\n                {calculationEngines.map((engine) => (\n                  <motion.div\n                    key={engine.id}\n                    initial={{ opacity: 0, scale: 0.9 }}\n                    animate={{ opacity: 1, scale: 1 }}\n                    className=\"industrial-card p-6\"\n                  >\n                    <div className=\"flex items-center space-x-3 mb-4\">\n                      <div className=\"w-12 h-12 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center text-white\">\n                        <Calculator className=\"h-6 w-6\" />\n                      </div>\n                      {engine.is_certified && (\n                        <span className=\"px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full\">\n                          ✓ Certifié\n                        </span>\n                      )}\n                    </div>\n\n                    <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">{engine.name}</h3>\n                    <p className=\"text-sm text-gray-600 mb-4\">{engine.description}</p>\n\n                    <div className=\"space-y-2 mb-4\">\n                      <div className=\"flex justify-between text-sm\">\n                        <span className=\"text-gray-600\">Paramètres d'entrée:</span>\n                        <span className=\"font-medium\">{engine.input_parameters.length}</span>\n                      </div>\n                      <div className=\"flex justify-between text-sm\">\n                        <span className=\"text-gray-600\">Paramètres de sortie:</span>\n                        <span className=\"font-medium\">{engine.output_parameters.length}</span>\n                      </div>\n                      <div className=\"flex justify-between text-sm\">\n                        <span className=\"text-gray-600\">Normes:</span>\n                        <span className=\"font-medium\">{engine.safety_standards.length}</span>\n                      </div>\n                    </div>\n\n                    <button className=\"w-full btn-premium text-sm py-2\">\n                      Utiliser l'Outil\n                    </button>\n                  </motion.div>\n                ))}\n              </div>\n\n              {/* Calculatrice rapide */}\n              <QuickCalculator />\n            </motion.div>\n          )}\n\n          {activeTab === 'certificates' && (\n            <motion.div\n              key=\"certificates\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -20 }}\n              className=\"space-y-6\"\n            >\n              {/* Générateur de certificat */}\n              <div className=\"bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-6 border border-green-200\">\n                <div className=\"flex items-center space-x-4 mb-4\">\n                  <Award className=\"h-8 w-8 text-green-600\" />\n                  <div>\n                    <h3 className=\"text-lg font-semibold text-green-900\">\n                      Générateur de Certificat de Conformité\n                    </h3>\n                    <p className=\"text-sm text-green-700\">\n                      Créez des certificats officiels pour vos projets validés\n                    </p>\n                  </div>\n                </div>\n                <div className=\"grid md:grid-cols-2 gap-4 mb-4\">\n                  <select className=\"px-3 py-2 border border-green-300 rounded-lg focus:ring-2 focus:ring-green-400\">\n                    <option>Sélectionner un projet...</option>\n                    {prescriptionProjects.map(project => (\n                      <option key={project.id} value={project.id}>\n                        {project.project_name}\n                      </option>\n                    ))}\n                  </select>\n                  <select className=\"px-3 py-2 border border-green-300 rounded-lg focus:ring-2 focus:ring-green-400\">\n                    <option>Type de certificat...</option>\n                    <option value=\"design\">Conception</option>\n                    <option value=\"installation\">Installation</option>\n                    <option value=\"testing\">Tests et essais</option>\n                    <option value=\"full_compliance\">Conformité complète</option>\n                  </select>\n                </div>\n                <button className=\"btn-premium\">\n                  <Award className=\"h-4 w-4 mr-2\" />\n                  Générer le Certificat\n                </button>\n              </div>\n\n              {/* Liste des certificats */}\n              {complianceCertificates.length === 0 ? (\n                <div className=\"text-center py-12 text-gray-500\">\n                  <Award className=\"h-16 w-16 mx-auto mb-4 text-gray-300\" />\n                  <h3 className=\"text-lg font-medium mb-2\">Aucun certificat émis</h3>\n                  <p>Générez votre premier certificat de conformité</p>\n                </div>\n              ) : (\n                <div className=\"space-y-4\">\n                  {complianceCertificates.map((certificate) => (\n                    <motion.div\n                      key={certificate.id}\n                      initial={{ opacity: 0, x: -20 }}\n                      animate={{ opacity: 1, x: 0 }}\n                      className=\"industrial-card p-6\"\n                    >\n                      <div className=\"flex items-start justify-between\">\n                        <div className=\"flex-1\">\n                          <div className=\"flex items-center space-x-3 mb-3\">\n                            <Award className=\"h-6 w-6 text-green-600\" />\n                            <span className=\"px-3 py-1 bg-green-100 text-green-800 text-sm font-medium rounded-full\">\n                              {certificate.certificate_type}\n                            </span>\n                            {certificate.is_active && (\n                              <span className=\"px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full\">\n                                Actif\n                              </span>\n                            )}\n                          </div>\n\n                          <h3 className=\"text-lg font-bold text-gray-900 mb-2\">\n                            Certificat N° {certificate.certificate_number}\n                          </h3>\n\n                          <div className=\"grid md:grid-cols-2 gap-4 mb-4\">\n                            <div>\n                              <span className=\"text-sm font-medium text-gray-700\">Émis pour:</span>\n                              <p className=\"text-sm text-gray-900\">{certificate.issued_to}</p>\n                            </div>\n                            <div>\n                              <span className=\"text-sm font-medium text-gray-700\">Validité:</span>\n                              <p className=\"text-sm text-gray-900\">\n                                {formatDate(certificate.valid_from)} → {formatDate(certificate.valid_until)}\n                              </p>\n                            </div>\n                          </div>\n\n                          <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n                            <span>Normes: {certificate.standards_covered.join(', ')}</span>\n                          </div>\n                        </div>\n\n                        <div className=\"ml-6 flex flex-col space-y-2\">\n                          <button className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\">\n                            <Download className=\"h-4 w-4 mr-2 inline\" />\n                            Télécharger\n                          </button>\n                          <button className=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\">\n                            Vérifier QR\n                          </button>\n                        </div>\n                      </div>\n                    </motion.div>\n                  ))}\n                </div>\n              )}\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AACA;AACA;AACA;AAxBA;;;;;;;;;AA+Be,SAAS,kBAAkB,EAAE,YAAY,EAAE,EAA0B;IAClF,MAAM,EACJ,qBAAqB,EACrB,oBAAoB,EACpB,cAAc,EACd,sBAAsB,EACtB,kBAAkB,EAClB,aAAa,EACb,gBAAgB,EAChB,wBAAwB,EACxB,uBAAuB,EACvB,iBAAiB,EACjB,yBAAyB,EACzB,qBAAqB,EACrB,gBAAgB,EAChB,mBAAmB,EACpB,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAEX,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8D;IACvG,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6D;IAEhH,6BAA6B;IAC7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB;YACtB,yBAAyB,qIAAA,CAAA,qBAAkB,CAAC,6BAA6B;YACzE,wBAAwB,qIAAA,CAAA,qBAAkB,CAAC,4BAA4B;YACvE,sBAAsB,qIAAA,CAAA,qBAAkB,CAAC,0BAA0B;YAEnE,0DAA0D;YAC1D,MAAM,WAAW,qIAAA,CAAA,qBAAkB,CAAC,4BAA4B;YAChE,MAAM,WAAW,SAAS,OAAO,CAAC,CAAA,UAChC,qIAAA,CAAA,qBAAkB,CAAC,sBAAsB,CAAC,QAAQ,EAAE;YAEtD,kBAAkB;QACpB;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;YACL,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B;gBACE,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;QACpC;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,gMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;YACxB,KAAK;gBACH,qBAAO,8OAAC,0MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK;gBACH,qBAAO,8OAAC,8MAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC/B,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B;gBACE,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;QAC/B;IACF;IAEA,MAAM,OAAO;QACX;YACE,IAAI;YACJ,OAAO;YACP,MAAM,8MAAA,CAAA,WAAQ;YACd,OAAO,sBAAsB,MAAM;YACnC,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,0MAAA,CAAA,WAAQ;YACd,OAAO,qBAAqB,MAAM;YAClC,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,8MAAA,CAAA,aAAU;YAChB,OAAO,mBAAmB,MAAM;YAChC,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO,uBAAuB,MAAM;YACpC,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAI,WAAW,CAAC,8BAA8B,EAAE,WAAW;;0BAE1D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;;0CAGpC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;0DACvC,8OAAC;gDAAI,WAAU;0DACZ,qBAAqB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,eAAe,MAAM;;;;;;;;;;;;kDAIxE,8OAAC;wCAAO,WAAU;;0DAChB,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;kCAOvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;;;;;;;0CAId,8OAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;gCACjD,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAM;;;;;;kDACpB,8OAAC;wCAAO,OAAM;kDAAa;;;;;;kDAC3B,8OAAC;wCAAO,OAAM;kDAAa;;;;;;kDAC3B,8OAAC;wCAAO,OAAM;kDAAS;;;;;;kDACvB,8OAAC;wCAAO,OAAM;kDAAS;;;;;;;;;;;;;;;;;;;;;;;;0BAM7B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC;wBACT,MAAM,OAAO,IAAI,IAAI;wBACrB,MAAM,WAAW,cAAc,IAAI,EAAE;wBAErC,qBACE,8OAAC;4BAEC,SAAS,IAAM,aAAa,IAAI,EAAE;4BAClC,WAAW,CAAC,uFAAuF,EACjG,WACI,oCACA,8EACJ;;8CAEF,8OAAC;oCAAK,WAAU;;;;;;8CAChB,8OAAC;8CAAM,IAAI,KAAK;;;;;;gCACf,IAAI,KAAK,GAAG,mBACX,8OAAC;oCAAK,WAAW,CAAC,2CAA2C,EAC3D,WAAW,gCAAgC,6BAC3C;8CACC,IAAI,KAAK;;;;;;;2BAdT,IAAI,EAAE;;;;;oBAmBjB;;;;;;;;;;;0BAKJ,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BACV,KAAK,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,YAAY;;;;;;;;;;;0BAK7C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,yLAAA,CAAA,kBAAe;oBAAC,MAAK;;wBACnB,cAAc,6BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,MAAM;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC3B,WAAU;sCAGV,cAAA,8OAAC;gCAAI,WAAU;0CACZ,sBAAsB,GAAG,CAAC,CAAC,yBAC1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAClC,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCAChC,WAAW,CAAC,kEAAkE,EAC5E,kBAAkB,OAAO,SAAS,EAAE,GAAG,0BAA0B,IACjE;wCACF,SAAS,IAAM,oBAAoB;;0DAEnC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,2BAA2B,EAC1C,SAAS,QAAQ,KAAK,eAAe,8BACrC,SAAS,QAAQ,KAAK,eAAe,kCACrC,SAAS,QAAQ,KAAK,WAAW,gCACjC,4BACD,uDAAuD,CAAC;kEACtD,gBAAgB,SAAS,QAAQ;;;;;;oDAEnC,SAAS,WAAW,kBACnB,8OAAC;wDAAK,WAAU;kEAAyE;;;;;;;;;;;;0DAM7F,8OAAC;gDAAG,WAAU;0DAA4C,SAAS,IAAI;;;;;;0DACvE,8OAAC;gDAAE,WAAU;0DAA2C,SAAS,WAAW;;;;;;0DAE5E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAW,CAAC,2CAA2C,EAC3D,SAAS,gBAAgB,KAAK,WAAW,4BACzC,SAAS,gBAAgB,KAAK,aAAa,kCAC3C,SAAS,gBAAgB,KAAK,iBAAiB,kCAC/C,+BACA;kEACC,SAAS,gBAAgB;;;;;;kEAE5B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;0EAAM,SAAS,WAAW;;;;;;;;;;;;;;;;;;0DAI/B,8OAAC;gDAAI,WAAU;;oDACZ,SAAS,mBAAmB,kBAC3B,8OAAC;wDAAK,WAAW,CAAC,2CAA2C,EAAE,CAAA,GAAA,mHAAA,CAAA,qBAAkB,AAAD,EAAE,SAAS,mBAAmB,GAAG;;4DAC9G,SAAS,mBAAmB;4DAAC;;;;;;;kEAGlC,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;;gEAAsB;gEAAG,SAAS,MAAM;;;;;;;;;;;;;;;;;;0DAI5D,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAO,WAAU;8DAAkC;;;;;;;;;;;;uCAtDjD,SAAS,EAAE;;;;;;;;;;2BAVlB;;;;;wBA0EP,cAAc,4BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,MAAM;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC3B,WAAU;sCAGT,qBAAqB,MAAM,KAAK,kBAC/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAG,WAAU;kDAA2B;;;;;;kDACzC,8OAAC;wCAAE,WAAU;kDAAO;;;;;;kDACpB,8OAAC;wCAAO,WAAU;;0DAChB,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;qDAKrC,8OAAC;gCAAI,WAAU;0CACZ,qBAAqB,GAAG,CAAC,CAAC,wBACzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAW,CAAC,kDAAkD,EAAE,eAAe,QAAQ,MAAM,GAAG;;wEACnG,cAAc,QAAQ,MAAM;sFAC7B,8OAAC;4EAAK,WAAU;sFAAQ,QAAQ,MAAM;;;;;;;;;;;;gEAEvC,QAAQ,mBAAmB,kBAC1B,8OAAC;oEAAK,WAAU;8EAAyE;;;;;;;;;;;;sEAM7F,8OAAC;4DAAG,WAAU;sEAAwC,QAAQ,YAAY;;;;;;sEAC1E,8OAAC;4DAAE,WAAU;sEAAsB,QAAQ,mBAAmB;;;;;;sEAE9D,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;;sFACC,8OAAC;4EAAK,WAAU;sFAAoC;;;;;;sFACpD,8OAAC;4EAAE,WAAU;sFAAyB,QAAQ,WAAW;;;;;;sFACzD,8OAAC;4EAAE,WAAU;sFAAyB,QAAQ,cAAc;;;;;;;;;;;;8EAE9D,8OAAC;;sFACC,8OAAC;4EAAK,WAAU;sFAAoC;;;;;;sFACpD,8OAAC;4EAAE,WAAU;sFAAyB,QAAQ,QAAQ;;;;;;;;;;;;8EAExD,8OAAC;;sFACC,8OAAC;4EAAK,WAAU;sFAAoC;;;;;;sFACpD,8OAAC;4EAAE,WAAU;;gFACV,QAAQ,UAAU,CAAC,cAAc;gFAAG;;;;;;;;;;;;;;;;;;;sEAK3C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;;wEAAK;wEAAO,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,UAAU;;;;;;;8EAC1C,8OAAC;;wEAAK;wEAAa,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,UAAU;;;;;;;gEAC/C,QAAQ,aAAa,kBACpB,8OAAC;;wEAAK;wEAAY,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,aAAa;;;;;;;;;;;;;;;;;;;8DAKxD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAO,WAAU;sEAAkF;;;;;;sEAGpG,8OAAC;4DAAO,WAAU;;8EAChB,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEAAwB;;;;;;;sEAG9C,8OAAC;4DAAO,WAAU;;8EAChB,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEAAwB;;;;;;;;;;;;;;;;;;;uCA1D3C,QAAQ,EAAE;;;;;;;;;;2BArBnB;;;;;wBA2FP,cAAc,gCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,MAAM;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC3B,WAAU;;8CAGV,8OAAC;oCAAI,WAAU;8CACZ,mBAAmB,GAAG,CAAC,CAAC,uBACvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAI;4CAClC,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CAChC,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,8MAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;wDAEvB,OAAO,YAAY,kBAClB,8OAAC;4DAAK,WAAU;sEAAyE;;;;;;;;;;;;8DAM7F,8OAAC;oDAAG,WAAU;8DAA4C,OAAO,IAAI;;;;;;8DACrE,8OAAC;oDAAE,WAAU;8DAA8B,OAAO,WAAW;;;;;;8DAE7D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,8OAAC;oEAAK,WAAU;8EAAe,OAAO,gBAAgB,CAAC,MAAM;;;;;;;;;;;;sEAE/D,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,8OAAC;oEAAK,WAAU;8EAAe,OAAO,iBAAiB,CAAC,MAAM;;;;;;;;;;;;sEAEhE,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,8OAAC;oEAAK,WAAU;8EAAe,OAAO,gBAAgB,CAAC,MAAM;;;;;;;;;;;;;;;;;;8DAIjE,8OAAC;oDAAO,WAAU;8DAAkC;;;;;;;2CAlC/C,OAAO,EAAE;;;;;;;;;;8CA0CpB,8OAAC,oJAAA,CAAA,UAAe;;;;;;2BApDZ;;;;;wBAwDP,cAAc,gCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,MAAM;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC3B,WAAU;;8CAGV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAuC;;;;;;sEAGrD,8OAAC;4DAAE,WAAU;sEAAyB;;;;;;;;;;;;;;;;;;sDAK1C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAO,WAAU;;sEAChB,8OAAC;sEAAO;;;;;;wDACP,qBAAqB,GAAG,CAAC,CAAA,wBACxB,8OAAC;gEAAwB,OAAO,QAAQ,EAAE;0EACvC,QAAQ,YAAY;+DADV,QAAQ,EAAE;;;;;;;;;;;8DAK3B,8OAAC;oDAAO,WAAU;;sEAChB,8OAAC;sEAAO;;;;;;sEACR,8OAAC;4DAAO,OAAM;sEAAS;;;;;;sEACvB,8OAAC;4DAAO,OAAM;sEAAe;;;;;;sEAC7B,8OAAC;4DAAO,OAAM;sEAAU;;;;;;sEACxB,8OAAC;4DAAO,OAAM;sEAAkB;;;;;;;;;;;;;;;;;;sDAGpC,8OAAC;4CAAO,WAAU;;8DAChB,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;gCAMrC,uBAAuB,MAAM,KAAK,kBACjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;4CAAG,WAAU;sDAA2B;;;;;;sDACzC,8OAAC;sDAAE;;;;;;;;;;;yDAGL,8OAAC;oCAAI,WAAU;8CACZ,uBAAuB,GAAG,CAAC,CAAC,4BAC3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAC9B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,WAAU;sDAEV,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,8OAAC;wEAAK,WAAU;kFACb,YAAY,gBAAgB;;;;;;oEAE9B,YAAY,SAAS,kBACpB,8OAAC;wEAAK,WAAU;kFAAuE;;;;;;;;;;;;0EAM3F,8OAAC;gEAAG,WAAU;;oEAAuC;oEACpC,YAAY,kBAAkB;;;;;;;0EAG/C,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAK,WAAU;0FAAoC;;;;;;0FACpD,8OAAC;gFAAE,WAAU;0FAAyB,YAAY,SAAS;;;;;;;;;;;;kFAE7D,8OAAC;;0FACC,8OAAC;gFAAK,WAAU;0FAAoC;;;;;;0FACpD,8OAAC;gFAAE,WAAU;;oFACV,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,YAAY,UAAU;oFAAE;oFAAI,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,YAAY,WAAW;;;;;;;;;;;;;;;;;;;0EAKhF,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;;wEAAK;wEAAS,YAAY,iBAAiB,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;;;kEAItD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAO,WAAU;;kFAChB,8OAAC,0MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEAAwB;;;;;;;0EAG9C,8OAAC;gEAAO,WAAU;0EAA+F;;;;;;;;;;;;;;;;;;2CA9ChH,YAAY,EAAE;;;;;;;;;;;2BArDvB;;;;;;;;;;;;;;;;;;;;;;AAkHlB", "debugId": null}}, {"offset": {"line": 3475, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/app/prescriptor/page.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { \n  ArrowLeft,\n  FileText,\n  Calculator,\n  Award,\n  Building,\n  Users,\n  TrendingUp,\n  CheckCircle,\n  Clock\n} from 'lucide-react'\nimport Link from 'next/link'\nimport PrescriptorModule from '@/components/prescriptor/PrescriptorModule'\n\nexport default function PrescriptorPage() {\n  const stats = [\n    {\n      label: 'Templates Disponibles',\n      value: '47',\n      change: '+5',\n      changeType: 'increase',\n      icon: <FileText className=\"h-6 w-6\" />\n    },\n    {\n      label: 'Projets Actifs',\n      value: '23',\n      change: '+8',\n      changeType: 'increase',\n      icon: <Building className=\"h-6 w-6\" />\n    },\n    {\n      label: 'Calculs Automatisés',\n      value: '156',\n      change: '+12',\n      changeType: 'increase',\n      icon: <Calculator className=\"h-6 w-6\" />\n    },\n    {\n      label: 'Certificats Émis',\n      value: '89',\n      change: '+15',\n      changeType: 'increase',\n      icon: <Award className=\"h-6 w-6\" />\n    }\n  ]\n\n  const prescriptionFlow = [\n    {\n      step: 1,\n      title: 'Sélection Template',\n      description: 'Choisissez un template adapté à votre projet',\n      icon: <FileText className=\"h-6 w-6\" />,\n      color: 'from-blue-500 to-blue-600'\n    },\n    {\n      step: 2,\n      title: 'Calculs Automatiques',\n      description: 'Les calculs se font automatiquement selon les normes',\n      icon: <Calculator className=\"h-6 w-6\" />,\n      color: 'from-green-500 to-green-600'\n    },\n    {\n      step: 3,\n      title: 'Validation Technique',\n      description: 'Vérification de conformité et recommandations',\n      icon: <CheckCircle className=\"h-6 w-6\" />,\n      color: 'from-purple-500 to-purple-600'\n    },\n    {\n      step: 4,\n      title: 'Certification',\n      description: 'Génération du certificat de conformité officiel',\n      icon: <Award className=\"h-6 w-6\" />,\n      color: 'from-amber-500 to-amber-600'\n    }\n  ]\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100\">\n      {/* Header */}\n      <header className=\"bg-white/80 backdrop-blur-md border-b border-slate-200 sticky top-0 z-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-16\">\n            <div className=\"flex items-center space-x-4\">\n              <Link \n                href=\"/\"\n                className=\"flex items-center space-x-2 text-slate-700 hover:text-amber-600 transition-colors\"\n              >\n                <ArrowLeft className=\"h-5 w-5\" />\n                <span>Retour à l'accueil</span>\n              </Link>\n            </div>\n            \n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-amber-400 to-amber-500 rounded-lg flex items-center justify-center\">\n                <FileText className=\"h-5 w-5 text-slate-900\" />\n              </div>\n              <div>\n                <h1 className=\"text-lg font-bold text-slate-900\">Pro Matos</h1>\n                <p className=\"text-xs text-slate-600\">Module Prescripteur</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Contenu principal */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Titre et description */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"mb-8\"\n        >\n          <h1 className=\"text-3xl font-bold text-slate-900 mb-2\">\n            Module Prescripteur Professionnel\n          </h1>\n          <p className=\"text-lg text-slate-600\">\n            Kits de prescription, calculs automatisés et certification de conformité pour imposer vos standards\n          </p>\n        </motion.div>\n\n        {/* Statistiques rapides */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.1 }}\n          className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\"\n        >\n          {stats.map((stat, index) => (\n            <motion.div\n              key={stat.label}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.1 + index * 0.05 }}\n              className=\"industrial-card p-6\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-slate-600\">{stat.label}</p>\n                  <div className=\"flex items-baseline space-x-2\">\n                    <p className=\"text-2xl font-bold text-slate-900\">{stat.value}</p>\n                    <span className={`text-sm font-medium ${\n                      stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'\n                    }`}>\n                      {stat.change}\n                    </span>\n                  </div>\n                </div>\n                <div className=\"p-3 bg-amber-100 rounded-lg text-amber-600\">\n                  {stat.icon}\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* Processus de prescription */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.2 }}\n          className=\"mb-8\"\n        >\n          <h2 className=\"text-2xl font-bold text-slate-900 mb-6\">Processus de Prescription Automatisé</h2>\n          <div className=\"grid md:grid-cols-4 gap-6\">\n            {prescriptionFlow.map((step, index) => (\n              <motion.div\n                key={step.step}\n                initial={{ opacity: 0, x: -20 }}\n                animate={{ opacity: 1, x: 0 }}\n                transition={{ delay: 0.3 + index * 0.1 }}\n                className=\"relative\"\n              >\n                <div className=\"industrial-card p-6 text-center\">\n                  <div className={`w-16 h-16 bg-gradient-to-r ${step.color} rounded-full flex items-center justify-center text-white mx-auto mb-4`}>\n                    {step.icon}\n                  </div>\n                  <div className=\"absolute -top-2 -left-2 w-8 h-8 bg-amber-500 text-white rounded-full flex items-center justify-center text-sm font-bold\">\n                    {step.step}\n                  </div>\n                  <h3 className=\"text-lg font-bold text-slate-900 mb-2\">{step.title}</h3>\n                  <p className=\"text-sm text-slate-600\">{step.description}</p>\n                </div>\n                \n                {/* Flèche de connexion */}\n                {index < prescriptionFlow.length - 1 && (\n                  <div className=\"hidden md:block absolute top-1/2 -right-3 transform -translate-y-1/2\">\n                    <div className=\"w-6 h-0.5 bg-amber-400\"></div>\n                    <div className=\"absolute -right-1 -top-1 w-2 h-2 bg-amber-400 rotate-45\"></div>\n                  </div>\n                )}\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n\n        {/* Module Prescripteur principal */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.4 }}\n        >\n          <PrescriptorModule />\n        </motion.div>\n\n        {/* Section avantages stratégiques */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.5 }}\n          className=\"mt-8 bg-gradient-to-r from-slate-900 to-slate-800 text-white rounded-lg p-8\"\n        >\n          <h3 className=\"text-2xl font-bold mb-6 text-center\">\n            Contrôlez les Prescriptions = Contrôlez le Marché\n          </h3>\n          <div className=\"grid md:grid-cols-3 gap-8\">\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-gradient-to-r from-amber-400 to-amber-500 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                <FileText className=\"h-8 w-8 text-slate-900\" />\n              </div>\n              <h4 className=\"text-xl font-bold mb-3\">Templates Standardisés</h4>\n              <p className=\"text-slate-300\">\n                Vos templates deviennent la référence. Les architectes et ingénieurs utilisent VOS standards.\n              </p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-gradient-to-r from-amber-400 to-amber-500 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                <Calculator className=\"h-8 w-8 text-slate-900\" />\n              </div>\n              <h4 className=\"text-xl font-bold mb-3\">Calculs Certifiés</h4>\n              <p className=\"text-slate-300\">\n                Vos calculs font autorité. Impossible de prescrire sans passer par votre validation technique.\n              </p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-gradient-to-r from-amber-400 to-amber-500 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                <Award className=\"h-8 w-8 text-slate-900\" />\n              </div>\n              <h4 className=\"text-xl font-bold mb-3\">Certification Obligatoire</h4>\n              <p className=\"text-slate-300\">\n                Vos certificats deviennent indispensables. Aucun projet sérieux sans votre validation.\n              </p>\n            </div>\n          </div>\n        </motion.div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AAfA;;;;;;AAiBe,SAAS;IACtB,MAAM,QAAQ;QACZ;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;QAC5B;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;QAC5B;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,oBAAM,8OAAC,8MAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;QAC9B;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;QACzB;KACD;IAED,MAAM,mBAAmB;QACvB;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,OAAO;QACT;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,8MAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;YAC5B,OAAO;QACT;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,2NAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;YAC7B,OAAO;QACT;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;0CAIV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhD,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CAGvD,8OAAC;gCAAE,WAAU;0CAAyB;;;;;;;;;;;;kCAMxC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;wBACzB,WAAU;kCAET,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO,MAAM,QAAQ;gCAAK;gCACxC,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAsC,KAAK,KAAK;;;;;;8DAC7D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAqC,KAAK,KAAK;;;;;;sEAC5D,8OAAC;4DAAK,WAAW,CAAC,oBAAoB,EACpC,KAAK,UAAU,KAAK,aAAa,mBAAmB,gBACpD;sEACC,KAAK,MAAM;;;;;;;;;;;;;;;;;;sDAIlB,8OAAC;4CAAI,WAAU;sDACZ,KAAK,IAAI;;;;;;;;;;;;+BAnBT,KAAK,KAAK;;;;;;;;;;kCA2BrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;wBACzB,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAI,WAAU;0CACZ,iBAAiB,GAAG,CAAC,CAAC,MAAM,sBAC3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO,MAAM,QAAQ;wCAAI;wCACvC,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,2BAA2B,EAAE,KAAK,KAAK,CAAC,sEAAsE,CAAC;kEAC7H,KAAK,IAAI;;;;;;kEAEZ,8OAAC;wDAAI,WAAU;kEACZ,KAAK,IAAI;;;;;;kEAEZ,8OAAC;wDAAG,WAAU;kEAAyC,KAAK,KAAK;;;;;;kEACjE,8OAAC;wDAAE,WAAU;kEAA0B,KAAK,WAAW;;;;;;;;;;;;4CAIxD,QAAQ,iBAAiB,MAAM,GAAG,mBACjC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;;;;;;;;uCArBd,KAAK,IAAI;;;;;;;;;;;;;;;;kCA8BtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;kCAEzB,cAAA,8OAAC,sJAAA,CAAA,UAAiB;;;;;;;;;;kCAIpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;wBACzB,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;0DAEtB,8OAAC;gDAAG,WAAU;0DAAyB;;;;;;0DACvC,8OAAC;gDAAE,WAAU;0DAAiB;;;;;;;;;;;;kDAIhC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,8MAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;0DAExB,8OAAC;gDAAG,WAAU;0DAAyB;;;;;;0DACvC,8OAAC;gDAAE,WAAU;0DAAiB;;;;;;;;;;;;kDAIhC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAEnB,8OAAC;gDAAG,WAAU;0DAAyB;;;;;;0DACvC,8OAAC;gDAAE,WAAU;0DAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5C", "debugId": null}}]}