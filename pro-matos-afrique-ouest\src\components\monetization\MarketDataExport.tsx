'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Database, 
  Download, 
  FileText,
  BarChart3,
  TrendingUp,
  Calendar,
  Filter,
  Settings,
  Globe,
  Users,
  Package,
  DollarSign,
  Clock,
  CheckCircle,
  AlertTriangle,
  RefreshCw
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { DatePickerWithRange } from '@/components/ui/date-range-picker'
import { Switch } from '@/components/ui/switch'
import { Progress } from '@/components/ui/progress'
import { toast } from 'sonner'

interface MarketDataSet {
  id: string
  name: string
  description: string
  category: 'sales' | 'products' | 'customers' | 'trends' | 'competition'
  format: 'json' | 'csv' | 'excel' | 'pdf'
  size: number // en MB
  lastUpdated: string
  updateFrequency: 'realtime' | 'hourly' | 'daily' | 'weekly' | 'monthly'
  accessLevel: 'public' | 'premium' | 'enterprise'
  price: number // en FCFA
  downloadCount: number
  fields: string[]
}

interface ExportRequest {
  id: string
  dataSetId: string
  dataSetName: string
  format: string
  dateRange: {
    from: string
    to: string
  }
  filters: Record<string, any>
  status: 'pending' | 'processing' | 'completed' | 'failed'
  requestDate: string
  completionDate?: string
  downloadUrl?: string
  fileSize?: number
}

interface MarketDataExportProps {
  className?: string
}

export default function MarketDataExport({ className = '' }: MarketDataExportProps) {
  const [dataSets] = useState<MarketDataSet[]>([
    {
      id: '1',
      name: 'Données de Ventes Régionales',
      description: 'Analyse complète des ventes par région, produit et période',
      category: 'sales',
      format: 'excel',
      size: 15.2,
      lastUpdated: new Date().toISOString(),
      updateFrequency: 'daily',
      accessLevel: 'premium',
      price: 25000,
      downloadCount: 156,
      fields: ['date', 'region', 'product_id', 'quantity', 'revenue', 'customer_type']
    },
    {
      id: '2',
      name: 'Catalogue Produits Complet',
      description: 'Base de données complète des produits avec prix et disponibilités',
      category: 'products',
      format: 'json',
      size: 8.7,
      lastUpdated: new Date(Date.now() - 3600000).toISOString(),
      updateFrequency: 'hourly',
      accessLevel: 'public',
      price: 0,
      downloadCount: 892,
      fields: ['product_id', 'name', 'category', 'price', 'stock', 'manufacturer']
    },
    {
      id: '3',
      name: 'Profils Clients B2B',
      description: 'Données démographiques et comportementales des clients professionnels',
      category: 'customers',
      format: 'csv',
      size: 12.4,
      lastUpdated: new Date(Date.now() - 86400000).toISOString(),
      updateFrequency: 'weekly',
      accessLevel: 'enterprise',
      price: 75000,
      downloadCount: 43,
      fields: ['customer_id', 'company', 'sector', 'location', 'purchase_history', 'credit_score']
    },
    {
      id: '4',
      name: 'Tendances du Marché',
      description: 'Analyse des tendances et prévisions du marché électrique en Afrique de l\'Ouest',
      category: 'trends',
      format: 'pdf',
      size: 5.8,
      lastUpdated: new Date(Date.now() - *********).toISOString(),
      updateFrequency: 'monthly',
      accessLevel: 'premium',
      price: 50000,
      downloadCount: 78,
      fields: ['trend_id', 'category', 'growth_rate', 'forecast', 'market_size']
    },
    {
      id: '5',
      name: 'Analyse Concurrentielle',
      description: 'Données sur les concurrents, prix et parts de marché',
      category: 'competition',
      format: 'excel',
      size: 9.3,
      lastUpdated: new Date(Date.now() - 1209600000).toISOString(),
      updateFrequency: 'weekly',
      accessLevel: 'enterprise',
      price: 100000,
      downloadCount: 29,
      fields: ['competitor', 'product', 'price', 'market_share', 'strengths', 'weaknesses']
    }
  ])

  const [exportRequests, setExportRequests] = useState<ExportRequest[]>([
    {
      id: '1',
      dataSetId: '1',
      dataSetName: 'Données de Ventes Régionales',
      format: 'excel',
      dateRange: {
        from: new Date(Date.now() - 2592000000).toISOString(),
        to: new Date().toISOString()
      },
      filters: { region: 'Abidjan', product_category: 'Disjoncteurs' },
      status: 'completed',
      requestDate: new Date(Date.now() - 3600000).toISOString(),
      completionDate: new Date(Date.now() - 1800000).toISOString(),
      downloadUrl: '/exports/sales-data-2024-01.xlsx',
      fileSize: 2.3
    },
    {
      id: '2',
      dataSetId: '2',
      dataSetName: 'Catalogue Produits Complet',
      format: 'json',
      dateRange: {
        from: new Date().toISOString(),
        to: new Date().toISOString()
      },
      filters: {},
      status: 'processing',
      requestDate: new Date(Date.now() - 900000).toISOString()
    }
  ])

  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedAccessLevel, setSelectedAccessLevel] = useState<string>('all')
  const [autoRefresh, setAutoRefresh] = useState(true)

  // Filtrer les datasets
  const filteredDataSets = dataSets.filter(ds => {
    if (selectedCategory !== 'all' && ds.category !== selectedCategory) return false
    if (selectedAccessLevel !== 'all' && ds.accessLevel !== selectedAccessLevel) return false
    return true
  })

  // Créer une demande d'export
  const createExportRequest = (dataSetId: string, format: string) => {
    const dataSet = dataSets.find(ds => ds.id === dataSetId)
    if (!dataSet) return

    const newRequest: ExportRequest = {
      id: Date.now().toString(),
      dataSetId,
      dataSetName: dataSet.name,
      format,
      dateRange: {
        from: new Date(Date.now() - 2592000000).toISOString(), // 30 jours
        to: new Date().toISOString()
      },
      filters: {},
      status: 'pending',
      requestDate: new Date().toISOString()
    }

    setExportRequests(prev => [newRequest, ...prev])
    
    // Simuler le traitement
    setTimeout(() => {
      setExportRequests(prev => prev.map(req => 
        req.id === newRequest.id 
          ? { ...req, status: 'processing' as const }
          : req
      ))
    }, 1000)

    setTimeout(() => {
      setExportRequests(prev => prev.map(req => 
        req.id === newRequest.id 
          ? { 
              ...req, 
              status: 'completed' as const,
              completionDate: new Date().toISOString(),
              downloadUrl: `/exports/${dataSet.name.toLowerCase().replace(/\s+/g, '-')}.${format}`,
              fileSize: Math.random() * 10 + 1
            }
          : req
      ))
    }, 5000)

    toast.success('Demande d\'export créée')
  }

  // Télécharger un fichier
  const downloadFile = (request: ExportRequest) => {
    if (request.status !== 'completed' || !request.downloadUrl) return

    // Simuler le téléchargement
    const link = document.createElement('a')
    link.href = request.downloadUrl
    link.download = `${request.dataSetName}.${request.format}`
    link.click()

    toast.success('Téléchargement démarré')
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'sales': return <TrendingUp className="h-4 w-4" />
      case 'products': return <Package className="h-4 w-4" />
      case 'customers': return <Users className="h-4 w-4" />
      case 'trends': return <BarChart3 className="h-4 w-4" />
      case 'competition': return <Globe className="h-4 w-4" />
      default: return <Database className="h-4 w-4" />
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'sales': return 'bg-green-100 text-green-800 border-green-200'
      case 'products': return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'customers': return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'trends': return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'competition': return 'bg-red-100 text-red-800 border-red-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getAccessLevelColor = (level: string) => {
    switch (level) {
      case 'public': return 'bg-green-100 text-green-800 border-green-200'
      case 'premium': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'enterprise': return 'bg-purple-100 text-purple-800 border-purple-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'processing': return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />
      case 'failed': return <AlertTriangle className="h-4 w-4 text-red-500" />
      default: return <Clock className="h-4 w-4 text-yellow-500" />
    }
  }

  const totalRevenue = dataSets.reduce((sum, ds) => sum + (ds.price * ds.downloadCount), 0)
  const totalDownloads = dataSets.reduce((sum, ds) => sum + ds.downloadCount, 0)

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-green-400 to-green-500 rounded-lg flex items-center justify-center">
                <Database className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle>Export de Données Market</CardTitle>
                <CardDescription>
                  Accédez aux données de marché et insights business
                </CardDescription>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">Auto-refresh</span>
                <Switch
                  checked={autoRefresh}
                  onCheckedChange={setAutoRefresh}
                />
              </div>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-2" />
                Paramètres
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Métriques */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <Database className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Datasets</p>
                <p className="text-2xl font-bold text-blue-600">{dataSets.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <Download className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Téléchargements</p>
                <p className="text-2xl font-bold text-green-600">{totalDownloads.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                <DollarSign className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Revenus</p>
                <p className="text-2xl font-bold text-purple-600">
                  {(totalRevenue / 1000000).toFixed(1)}M FCFA
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                <RefreshCw className="h-6 w-6 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Exports en cours</p>
                <p className="text-2xl font-bold text-orange-600">
                  {exportRequests.filter(r => r.status === 'processing').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filtres */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">Filtres :</span>
            </div>

            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Catégorie" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Toutes les catégories</SelectItem>
                <SelectItem value="sales">Ventes</SelectItem>
                <SelectItem value="products">Produits</SelectItem>
                <SelectItem value="customers">Clients</SelectItem>
                <SelectItem value="trends">Tendances</SelectItem>
                <SelectItem value="competition">Concurrence</SelectItem>
              </SelectContent>
            </Select>

            <Select value={selectedAccessLevel} onValueChange={setSelectedAccessLevel}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Niveau d'accès" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tous les niveaux</SelectItem>
                <SelectItem value="public">Public</SelectItem>
                <SelectItem value="premium">Premium</SelectItem>
                <SelectItem value="enterprise">Enterprise</SelectItem>
              </SelectContent>
            </Select>

            <Button variant="outline" size="sm">
              Réinitialiser
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Datasets disponibles */}
      <Card>
        <CardHeader>
          <CardTitle>Datasets Disponibles</CardTitle>
          <CardDescription>
            Sélectionnez les données à exporter selon vos besoins
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredDataSets.map((dataSet) => (
              <motion.div
                key={dataSet.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                      {getCategoryIcon(dataSet.category)}
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">{dataSet.name}</h4>
                      <p className="text-sm text-gray-600">{dataSet.description}</p>
                    </div>
                  </div>

                  <div className="flex flex-col items-end space-y-1">
                    <Badge className={getCategoryColor(dataSet.category)}>
                      {dataSet.category}
                    </Badge>
                    <Badge className={getAccessLevelColor(dataSet.accessLevel)}>
                      {dataSet.accessLevel}
                    </Badge>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                  <div>
                    <span className="text-gray-600">Taille :</span>
                    <span className="ml-2 font-medium">{dataSet.size} MB</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Format :</span>
                    <span className="ml-2 font-medium uppercase">{dataSet.format}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Fréquence :</span>
                    <span className="ml-2 font-medium">{dataSet.updateFrequency}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Prix :</span>
                    <span className="ml-2 font-medium">
                      {dataSet.price === 0 ? 'Gratuit' : `${dataSet.price.toLocaleString()} FCFA`}
                    </span>
                  </div>
                </div>

                <div className="mb-4">
                  <h5 className="text-sm font-medium text-gray-900 mb-2">Champs disponibles</h5>
                  <div className="flex flex-wrap gap-1">
                    {dataSet.fields.slice(0, 4).map((field, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {field}
                      </Badge>
                    ))}
                    {dataSet.fields.length > 4 && (
                      <Badge variant="outline" className="text-xs">
                        +{dataSet.fields.length - 4}
                      </Badge>
                    )}
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-600">
                    <span>{dataSet.downloadCount} téléchargements</span>
                    <span className="mx-2">•</span>
                    <span>MAJ: {new Date(dataSet.lastUpdated).toLocaleDateString('fr-FR')}</span>
                  </div>

                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => createExportRequest(dataSet.id, dataSet.format)}
                    >
                      <Download className="h-3 w-3 mr-1" />
                      Exporter
                    </Button>
                    <Button variant="outline" size="sm">
                      <FileText className="h-3 w-3 mr-1" />
                      Aperçu
                    </Button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Historique des exports */}
      <Card>
        <CardHeader>
          <CardTitle>Historique des Exports</CardTitle>
          <CardDescription>
            Suivez le statut de vos demandes d'export
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {exportRequests.map((request) => (
              <div key={request.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(request.status)}
                    <div>
                      <h4 className="font-medium text-gray-900">{request.dataSetName}</h4>
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <span>Format: {request.format.toUpperCase()}</span>
                        <span>•</span>
                        <span>Demandé: {new Date(request.requestDate).toLocaleDateString('fr-FR')}</span>
                        {request.fileSize && (
                          <>
                            <span>•</span>
                            <span>Taille: {request.fileSize.toFixed(1)} MB</span>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Badge variant="outline">
                    {request.status === 'pending' && 'En attente'}
                    {request.status === 'processing' && 'Traitement'}
                    {request.status === 'completed' && 'Terminé'}
                    {request.status === 'failed' && 'Échec'}
                  </Badge>

                  {request.status === 'completed' && (
                    <Button
                      size="sm"
                      onClick={() => downloadFile(request)}
                      className="bg-green-500 hover:bg-green-600"
                    >
                      <Download className="h-3 w-3 mr-1" />
                      Télécharger
                    </Button>
                  )}

                  {request.status === 'processing' && (
                    <div className="flex items-center space-x-2">
                      <Progress value={Math.random() * 100} className="w-24 h-2" />
                      <span className="text-sm text-gray-600">
                        {Math.floor(Math.random() * 100)}%
                      </span>
                    </div>
                  )}
                </div>
              </div>
            ))}

            {exportRequests.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <Download className="h-12 w-12 mx-auto mb-3 text-gray-300" />
                <p>Aucun export en cours</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Informations sur l'API */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-6">
          <div className="flex items-start space-x-4">
            <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center flex-shrink-0">
              <Database className="h-6 w-6 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-blue-900 mb-2">
                Accès API Développeur
              </h3>
              <p className="text-blue-700 mb-4">
                Intégrez nos données directement dans vos applications avec notre API REST.
                Accès en temps réel aux données de marché, authentification sécurisée et documentation complète.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-blue-800">Endpoints RESTful</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-blue-800">Authentification OAuth 2.0</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-blue-800">Rate limiting intelligent</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-blue-800">Webhooks en temps réel</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-blue-800">SDK JavaScript/Python</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-blue-800">Support technique dédié</span>
                </div>
              </div>
              <div className="mt-4">
                <Button variant="outline" className="border-blue-300 text-blue-700 hover:bg-blue-100">
                  Documentation API
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
