'use client'

import { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Bell, 
  X, 
  AlertTriangle, 
  Info, 
  CheckCircle,
  Zap,
  TrendingUp,
  Package,
  Users,
  Clock,
  Settings,
  MapPin
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { toast } from 'sonner'

interface Notification {
  id: string
  type: 'alert' | 'info' | 'success' | 'warning' | 'critical'
  category: 'stock' | 'price' | 'technical' | 'training' | 'system' | 'market'
  title: string
  message: string
  timestamp: string
  read: boolean
  priority: 'low' | 'medium' | 'high' | 'critical'
  actionUrl?: string
  actionLabel?: string
  geolocation?: {
    city: string
    country: string
    distance?: number
  }
}

interface NotificationSettings {
  enabled: boolean
  sound: boolean
  desktop: boolean
  categories: {
    stock: boolean
    price: boolean
    technical: boolean
    training: boolean
    system: boolean
    market: boolean
  }
  priorities: {
    low: boolean
    medium: boolean
    high: boolean
    critical: boolean
  }
  geolocation: boolean
  maxDistance: number
}

interface RealTimeNotificationsProps {
  className?: string
}

export default function RealTimeNotifications({ className = '' }: RealTimeNotificationsProps) {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [isOpen, setIsOpen] = useState(false)
  const [settings, setSettings] = useState<NotificationSettings>({
    enabled: true,
    sound: true,
    desktop: true,
    categories: {
      stock: true,
      price: true,
      technical: true,
      training: true,
      system: false,
      market: true
    },
    priorities: {
      low: false,
      medium: true,
      high: true,
      critical: true
    },
    geolocation: true,
    maxDistance: 50
  })
  const [showSettings, setShowSettings] = useState(false)
  const [userLocation, setUserLocation] = useState<{ lat: number, lng: number } | null>(null)
  const audioRef = useRef<HTMLAudioElement | null>(null)

  // Initialiser la géolocalisation
  useEffect(() => {
    if (settings.geolocation && navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setUserLocation({
            lat: position.coords.latitude,
            lng: position.coords.longitude
          })
        },
        (error) => {
          console.warn('Géolocalisation non disponible:', error)
        }
      )
    }
  }, [settings.geolocation])

  // Simuler des notifications temps réel
  useEffect(() => {
    if (!settings.enabled) return

    const interval = setInterval(() => {
      const sampleNotifications: Omit<Notification, 'id' | 'timestamp' | 'read'>[] = [
        {
          type: 'alert',
          category: 'stock',
          title: 'Rupture de stock imminente',
          message: 'Disjoncteurs C60N 32A - Stock critique chez CFAO Abidjan',
          priority: 'high',
          actionUrl: '/hub?filter=stock',
          actionLabel: 'Voir détails',
          geolocation: { city: 'Abidjan', country: 'Côte d\'Ivoire', distance: 12 }
        },
        {
          type: 'info',
          category: 'price',
          title: 'Baisse de prix',
          message: 'Câbles H07V-U 2.5mm² : -15% chez Schneider Electric',
          priority: 'medium',
          actionUrl: '/hub?filter=price',
          actionLabel: 'Profiter',
          geolocation: { city: 'Dakar', country: 'Sénégal', distance: 25 }
        },
        {
          type: 'critical',
          category: 'technical',
          title: 'Alerte sécurité',
          message: 'Rappel produit : Prises Legrand série 752xx défectueuses',
          priority: 'critical',
          actionUrl: '/expert?alert=safety',
          actionLabel: 'Consulter expert',
          geolocation: { city: 'Bamako', country: 'Mali', distance: 8 }
        },
        {
          type: 'success',
          category: 'training',
          title: 'Nouvelle formation disponible',
          message: 'Formation NF C 15-100 - Places disponibles pour demain',
          priority: 'medium',
          actionUrl: '/hub?tab=training',
          actionLabel: 'S\'inscrire'
        },
        {
          type: 'warning',
          category: 'market',
          title: 'Tendance marché',
          message: 'Augmentation prévue du cuivre : +8% la semaine prochaine',
          priority: 'high',
          actionUrl: '/hub?filter=market',
          actionLabel: 'Analyser'
        }
      ]

      // Filtrer selon les paramètres
      const filteredNotifications = sampleNotifications.filter(notif => {
        if (!settings.categories[notif.category]) return false
        if (!settings.priorities[notif.priority]) return false
        
        // Filtrer par géolocalisation si activée
        if (settings.geolocation && notif.geolocation && userLocation) {
          const distance = notif.geolocation.distance || 0
          if (distance > settings.maxDistance) return false
        }
        
        return true
      })

      if (filteredNotifications.length > 0) {
        const randomNotif = filteredNotifications[Math.floor(Math.random() * filteredNotifications.length)]
        const newNotification: Notification = {
          ...randomNotif,
          id: Date.now().toString(),
          timestamp: new Date().toISOString(),
          read: false
        }

        setNotifications(prev => [newNotification, ...prev.slice(0, 19)])

        // Son de notification
        if (settings.sound && audioRef.current) {
          audioRef.current.play().catch(() => {})
        }

        // Notification desktop
        if (settings.desktop && 'Notification' in window && Notification.permission === 'granted') {
          new Notification(newNotification.title, {
            body: newNotification.message,
            icon: '/favicon.ico',
            tag: newNotification.id
          })
        }

        // Toast pour notifications critiques
        if (newNotification.priority === 'critical') {
          toast.error(newNotification.title, {
            description: newNotification.message,
            action: newNotification.actionUrl ? {
              label: newNotification.actionLabel || 'Voir',
              onClick: () => window.location.href = newNotification.actionUrl!
            } : undefined
          })
        }
      }
    }, Math.random() * 30000 + 10000)

    return () => clearInterval(interval)
  }, [settings, userLocation])

  // Demander permission pour notifications desktop
  useEffect(() => {
    if (settings.desktop && 'Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission()
    }
  }, [settings.desktop])

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'alert': return <AlertTriangle className="h-5 w-5 text-orange-500" />
      case 'critical': return <AlertTriangle className="h-5 w-5 text-red-500" />
      case 'success': return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'warning': return <AlertTriangle className="h-5 w-5 text-yellow-500" />
      default: return <Info className="h-5 w-5 text-blue-500" />
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'stock': return <Package className="h-4 w-4" />
      case 'price': return <TrendingUp className="h-4 w-4" />
      case 'technical': return <Zap className="h-4 w-4" />
      case 'training': return <Users className="h-4 w-4" />
      case 'market': return <TrendingUp className="h-4 w-4" />
      default: return <Bell className="h-4 w-4" />
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200'
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'medium': return 'bg-blue-100 text-blue-800 border-blue-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const markAsRead = (id: string) => {
    setNotifications(prev => prev.map(notif => 
      notif.id === id ? { ...notif, read: true } : notif
    ))
  }

  const markAllAsRead = () => {
    setNotifications(prev => prev.map(notif => ({ ...notif, read: true })))
  }

  const clearAll = () => {
    setNotifications([])
  }

  const unreadCount = notifications.filter(n => !n.read).length

  return (
    <>
      {/* Audio pour les sons de notification */}
      <audio ref={audioRef} preload="auto">
        <source src="/notification-sound.mp3" type="audio/mpeg" />
        <source src="/notification-sound.wav" type="audio/wav" />
      </audio>

      {/* Bouton de notification */}
      <div className={`relative ${className}`}>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsOpen(!isOpen)}
          className="relative p-2"
        >
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <Badge className="absolute -top-1 -right-1 h-5 w-5 p-0 flex items-center justify-center bg-red-500 text-white text-xs">
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}
        </Button>

        {/* Panel de notifications */}
        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ opacity: 0, y: 10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: 10, scale: 0.95 }}
              className="absolute right-0 top-full mt-2 w-96 bg-white rounded-lg shadow-xl border border-gray-200 z-50"
            >
              {/* Header */}
              <div className="p-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">
                    Notifications {unreadCount > 0 && `(${unreadCount})`}
                  </h3>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowSettings(!showSettings)}
                    >
                      <Settings className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setIsOpen(false)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {notifications.length > 0 && (
                  <div className="flex items-center space-x-2 mt-2">
                    <Button variant="ghost" size="sm" onClick={markAllAsRead}>
                      Tout marquer lu
                    </Button>
                    <Button variant="ghost" size="sm" onClick={clearAll}>
                      Tout effacer
                    </Button>
                  </div>
                )}
              </div>

              {/* Paramètres */}
              {showSettings && (
                <div className="p-4 border-b border-gray-200 bg-gray-50">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Notifications activées</span>
                      <Switch
                        checked={settings.enabled}
                        onCheckedChange={(checked) =>
                          setSettings(prev => ({ ...prev, enabled: checked }))
                        }
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Son</span>
                      <Switch
                        checked={settings.sound}
                        onCheckedChange={(checked) =>
                          setSettings(prev => ({ ...prev, sound: checked }))
                        }
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Notifications desktop</span>
                      <Switch
                        checked={settings.desktop}
                        onCheckedChange={(checked) =>
                          setSettings(prev => ({ ...prev, desktop: checked }))
                        }
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Géolocalisation</span>
                      <Switch
                        checked={settings.geolocation}
                        onCheckedChange={(checked) =>
                          setSettings(prev => ({ ...prev, geolocation: checked }))
                        }
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Liste des notifications */}
              <div className="max-h-96 overflow-y-auto">
                {notifications.length === 0 ? (
                  <div className="p-8 text-center">
                    <Bell className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                    <p className="text-gray-500">Aucune notification</p>
                  </div>
                ) : (
                  <div className="divide-y divide-gray-100">
                    {notifications.map((notification) => (
                      <motion.div
                        key={notification.id}
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        className={`p-4 hover:bg-gray-50 cursor-pointer ${
                          !notification.read ? 'bg-blue-50' : ''
                        }`}
                        onClick={() => markAsRead(notification.id)}
                      >
                        <div className="flex items-start space-x-3">
                          <div className="flex-shrink-0">
                            {getNotificationIcon(notification.type)}
                          </div>

                          <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-2 mb-1">
                              <p className="text-sm font-medium text-gray-900 truncate">
                                {notification.title}
                              </p>
                              {!notification.read && (
                                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                              )}
                            </div>

                            <p className="text-sm text-gray-600 mb-2">
                              {notification.message}
                            </p>

                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-2">
                                <Badge className={getPriorityColor(notification.priority)}>
                                  {notification.priority}
                                </Badge>
                                <div className="flex items-center space-x-1 text-xs text-gray-500">
                                  {getCategoryIcon(notification.category)}
                                  <span>{notification.category}</span>
                                </div>
                                {notification.geolocation && (
                                  <div className="flex items-center space-x-1 text-xs text-gray-500">
                                    <MapPin className="h-3 w-3" />
                                    <span>{notification.geolocation.city}</span>
                                    {notification.geolocation.distance && (
                                      <span>({notification.geolocation.distance}km)</span>
                                    )}
                                  </div>
                                )}
                              </div>

                              <div className="flex items-center space-x-1 text-xs text-gray-500">
                                <Clock className="h-3 w-3" />
                                <span>
                                  {new Date(notification.timestamp).toLocaleTimeString('fr-FR', {
                                    hour: '2-digit',
                                    minute: '2-digit'
                                  })}
                                </span>
                              </div>
                            </div>

                            {notification.actionUrl && (
                              <Button
                                variant="ghost"
                                size="sm"
                                className="mt-2 h-8 text-xs"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  window.location.href = notification.actionUrl!
                                }}
                              >
                                {notification.actionLabel || 'Voir détails'}
                              </Button>
                            )}
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </>
  )
}
