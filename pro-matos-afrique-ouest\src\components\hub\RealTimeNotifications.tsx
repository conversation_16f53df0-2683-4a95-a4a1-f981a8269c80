'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  X, 
  Bell, 
  AlertTriangle, 
  TrendingUp, 
  Package,
  Newspaper,
  CheckCircle
} from 'lucide-react'
import { useStore } from '@/store/useStore'
import { formatDate } from '@/lib/utils'

interface Notification {
  id: string
  type: 'market_alert' | 'stock_update' | 'news_update' | 'system'
  title: string
  message: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  timestamp: string
  isRead: boolean
  autoHide?: boolean
}

export default function RealTimeNotifications() {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [isVisible, setIsVisible] = useState(true)
  const { realTimeConnected } = useStore()

  // Simulation de nouvelles notifications
  useEffect(() => {
    if (!realTimeConnected) return

    const interval = setInterval(() => {
      const notificationTypes = [
        {
          type: 'market_alert' as const,
          title: 'Nouvelle Alerte Marché',
          message: 'Stock critique détecté chez ElectroDistrib Abidjan',
          severity: 'high' as const,
          autoHide: false
        },
        {
          type: 'stock_update' as const,
          title: 'Mise à Jour Stock',
          message: 'Réapprovisionnement de 200 unités - Disjoncteurs C32',
          severity: 'medium' as const,
          autoHide: true
        },
        {
          type: 'news_update' as const,
          title: 'Nouvelle Actualité',
          message: 'Nouvelle réglementation photovoltaïque au Sénégal',
          severity: 'low' as const,
          autoHide: true
        },
        {
          type: 'system' as const,
          title: 'Système',
          message: 'Synchronisation des données terminée avec succès',
          severity: 'low' as const,
          autoHide: true
        }
      ]

      // Ajouter une notification aléatoire toutes les 45 secondes
      if (Math.random() < 0.3) {
        const randomNotification = notificationTypes[Math.floor(Math.random() * notificationTypes.length)]
        const newNotification: Notification = {
          id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          ...randomNotification,
          timestamp: new Date().toISOString(),
          isRead: false
        }

        setNotifications(prev => [newNotification, ...prev.slice(0, 4)]) // Garder max 5 notifications

        // Auto-hide après 5 secondes si autoHide est true
        if (newNotification.autoHide) {
          setTimeout(() => {
            setNotifications(prev => prev.filter(n => n.id !== newNotification.id))
          }, 5000)
        }
      }
    }, 45000) // Toutes les 45 secondes

    return () => clearInterval(interval)
  }, [realTimeConnected])

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'market_alert':
        return <AlertTriangle className="h-5 w-5" />
      case 'stock_update':
        return <Package className="h-5 w-5" />
      case 'news_update':
        return <Newspaper className="h-5 w-5" />
      case 'system':
        return <CheckCircle className="h-5 w-5" />
      default:
        return <Bell className="h-5 w-5" />
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-500 border-red-600 text-white'
      case 'high':
        return 'bg-orange-500 border-orange-600 text-white'
      case 'medium':
        return 'bg-blue-500 border-blue-600 text-white'
      case 'low':
        return 'bg-green-500 border-green-600 text-white'
      default:
        return 'bg-gray-500 border-gray-600 text-white'
    }
  }

  const markAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(notif => 
        notif.id === id ? { ...notif, isRead: true } : notif
      )
    )
  }

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(notif => notif.id !== id))
  }

  const clearAllNotifications = () => {
    setNotifications([])
  }

  if (!isVisible || notifications.length === 0) {
    return null
  }

  return (
    <div className="fixed top-4 right-4 z-50 w-96 max-w-sm">
      {/* Header des notifications */}
      <div className="bg-white rounded-t-lg border border-gray-200 px-4 py-3 flex items-center justify-between shadow-lg">
        <div className="flex items-center space-x-2">
          <Bell className="h-5 w-5 text-amber-600" />
          <span className="font-semibold text-gray-900">Notifications</span>
          {realTimeConnected && (
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          )}
        </div>
        <div className="flex items-center space-x-2">
          {notifications.length > 1 && (
            <button
              onClick={clearAllNotifications}
              className="text-xs text-gray-500 hover:text-gray-700"
            >
              Tout effacer
            </button>
          )}
          <button
            onClick={() => setIsVisible(false)}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Liste des notifications */}
      <div className="max-h-96 overflow-y-auto">
        <AnimatePresence>
          {notifications.map((notification, index) => (
            <motion.div
              key={notification.id}
              initial={{ opacity: 0, x: 300, scale: 0.8 }}
              animate={{ opacity: 1, x: 0, scale: 1 }}
              exit={{ opacity: 0, x: 300, scale: 0.8 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className={`border-x border-gray-200 ${
                index === notifications.length - 1 ? 'border-b rounded-b-lg' : 'border-b-0'
              } ${
                notification.isRead ? 'bg-gray-50' : 'bg-white'
              } shadow-lg`}
            >
              <div className="p-4">
                <div className="flex items-start space-x-3">
                  <div className={`p-2 rounded-lg ${getSeverityColor(notification.severity)}`}>
                    {getNotificationIcon(notification.type)}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <h4 className="text-sm font-semibold text-gray-900 truncate">
                        {notification.title}
                      </h4>
                      <button
                        onClick={() => removeNotification(notification.id)}
                        className="text-gray-400 hover:text-gray-600 ml-2"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </div>
                    
                    <p className="text-sm text-gray-600 mb-2">
                      {notification.message}
                    </p>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-500">
                        {formatDate(notification.timestamp)}
                      </span>
                      
                      {!notification.isRead && (
                        <button
                          onClick={() => markAsRead(notification.id)}
                          className="text-xs text-amber-600 hover:text-amber-700 font-medium"
                        >
                          Marquer comme lu
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* Bouton pour réafficher si masqué */}
      {!isVisible && (
        <motion.button
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          onClick={() => setIsVisible(true)}
          className="fixed top-4 right-4 bg-amber-500 text-white p-3 rounded-full shadow-lg hover:bg-amber-600 transition-colors"
        >
          <Bell className="h-5 w-5" />
          {notifications.filter(n => !n.isRead).length > 0 && (
            <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
              {notifications.filter(n => !n.isRead).length}
            </span>
          )}
        </motion.button>
      )}
    </div>
  )
}
