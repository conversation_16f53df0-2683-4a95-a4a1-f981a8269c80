'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Mail, Shield, Zap, Crown } from 'lucide-react'
import { useAuthStore } from '@/lib/stores/authStore'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { toast } from 'sonner'
export default function SignInPage() {
  const [email, setEmail] = useState('')
  const [loading, setLoading] = useState(false)
  const [emailSent, setEmailSent] = useState(false)
  const { signIn } = useAuthStore()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!email) {
      toast.error('Veuillez saisir votre email')
      return
    }

    setLoading(true)
    toast.loading('Envoi du lien de connexion...', { id: 'signin' })

    try {
      const { error } = await signIn(email)
      
      if (error) throw error

      setEmailSent(true)
      toast.success('Lien de connexion envoyé !', { 
        id: 'signin',
        description: 'Vérifiez votre boîte email'
      })

    } catch (error: any) {
      console.error('Erreur de connexion:', error)
      toast.error('Erreur lors de l\'envoi', { 
        id: 'signin',
        description: error.message || 'Veuillez réessayer'
      })
    } finally {
      setLoading(false)
    }
  }

  const features = [
    {
      icon: <Shield className="h-6 w-6 text-blue-500" />,
      title: 'Hub Information',
      description: 'Alertes et veille technologique en temps réel'
    },
    {
      icon: <Zap className="h-6 w-6 text-green-500" />,
      title: 'Validation Technique',
      description: 'Expertise et validation par nos ingénieurs'
    },
    {
      icon: <Crown className="h-6 w-6 text-amber-500" />,
      title: 'Club Exclusif',
      description: 'Réseau premium et événements VIP'
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center p-4">
      <div className="w-full max-w-6xl grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
        
        {/* Left Side - Branding */}
        <motion.div
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          className="text-center lg:text-left space-y-8"
        >
          <div>
            <div className="flex items-center justify-center lg:justify-start space-x-3 mb-6">
              <div className="w-12 h-12 bg-gradient-to-r from-amber-500 to-amber-600 rounded-xl flex items-center justify-center">
                <Shield className="h-7 w-7 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Pro Matos</h1>
                <p className="text-sm text-gray-600">Afrique Ouest</p>
              </div>
            </div>
            
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              L'écosystème professionnel de l'électrique
            </h2>
            <p className="text-xl text-gray-600 mb-8">
              Rejoignez la communauté des experts électriciens d'Afrique de l'Ouest. 
              Accédez aux outils, ressources et réseau dont vous avez besoin pour exceller.
            </p>
          </div>

          <div className="space-y-6">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 + index * 0.1 }}
                className="flex items-start space-x-4"
              >
                <div className="flex-shrink-0">
                  {feature.icon}
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-1">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {feature.description}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>

          <div className="flex items-center justify-center lg:justify-start space-x-8 text-sm text-gray-500">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">1,200+</div>
              <div>Professionnels</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">50+</div>
              <div>Experts</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">15</div>
              <div>Pays</div>
            </div>
          </div>
        </motion.div>

        {/* Right Side - Auth Form */}
        <motion.div
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2 }}
          className="w-full max-w-md mx-auto"
        >
          <Card className="shadow-xl border-0">
            <CardHeader className="text-center pb-8">
              <CardTitle className="text-2xl font-bold text-gray-900">
                Connexion
              </CardTitle>
              <CardDescription className="text-gray-600">
                Connectez-vous avec votre email professionnel
              </CardDescription>
            </CardHeader>
            
            <CardContent className="space-y-6">
              {emailSent ? (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="text-center space-y-4"
                >
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                    <Mail className="h-8 w-8 text-green-600" />
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      Email envoyé !
                    </h3>
                    <p className="text-gray-600 mb-4">
                      Nous avons envoyé un lien de connexion à <strong>{email}</strong>
                    </p>
                    <p className="text-sm text-gray-500">
                      Vérifiez votre boîte email et cliquez sur le lien pour vous connecter.
                    </p>
                  </div>
                  
                  <Button
                    variant="outline"
                    onClick={() => {
                      setEmailSent(false)
                      setEmail('')
                    }}
                    className="w-full"
                  >
                    Utiliser un autre email
                  </Button>
                </motion.div>
              ) : (
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="email">Email professionnel</Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      required
                      className="h-12"
                    />
                  </div>

                  <Alert>
                    <Mail className="h-4 w-4" />
                    <AlertDescription>
                      Nous vous enverrons un lien de connexion sécurisé par email. 
                      Aucun mot de passe requis.
                    </AlertDescription>
                  </Alert>

                  <Button
                    type="submit"
                    disabled={loading}
                    className="w-full h-12 bg-amber-600 hover:bg-amber-700 text-white font-semibold"
                  >
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                        Envoi en cours...
                      </>
                    ) : (
                      <>
                        <Mail className="h-4 w-4 mr-2" />
                        Envoyer le lien de connexion
                      </>
                    )}
                  </Button>
                </form>
              )}

              <div className="text-center">
                <p className="text-xs text-gray-500">
                  En vous connectant, vous acceptez nos{' '}
                  <a href="#" className="text-amber-600 hover:underline">
                    conditions d'utilisation
                  </a>{' '}
                  et notre{' '}
                  <a href="#" className="text-amber-600 hover:underline">
                    politique de confidentialité
                  </a>
                </p>
              </div>
            </CardContent>
          </Card>

          <div className="mt-8 text-center">
            <p className="text-sm text-gray-600">
              Nouveau sur Pro Matos ?{' '}
              <Button variant="link" className="p-0 h-auto text-amber-600">
                Découvrir nos offres
              </Button>
            </p>
          </div>
        </motion.div>
      </div>
    </div>
  )
}
