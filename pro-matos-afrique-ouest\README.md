# Pro Matos Afrique Ouest

L'écosystème professionnel de référence pour les électriciens d'Afrique de l'Ouest.

## 🚀 Démarrage Rapide

### Prérequis
- Node.js 18+ 
- npm 8+
- Compte Supabase

### Installation

```bash
# Cloner le projet
git clone <repository-url>
cd pro-matos-afrique-ouest

# Installer les dépendances (Next.js 14.2.4 locked)
npm install --legacy-peer-deps

# Installer les navigateurs Playwright
npx playwright install

# Configurer les variables d'environnement
cp .env.example .env.local
# Éditer .env.local avec vos clés Supabase

# Lancer en développement
npm run dev
```

L'application sera disponible sur [http://localhost:3000](http://localhost:3000)

## 🏗️ Architecture

### Stack Technique
- **Frontend**: Next.js 14.2.4 (App Router) + React 18 + TypeScript
- **Styling**: Tailwind CSS 3.4 + shadcn/ui + Framer Motion
- **Backend**: Supabase (PostgreSQL + Auth + Storage)
- **State**: Zustand
- **Tests**: Playwright

### Structure du Projet
```
├── app/                    # Pages Next.js (App Router)
│   ├── auth/              # Authentification
│   ├── hub/               # Hub d'information
│   ├── validation/        # Validation technique
│   ├── kits/              # Kits de prescription
│   ├── club/              # Club VIP
│   └── crm/               # Administration
├── src/components/        # Composants React
│   ├── ui/                # Composants UI (shadcn)
│   └── layout/            # Layouts
├── src/lib/               # Utilitaires et configuration
│   ├── stores/            # Stores Zustand
│   ├── supabase/          # Configuration Supabase
│   └── types/             # Types TypeScript
└── tests/                 # Tests E2E Playwright
```

## 🗃️ Base de Données

### Configuration Supabase

1. **Créer un projet Supabase**
2. **Exécuter le schéma** : `lib/supabase/schema.sql`
3. **Insérer les données de test** : `lib/supabase/seed.sql`
4. **Configurer les variables d'environnement** :

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### Schéma Principal
- `users` - Profils utilisateurs avec rôles
- `alerts` - Alertes et informations
- `validations` - Demandes de validation technique
- `kits` - Kits de prescription
- `club_events` - Événements VIP

## 🎯 Fonctionnalités

### 🔵 Hub d'Information (`/hub`)
- Alertes temps réel (normes, ruptures, promotions)
- Système d'abonnement aux alertes
- Filtrage et recherche avancée
- Dashboard avec métriques live

### 🟢 Validation Technique (`/validation`)
- Upload de documents (PDF, images)
- Formulaire de contexte détaillé
- Historique des validations (`/validation/history`)
- Notifications email automatiques

### 🟣 Kits de Prescription (`/kits`)
- Catalogue de documents techniques
- Téléchargements avec compteurs
- Kits premium pour membres VIP
- Catégorisation et recherche

### 🟡 Club VIP (`/club`)
- Événements exclusifs avec inscriptions
- Processus d'upgrade vers VIP
- Badge personnalisé téléchargeable
- Réseau professionnel premium

### 🔴 CRM Admin (`/crm`)
- Gestion des utilisateurs
- Système de scoring (white/grey/black)
- Validation des demandes techniques
- Analytics et métriques

## 🔐 Authentification & Permissions

### Rôles Utilisateurs
- **Guest** : Accès limité au hub
- **Member** : Accès validation + kits
- **VIP** : Accès club + kits premium
- **Admin** : Accès CRM complet

### Magic Link Auth
- Connexion sans mot de passe
- Liens sécurisés par email
- Session persistante

## 🧪 Tests

### Lancer les Tests
```bash
# Tests E2E
npm run test

# Interface de test
npm run test:ui

# Tests spécifiques
npx playwright test auth-flow.spec.ts
```

### Couverture de Test
- ✅ Flux d'authentification
- ✅ Navigation hub avec filtres
- ✅ Upload de validation
- ✅ Gestion CRM admin
- ✅ Responsive design

## 📱 Responsive Design

### Breakpoints
- **Mobile** : < 768px
- **Tablet** : 768px - 1024px  
- **Desktop** : > 1024px

### Fonctionnalités Mobile
- Menu hamburger avec overlay
- Navigation tactile optimisée
- Grilles adaptatives
- Performance 90+ Lighthouse

## 🚀 Déploiement

### Vercel (Recommandé)
```bash
# Connecter à Vercel
vercel

# Configurer les variables d'environnement
vercel env add NEXT_PUBLIC_SUPABASE_URL
vercel env add NEXT_PUBLIC_SUPABASE_ANON_KEY

# Déployer
vercel --prod
```

### Variables d'Environnement Requises
```env
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
```

## 🔧 Scripts Disponibles

```bash
npm run dev          # Développement Next.js 14
npm run build        # Build de production
npm run start        # Serveur de production
npm run lint         # Linting ESLint
npm run test         # Tests Playwright
npm run test:ui      # Interface de test
npm run type-check   # Vérification TypeScript
```

## 📊 Performance

### Objectifs Lighthouse
- **Performance** : > 90
- **Accessibility** : > 95
- **Best Practices** : > 90
- **SEO** : > 90

### Optimisations
- Images Next.js optimisées
- Code splitting automatique
- Lazy loading des composants
- Cache Supabase intelligent

## 🤝 Contribution

### Workflow de Développement
1. Fork du projet
2. Créer une branche feature
3. Développer avec tests
4. Pull request avec description

### Standards de Code
- TypeScript strict
- ESLint + Prettier
- Commits conventionnels
- Tests obligatoires

## 📄 Licence

Propriétaire - Pro Matos Afrique Ouest

## 🆘 Support

- **Documentation** : Ce README
- **Issues** : GitHub Issues
- **Email** : <EMAIL>

---

**Pro Matos Afrique Ouest** - L'écosystème qui transforme l'industrie électrique en Afrique de l'Ouest 🌍⚡
