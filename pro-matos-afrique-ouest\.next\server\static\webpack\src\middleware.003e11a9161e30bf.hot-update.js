"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("src/middleware",{

/***/ "(middleware)/./src/middleware.ts":
/*!***************************!*\
  !*** ./src/middleware.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\nasync function middleware(req) {\n    const res = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    // TEMPORAIRE: Désactiver la protection pour permettre l'accès libre pendant les tests\n    // TODO: Réactiver l'authentification une fois Supabase configuré\n    /*\n  const supabase = createMiddlewareClient({ req, res })\n\n  // Vérifier la session utilisateur\n  const {\n    data: { session },\n  } = await supabase.auth.getSession()\n\n  // Routes protégées qui nécessitent une authentification\n  const protectedRoutes = ['/hub', '/validation', '/kits', '/club', '/crm', '/expert', '/prescriptor']\n  const isProtectedRoute = protectedRoutes.some(route => req.nextUrl.pathname.startsWith(route))\n\n  // Rediriger vers la connexion si pas authentifié\n  if (isProtectedRoute && !session) {\n    return NextResponse.redirect(new URL('/auth/signin', req.url))\n  }\n  */ return res;\n}\nconst config = {\n    matcher: [\n        /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder\n     */ \"/((?!api|_next/static|_next/image|favicon.ico|public).*)\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKG1pZGRsZXdhcmUpLy4vc3JjL21pZGRsZXdhcmUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQzBDO0FBR25DLGVBQWVDLFdBQVdDLEdBQWdCO0lBQy9DLE1BQU1DLE1BQU1ILHFEQUFZQSxDQUFDSSxJQUFJO0lBRTdCLHNGQUFzRjtJQUN0RixpRUFBaUU7SUFFakU7Ozs7Ozs7Ozs7Ozs7Ozs7RUFnQkEsR0FFQSxPQUFPRDtBQUNUO0FBRU8sTUFBTUUsU0FBUztJQUNwQkMsU0FBUztRQUNQOzs7Ozs7O0tBT0MsR0FDRDtLQUNEO0FBQ0gsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvbWlkZGxld2FyZS50cz9kMTk5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZU1pZGRsZXdhcmVDbGllbnQgfSBmcm9tICdAc3VwYWJhc2UvYXV0aC1oZWxwZXJzLW5leHRqcydcbmltcG9ydCB7IE5leHRSZXNwb25zZSB9IGZyb20gJ25leHQvc2VydmVyJ1xuaW1wb3J0IHR5cGUgeyBOZXh0UmVxdWVzdCB9IGZyb20gJ25leHQvc2VydmVyJ1xuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gbWlkZGxld2FyZShyZXE6IE5leHRSZXF1ZXN0KSB7XG4gIGNvbnN0IHJlcyA9IE5leHRSZXNwb25zZS5uZXh0KClcblxuICAvLyBURU1QT1JBSVJFOiBEw6lzYWN0aXZlciBsYSBwcm90ZWN0aW9uIHBvdXIgcGVybWV0dHJlIGwnYWNjw6hzIGxpYnJlIHBlbmRhbnQgbGVzIHRlc3RzXG4gIC8vIFRPRE86IFLDqWFjdGl2ZXIgbCdhdXRoZW50aWZpY2F0aW9uIHVuZSBmb2lzIFN1cGFiYXNlIGNvbmZpZ3Vyw6lcblxuICAvKlxuICBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZU1pZGRsZXdhcmVDbGllbnQoeyByZXEsIHJlcyB9KVxuXG4gIC8vIFbDqXJpZmllciBsYSBzZXNzaW9uIHV0aWxpc2F0ZXVyXG4gIGNvbnN0IHtcbiAgICBkYXRhOiB7IHNlc3Npb24gfSxcbiAgfSA9IGF3YWl0IHN1cGFiYXNlLmF1dGguZ2V0U2Vzc2lvbigpXG5cbiAgLy8gUm91dGVzIHByb3TDqWfDqWVzIHF1aSBuw6ljZXNzaXRlbnQgdW5lIGF1dGhlbnRpZmljYXRpb25cbiAgY29uc3QgcHJvdGVjdGVkUm91dGVzID0gWycvaHViJywgJy92YWxpZGF0aW9uJywgJy9raXRzJywgJy9jbHViJywgJy9jcm0nLCAnL2V4cGVydCcsICcvcHJlc2NyaXB0b3InXVxuICBjb25zdCBpc1Byb3RlY3RlZFJvdXRlID0gcHJvdGVjdGVkUm91dGVzLnNvbWUocm91dGUgPT4gcmVxLm5leHRVcmwucGF0aG5hbWUuc3RhcnRzV2l0aChyb3V0ZSkpXG5cbiAgLy8gUmVkaXJpZ2VyIHZlcnMgbGEgY29ubmV4aW9uIHNpIHBhcyBhdXRoZW50aWZpw6lcbiAgaWYgKGlzUHJvdGVjdGVkUm91dGUgJiYgIXNlc3Npb24pIHtcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLnJlZGlyZWN0KG5ldyBVUkwoJy9hdXRoL3NpZ25pbicsIHJlcS51cmwpKVxuICB9XG4gICovXG5cbiAgcmV0dXJuIHJlc1xufVxuXG5leHBvcnQgY29uc3QgY29uZmlnID0ge1xuICBtYXRjaGVyOiBbXG4gICAgLypcbiAgICAgKiBNYXRjaCBhbGwgcmVxdWVzdCBwYXRocyBleGNlcHQgZm9yIHRoZSBvbmVzIHN0YXJ0aW5nIHdpdGg6XG4gICAgICogLSBhcGkgKEFQSSByb3V0ZXMpXG4gICAgICogLSBfbmV4dC9zdGF0aWMgKHN0YXRpYyBmaWxlcylcbiAgICAgKiAtIF9uZXh0L2ltYWdlIChpbWFnZSBvcHRpbWl6YXRpb24gZmlsZXMpXG4gICAgICogLSBmYXZpY29uLmljbyAoZmF2aWNvbiBmaWxlKVxuICAgICAqIC0gcHVibGljIGZvbGRlclxuICAgICAqL1xuICAgICcvKCg/IWFwaXxfbmV4dC9zdGF0aWN8X25leHQvaW1hZ2V8ZmF2aWNvbi5pY298cHVibGljKS4qKScsXG4gIF0sXG59XG4iXSwibmFtZXMiOlsiTmV4dFJlc3BvbnNlIiwibWlkZGxld2FyZSIsInJlcSIsInJlcyIsIm5leHQiLCJjb25maWciLCJtYXRjaGVyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.ts\n");

/***/ })

});