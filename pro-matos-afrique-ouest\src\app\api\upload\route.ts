import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { FileService } from '@/lib/services/fileService'
import { EmailService } from '@/lib/services/emailService'

export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })

    // Vérifier l'authentification
    const { data: { session }, error: authError } = await supabase.auth.getSession()

    if (authError || !session) {
      return NextResponse.json(
        { error: 'Non authentifié' },
        { status: 401 }
      )
    }

    const formData = await request.formData()
    const file = formData.get('file') as File
    const type = formData.get('type') as string // 'validation' | 'kit' | 'avatar'
    const title = formData.get('title') as string
    const description = formData.get('description') as string
    const projectType = formData.get('projectType') as string
    const urgency = formData.get('urgency') as string

    if (!file || !type) {
      return NextResponse.json(
        { error: 'Fichier et type requis' },
        { status: 400 }
      )
    }

    let uploadResult

    // Upload selon le type
    switch (type) {
      case 'validation':
        if (!title || !description) {
          return NextResponse.json(
            { error: 'Titre et description requis pour validation' },
            { status: 400 }
          )
        }

        // Upload du fichier
        uploadResult = await FileService.uploadValidationFile(file, session.user.id)

        if (!uploadResult.success) {
          return NextResponse.json(
            { error: uploadResult.error },
            { status: 400 }
          )
        }

        // Enregistrer en base
        const { data: validation, error: dbError } = await supabase
          .from('validations')
          .insert({
            user_id: session.user.id,
            title,
            description,
            project_type: projectType,
            urgency: urgency || 'normal',
            file_url: uploadResult.url,
            file_name: file.name,
            file_size: file.size,
            status: 'En cours'
          })
          .select()
          .single()

        if (dbError) {
          console.error('Erreur DB validation:', dbError)
          return NextResponse.json(
            { error: 'Erreur sauvegarde validation' },
            { status: 500 }
          )
        }

        // Récupérer les infos utilisateur pour l'email
        const { data: user } = await supabase
          .from('users')
          .select('full_name, email')
          .eq('id', session.user.id)
          .single()

        // Envoyer les notifications email
        const emailResult = await EmailService.sendValidationNotification({
          userEmail: session.user.email || '',
          userName: user?.full_name || session.user.email || '',
          validationTitle: title,
          validationDescription: description,
          projectType: projectType || '',
          urgency: urgency || 'normal',
          fileName: file.name,
          validationId: validation.id
        })

        if (!emailResult.success) {
          console.error('Erreur envoi email:', emailResult.error)
          // Ne pas faire échouer la requête pour un problème d'email
        }

        return NextResponse.json({
          success: true,
          data: {
            validation,
            fileUrl: uploadResult.url,
            emailSent: emailResult.success
          }
        })

      case 'kit':
        const kitId = formData.get('kitId') as string
        if (!kitId) {
          return NextResponse.json(
            { error: 'ID du kit requis' },
            { status: 400 }
          )
        }

        uploadResult = await FileService.uploadKitFile(file, kitId)

        if (!uploadResult.success) {
          return NextResponse.json(
            { error: uploadResult.error },
            { status: 400 }
          )
        }

        return NextResponse.json({
          success: true,
          data: {
            fileUrl: uploadResult.url,
            filePath: uploadResult.path
          }
        })

      default:
        return NextResponse.json(
          { error: 'Type d\'upload non supporté' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('Erreur API upload:', error)
    return NextResponse.json(
      { error: 'Erreur serveur' },
      { status: 500 }
    )
  }
}
