import { supabase } from '@/lib/supabase/client'

export interface EmailTemplate {
  subject: string
  html: string
  text: string
}

export interface ValidationEmailData {
  userEmail: string
  userName: string
  validationTitle: string
  validationDescription: string
  projectType: string
  urgency: string
  fileName?: string
  validationId: number
}

export interface NotificationEmailData {
  userEmail: string
  userName: string
  alertTitle: string
  alertBody: string
  alertType: string
  alertCategory: string
}

export class EmailService {
  private static readonly ADMIN_EMAIL = '<EMAIL>'
  private static readonly FROM_EMAIL = '<EMAIL>'
  private static readonly APP_NAME = 'Pro Matos Afrique Ouest'

  /**
   * Template pour notification de validation technique
   */
  static getValidationNotificationTemplate(data: ValidationEmailData): EmailTemplate {
    const urgencyColors = {
      low: '#10B981',
      normal: '#F59E0B', 
      high: '#EF4444'
    }

    const urgencyLabels = {
      low: 'Faible',
      normal: 'Normale',
      high: 'Élevée'
    }

    const subject = `[${EmailService.APP_NAME}] Nouvelle demande de validation - ${data.validationTitle}`

    const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${subject}</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: #f8fafc; padding: 30px; border-radius: 0 0 8px 8px; }
        .card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .urgency { display: inline-block; padding: 4px 12px; border-radius: 20px; color: white; font-weight: bold; }
        .button { display: inline-block; background: #1e40af; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }
        .footer { text-align: center; color: #6b7280; font-size: 14px; margin-top: 30px; }
        .info-row { display: flex; justify-content: space-between; margin: 10px 0; padding: 10px 0; border-bottom: 1px solid #e5e7eb; }
        .info-label { font-weight: bold; color: #374151; }
        .info-value { color: #6b7280; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>🔧 Nouvelle Demande de Validation</h1>
          <p>Une nouvelle demande de validation technique a été soumise</p>
        </div>
        
        <div class="content">
          <div class="card">
            <h2>📋 Détails de la demande</h2>
            
            <div class="info-row">
              <span class="info-label">Titre :</span>
              <span class="info-value">${data.validationTitle}</span>
            </div>
            
            <div class="info-row">
              <span class="info-label">Type de projet :</span>
              <span class="info-value">${data.projectType || 'Non spécifié'}</span>
            </div>
            
            <div class="info-row">
              <span class="info-label">Urgence :</span>
              <span class="urgency" style="background-color: ${urgencyColors[data.urgency as keyof typeof urgencyColors]}">
                ${urgencyLabels[data.urgency as keyof typeof urgencyLabels]}
              </span>
            </div>
            
            ${data.fileName ? `
            <div class="info-row">
              <span class="info-label">Fichier joint :</span>
              <span class="info-value">📎 ${data.fileName}</span>
            </div>
            ` : ''}
            
            <div class="info-row">
              <span class="info-label">Demandeur :</span>
              <span class="info-value">${data.userName} (${data.userEmail})</span>
            </div>
          </div>

          <div class="card">
            <h3>📝 Description</h3>
            <p style="background: #f1f5f9; padding: 15px; border-radius: 6px; margin: 10px 0;">
              ${data.validationDescription}
            </p>
          </div>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXT_PUBLIC_APP_URL}/crm" class="button">
              🔍 Voir dans le CRM
            </a>
          </div>
        </div>

        <div class="footer">
          <p>📧 Email automatique de ${EmailService.APP_NAME}</p>
          <p>🌍 Votre plateforme professionnelle pour l'Afrique de l'Ouest</p>
        </div>
      </div>
    </body>
    </html>
    `

    const text = `
    ${EmailService.APP_NAME} - Nouvelle demande de validation

    Titre: ${data.validationTitle}
    Type: ${data.projectType || 'Non spécifié'}
    Urgence: ${urgencyLabels[data.urgency as keyof typeof urgencyLabels]}
    ${data.fileName ? `Fichier: ${data.fileName}` : ''}
    
    Demandeur: ${data.userName} (${data.userEmail})
    
    Description:
    ${data.validationDescription}
    
    Voir dans le CRM: ${process.env.NEXT_PUBLIC_APP_URL}/crm
    `

    return { subject, html, text }
  }

  /**
   * Template pour confirmation utilisateur
   */
  static getValidationConfirmationTemplate(data: ValidationEmailData): EmailTemplate {
    const subject = `[${EmailService.APP_NAME}] Confirmation de votre demande de validation`

    const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${subject}</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #059669 0%, #10b981 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: #f0fdf4; padding: 30px; border-radius: 0 0 8px 8px; }
        .card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .button { display: inline-block; background: #059669; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }
        .footer { text-align: center; color: #6b7280; font-size: 14px; margin-top: 30px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>✅ Demande Reçue</h1>
          <p>Votre demande de validation a été transmise à notre équipe technique</p>
        </div>
        
        <div class="content">
          <div class="card">
            <h2>Bonjour ${data.userName},</h2>
            <p>Nous avons bien reçu votre demande de validation technique :</p>
            <p><strong>"${data.validationTitle}"</strong></p>
            
            <p>Notre équipe d'experts va examiner votre demande et vous répondra dans les plus brefs délais.</p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${process.env.NEXT_PUBLIC_APP_URL}/validation/history" class="button">
                📋 Suivre ma demande
              </a>
            </div>
          </div>
        </div>

        <div class="footer">
          <p>📧 Email automatique de ${EmailService.APP_NAME}</p>
          <p>🌍 Votre plateforme professionnelle pour l'Afrique de l'Ouest</p>
        </div>
      </div>
    </body>
    </html>
    `

    const text = `
    ${EmailService.APP_NAME} - Confirmation de demande

    Bonjour ${data.userName},

    Nous avons bien reçu votre demande de validation technique :
    "${data.validationTitle}"

    Notre équipe d'experts va examiner votre demande et vous répondra dans les plus brefs délais.

    Suivre votre demande: ${process.env.NEXT_PUBLIC_APP_URL}/validation/history
    `

    return { subject, html, text }
  }

  /**
   * Envoie un email de notification de validation
   */
  static async sendValidationNotification(data: ValidationEmailData): Promise<{ success: boolean; error?: string }> {
    try {
      // Template pour l'admin
      const adminTemplate = EmailService.getValidationNotificationTemplate(data)
      
      // Template pour l'utilisateur
      const userTemplate = EmailService.getValidationConfirmationTemplate(data)

      // Envoyer à l'admin (simulation - en production utiliser un service email)
      console.log('📧 Email admin:', {
        to: EmailService.ADMIN_EMAIL,
        subject: adminTemplate.subject,
        html: adminTemplate.html
      })

      // Envoyer à l'utilisateur (simulation)
      console.log('📧 Email utilisateur:', {
        to: data.userEmail,
        subject: userTemplate.subject,
        html: userTemplate.html
      })

      // En production, remplacer par un vrai service email (SendGrid, Resend, etc.)
      // await sendEmail(EmailService.ADMIN_EMAIL, adminTemplate)
      // await sendEmail(data.userEmail, userTemplate)

      return { success: true }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erreur envoi email'
      }
    }
  }

  /**
   * Envoie une notification d'alerte
   */
  static async sendAlertNotification(data: NotificationEmailData): Promise<{ success: boolean; error?: string }> {
    try {
      const subject = `[${EmailService.APP_NAME}] Nouvelle alerte - ${data.alertTitle}`

      const html = `
      <h2>🚨 Nouvelle Alerte</h2>
      <p><strong>${data.alertTitle}</strong></p>
      <p>Catégorie: ${data.alertCategory}</p>
      <p>Type: ${data.alertType}</p>
      <p>${data.alertBody}</p>
      `

      console.log('📧 Email alerte:', {
        to: data.userEmail,
        subject,
        html
      })

      return { success: true }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erreur envoi alerte'
      }
    }
  }
}
