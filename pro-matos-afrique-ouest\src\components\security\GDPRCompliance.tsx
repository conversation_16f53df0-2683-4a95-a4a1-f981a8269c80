'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Shield, 
  FileText, 
  Download,
  Trash2,
  Eye,
  Edit,
  Check,
  X,
  AlertTriangle,
  Info,
  Clock,
  User,
  Database,
  Lock,
  Settings,
  Mail
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { toast } from 'sonner'

interface ConsentRecord {
  id: string
  type: 'essential' | 'analytics' | 'marketing' | 'personalization'
  name: string
  description: string
  granted: boolean
  timestamp: string
  version: string
  source: 'explicit' | 'implicit' | 'legitimate_interest'
}

interface DataCategory {
  id: string
  name: string
  description: string
  dataTypes: string[]
  purpose: string
  retention: string
  thirdParties: string[]
  lawfulBasis: string
}

interface GDPRRequest {
  id: string
  type: 'access' | 'rectification' | 'erasure' | 'portability' | 'restriction'
  status: 'pending' | 'processing' | 'completed' | 'rejected'
  requestDate: string
  completionDate?: string
  description: string
}

interface GDPRComplianceProps {
  className?: string
}

export default function GDPRCompliance({ className = '' }: GDPRComplianceProps) {
  const [consents, setConsents] = useState<ConsentRecord[]>([
    {
      id: '1',
      type: 'essential',
      name: 'Cookies Essentiels',
      description: 'Nécessaires au fonctionnement du site',
      granted: true,
      timestamp: new Date().toISOString(),
      version: '1.0',
      source: 'explicit'
    },
    {
      id: '2',
      type: 'analytics',
      name: 'Analyse d\'Audience',
      description: 'Mesure de performance et amélioration',
      granted: false,
      timestamp: new Date(Date.now() - 86400000).toISOString(),
      version: '1.0',
      source: 'explicit'
    },
    {
      id: '3',
      type: 'marketing',
      name: 'Marketing Ciblé',
      description: 'Publicités personnalisées',
      granted: false,
      timestamp: new Date(Date.now() - 172800000).toISOString(),
      version: '1.0',
      source: 'explicit'
    },
    {
      id: '4',
      type: 'personalization',
      name: 'Personnalisation',
      description: 'Contenu adapté à vos préférences',
      granted: true,
      timestamp: new Date(Date.now() - 259200000).toISOString(),
      version: '1.0',
      source: 'explicit'
    }
  ])

  const [dataCategories] = useState<DataCategory[]>([
    {
      id: '1',
      name: 'Données d\'Identification',
      description: 'Informations permettant de vous identifier',
      dataTypes: ['Nom', 'Prénom', 'Email', 'Téléphone', 'Adresse'],
      purpose: 'Gestion du compte utilisateur et communication',
      retention: '3 ans après dernière activité',
      thirdParties: ['Fournisseur email', 'Service de paiement'],
      lawfulBasis: 'Exécution du contrat'
    },
    {
      id: '2',
      name: 'Données Techniques',
      description: 'Informations sur votre utilisation du service',
      dataTypes: ['Adresse IP', 'Navigateur', 'Système d\'exploitation', 'Cookies'],
      purpose: 'Amélioration du service et sécurité',
      retention: '13 mois',
      thirdParties: ['Service d\'analyse', 'CDN'],
      lawfulBasis: 'Intérêt légitime'
    },
    {
      id: '3',
      name: 'Données Professionnelles',
      description: 'Informations liées à votre activité',
      dataTypes: ['Entreprise', 'Poste', 'Secteur d\'activité', 'Projets'],
      purpose: 'Personnalisation du service',
      retention: '5 ans',
      thirdParties: ['Partenaires commerciaux'],
      lawfulBasis: 'Consentement'
    }
  ])

  const [requests, setRequests] = useState<GDPRRequest[]>([
    {
      id: '1',
      type: 'access',
      status: 'completed',
      requestDate: new Date(Date.now() - 604800000).toISOString(),
      completionDate: new Date(Date.now() - 518400000).toISOString(),
      description: 'Demande d\'accès aux données personnelles'
    },
    {
      id: '2',
      type: 'rectification',
      status: 'processing',
      requestDate: new Date(Date.now() - 172800000).toISOString(),
      description: 'Correction de l\'adresse email'
    }
  ])

  const [showConsentBanner, setShowConsentBanner] = useState(false)

  // Vérifier si le consentement est requis
  useEffect(() => {
    const hasGivenConsent = localStorage.getItem('gdpr-consent')
    if (!hasGivenConsent) {
      setShowConsentBanner(true)
    }
  }, [])

  // Mettre à jour un consentement
  const updateConsent = (consentId: string, granted: boolean) => {
    setConsents(prev => prev.map(consent => 
      consent.id === consentId 
        ? { 
            ...consent, 
            granted, 
            timestamp: new Date().toISOString(),
            source: 'explicit' as const
          }
        : consent
    ))

    toast.success(`Consentement ${granted ? 'accordé' : 'retiré'} pour ${consents.find(c => c.id === consentId)?.name}`)
  }

  // Accepter tous les consentements
  const acceptAllConsents = () => {
    setConsents(prev => prev.map(consent => ({
      ...consent,
      granted: true,
      timestamp: new Date().toISOString(),
      source: 'explicit' as const
    })))

    localStorage.setItem('gdpr-consent', 'all-accepted')
    setShowConsentBanner(false)
    toast.success('Tous les consentements ont été acceptés')
  }

  // Refuser les consentements non essentiels
  const rejectNonEssential = () => {
    setConsents(prev => prev.map(consent => ({
      ...consent,
      granted: consent.type === 'essential',
      timestamp: new Date().toISOString(),
      source: 'explicit' as const
    })))

    localStorage.setItem('gdpr-consent', 'essential-only')
    setShowConsentBanner(false)
    toast.success('Seuls les cookies essentiels ont été acceptés')
  }

  // Créer une nouvelle demande RGPD
  const createGDPRRequest = (type: GDPRRequest['type']) => {
    const newRequest: GDPRRequest = {
      id: Date.now().toString(),
      type,
      status: 'pending',
      requestDate: new Date().toISOString(),
      description: getRequestDescription(type)
    }

    setRequests(prev => [newRequest, ...prev])
    toast.success('Demande RGPD créée avec succès')
  }

  const getRequestDescription = (type: GDPRRequest['type']): string => {
    switch (type) {
      case 'access': return 'Demande d\'accès aux données personnelles'
      case 'rectification': return 'Demande de rectification des données'
      case 'erasure': return 'Demande d\'effacement des données'
      case 'portability': return 'Demande de portabilité des données'
      case 'restriction': return 'Demande de limitation du traitement'
      default: return 'Demande RGPD'
    }
  }

  // Télécharger les données
  const downloadData = () => {
    const data = {
      consents,
      dataCategories,
      requests,
      exportDate: new Date().toISOString()
    }

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'mes-donnees-rgpd.json'
    a.click()
    URL.revokeObjectURL(url)

    toast.success('Données téléchargées avec succès')
  }

  const getConsentTypeColor = (type: string) => {
    switch (type) {
      case 'essential': return 'bg-green-100 text-green-800 border-green-200'
      case 'analytics': return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'marketing': return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'personalization': return 'bg-orange-100 text-orange-800 border-orange-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getRequestStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800 border-green-200'
      case 'processing': return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'rejected': return 'bg-red-100 text-red-800 border-red-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getRequestTypeIcon = (type: string) => {
    switch (type) {
      case 'access': return <Eye className="h-4 w-4" />
      case 'rectification': return <Edit className="h-4 w-4" />
      case 'erasure': return <Trash2 className="h-4 w-4" />
      case 'portability': return <Download className="h-4 w-4" />
      case 'restriction': return <Lock className="h-4 w-4" />
      default: return <FileText className="h-4 w-4" />
    }
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-400 to-blue-500 rounded-lg flex items-center justify-center">
                <Shield className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle>Conformité RGPD</CardTitle>
                <CardDescription>
                  Gestion de vos données personnelles et de vos droits
                </CardDescription>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Badge className="bg-green-100 text-green-800 border-green-200">
                <Check className="h-3 w-3 mr-1" />
                Conforme
              </Badge>
              <Button onClick={downloadData} variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Exporter mes données
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Contenu principal avec onglets */}
      <Tabs defaultValue="consents" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="consents">Consentements</TabsTrigger>
          <TabsTrigger value="data">Mes Données</TabsTrigger>
          <TabsTrigger value="requests">Mes Demandes</TabsTrigger>
          <TabsTrigger value="rights">Mes Droits</TabsTrigger>
        </TabsList>

        {/* Onglet Consentements */}
        <TabsContent value="consents" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Gestion des Consentements</CardTitle>
              <CardDescription>
                Contrôlez l'utilisation de vos données personnelles
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {consents.map((consent) => (
                  <div key={consent.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h4 className="font-medium text-gray-900">{consent.name}</h4>
                        <Badge className={getConsentTypeColor(consent.type)}>
                          {consent.type}
                        </Badge>
                        {consent.type === 'essential' && (
                          <Badge variant="outline" className="text-xs">
                            Obligatoire
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{consent.description}</p>
                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        <span>Version: {consent.version}</span>
                        <span>•</span>
                        <span>Modifié: {new Date(consent.timestamp).toLocaleDateString('fr-FR')}</span>
                        <span>•</span>
                        <span>Source: {consent.source}</span>
                      </div>
                    </div>
                    
                    <Switch
                      checked={consent.granted}
                      onCheckedChange={(checked) => updateConsent(consent.id, checked)}
                      disabled={consent.type === 'essential'}
                    />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Onglet Mes Données */}
        <TabsContent value="data" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {dataCategories.map((category) => (
              <Card key={category.id}>
                <CardHeader>
                  <CardTitle className="text-lg">{category.name}</CardTitle>
                  <CardDescription>{category.description}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h5 className="font-medium text-gray-900 mb-2">Types de données</h5>
                    <div className="flex flex-wrap gap-1">
                      {category.dataTypes.map((type, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {type}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h5 className="font-medium text-gray-900 mb-1">Finalité</h5>
                    <p className="text-sm text-gray-600">{category.purpose}</p>
                  </div>

                  <div>
                    <h5 className="font-medium text-gray-900 mb-1">Durée de conservation</h5>
                    <p className="text-sm text-gray-600">{category.retention}</p>
                  </div>

                  <div>
                    <h5 className="font-medium text-gray-900 mb-1">Base légale</h5>
                    <Badge className="bg-blue-100 text-blue-800 border-blue-200">
                      {category.lawfulBasis}
                    </Badge>
                  </div>

                  {category.thirdParties.length > 0 && (
                    <div>
                      <h5 className="font-medium text-gray-900 mb-2">Tiers destinataires</h5>
                      <div className="space-y-1">
                        {category.thirdParties.map((party, index) => (
                          <div key={index} className="text-sm text-gray-600 flex items-center">
                            <div className="w-2 h-2 bg-gray-400 rounded-full mr-2"></div>
                            {party}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Onglet Mes Demandes */}
        <TabsContent value="requests" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Mes Demandes RGPD</CardTitle>
                  <CardDescription>
                    Historique de vos demandes d'exercice de droits
                  </CardDescription>
                </div>

                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => createGDPRRequest('access')}
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    Accès
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => createGDPRRequest('rectification')}
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Rectification
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => createGDPRRequest('erasure')}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Effacement
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {requests.map((request) => (
                  <div key={request.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                        {getRequestTypeIcon(request.type)}
                      </div>

                      <div>
                        <h4 className="font-medium text-gray-900">{request.description}</h4>
                        <div className="flex items-center space-x-4 text-sm text-gray-600">
                          <span>Demandé le {new Date(request.requestDate).toLocaleDateString('fr-FR')}</span>
                          {request.completionDate && (
                            <>
                              <span>•</span>
                              <span>Traité le {new Date(request.completionDate).toLocaleDateString('fr-FR')}</span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>

                    <Badge className={getRequestStatusColor(request.status)}>
                      {request.status === 'pending' && 'En attente'}
                      {request.status === 'processing' && 'En cours'}
                      {request.status === 'completed' && 'Terminé'}
                      {request.status === 'rejected' && 'Rejeté'}
                    </Badge>
                  </div>
                ))}

                {requests.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <FileText className="h-12 w-12 mx-auto mb-3 text-gray-300" />
                    <p>Aucune demande RGPD</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Onglet Mes Droits */}
        <TabsContent value="rights" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Eye className="h-5 w-5" />
                  <span>Droit d'Accès</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-4">
                  Vous avez le droit d'obtenir une copie de vos données personnelles que nous traitons.
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => createGDPRRequest('access')}
                >
                  Demander l'accès
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Edit className="h-5 w-5" />
                  <span>Droit de Rectification</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-4">
                  Vous pouvez demander la correction de données inexactes ou incomplètes.
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => createGDPRRequest('rectification')}
                >
                  Demander une correction
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Trash2 className="h-5 w-5" />
                  <span>Droit à l'Effacement</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-4">
                  Vous pouvez demander la suppression de vos données personnelles dans certains cas.
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => createGDPRRequest('erasure')}
                >
                  Demander l'effacement
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Download className="h-5 w-5" />
                  <span>Droit à la Portabilité</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-4">
                  Vous pouvez récupérer vos données dans un format structuré et lisible.
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => createGDPRRequest('portability')}
                >
                  Demander la portabilité
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Banner de consentement */}
      <AnimatePresence>
        {showConsentBanner && (
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 50 }}
            className="fixed bottom-4 left-4 right-4 z-50"
          >
            <Card className="bg-white border-2 border-blue-200 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-start space-x-4">
                  <Info className="h-6 w-6 text-blue-500 mt-1 flex-shrink-0" />
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900 mb-2">
                      Gestion des Cookies et Données Personnelles
                    </h3>
                    <p className="text-sm text-gray-600 mb-4">
                      Nous utilisons des cookies et traitons vos données personnelles pour améliorer votre expérience.
                      Vous pouvez personnaliser vos préférences ou accepter tous les cookies.
                    </p>
                    <div className="flex flex-wrap gap-2">
                      <Button onClick={acceptAllConsents} size="sm">
                        Accepter tout
                      </Button>
                      <Button onClick={rejectNonEssential} variant="outline" size="sm">
                        Cookies essentiels uniquement
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setShowConsentBanner(false)}
                      >
                        Personnaliser
                      </Button>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowConsentBanner(false)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
