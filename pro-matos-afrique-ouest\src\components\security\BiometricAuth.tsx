'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Fingerprint, 
  Eye, 
  Smartphone,
  Shield,
  CheckCircle,
  AlertTriangle,
  X,
  Settings,
  Lock,
  Unlock,
  <PERSON>,
  <PERSON>an,
  User<PERSON>heck
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Progress } from '@/components/ui/progress'
import { toast } from 'sonner'

interface BiometricCapability {
  type: 'fingerprint' | 'face' | 'voice' | 'iris'
  available: boolean
  enrolled: boolean
  lastUsed?: string
  accuracy: number
}

interface BiometricAuthProps {
  onAuthSuccess: (method: string) => void
  onAuthFailure: (error: string) => void
  className?: string
}

export default function BiometricAuth({ onAuthSuccess, onAuthFailure, className = '' }: BiometricAuthProps) {
  const [isAuthenticating, setIsAuthenticating] = useState(false)
  const [authProgress, setAuthProgress] = useState(0)
  const [selectedMethod, setSelectedMethod] = useState<string | null>(null)
  const [showSettings, setShowSettings] = useState(false)
  const [capabilities, setCapabilities] = useState<BiometricCapability[]>([
    {
      type: 'fingerprint',
      available: true,
      enrolled: true,
      lastUsed: new Date(Date.now() - 3600000).toISOString(),
      accuracy: 99.2
    },
    {
      type: 'face',
      available: true,
      enrolled: false,
      accuracy: 97.8
    },
    {
      type: 'voice',
      available: false,
      enrolled: false,
      accuracy: 95.5
    },
    {
      type: 'iris',
      available: false,
      enrolled: false,
      accuracy: 99.9
    }
  ])

  const [settings, setSettings] = useState({
    biometricEnabled: true,
    fallbackToPassword: true,
    multiFactorRequired: false,
    autoLock: true,
    lockTimeout: 300, // 5 minutes
    maxAttempts: 3
  })

  // Vérifier les capacités biométriques du navigateur
  useEffect(() => {
    checkBiometricCapabilities()
  }, [])

  const checkBiometricCapabilities = async () => {
    try {
      // Vérifier WebAuthn
      if (window.PublicKeyCredential) {
        const available = await PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable()
        
        setCapabilities(prev => prev.map(cap => ({
          ...cap,
          available: cap.type === 'fingerprint' || cap.type === 'face' ? available : cap.available
        })))
      }

      // Vérifier les permissions de caméra pour la reconnaissance faciale
      if (navigator.mediaDevices) {
        try {
          const stream = await navigator.mediaDevices.getUserMedia({ video: true })
          stream.getTracks().forEach(track => track.stop())
          
          setCapabilities(prev => prev.map(cap => 
            cap.type === 'face' ? { ...cap, available: true } : cap
          ))
        } catch (error) {
          console.log('Caméra non disponible pour reconnaissance faciale')
        }
      }
    } catch (error) {
      console.error('Erreur vérification capacités biométriques:', error)
    }
  }

  // Simuler l'authentification biométrique
  const authenticateWithBiometric = async (method: string) => {
    if (!settings.biometricEnabled) {
      toast.error('Authentification biométrique désactivée')
      return
    }

    setIsAuthenticating(true)
    setSelectedMethod(method)
    setAuthProgress(0)

    try {
      // Simuler le processus d'authentification
      for (let i = 0; i <= 100; i += 10) {
        setAuthProgress(i)
        await new Promise(resolve => setTimeout(resolve, 200))
      }

      // Simuler succès/échec
      const success = Math.random() > 0.1 // 90% de succès

      if (success) {
        // Mettre à jour la dernière utilisation
        setCapabilities(prev => prev.map(cap => 
          cap.type === method ? { ...cap, lastUsed: new Date().toISOString() } : cap
        ))

        onAuthSuccess(method)
        toast.success(`Authentification ${method} réussie`)
      } else {
        onAuthFailure(`Échec authentification ${method}`)
        toast.error(`Échec de l'authentification ${method}`)
      }
    } catch (error) {
      onAuthFailure(`Erreur ${method}: ${error}`)
      toast.error(`Erreur lors de l'authentification ${method}`)
    } finally {
      setIsAuthenticating(false)
      setSelectedMethod(null)
      setAuthProgress(0)
    }
  }

  // Inscrire une nouvelle méthode biométrique
  const enrollBiometric = async (method: string) => {
    setIsAuthenticating(true)
    setSelectedMethod(method)

    try {
      // Simuler l'inscription
      for (let i = 0; i <= 100; i += 5) {
        setAuthProgress(i)
        await new Promise(resolve => setTimeout(resolve, 100))
      }

      setCapabilities(prev => prev.map(cap => 
        cap.type === method ? { ...cap, enrolled: true } : cap
      ))

      toast.success(`${method} inscrit avec succès`)
    } catch (error) {
      toast.error(`Erreur lors de l'inscription ${method}`)
    } finally {
      setIsAuthenticating(false)
      setSelectedMethod(null)
      setAuthProgress(0)
    }
  }

  const getMethodIcon = (type: string) => {
    switch (type) {
      case 'fingerprint': return <Fingerprint className="h-6 w-6" />
      case 'face': return <Scan className="h-6 w-6" />
      case 'voice': return <Smartphone className="h-6 w-6" />
      case 'iris': return <Eye className="h-6 w-6" />
      default: return <Shield className="h-6 w-6" />
    }
  }

  const getMethodName = (type: string) => {
    switch (type) {
      case 'fingerprint': return 'Empreinte digitale'
      case 'face': return 'Reconnaissance faciale'
      case 'voice': return 'Reconnaissance vocale'
      case 'iris': return 'Reconnaissance iris'
      default: return 'Méthode inconnue'
    }
  }

  const getMethodColor = (type: string) => {
    switch (type) {
      case 'fingerprint': return 'from-blue-400 to-blue-500'
      case 'face': return 'from-green-400 to-green-500'
      case 'voice': return 'from-purple-400 to-purple-500'
      case 'iris': return 'from-red-400 to-red-500'
      default: return 'from-gray-400 to-gray-500'
    }
  }

  const availableMethods = capabilities.filter(cap => cap.available && cap.enrolled)
  const enrollableMethods = capabilities.filter(cap => cap.available && !cap.enrolled)

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-400 to-blue-500 rounded-lg flex items-center justify-center">
                <Shield className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle>Authentification Biométrique</CardTitle>
                <CardDescription>
                  Sécurisez votre compte avec vos données biométriques
                </CardDescription>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Badge className={settings.biometricEnabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                {settings.biometricEnabled ? 'Activé' : 'Désactivé'}
              </Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowSettings(!showSettings)}
              >
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>

        {/* Paramètres */}
        {showSettings && (
          <CardContent className="border-t border-gray-200 bg-gray-50">
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Authentification biométrique</span>
                  <Switch
                    checked={settings.biometricEnabled}
                    onCheckedChange={(checked) => 
                      setSettings(prev => ({ ...prev, biometricEnabled: checked }))
                    }
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Fallback mot de passe</span>
                  <Switch
                    checked={settings.fallbackToPassword}
                    onCheckedChange={(checked) => 
                      setSettings(prev => ({ ...prev, fallbackToPassword: checked }))
                    }
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Double authentification</span>
                  <Switch
                    checked={settings.multiFactorRequired}
                    onCheckedChange={(checked) => 
                      setSettings(prev => ({ ...prev, multiFactorRequired: checked }))
                    }
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Verrouillage automatique</span>
                  <Switch
                    checked={settings.autoLock}
                    onCheckedChange={(checked) => 
                      setSettings(prev => ({ ...prev, autoLock: checked }))
                    }
                  />
                </div>
              </div>
            </div>
          </CardContent>
        )}
      </Card>

      {/* Méthodes disponibles */}
      {availableMethods.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <UserCheck className="h-5 w-5" />
              <span>Méthodes Configurées</span>
            </CardTitle>
            <CardDescription>
              Choisissez votre méthode d'authentification préférée
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {availableMethods.map((method) => (
                <motion.div
                  key={method.type}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Card className="cursor-pointer hover:shadow-md transition-shadow">
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-4">
                        <div className={`w-12 h-12 bg-gradient-to-r ${getMethodColor(method.type)} rounded-lg flex items-center justify-center text-white`}>
                          {getMethodIcon(method.type)}
                        </div>
                        
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900">{getMethodName(method.type)}</h4>
                          <div className="flex items-center space-x-2 text-sm text-gray-600">
                            <span>Précision: {method.accuracy}%</span>
                            {method.lastUsed && (
                              <>
                                <span>•</span>
                                <span>Utilisé: {new Date(method.lastUsed).toLocaleDateString('fr-FR')}</span>
                              </>
                            )}
                          </div>
                        </div>
                        
                        <Button
                          onClick={() => authenticateWithBiometric(method.type)}
                          disabled={isAuthenticating || !settings.biometricEnabled}
                          className="bg-blue-500 hover:bg-blue-600"
                        >
                          {isAuthenticating && selectedMethod === method.type ? (
                            <div className="flex items-center space-x-2">
                              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                              <span>Auth...</span>
                            </div>
                          ) : (
                            <>
                              <Unlock className="h-4 w-4 mr-2" />
                              Authentifier
                            </>
                          )}
                        </Button>
                      </div>
                      
                      {/* Barre de progression */}
                      {isAuthenticating && selectedMethod === method.type && (
                        <div className="mt-3">
                          <Progress value={authProgress} className="h-2" />
                          <p className="text-xs text-gray-500 mt-1">
                            Authentification en cours... {authProgress}%
                          </p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Méthodes à configurer */}
      {enrollableMethods.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Key className="h-5 w-5" />
              <span>Ajouter une Méthode</span>
            </CardTitle>
            <CardDescription>
              Configurez des méthodes d'authentification supplémentaires
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {enrollableMethods.map((method) => (
                <Card key={method.type} className="border-dashed border-2 border-gray-300">
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-4">
                      <div className={`w-12 h-12 bg-gradient-to-r ${getMethodColor(method.type)} opacity-50 rounded-lg flex items-center justify-center text-white`}>
                        {getMethodIcon(method.type)}
                      </div>

                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900">{getMethodName(method.type)}</h4>
                        <p className="text-sm text-gray-600">
                          Précision: {method.accuracy}% • Non configuré
                        </p>
                      </div>

                      <Button
                        variant="outline"
                        onClick={() => enrollBiometric(method.type)}
                        disabled={isAuthenticating}
                      >
                        {isAuthenticating && selectedMethod === method.type ? (
                          <div className="flex items-center space-x-2">
                            <div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                            <span>Config...</span>
                          </div>
                        ) : (
                          <>
                            <Lock className="h-4 w-4 mr-2" />
                            Configurer
                          </>
                        )}
                      </Button>
                    </div>

                    {/* Barre de progression pour l'inscription */}
                    {isAuthenticating && selectedMethod === method.type && (
                      <div className="mt-3">
                        <Progress value={authProgress} className="h-2" />
                        <p className="text-xs text-gray-500 mt-1">
                          Configuration en cours... {authProgress}%
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Méthodes non disponibles */}
      {capabilities.filter(cap => !cap.available).length > 0 && (
        <Card className="bg-gray-50 border-gray-200">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-gray-600">
              <AlertTriangle className="h-5 w-5" />
              <span>Méthodes Non Disponibles</span>
            </CardTitle>
            <CardDescription>
              Ces méthodes ne sont pas supportées par votre appareil
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {capabilities.filter(cap => !cap.available).map((method) => (
                <div key={method.type} className="flex items-center space-x-4 p-3 bg-white rounded-lg border border-gray-200 opacity-60">
                  <div className="w-10 h-10 bg-gray-300 rounded-lg flex items-center justify-center text-gray-500">
                    {getMethodIcon(method.type)}
                  </div>

                  <div className="flex-1">
                    <h4 className="font-medium text-gray-700">{getMethodName(method.type)}</h4>
                    <p className="text-sm text-gray-500">
                      Non supporté sur cet appareil
                    </p>
                  </div>

                  <Badge variant="outline" className="text-gray-500 border-gray-300">
                    Indisponible
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Fallback si aucune méthode disponible */}
      {availableMethods.length === 0 && enrollableMethods.length === 0 && (
        <Card className="bg-yellow-50 border-yellow-200">
          <CardContent className="p-6 text-center">
            <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-yellow-900 mb-2">
              Aucune méthode biométrique disponible
            </h3>
            <p className="text-yellow-700 mb-4">
              Votre appareil ne supporte pas l'authentification biométrique ou aucune méthode n'est configurée.
            </p>
            <Button variant="outline" className="border-yellow-300 text-yellow-700 hover:bg-yellow-100">
              Utiliser un mot de passe
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Informations de sécurité */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-4">
          <div className="flex items-start space-x-3">
            <Shield className="h-5 w-5 text-blue-500 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-900 mb-1">Sécurité Renforcée</h4>
              <p className="text-sm text-blue-700">
                Vos données biométriques sont stockées localement sur votre appareil et ne sont jamais transmises à nos serveurs.
                L'authentification utilise des standards de sécurité avancés pour protéger votre identité.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
