'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Award,
  Download,
  Share2,
  Qr<PERSON>ode,
  CheckCircle,
  AlertTriangle,
  FileText,
  Calendar,
  User,
  Building,
  Printer,
  Mail
} from 'lucide-react'
import { useStore } from '@/store/useStore'
import { PrescriptorService } from '@/services/prescriptorService'
import { formatDate } from '@/lib/utils'

interface CertificateGeneratorProps {
  onClose?: () => void
}

export default function CertificateGenerator({ onClose }: CertificateGeneratorProps) {
  const { prescriptionProjects, addComplianceCertificate } = useStore()
  const [selectedProject, setSelectedProject] = useState('')
  const [certificateType, setCertificateType] = useState<'design' | 'installation' | 'testing' | 'full_compliance'>('design')
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedCertificate, setGeneratedCertificate] = useState<any>(null)
  const [showPreview, setShowPreview] = useState(false)

  const certificateTypes = [
    {
      id: 'design',
      label: 'Certificat de Conception',
      description: 'Validation de la conception et des calculs techniques',
      icon: <FileText className="h-5 w-5" />,
      color: 'from-blue-500 to-blue-600',
      validity: 12, // mois
      price: 150000 // FCFA
    },
    {
      id: 'installation',
      label: 'Certificat d\'Installation',
      description: 'Conformité de l\'installation selon les plans',
      icon: <Building className="h-5 w-5" />,
      color: 'from-green-500 to-green-600',
      validity: 24,
      price: 200000
    },
    {
      id: 'testing',
      label: 'Certificat de Tests',
      description: 'Validation des tests et essais de mise en service',
      icon: <CheckCircle className="h-5 w-5" />,
      color: 'from-purple-500 to-purple-600',
      validity: 12,
      price: 100000
    },
    {
      id: 'full_compliance',
      label: 'Certificat de Conformité Complète',
      description: 'Conformité totale : conception, installation et tests',
      icon: <Award className="h-5 w-5" />,
      color: 'from-amber-500 to-amber-600',
      validity: 36,
      price: 400000
    }
  ]

  const selectedProjectData = prescriptionProjects.find(p => p.id === selectedProject)
  const selectedCertType = certificateTypes.find(t => t.id === certificateType)

  const generateCertificate = () => {
    if (!selectedProjectData || !selectedCertType) return

    setIsGenerating(true)

    setTimeout(() => {
      const certificate = PrescriptorService.generateComplianceCertificate(selectedProjectData)
      
      // Personnaliser selon le type
      const customizedCertificate = {
        ...certificate,
        certificate_type: certificateType,
        certificate_number: `PMO-${certificateType.toUpperCase()}-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`,
        valid_until: new Date(Date.now() + selectedCertType.validity * 30 * 24 * 60 * 60 * 1000).toISOString(),
        standards_covered: getStandardsByCertificateType(certificateType),
        test_results: getTestResultsByCertificateType(certificateType),
        cost: selectedCertType.price
      }

      setGeneratedCertificate(customizedCertificate)
      addComplianceCertificate(customizedCertificate)
      setIsGenerating(false)
      setShowPreview(true)
    }, 3000)
  }

  const getStandardsByCertificateType = (type: string) => {
    switch (type) {
      case 'design':
        return ['NF C 15-100', 'RT 2012', 'Accessibilité PMR']
      case 'installation':
        return ['NF C 15-100', 'Décret n°88-1056', 'Code du travail']
      case 'testing':
        return ['NF C 15-100', 'Guide UTE C 15-900', 'IEC 60364-6']
      case 'full_compliance':
        return ['NF C 15-100', 'RT 2012', 'Décret n°88-1056', 'IEC 60364', 'Code du travail']
      default:
        return ['NF C 15-100']
    }
  }

  const getTestResultsByCertificateType = (type: string) => {
    switch (type) {
      case 'design':
        return {
          'Calculs de puissance': 'Conforme',
          'Dimensionnement câbles': 'Conforme',
          'Schémas unifilaires': 'Conforme',
          'Protection différentielle': 'Conforme'
        }
      case 'installation':
        return {
          'Continuité des conducteurs': 'Conforme',
          'Résistance d\'isolement': 'Conforme',
          'Efficacité de la protection': 'Conforme',
          'Fonctionnement différentiel': 'Conforme'
        }
      case 'testing':
        return {
          'Tests de continuité': 'Conforme',
          'Mesure d\'isolement': 'Conforme',
          'Tests différentiels': 'Conforme',
          'Vérification fonctionnelle': 'Conforme'
        }
      case 'full_compliance':
        return {
          'Conception': 'Conforme',
          'Installation': 'Conforme',
          'Tests et essais': 'Conforme',
          'Documentation': 'Conforme',
          'Formation utilisateurs': 'Conforme'
        }
      default:
        return {}
    }
  }

  const downloadCertificate = () => {
    if (!generatedCertificate) return

    // Simulation de génération PDF
    const certificateContent = `
CERTIFICAT DE CONFORMITÉ
Pro Matos Afrique Ouest - Bureau d'Études Certifié

Certificat N°: ${generatedCertificate.certificate_number}
Type: ${selectedCertType?.label}
Projet: ${selectedProjectData?.project_name}
Client: ${selectedProjectData?.client_name}

Émis le: ${formatDate(generatedCertificate.valid_from)}
Valide jusqu'au: ${formatDate(generatedCertificate.valid_until)}

NORMES COUVERTES:
${generatedCertificate.standards_covered.map((s: string) => `- ${s}`).join('\n')}

RÉSULTATS DES TESTS:
${Object.entries(generatedCertificate.test_results).map(([test, result]) => `- ${test}: ${result}`).join('\n')}

Ce certificat atteste que le projet respecte les normes en vigueur.

Signature numérique: ${generatedCertificate.digital_signature}
Vérification QR: ${generatedCertificate.qr_verification}
    `.trim()

    const blob = new Blob([certificateContent], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `certificat-${generatedCertificate.certificate_number}.txt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const shareCertificate = () => {
    if (!generatedCertificate) return
    
    const shareText = `Certificat de conformité ${generatedCertificate.certificate_number} émis par Pro Matos Afrique Ouest. Vérification: ${generatedCertificate.qr_verification}`
    
    if (navigator.share) {
      navigator.share({
        title: 'Certificat de Conformité',
        text: shareText,
        url: generatedCertificate.qr_verification
      })
    } else {
      navigator.clipboard.writeText(shareText)
      alert('Lien de partage copié dans le presse-papiers')
    }
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 max-w-4xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <div className="w-12 h-12 bg-gradient-to-r from-amber-500 to-amber-600 rounded-lg flex items-center justify-center text-white">
            <Award className="h-6 w-6" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Générateur de Certificat</h2>
            <p className="text-gray-600">Certification officielle de conformité Pro Matos</p>
          </div>
        </div>
        {onClose && (
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            ×
          </button>
        )}
      </div>

      {!showPreview ? (
        <div className="space-y-6">
          {/* Sélection du projet */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Projet à certifier
            </label>
            <select
              value={selectedProject}
              onChange={(e) => setSelectedProject(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400"
            >
              <option value="">Sélectionner un projet...</option>
              {prescriptionProjects.map(project => (
                <option key={project.id} value={project.id}>
                  {project.project_name} - {project.client_name}
                </option>
              ))}
            </select>
          </div>

          {/* Types de certificat */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-4">
              Type de certificat
            </label>
            <div className="grid md:grid-cols-2 gap-4">
              {certificateTypes.map((type) => (
                <button
                  key={type.id}
                  onClick={() => setCertificateType(type.id as any)}
                  className={`p-4 rounded-lg border-2 transition-all text-left ${
                    certificateType === type.id
                      ? 'border-amber-400 bg-amber-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-start space-x-3">
                    <div className={`w-10 h-10 bg-gradient-to-r ${type.color} rounded-lg flex items-center justify-center text-white`}>
                      {type.icon}
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 mb-1">{type.label}</h3>
                      <p className="text-sm text-gray-600 mb-2">{type.description}</p>
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span>Validité: {type.validity} mois</span>
                        <span className="font-medium text-amber-600">
                          {type.price.toLocaleString()} FCFA
                        </span>
                      </div>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Informations du projet sélectionné */}
          {selectedProjectData && (
            <div className="bg-gray-50 rounded-lg p-4 border">
              <h3 className="font-semibold text-gray-900 mb-3">Informations du Projet</h3>
              <div className="grid md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Nom:</span>
                  <p>{selectedProjectData.project_name}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Client:</span>
                  <p>{selectedProjectData.client_name} - {selectedProjectData.client_company}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Localisation:</span>
                  <p>{selectedProjectData.location}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Statut:</span>
                  <p className="capitalize">{selectedProjectData.status}</p>
                </div>
              </div>
            </div>
          )}

          {/* Bouton de génération */}
          <div className="flex justify-center">
            <button
              onClick={generateCertificate}
              disabled={!selectedProject || !certificateType || isGenerating}
              className="btn-premium px-8 py-3 text-lg disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isGenerating ? (
                <div className="flex items-center space-x-2">
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>Génération du certificat...</span>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <Award className="h-5 w-5" />
                  <span>Générer le Certificat</span>
                  {selectedCertType && (
                    <span className="ml-2 text-sm">
                      ({selectedCertType.price.toLocaleString()} FCFA)
                    </span>
                  )}
                </div>
              )}
            </button>
          </div>
        </div>
      ) : (
        /* Aperçu du certificat généré */
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          {/* Succès */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <CheckCircle className="h-6 w-6 text-green-600" />
              <div>
                <h3 className="font-semibold text-green-900">Certificat généré avec succès !</h3>
                <p className="text-sm text-green-700">
                  Certificat N° {generatedCertificate?.certificate_number} prêt à être téléchargé
                </p>
              </div>
            </div>
          </div>

          {/* Aperçu du certificat */}
          <div className="bg-gradient-to-br from-amber-50 to-yellow-50 border-2 border-amber-200 rounded-lg p-6">
            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-gradient-to-r from-amber-500 to-amber-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <Award className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-amber-900 mb-2">
                CERTIFICAT DE CONFORMITÉ
              </h3>
              <p className="text-amber-700">Pro Matos Afrique Ouest - Bureau d'Études Certifié</p>
            </div>

            <div className="grid md:grid-cols-2 gap-6 mb-6">
              <div className="space-y-3">
                <div>
                  <span className="text-sm font-medium text-amber-800">N° Certificat:</span>
                  <p className="font-mono text-amber-900">{generatedCertificate?.certificate_number}</p>
                </div>
                <div>
                  <span className="text-sm font-medium text-amber-800">Type:</span>
                  <p className="text-amber-900">{selectedCertType?.label}</p>
                </div>
                <div>
                  <span className="text-sm font-medium text-amber-800">Projet:</span>
                  <p className="text-amber-900">{selectedProjectData?.project_name}</p>
                </div>
              </div>
              <div className="space-y-3">
                <div>
                  <span className="text-sm font-medium text-amber-800">Client:</span>
                  <p className="text-amber-900">{selectedProjectData?.client_name}</p>
                </div>
                <div>
                  <span className="text-sm font-medium text-amber-800">Émis le:</span>
                  <p className="text-amber-900">{formatDate(generatedCertificate?.valid_from)}</p>
                </div>
                <div>
                  <span className="text-sm font-medium text-amber-800">Valide jusqu'au:</span>
                  <p className="text-amber-900">{formatDate(generatedCertificate?.valid_until)}</p>
                </div>
              </div>
            </div>

            <div className="text-center">
              <QrCode className="h-12 w-12 mx-auto text-amber-600 mb-2" />
              <p className="text-xs text-amber-700">Code QR de vérification</p>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-center space-x-4">
            <button
              onClick={downloadCertificate}
              className="flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Download className="h-5 w-5" />
              <span>Télécharger PDF</span>
            </button>
            <button
              onClick={shareCertificate}
              className="flex items-center space-x-2 px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <Share2 className="h-5 w-5" />
              <span>Partager</span>
            </button>
            <button className="flex items-center space-x-2 px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
              <Printer className="h-5 w-5" />
              <span>Imprimer</span>
            </button>
          </div>

          <div className="text-center">
            <button
              onClick={() => {
                setShowPreview(false)
                setSelectedProject('')
                setGeneratedCertificate(null)
              }}
              className="text-amber-600 hover:text-amber-700 font-medium"
            >
              Générer un nouveau certificat
            </button>
          </div>
        </motion.div>
      )}
    </div>
  )
}
