"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/hub/page",{

/***/ "(app-pages-browser)/./src/lib/stores/alertStore.ts":
/*!**************************************!*\
  !*** ./src/lib/stores/alertStore.ts ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAlertActions: function() { return /* binding */ useAlertActions; },\n/* harmony export */   useAlertStore: function() { return /* binding */ useAlertStore; }\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n\n\n// Données d'exemple par défaut\nconst DEFAULT_ALERTS = [\n    {\n        id: 1,\n        title: \"Nouvelle norme NF C 15-100 - Amendement A6\",\n        body: \"Mise \\xe0 jour importante des r\\xe8gles d'installation \\xe9lectrique pour les b\\xe2timents r\\xe9sidentiels et tertiaires.\",\n        type: \"info\",\n        category: \"R\\xe9glementation\",\n        is_active: true,\n        created_at: new Date().toISOString()\n    },\n    {\n        id: 2,\n        title: \"Rupture de stock - Disjoncteurs Schneider\",\n        body: \"Stock \\xe9puis\\xe9 sur les disjoncteurs C60N 32A chez plusieurs fournisseurs d'Abidjan.\",\n        type: \"warning\",\n        category: \"Stock\",\n        is_active: true,\n        created_at: new Date(Date.now() - 3600000).toISOString()\n    },\n    {\n        id: 3,\n        title: \"Formation technique Legrand\",\n        body: \"Session de formation sur les nouveaux produits de la gamme Mosaic disponible.\",\n        type: \"info\",\n        category: \"Formation\",\n        is_active: true,\n        created_at: new Date(Date.now() - 7200000).toISOString()\n    },\n    {\n        id: 4,\n        title: \"Alerte s\\xe9curit\\xe9 - Rappel produit\",\n        body: \"Rappel de s\\xe9curit\\xe9 sur certains mod\\xe8les de prises \\xe9lectriques d\\xe9fectueuses.\",\n        type: \"critical\",\n        category: \"S\\xe9curit\\xe9\",\n        is_active: true,\n        created_at: new Date(Date.now() - 10800000).toISOString()\n    },\n    {\n        id: 5,\n        title: \"Promotion sp\\xe9ciale - C\\xe2bles \\xe9lectriques\",\n        body: \"Remise de 20% sur tous les c\\xe2bles \\xe9lectriques ce mois-ci.\",\n        type: \"promo\",\n        category: \"Promotion\",\n        is_active: true,\n        created_at: new Date(Date.now() - 14400000).toISOString()\n    }\n];\nconst useAlertStore = (0,zustand__WEBPACK_IMPORTED_MODULE_1__.create)((set, get)=>({\n        alerts: [],\n        userAlerts: [],\n        loading: false,\n        fetchAlerts: async ()=>{\n            try {\n                set({\n                    loading: true\n                });\n                const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"alerts\").select(\"*\").eq(\"is_active\", true).order(\"created_at\", {\n                    ascending: false\n                });\n                if (error) {\n                    console.error(\"Erreur Supabase, utilisation des donn\\xe9es d'exemple:\", error);\n                    // Données d'exemple si Supabase échoue\n                    const exampleAlerts = [\n                        {\n                            id: 1,\n                            title: \"Nouvelle norme NF C 15-100 - Amendement A6\",\n                            body: \"Mise \\xe0 jour importante des r\\xe8gles d'installation \\xe9lectrique pour les b\\xe2timents r\\xe9sidentiels et tertiaires.\",\n                            type: \"info\",\n                            category: \"R\\xe9glementation\",\n                            is_active: true,\n                            created_at: new Date().toISOString()\n                        },\n                        {\n                            id: 2,\n                            title: \"Rupture de stock - Disjoncteurs Schneider\",\n                            body: \"Stock \\xe9puis\\xe9 sur les disjoncteurs C60N 32A chez plusieurs fournisseurs d'Abidjan.\",\n                            type: \"warning\",\n                            category: \"Stock\",\n                            is_active: true,\n                            created_at: new Date(Date.now() - 3600000).toISOString()\n                        },\n                        {\n                            id: 3,\n                            title: \"Formation technique Legrand\",\n                            body: \"Session de formation sur les nouveaux produits de la gamme Mosaic disponible.\",\n                            type: \"info\",\n                            category: \"Formation\",\n                            is_active: true,\n                            created_at: new Date(Date.now() - 7200000).toISOString()\n                        },\n                        {\n                            id: 4,\n                            title: \"Alerte s\\xe9curit\\xe9 - Rappel produit\",\n                            body: \"Rappel de s\\xe9curit\\xe9 sur certains mod\\xe8les de prises \\xe9lectriques d\\xe9fectueuses.\",\n                            type: \"critical\",\n                            category: \"S\\xe9curit\\xe9\",\n                            is_active: true,\n                            created_at: new Date(Date.now() - 10800000).toISOString()\n                        }\n                    ];\n                    set({\n                        alerts: exampleAlerts\n                    });\n                } else {\n                    set({\n                        alerts: data || []\n                    });\n                }\n            } catch (error) {\n                console.error(\"Erreur lors du chargement des alertes:\", error);\n                // Données d'exemple en cas d'erreur\n                const exampleAlerts = [\n                    {\n                        id: 1,\n                        title: \"Nouvelle norme NF C 15-100 - Amendement A6\",\n                        body: \"Mise \\xe0 jour importante des r\\xe8gles d'installation \\xe9lectrique pour les b\\xe2timents r\\xe9sidentiels et tertiaires.\",\n                        type: \"info\",\n                        category: \"R\\xe9glementation\",\n                        is_active: true,\n                        created_at: new Date().toISOString()\n                    },\n                    {\n                        id: 2,\n                        title: \"Rupture de stock - Disjoncteurs Schneider\",\n                        body: \"Stock \\xe9puis\\xe9 sur les disjoncteurs C60N 32A chez plusieurs fournisseurs d'Abidjan.\",\n                        type: \"warning\",\n                        category: \"Stock\",\n                        is_active: true,\n                        created_at: new Date(Date.now() - 3600000).toISOString()\n                    }\n                ];\n                set({\n                    alerts: exampleAlerts\n                });\n            } finally{\n                set({\n                    loading: false\n                });\n            }\n        },\n        fetchUserAlerts: async (userId)=>{\n            try {\n                const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"user_alerts\").select(\"*\").eq(\"user_id\", userId);\n                if (error) throw error;\n                set({\n                    userAlerts: data || []\n                });\n            } catch (error) {\n                console.error(\"Erreur lors du chargement des abonnements:\", error);\n            }\n        },\n        subscribeToAlert: async (alertId)=>{\n            try {\n                const { data: { user } } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n                if (!user) throw new Error(\"Non authentifi\\xe9\");\n                const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"user_alerts\").insert({\n                    user_id: user.id,\n                    alert_id: alertId\n                });\n                if (error) throw error;\n                // Mettre à jour l'état local\n                const { userAlerts } = get();\n                set({\n                    userAlerts: [\n                        ...userAlerts,\n                        {\n                            user_id: user.id,\n                            alert_id: alertId,\n                            subscribed_at: new Date().toISOString()\n                        }\n                    ]\n                });\n                return {\n                    error: null\n                };\n            } catch (error) {\n                return {\n                    error: error\n                };\n            }\n        },\n        unsubscribeFromAlert: async (alertId)=>{\n            try {\n                const { data: { user } } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n                if (!user) throw new Error(\"Non authentifi\\xe9\");\n                const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"user_alerts\").delete().eq(\"user_id\", user.id).eq(\"alert_id\", alertId);\n                if (error) throw error;\n                // Mettre à jour l'état local\n                const { userAlerts } = get();\n                set({\n                    userAlerts: userAlerts.filter((ua)=>ua.alert_id !== alertId)\n                });\n                return {\n                    error: null\n                };\n            } catch (error) {\n                return {\n                    error: error\n                };\n            }\n        },\n        isSubscribed: (alertId)=>{\n            const { userAlerts } = get();\n            return userAlerts.some((ua)=>ua.alert_id === alertId);\n        }\n    }));\n// Hook pour les notifications toast\nconst useAlertActions = ()=>{\n    const { subscribeToAlert, unsubscribeFromAlert, isSubscribed } = useAlertStore();\n    const handleSubscribe = async (alertId, onSuccess, onError)=>{\n        const { error } = await subscribeToAlert(alertId);\n        if (error) {\n            onError === null || onError === void 0 ? void 0 : onError(error.message);\n        } else {\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess();\n        }\n    };\n    const handleUnsubscribe = async (alertId, onSuccess, onError)=>{\n        const { error } = await unsubscribeFromAlert(alertId);\n        if (error) {\n            onError === null || onError === void 0 ? void 0 : onError(error.message);\n        } else {\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess();\n        }\n    };\n    return {\n        handleSubscribe,\n        handleUnsubscribe,\n        isSubscribed\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/stores/alertStore.ts\n"));

/***/ })

});