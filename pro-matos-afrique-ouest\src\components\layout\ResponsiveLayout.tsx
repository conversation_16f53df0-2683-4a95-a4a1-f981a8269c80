'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  ChevronLeft,
  ChevronRight,
  Grid,
  List,
  Maximize2,
  Minimize2,
  Smartphone,
  Tablet,
  Monitor
} from 'lucide-react'

interface ResponsiveLayoutProps {
  children: React.ReactNode
  title?: string
  subtitle?: string
  sidebar?: React.ReactNode
  actions?: React.ReactNode
  className?: string
}

export default function ResponsiveLayout({ 
  children, 
  title, 
  subtitle, 
  sidebar, 
  actions,
  className = '' 
}: ResponsiveLayoutProps) {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [screenSize, setScreenSize] = useState<'mobile' | 'tablet' | 'desktop'>('desktop')

  useEffect(() => {
    const handleResize = () => {
      if (typeof window === 'undefined') return

      const width = window.innerWidth
      if (width < 768) {
        setScreenSize('mobile')
        setSidebarCollapsed(true)
      } else if (width < 1024) {
        setScreenSize('tablet')
        setSidebarCollapsed(false)
      } else {
        setScreenSize('desktop')
        setSidebarCollapsed(false)
      }
    }

    handleResize()
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', handleResize)
      return () => window.removeEventListener('resize', handleResize)
    }
  }, [])

  const toggleFullscreen = () => {
    if (typeof document === 'undefined') return

    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen()
      setIsFullscreen(true)
    } else {
      document.exitFullscreen()
      setIsFullscreen(false)
    }
  }

  return (
    <div className={`min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 ${className}`}>
      {/* Header responsive */}
      <div className="bg-white/80 backdrop-blur-md border-b border-slate-200 sticky top-16 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Titre et contrôles */}
            <div className="flex items-center space-x-4">
              {sidebar && (
                <button
                  type="button"
                  onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
                  className="p-2 text-slate-600 hover:text-slate-900 hover:bg-slate-100 rounded-lg transition-colors lg:hidden"
                >
                  {sidebarCollapsed ? <ChevronRight className="h-5 w-5" /> : <ChevronLeft className="h-5 w-5" />}
                </button>
              )}
              
              <div>
                {title && (
                  <h1 className="text-xl font-bold text-slate-900 truncate max-w-xs sm:max-w-none">
                    {title}
                  </h1>
                )}
                {subtitle && (
                  <p className="text-sm text-slate-600 hidden sm:block">
                    {subtitle}
                  </p>
                )}
              </div>
            </div>

            {/* Contrôles d'affichage */}
            <div className="flex items-center space-x-2">
              {/* Indicateur de taille d'écran */}
              <div className="hidden md:flex items-center space-x-1 px-3 py-1 bg-slate-100 rounded-lg">
                {screenSize === 'mobile' && <Smartphone className="h-4 w-4 text-slate-600" />}
                {screenSize === 'tablet' && <Tablet className="h-4 w-4 text-slate-600" />}
                {screenSize === 'desktop' && <Monitor className="h-4 w-4 text-slate-600" />}
                <span className="text-xs text-slate-600 capitalize">{screenSize}</span>
              </div>

              {/* Mode d'affichage */}
              <div className="hidden sm:flex items-center bg-slate-100 rounded-lg p-1">
                <button
                  type="button"
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded transition-colors ${
                    viewMode === 'grid'
                      ? 'bg-white text-slate-900 shadow-sm'
                      : 'text-slate-600 hover:text-slate-900'
                  }`}
                  title="Vue grille"
                >
                  <Grid className="h-4 w-4" />
                </button>
                <button
                  type="button"
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded transition-colors ${
                    viewMode === 'list'
                      ? 'bg-white text-slate-900 shadow-sm'
                      : 'text-slate-600 hover:text-slate-900'
                  }`}
                  title="Vue liste"
                >
                  <List className="h-4 w-4" />
                </button>
              </div>

              {/* Plein écran */}
              <button
                type="button"
                onClick={toggleFullscreen}
                className="p-2 text-slate-600 hover:text-slate-900 hover:bg-slate-100 rounded-lg transition-colors hidden lg:block"
                title={isFullscreen ? 'Quitter le plein écran' : 'Plein écran'}
              >
                {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
              </button>

              {/* Actions personnalisées */}
              {actions && (
                <div className="flex items-center space-x-2">
                  {actions}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Contenu principal */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="flex gap-6">
          {/* Sidebar */}
          {sidebar && (
            <motion.aside
              initial={false}
              animate={{
                width: sidebarCollapsed ? 0 : screenSize === 'mobile' ? '100%' : '320px',
                opacity: sidebarCollapsed ? 0 : 1
              }}
              transition={{ duration: 0.3 }}
              className={`${
                screenSize === 'mobile' 
                  ? 'fixed inset-0 z-50 bg-white' 
                  : 'relative'
              } overflow-hidden`}
            >
              {!sidebarCollapsed && (
                <div className="h-full">
                  {screenSize === 'mobile' && (
                    <div className="flex items-center justify-between p-4 border-b border-slate-200">
                      <h2 className="text-lg font-semibold text-slate-900">Menu</h2>
                      <button
                        type="button"
                        onClick={() => setSidebarCollapsed(true)}
                        className="p-2 text-slate-600 hover:text-slate-900"
                        title="Fermer le menu"
                      >
                        <ChevronLeft className="h-5 w-5" />
                      </button>
                    </div>
                  )}
                  <div className={screenSize === 'mobile' ? 'p-4' : ''}>
                    {sidebar}
                  </div>
                </div>
              )}
            </motion.aside>
          )}

          {/* Contenu principal */}
          <main className="flex-1 min-w-0">
            <div className={`${viewMode === 'grid' ? 'space-y-6' : 'space-y-4'}`}>
              {children}
            </div>
          </main>
        </div>
      </div>

      {/* Overlay mobile pour sidebar */}
      {screenSize === 'mobile' && !sidebarCollapsed && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={() => setSidebarCollapsed(true)}
        />
      )}

      {/* Indicateurs de responsive design */}
      <div className="fixed bottom-4 right-4 z-50">
        <div className="bg-slate-900 text-white px-3 py-2 rounded-lg text-xs font-mono">
          <div className="flex items-center space-x-2">
            {screenSize === 'mobile' && <Smartphone className="h-3 w-3" />}
            {screenSize === 'tablet' && <Tablet className="h-3 w-3" />}
            {screenSize === 'desktop' && <Monitor className="h-3 w-3" />}
            <span>{typeof window !== 'undefined' ? window.innerWidth : 0}px</span>
          </div>
        </div>
      </div>

      {/* Breakpoints indicator (dev only) */}
      <div className="fixed bottom-4 left-4 z-50 bg-slate-900 text-white px-3 py-2 rounded-lg text-xs font-mono">
        <div className="sm:hidden">XS (&lt;640px)</div>
        <div className="hidden sm:block md:hidden">SM (640px+)</div>
        <div className="hidden md:block lg:hidden">MD (768px+)</div>
        <div className="hidden lg:block xl:hidden">LG (1024px+)</div>
        <div className="hidden xl:block 2xl:hidden">XL (1280px+)</div>
        <div className="hidden 2xl:block">2XL (1536px+)</div>
      </div>
    </div>
  )
}
