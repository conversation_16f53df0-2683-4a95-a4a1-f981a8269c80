#!/usr/bin/env node

/**
 * Script pour initialiser la base de données Supabase
 * Usage: node scripts/setup-database.js
 */

const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

// Charger les variables d'environnement
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Variables d\'environnement manquantes:')
  console.error('   NEXT_PUBLIC_SUPABASE_URL:', supabaseUrl ? '✅' : '❌')
  console.error('   SUPABASE_SERVICE_ROLE_KEY:', supabaseServiceKey ? '✅' : '❌')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function setupDatabase() {
  try {
    console.log('🚀 Initialisation de la base de données...')
    
    // Lire le fichier de migration
    const migrationPath = path.join(__dirname, '../supabase/migrations/001_initial_setup.sql')
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8')
    
    // Diviser le SQL en commandes individuelles
    const commands = migrationSQL
      .split(';')
      .map(cmd => cmd.trim())
      .filter(cmd => cmd.length > 0 && !cmd.startsWith('--'))
    
    console.log(`📝 Exécution de ${commands.length} commandes SQL...`)
    
    // Exécuter chaque commande
    for (let i = 0; i < commands.length; i++) {
      const command = commands[i] + ';'
      console.log(`   ${i + 1}/${commands.length}: ${command.substring(0, 50)}...`)
      
      try {
        const { error } = await supabase.rpc('exec_sql', { sql: command })
        if (error) {
          console.warn(`⚠️  Avertissement pour la commande ${i + 1}:`, error.message)
        }
      } catch (err) {
        console.warn(`⚠️  Erreur pour la commande ${i + 1}:`, err.message)
      }
    }
    
    // Vérifier que les tables ont été créées
    console.log('\n🔍 Vérification des tables...')
    
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
    
    if (tablesError) {
      console.error('❌ Erreur lors de la vérification des tables:', tablesError)
    } else {
      const tableNames = tables.map(t => t.table_name)
      console.log('✅ Tables créées:', tableNames.join(', '))
    }
    
    // Vérifier les données d'exemple
    console.log('\n📊 Vérification des données...')
    
    const { data: alerts, error: alertsError } = await supabase
      .from('alerts')
      .select('id, title')
      .limit(5)
    
    if (alertsError) {
      console.error('❌ Erreur lors de la vérification des alertes:', alertsError)
    } else {
      console.log(`✅ ${alerts.length} alertes d'exemple créées`)
      alerts.forEach(alert => {
        console.log(`   - ${alert.title}`)
      })
    }
    
    console.log('\n🎉 Base de données initialisée avec succès!')
    console.log('\n📋 Prochaines étapes:')
    console.log('   1. Connectez-vous à l\'application')
    console.log('   2. Votre profil sera créé automatiquement')
    console.log('   3. Les alertes d\'exemple seront visibles')
    
  } catch (error) {
    console.error('❌ Erreur lors de l\'initialisation:', error)
    process.exit(1)
  }
}

// Alternative: Utiliser l'API REST de Supabase si RPC ne fonctionne pas
async function setupDatabaseAlternative() {
  try {
    console.log('🚀 Initialisation alternative de la base de données...')
    
    // Créer les alertes d'exemple directement
    const exampleAlerts = [
      {
        title: 'Nouvelle norme NF C 15-100 - Amendement A6',
        body: 'Mise à jour importante des règles d\'installation électrique pour les bâtiments résidentiels et tertiaires.',
        type: 'info',
        category: 'Réglementation',
        is_active: true
      },
      {
        title: 'Rupture de stock - Disjoncteurs Schneider',
        body: 'Stock épuisé sur les disjoncteurs C60N 32A chez plusieurs fournisseurs d\'Abidjan.',
        type: 'warning',
        category: 'Stock',
        is_active: true
      },
      {
        title: 'Formation technique Legrand',
        body: 'Session de formation sur les nouveaux produits de la gamme Mosaic disponible.',
        type: 'info',
        category: 'Formation',
        is_active: true
      },
      {
        title: 'Alerte sécurité - Rappel produit',
        body: 'Rappel de sécurité sur certains modèles de prises électriques défectueuses.',
        type: 'critical',
        category: 'Sécurité',
        is_active: true
      }
    ]
    
    console.log('📝 Insertion des alertes d\'exemple...')
    
    const { data, error } = await supabase
      .from('alerts')
      .insert(exampleAlerts)
      .select()
    
    if (error) {
      console.error('❌ Erreur lors de l\'insertion des alertes:', error)
    } else {
      console.log(`✅ ${data.length} alertes d'exemple créées`)
    }
    
    console.log('\n🎉 Données d\'exemple ajoutées avec succès!')
    
  } catch (error) {
    console.error('❌ Erreur lors de l\'initialisation alternative:', error)
  }
}

// Exécuter le script
if (require.main === module) {
  setupDatabaseAlternative()
    .then(() => process.exit(0))
    .catch(() => process.exit(1))
}

module.exports = { setupDatabase, setupDatabaseAlternative }
