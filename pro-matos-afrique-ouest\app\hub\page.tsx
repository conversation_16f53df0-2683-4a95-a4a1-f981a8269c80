'use client'

import { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Bell, 
  AlertTriangle, 
  Info, 
  Zap,
  Calendar,
  Filter,
  Search
} from 'lucide-react'
import { useAuthStore } from '@/lib/stores/authStore'
import { useAlertStore, useAlertActions } from '@/lib/stores/alertStore'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { toast } from 'sonner'
import DashboardLayout from '@/components/layout/DashboardLayout'

export default function HubPage() {
  const { user } = useAuthStore()
  const { alerts, userAlerts, loading, fetchAlerts, fetchUserAlerts } = useAlertStore()
  const { handleSubscribe, handleUnsubscribe, isSubscribed } = useAlertActions()
  const [searchQuery, setSearchQuery] = useState('')
  const [filterType, setFilterType] = useState<string>('all')

  useEffect(() => {
    fetchAlerts()
    if (user) {
      fetchUserAlerts(user.id)
    }
  }, [user, fetchAlerts, fetchUserAlerts])

  const filteredAlerts = alerts.filter(alert => {
    const matchesSearch = alert.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         alert.body.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesType = filterType === 'all' || alert.type === filterType
    
    return matchesSearch && matchesType
  })

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'critical':
        return <AlertTriangle className="h-5 w-5 text-red-500" />
      case 'warning':
        return <Zap className="h-5 w-5 text-yellow-500" />
      case 'promo':
        return <Bell className="h-5 w-5 text-green-500" />
      default:
        return <Info className="h-5 w-5 text-blue-500" />
    }
  }

  const getAlertBadgeColor = (type: string) => {
    switch (type) {
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'promo':
        return 'bg-green-100 text-green-800 border-green-200'
      default:
        return 'bg-blue-100 text-blue-800 border-blue-200'
    }
  }

  const handleAlertAction = async (alertId: number, subscribed: boolean) => {
    if (subscribed) {
      await handleUnsubscribe(
        alertId,
        () => toast.success('Désabonnement réussi'),
        (error) => toast.error(`Erreur: ${error}`)
      )
    } else {
      await handleSubscribe(
        alertId,
        () => toast.success('Abonnement réussi'),
        (error) => toast.error(`Erreur: ${error}`)
      )
    }
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0"
        >
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Hub d'Information</h1>
            <p className="text-gray-600">
              Restez informé des dernières actualités et alertes du secteur électrique
            </p>
          </div>
          
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm text-green-600 font-medium">En direct</span>
          </div>
        </motion.div>

        {/* Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="flex flex-col space-y-4 md:flex-row md:items-center md:space-y-0 md:space-x-4"
        >
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Rechercher dans les alertes..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <Select value={filterType} onValueChange={setFilterType}>
            <SelectTrigger className="w-full md:w-48">
              <Filter className="h-4 w-4 mr-2" />
              <SelectValue placeholder="Type d'alerte" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Toutes les alertes</SelectItem>
              <SelectItem value="critical">Critiques</SelectItem>
              <SelectItem value="warning">Avertissements</SelectItem>
              <SelectItem value="info">Informations</SelectItem>
              <SelectItem value="promo">Promotions</SelectItem>
            </SelectContent>
          </Select>
        </motion.div>

        {/* Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-6"
        >
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <Bell className="h-8 w-8 text-blue-500" />
                <div>
                  <p className="text-2xl font-bold text-gray-900" data-testid="stat-number">{alerts.length}</p>
                  <p className="text-sm text-gray-600">Alertes actives</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <Calendar className="h-8 w-8 text-green-500" />
                <div>
                  <p className="text-2xl font-bold text-gray-900" data-testid="stat-number">{userAlerts.length}</p>
                  <p className="text-sm text-gray-600">Abonnements</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <Zap className="h-8 w-8 text-amber-500" />
                <div>
                  <p className="text-2xl font-bold text-gray-900" data-testid="stat-number">
                    {alerts.filter(a => a.type === 'critical').length}
                  </p>
                  <p className="text-sm text-gray-600">Alertes critiques</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Alerts List */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="space-y-4"
        >
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-500"></div>
            </div>
          ) : filteredAlerts.length === 0 ? (
            <Card>
              <CardContent className="p-12 text-center">
                <Bell className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Aucune alerte trouvée
                </h3>
                <p className="text-gray-600">
                  Aucune alerte ne correspond à vos critères de recherche.
                </p>
              </CardContent>
            </Card>
          ) : (
            filteredAlerts.map((alert, index) => {
              const subscribed = isSubscribed(alert.id)
              
              return (
                <motion.div
                  key={alert.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className="hover:shadow-md transition-shadow" data-testid="alert-card">
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start space-x-4 flex-1">
                          {getAlertIcon(alert.type)}
                          
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-2">
                              <h3 className="text-lg font-semibold text-gray-900">
                                {alert.title}
                              </h3>
                              <Badge className={getAlertBadgeColor(alert.type)}>
                                {alert.type.toUpperCase()}
                              </Badge>
                            </div>
                            
                            <p className="text-gray-600 mb-3">
                              {alert.body}
                            </p>
                            
                            <div className="flex items-center space-x-4 text-sm text-gray-500">
                              <span>Catégorie: {alert.category}</span>
                              <span>
                                {new Date(alert.created_at).toLocaleDateString('fr-FR')}
                              </span>
                            </div>
                          </div>
                        </div>
                        
                        <Button
                          variant={subscribed ? "outline" : "default"}
                          size="sm"
                          onClick={() => handleAlertAction(alert.id, subscribed)}
                          className={subscribed ? "text-red-600 border-red-200 hover:bg-red-50" : ""}
                        >
                          {subscribed ? 'Se désabonner' : 'S\'abonner'}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              )
            })
          )}
        </motion.div>
      </div>
    </DashboardLayout>
  )
}
