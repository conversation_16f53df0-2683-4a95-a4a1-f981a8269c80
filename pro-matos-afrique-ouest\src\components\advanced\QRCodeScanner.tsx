'use client'

import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Camera, 
  X, 
  Flashlight, 
  RotateCcw,
  CheckCircle,
  AlertTriangle,
  Package,
  Barcode,
  QrCode,
  Upload,
  Download,
  Search,
  Info
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { toast } from 'sonner'

interface ScanResult {
  type: 'qr' | 'barcode'
  data: string
  timestamp: string
  productInfo?: {
    name: string
    brand: string
    model: string
    category: string
    price?: number
    availability: 'in_stock' | 'low_stock' | 'out_of_stock'
    specifications: Record<string, string>
    suppliers: Array<{
      name: string
      price: number
      location: string
      stock: number
    }>
  }
}

interface QRCodeScannerProps {
  isOpen: boolean
  onClose: () => void
  onScanResult: (result: ScanResult) => void
  className?: string
}

export default function QRCodeScanner({ isOpen, onClose, onScanResult, className = '' }: QRCodeScannerProps) {
  const [isScanning, setIsScanning] = useState(false)
  const [flashEnabled, setFlashEnabled] = useState(false)
  const [cameraFacing, setCameraFacing] = useState<'user' | 'environment'>('environment')
  const [scanHistory, setScanHistory] = useState<ScanResult[]>([])
  const [manualInput, setManualInput] = useState('')
  const [isProcessing, setIsProcessing] = useState(false)
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const streamRef = useRef<MediaStream | null>(null)

  // Base de données produits simulée
  const productDatabase: Record<string, ScanResult['productInfo']> = {
    '3245060501013': {
      name: 'Disjoncteur C60N 32A',
      brand: 'Schneider Electric',
      model: 'C60N-C32',
      category: 'Protection',
      price: 45000,
      availability: 'in_stock',
      specifications: {
        'Courant nominal': '32A',
        'Courbe': 'C',
        'Pouvoir de coupure': '6kA',
        'Nombre de pôles': '1P',
        'Norme': 'NF EN 60898'
      },
      suppliers: [
        { name: 'CFAO Abidjan', price: 45000, location: 'Abidjan', stock: 150 },
        { name: 'ElectroDistrib', price: 47000, location: 'Dakar', stock: 89 },
        { name: 'TechElec Mali', price: 46500, location: 'Bamako', stock: 45 }
      ]
    },
    '3606480076428': {
      name: 'Câble H07V-U 2.5mm²',
      brand: 'Nexans',
      model: 'H07V-U 2.5',
      category: 'Câblage',
      price: 850,
      availability: 'in_stock',
      specifications: {
        'Section': '2.5mm²',
        'Type': 'H07V-U',
        'Tension': '750V',
        'Couleur': 'Bleu',
        'Conditionnement': '100m'
      },
      suppliers: [
        { name: 'CableTech CI', price: 850, location: 'Abidjan', stock: 500 },
        { name: 'ElectroWest', price: 880, location: 'Dakar', stock: 300 }
      ]
    },
    'PRO_MATOS_QR_001': {
      name: 'Prise 2P+T 16A Mosaic',
      brand: 'Legrand',
      model: 'Mosaic 077111',
      category: 'Appareillage',
      price: 12500,
      availability: 'low_stock',
      specifications: {
        'Type': '2P+T',
        'Courant': '16A',
        'Série': 'Mosaic',
        'Couleur': 'Blanc',
        'Norme': 'NF'
      },
      suppliers: [
        { name: 'Legrand Store', price: 12500, location: 'Abidjan', stock: 15 },
        { name: 'ElectroPlus', price: 13000, location: 'Bouaké', stock: 8 }
      ]
    }
  }

  // Démarrer la caméra
  const startCamera = async () => {
    try {
      setIsScanning(true)
      
      const constraints = {
        video: {
          facingMode: cameraFacing,
          width: { ideal: 1280 },
          height: { ideal: 720 }
        }
      }

      const stream = await navigator.mediaDevices.getUserMedia(constraints)
      streamRef.current = stream
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream
        videoRef.current.play()
      }

      // Simuler la détection de codes
      setTimeout(() => {
        if (isScanning) {
          simulateScan()
        }
      }, 3000)

    } catch (error) {
      console.error('Erreur caméra:', error)
      toast.error('Impossible d\'accéder à la caméra')
      setIsScanning(false)
    }
  }

  // Arrêter la caméra
  const stopCamera = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop())
      streamRef.current = null
    }
    setIsScanning(false)
  }

  // Simuler un scan (en attendant une vraie implémentation)
  const simulateScan = () => {
    const codes = Object.keys(productDatabase)
    const randomCode = codes[Math.floor(Math.random() * codes.length)]
    
    const result: ScanResult = {
      type: randomCode.startsWith('PRO_MATOS') ? 'qr' : 'barcode',
      data: randomCode,
      timestamp: new Date().toISOString(),
      productInfo: productDatabase[randomCode]
    }

    handleScanResult(result)
  }

  // Traiter le résultat du scan
  const handleScanResult = (result: ScanResult) => {
    setIsProcessing(true)
    
    setTimeout(() => {
      setScanHistory(prev => [result, ...prev.slice(0, 9)]) // Garder 10 derniers scans
      onScanResult(result)
      setIsProcessing(false)
      
      if (result.productInfo) {
        toast.success(`Produit trouvé : ${result.productInfo.name}`)
      } else {
        toast.warning('Code scanné mais produit non trouvé dans la base')
      }
    }, 1000)
  }

  // Saisie manuelle
  const handleManualInput = () => {
    if (!manualInput.trim()) return

    const result: ScanResult = {
      type: manualInput.length > 13 ? 'qr' : 'barcode',
      data: manualInput,
      timestamp: new Date().toISOString(),
      productInfo: productDatabase[manualInput]
    }

    handleScanResult(result)
    setManualInput('')
  }

  // Basculer le flash
  const toggleFlash = async () => {
    if (streamRef.current) {
      const track = streamRef.current.getVideoTracks()[0]
      if (track && 'torch' in track.getCapabilities()) {
        try {
          await track.applyConstraints({
            advanced: [{ torch: !flashEnabled } as any]
          })
          setFlashEnabled(!flashEnabled)
        } catch (error) {
          toast.error('Flash non disponible')
        }
      }
    }
  }

  // Changer de caméra
  const switchCamera = () => {
    stopCamera()
    setCameraFacing(prev => prev === 'user' ? 'environment' : 'user')
    setTimeout(startCamera, 500)
  }

  // Nettoyer lors de la fermeture
  useEffect(() => {
    if (!isOpen) {
      stopCamera()
    }
    return () => stopCamera()
  }, [isOpen])

  const getAvailabilityColor = (availability: string) => {
    switch (availability) {
      case 'in_stock': return 'bg-green-100 text-green-800 border-green-200'
      case 'low_stock': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'out_of_stock': return 'bg-red-100 text-red-800 border-red-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getAvailabilityText = (availability: string) => {
    switch (availability) {
      case 'in_stock': return 'En stock'
      case 'low_stock': return 'Stock faible'
      case 'out_of_stock': return 'Rupture'
      default: return 'Inconnu'
    }
  }

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-amber-400 to-amber-500 rounded-lg flex items-center justify-center">
                <QrCode className="h-6 w-6 text-white" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-900">Scanner QR/Code-barres</h2>
                <p className="text-gray-600">Scannez ou saisissez un code produit</p>
              </div>
            </div>
            
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-5 w-5" />
            </Button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 p-6">
            {/* Zone de scan */}
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Camera className="h-5 w-5" />
                    <span>Caméra</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="relative">
                    {/* Vidéo de la caméra */}
                    <div className="relative bg-gray-900 rounded-lg overflow-hidden aspect-video">
                      {isScanning ? (
                        <>
                          <video
                            ref={videoRef}
                            className="w-full h-full object-cover"
                            autoPlay
                            playsInline
                            muted
                          />
                          
                          {/* Overlay de scan */}
                          <div className="absolute inset-0 flex items-center justify-center">
                            <div className="w-48 h-48 border-2 border-amber-400 rounded-lg relative">
                              <div className="absolute top-0 left-0 w-6 h-6 border-t-4 border-l-4 border-amber-400"></div>
                              <div className="absolute top-0 right-0 w-6 h-6 border-t-4 border-r-4 border-amber-400"></div>
                              <div className="absolute bottom-0 left-0 w-6 h-6 border-b-4 border-l-4 border-amber-400"></div>
                              <div className="absolute bottom-0 right-0 w-6 h-6 border-b-4 border-r-4 border-amber-400"></div>
                              
                              {isProcessing && (
                                <div className="absolute inset-0 bg-green-500 bg-opacity-20 flex items-center justify-center">
                                  <CheckCircle className="h-12 w-12 text-green-500" />
                                </div>
                              )}
                            </div>
                          </div>
                          
                          {/* Ligne de scan animée */}
                          <motion.div
                            className="absolute left-1/2 transform -translate-x-1/2 w-48 h-0.5 bg-amber-400"
                            animate={{ y: [100, 200, 100] }}
                            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                          />
                        </>
                      ) : (
                        <div className="flex items-center justify-center h-full">
                          <div className="text-center text-gray-400">
                            <Camera className="h-16 w-16 mx-auto mb-4" />
                            <p>Caméra arrêtée</p>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Contrôles caméra */}
                    <div className="flex items-center justify-center space-x-4 mt-4">
                      <Button
                        onClick={isScanning ? stopCamera : startCamera}
                        className={isScanning ? 'bg-red-500 hover:bg-red-600' : 'bg-green-500 hover:bg-green-600'}
                      >
                        <Camera className="h-4 w-4 mr-2" />
                        {isScanning ? 'Arrêter' : 'Démarrer'}
                      </Button>
                      
                      <Button variant="outline" onClick={toggleFlash} disabled={!isScanning}>
                        <Flashlight className={`h-4 w-4 mr-2 ${flashEnabled ? 'text-yellow-500' : ''}`} />
                        Flash
                      </Button>
                      
                      <Button variant="outline" onClick={switchCamera} disabled={!isScanning}>
                        <RotateCcw className="h-4 w-4 mr-2" />
                        Changer
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Saisie manuelle */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Barcode className="h-5 w-5" />
                    <span>Saisie Manuelle</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex space-x-2">
                    <Input
                      value={manualInput}
                      onChange={(e) => setManualInput(e.target.value)}
                      placeholder="Saisissez le code..."
                      onKeyPress={(e) => e.key === 'Enter' && handleManualInput()}
                    />
                    <Button onClick={handleManualInput} disabled={!manualInput.trim()}>
                      <Search className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  <div className="mt-3 text-xs text-gray-500">
                    <p>Codes d'exemple :</p>
                    <div className="flex flex-wrap gap-2 mt-1">
                      {Object.keys(productDatabase).slice(0, 3).map(code => (
                        <button
                          key={code}
                          onClick={() => setManualInput(code)}
                          className="px-2 py-1 bg-gray-100 rounded text-xs hover:bg-gray-200"
                        >
                          {code}
                        </button>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Résultats et historique */}
            <div className="space-y-4">
              {/* Dernier résultat */}
              {scanHistory.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Package className="h-5 w-5" />
                      <span>Dernier Scan</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {(() => {
                      const lastScan = scanHistory[0]
                      return (
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              <Badge variant="outline">
                                {lastScan.type === 'qr' ? <QrCode className="h-3 w-3 mr-1" /> : <Barcode className="h-3 w-3 mr-1" />}
                                {lastScan.type.toUpperCase()}
                              </Badge>
                              <span className="text-sm text-gray-600">{lastScan.data}</span>
                            </div>
                            <span className="text-xs text-gray-500">
                              {new Date(lastScan.timestamp).toLocaleTimeString('fr-FR')}
                            </span>
                          </div>

                          {lastScan.productInfo ? (
                            <div className="space-y-3">
                              <div>
                                <h4 className="font-semibold text-gray-900">{lastScan.productInfo.name}</h4>
                                <p className="text-sm text-gray-600">{lastScan.productInfo.brand} - {lastScan.productInfo.model}</p>
                              </div>

                              <div className="flex items-center space-x-2">
                                <Badge className={getAvailabilityColor(lastScan.productInfo.availability)}>
                                  {getAvailabilityText(lastScan.productInfo.availability)}
                                </Badge>
                                <Badge variant="outline">{lastScan.productInfo.category}</Badge>
                                {lastScan.productInfo.price && (
                                  <span className="text-lg font-bold text-amber-600">
                                    {lastScan.productInfo.price.toLocaleString()} FCFA
                                  </span>
                                )}
                              </div>

                              <div className="flex space-x-2">
                                <Button size="sm" className="bg-amber-500 hover:bg-amber-600">
                                  <Download className="h-3 w-3 mr-1" />
                                  Ajouter au devis
                                </Button>
                                <Button variant="outline" size="sm">
                                  <Info className="h-3 w-3 mr-1" />
                                  Plus d'infos
                                </Button>
                              </div>
                            </div>
                          ) : (
                            <div className="flex items-center space-x-2 text-yellow-600">
                              <AlertTriangle className="h-4 w-4" />
                              <span className="text-sm">Produit non trouvé dans la base de données</span>
                            </div>
                          )}
                        </div>
                      )
                    })()}
                  </CardContent>
                </Card>
              )}

              {/* Historique des scans */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Upload className="h-5 w-5" />
                      <span>Historique</span>
                    </div>
                    <Badge variant="outline">{scanHistory.length}</Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {scanHistory.length === 0 ? (
                    <div className="text-center text-gray-500 py-8">
                      <QrCode className="h-12 w-12 mx-auto mb-3 text-gray-300" />
                      <p>Aucun scan effectué</p>
                    </div>
                  ) : (
                    <div className="space-y-2 max-h-64 overflow-y-auto">
                      {scanHistory.map((scan, index) => (
                        <motion.div
                          key={`${scan.data}-${scan.timestamp}`}
                          initial={{ opacity: 0, x: 20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.1 }}
                          className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer"
                          onClick={() => handleScanResult(scan)}
                        >
                          <div className="flex items-center space-x-3">
                            {scan.type === 'qr' ?
                              <QrCode className="h-4 w-4 text-blue-500" /> :
                              <Barcode className="h-4 w-4 text-green-500" />
                            }
                            <div>
                              <div className="font-medium text-sm">
                                {scan.productInfo?.name || scan.data}
                              </div>
                              {scan.productInfo && (
                                <div className="text-xs text-gray-600">
                                  {scan.productInfo.brand} - {scan.productInfo.category}
                                </div>
                              )}
                            </div>
                          </div>

                          <div className="text-right">
                            <div className="text-xs text-gray-500">
                              {new Date(scan.timestamp).toLocaleTimeString('fr-FR')}
                            </div>
                            {scan.productInfo?.availability && (
                              <Badge
                                className={`text-xs ${getAvailabilityColor(scan.productInfo.availability)}`}
                              >
                                {getAvailabilityText(scan.productInfo.availability)}
                              </Badge>
                            )}
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 p-4 bg-gray-50">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-600">
                <span className="font-medium">{scanHistory.length}</span> scan(s) effectué(s)
              </div>

              <div className="flex space-x-2">
                <Button variant="outline" size="sm" onClick={() => setScanHistory([])}>
                  Effacer historique
                </Button>
                <Button variant="outline" size="sm">
                  <Download className="h-3 w-3 mr-1" />
                  Exporter
                </Button>
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}
