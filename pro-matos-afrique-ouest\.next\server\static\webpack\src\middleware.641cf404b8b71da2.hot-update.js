"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("src/middleware",{

/***/ "(middleware)/./src/middleware.ts":
/*!***************************!*\
  !*** ./src/middleware.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(middleware)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\n\nasync function middleware(req) {\n    const res = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next();\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createMiddlewareClient)({\n        req,\n        res\n    });\n    // Vérifier la session utilisateur\n    const { data: { session } } = await supabase.auth.getSession();\n    // Routes protégées qui nécessitent une authentification\n    const protectedRoutes = [\n        \"/hub\",\n        \"/validation\",\n        \"/kits\",\n        \"/club\",\n        \"/crm\",\n        \"/expert\",\n        \"/prescriptor\"\n    ];\n    const isProtectedRoute = protectedRoutes.some((route)=>req.nextUrl.pathname.startsWith(route));\n    // Rediriger vers la connexion si pas authentifié\n    if (isProtectedRoute && !session) {\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(new URL(\"/auth/signin\", req.url));\n    }\n    // Si authentifié, ajouter les informations de base aux headers\n    if (session && isProtectedRoute) {\n        // Ajouter les informations utilisateur de base aux headers\n        const requestHeaders = new Headers(req.headers);\n        requestHeaders.set(\"x-user-id\", session.user.id);\n        requestHeaders.set(\"x-user-email\", session.user.email || \"\");\n        requestHeaders.set(\"x-user-role\", \"member\") // Rôle par défaut\n        ;\n        requestHeaders.set(\"x-user-statut\", \"white\") // Statut par défaut\n        ;\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n            request: {\n                headers: requestHeaders\n            }\n        });\n    }\n    return res;\n}\nconst config = {\n    matcher: [\n        /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder\n     */ \"/((?!api|_next/static|_next/image|favicon.ico|public).*)\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.ts\n");

/***/ })

});