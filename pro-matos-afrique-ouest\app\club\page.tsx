'use client'

import { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Crown, 
  Calendar, 
  Users, 
  Star,
  Gift,
  Zap,
  Shield,
  Award,
  MapPin,
  Clock,
  UserPlus,
  CreditCard,
  Download
} from 'lucide-react'
import { useAuthStore, usePermissions } from '@/lib/stores/authStore'
import { supabase } from '@/lib/supabase/client'
import { ClubEvent } from '@/lib/types/database'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { formatDate } from '@/lib/utils'
import { toast } from 'sonner'
import DashboardLayout from '@/components/layout/DashboardLayout'

export default function ClubPage() {
  const { user, profile } = useAuthStore()
  const permissions = usePermissions()
  const [events, setEvents] = useState<ClubEvent[]>([])
  const [userRegistrations, setUserRegistrations] = useState<number[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchEvents()
    if (user) {
      fetchUserRegistrations()
    }
  }, [user])

  const fetchEvents = async () => {
    try {
      setLoading(true)

      const { data, error } = await supabase
        .from('club_events')
        .select('*')
        .gte('event_date', new Date().toISOString())
        .order('event_date', { ascending: true })

      if (error) throw error

      setEvents(data || [])
    } catch (error) {
      console.error('Erreur lors du chargement des événements:', error)
      toast.error('Erreur lors du chargement des événements')
    } finally {
      setLoading(false)
    }
  }

  const fetchUserRegistrations = async () => {
    try {
      const { data, error } = await supabase
        .from('event_registrations')
        .select('event_id')
        .eq('user_id', user?.id)

      if (error) throw error

      setUserRegistrations(data?.map(r => r.event_id) || [])
    } catch (error) {
      console.error('Erreur lors du chargement des inscriptions:', error)
    }
  }

  const handleEventRegistration = async (eventId: number) => {
    if (!user) {
      toast.error('Vous devez être connecté')
      return
    }

    if (!permissions.canAccessClub) {
      toast.error('Accès VIP requis', {
        description: 'Rejoignez le Club Pro pour accéder aux événements exclusifs'
      })
      return
    }

    try {
      toast.loading('Inscription...', { id: 'registration' })

      const { error } = await supabase
        .from('event_registrations')
        .insert({
          user_id: user.id,
          event_id: eventId
        })

      if (error) throw error

      // Mettre à jour le compteur
      const event = events.find(e => e.id === eventId)
      if (event) {
        const { error: updateError } = await supabase
          .from('club_events')
          .update({ current_participants: event.current_participants + 1 })
          .eq('id', eventId)

        if (updateError) throw updateError

        setEvents(prev => prev.map(e => 
          e.id === eventId 
            ? { ...e, current_participants: e.current_participants + 1 }
            : e
        ))
      }

      setUserRegistrations(prev => [...prev, eventId])
      toast.success('Inscription réussie', { id: 'registration' })

    } catch (error) {
      console.error('Erreur lors de l\'inscription:', error)
      toast.error('Erreur lors de l\'inscription', { id: 'registration' })
    }
  }

  const handleUpgradeToVip = () => {
    // Simulation du processus d'upgrade
    toast.loading('Redirection vers le paiement...', { id: 'upgrade' })
    
    setTimeout(() => {
      toast.success('Processus d\'upgrade initié', { id: 'upgrade' })
      // En production, rediriger vers Stripe Checkout
      window.open('https://checkout.stripe.com/test', '_blank')
    }, 1500)
  }

  const vipBenefits = [
    {
      icon: <Crown className="h-6 w-6 text-amber-500" />,
      title: 'Accès Exclusif',
      description: 'Événements privés et contenus premium réservés aux membres VIP'
    },
    {
      icon: <Zap className="h-6 w-6 text-blue-500" />,
      title: 'Support Prioritaire',
      description: 'Assistance technique prioritaire et consultation d\'experts dédiés'
    },
    {
      icon: <Gift className="h-6 w-6 text-green-500" />,
      title: 'Avantages Commerciaux',
      description: 'Remises exclusives et accès anticipé aux nouveaux produits'
    },
    {
      icon: <Shield className="h-6 w-6 text-purple-500" />,
      title: 'Réseau Premium',
      description: 'Networking avec l\'élite des professionnels de l\'électrique'
    }
  ]

  if (!permissions.canAccessClub) {
    return (
      <DashboardLayout>
        <div className="space-y-8">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center"
          >
            <div className="w-20 h-20 bg-gradient-to-r from-amber-500 to-amber-600 rounded-full flex items-center justify-center mx-auto mb-6">
              <Crown className="h-10 w-10 text-white" />
            </div>
            <h1 className="text-4xl font-bold text-gray-900 mb-4">Club Pro Matos</h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Rejoignez l'élite des professionnels de l'électrique en Afrique de l'Ouest
            </p>
          </motion.div>

          {/* Benefits */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="grid grid-cols-1 md:grid-cols-2 gap-6"
          >
            {vipBenefits.map((benefit, index) => (
              <Card key={index} className="text-center">
                <CardContent className="p-6">
                  <div className="flex justify-center mb-4">
                    {benefit.icon}
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {benefit.title}
                  </h3>
                  <p className="text-gray-600">
                    {benefit.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </motion.div>

          {/* CTA */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="text-center"
          >
            <Card className="max-w-md mx-auto">
              <CardContent className="p-8">
                <Crown className="h-12 w-12 text-amber-500 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  Devenir Membre VIP
                </h3>
                <p className="text-gray-600 mb-6">
                  Accédez à tous les avantages exclusifs du Club Pro Matos
                </p>
                <div className="text-3xl font-bold text-amber-600 mb-6">
                  150,000 FCFA<span className="text-lg text-gray-500">/mois</span>
                </div>
                <Button 
                  onClick={handleUpgradeToVip}
                  className="w-full bg-amber-600 hover:bg-amber-700"
                  size="lg"
                >
                  <CreditCard className="h-5 w-5 mr-2" />
                  Devenir VIP Maintenant
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0"
        >
          <div>
            <div className="flex items-center space-x-3 mb-2">
              <Crown className="h-8 w-8 text-amber-500" />
              <h1 className="text-3xl font-bold text-gray-900">Club Pro Matos</h1>
              <Badge className="bg-amber-100 text-amber-800 border-amber-200">
                VIP
              </Badge>
            </div>
            <p className="text-gray-600">
              Bienvenue dans votre espace exclusif, {profile?.full_name}
            </p>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-amber-600">{userRegistrations.length}</div>
              <div className="text-xs text-gray-500">Événements</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-amber-600">VIP</div>
              <div className="text-xs text-gray-500">Statut</div>
            </div>
          </div>
        </motion.div>

        {/* Quick Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-4 gap-6"
        >
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <Calendar className="h-8 w-8 text-blue-500" />
                <div>
                  <p className="text-2xl font-bold text-gray-900">{events.length}</p>
                  <p className="text-sm text-gray-600">Événements à venir</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <Users className="h-8 w-8 text-green-500" />
                <div>
                  <p className="text-2xl font-bold text-gray-900">1,234</p>
                  <p className="text-sm text-gray-600">Membres VIP</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <Award className="h-8 w-8 text-purple-500" />
                <div>
                  <p className="text-2xl font-bold text-gray-900">89</p>
                  <p className="text-sm text-gray-600">Experts disponibles</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <Star className="h-8 w-8 text-amber-500" />
                <div>
                  <p className="text-2xl font-bold text-gray-900">4.9</p>
                  <p className="text-sm text-gray-600">Satisfaction</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Upcoming Events */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Événements Exclusifs</h2>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {loading ? (
              Array.from({ length: 4 }).map((_, index) => (
                <Card key={index} className="animate-pulse">
                  <CardContent className="p-6">
                    <div className="h-4 bg-gray-200 rounded mb-4"></div>
                    <div className="h-3 bg-gray-200 rounded mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded mb-4"></div>
                    <div className="h-8 bg-gray-200 rounded"></div>
                  </CardContent>
                </Card>
              ))
            ) : events.length === 0 ? (
              <div className="col-span-full">
                <Card>
                  <CardContent className="p-12 text-center">
                    <Calendar className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      Aucun événement à venir
                    </h3>
                    <p className="text-gray-600">
                      De nouveaux événements exclusifs seront bientôt disponibles.
                    </p>
                  </CardContent>
                </Card>
              </div>
            ) : (
              events.map((event, index) => {
                const isRegistered = userRegistrations.includes(event.id)
                const isFull = event.current_participants >= event.max_participants
                
                return (
                  <motion.div
                    key={event.id}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <Card className="h-full hover:shadow-lg transition-shadow">
                      <CardHeader>
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <CardTitle className="text-lg mb-2">{event.title}</CardTitle>
                            <div className="flex items-center space-x-2 mb-2">
                              {event.is_vip_only && (
                                <Badge className="bg-amber-100 text-amber-800 border-amber-200">
                                  <Crown className="h-3 w-3 mr-1" />
                                  VIP Exclusif
                                </Badge>
                              )}
                              {isRegistered && (
                                <Badge className="bg-green-100 text-green-800 border-green-200">
                                  <UserPlus className="h-3 w-3 mr-1" />
                                  Inscrit
                                </Badge>
                              )}
                              {isFull && (
                                <Badge className="bg-red-100 text-red-800 border-red-200">
                                  Complet
                                </Badge>
                              )}
                            </div>
                          </div>
                          <Calendar className="h-8 w-8 text-gray-400" />
                        </div>
                        <CardDescription>{event.description}</CardDescription>
                      </CardHeader>
                      
                      <CardContent>
                        <div className="space-y-3 mb-4">
                          <div className="flex items-center space-x-2 text-sm text-gray-600">
                            <Clock className="h-4 w-4" />
                            <span>{formatDate(event.event_date)}</span>
                          </div>
                          
                          {event.location && (
                            <div className="flex items-center space-x-2 text-sm text-gray-600">
                              <MapPin className="h-4 w-4" />
                              <span>{event.location}</span>
                            </div>
                          )}
                          
                          <div className="flex items-center space-x-2 text-sm text-gray-600">
                            <Users className="h-4 w-4" />
                            <span>
                              {event.current_participants}/{event.max_participants} participants
                            </span>
                          </div>
                        </div>
                        
                        <Button
                          onClick={() => handleEventRegistration(event.id)}
                          disabled={isRegistered || isFull}
                          className={`w-full ${
                            isRegistered 
                              ? 'bg-green-600 hover:bg-green-700' 
                              : 'bg-amber-600 hover:bg-amber-700'
                          }`}
                        >
                          {isRegistered ? (
                            <>
                              <UserPlus className="h-4 w-4 mr-2" />
                              Déjà inscrit
                            </>
                          ) : isFull ? (
                            'Événement complet'
                          ) : (
                            <>
                              <UserPlus className="h-4 w-4 mr-2" />
                              S'inscrire
                            </>
                          )}
                        </Button>
                      </CardContent>
                    </Card>
                  </motion.div>
                )
              })
            )}
          </div>
        </motion.div>

        {/* VIP Badge Generator */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Award className="h-6 w-6 text-amber-500" />
                <span>Badge VIP Personnel</span>
              </CardTitle>
              <CardDescription>
                Téléchargez votre badge VIP personnalisé pour vos signatures et documents
              </CardDescription>
            </CardHeader>
            
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 bg-gradient-to-r from-amber-500 to-amber-600 rounded-lg flex items-center justify-center">
                    <Crown className="h-8 w-8 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{profile?.full_name}</h3>
                    <p className="text-sm text-gray-600">Membre VIP Pro Matos</p>
                    <p className="text-xs text-amber-600">Certifié depuis 2024</p>
                  </div>
                </div>
                
                <Button variant="outline">
                  <Download className="h-4 w-4 mr-2" />
                  Télécharger Badge SVG
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </DashboardLayout>
  )
}
