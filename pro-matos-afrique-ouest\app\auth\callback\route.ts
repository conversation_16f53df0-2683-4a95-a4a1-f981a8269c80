import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url)
  const code = requestUrl.searchParams.get('code')

  if (code) {
    const cookieStore = cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    try {
      await supabase.auth.exchangeCodeForSession(code)
    } catch (error) {
      console.error('Erreur lors de l\'échange du code:', error)
      return NextResponse.redirect(`${requestUrl.origin}/auth/signin?error=auth_error`)
    }
  }

  // Rediriger vers le dashboard après connexion réussie
  return NextResponse.redirect(`${requestUrl.origin}/hub`)
}
