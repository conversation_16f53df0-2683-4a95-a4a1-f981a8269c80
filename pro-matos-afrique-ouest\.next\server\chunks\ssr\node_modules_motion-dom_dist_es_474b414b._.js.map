{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/animation/utils/get-value-transition.mjs"], "sourcesContent": ["function getValueTransition(transition, key) {\n    return (transition?.[key] ??\n        transition?.[\"default\"] ??\n        transition);\n}\n\nexport { getValueTransition };\n"], "names": [], "mappings": ";;;AAAA,SAAS,mBAAmB,UAAU,EAAE,GAAG;IACvC,OAAQ,YAAY,CAAC,IAAI,IACrB,YAAY,CAAC,UAAU,IACvB;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 20, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/frameloop/order.mjs"], "sourcesContent": ["const stepsOrder = [\n    \"setup\", // Compute\n    \"read\", // Read\n    \"resolveKeyframes\", // Write/Read/Write/Read\n    \"preUpdate\", // Compute\n    \"update\", // Compute\n    \"preRender\", // Compute\n    \"render\", // Write\n    \"postRender\", // Compute\n];\n\nexport { stepsOrder };\n"], "names": [], "mappings": ";;;AAAA,MAAM,aAAa;IACf;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/stats/buffer.mjs"], "sourcesContent": ["const statsBuffer = {\n    value: null,\n    addProjectionMetrics: null,\n};\n\nexport { statsBuffer };\n"], "names": [], "mappings": ";;;AAAA,MAAM,cAAc;IAChB,OAAO;IACP,sBAAsB;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/frameloop/render-step.mjs"], "sourcesContent": ["import { statsBuffer } from '../stats/buffer.mjs';\n\nfunction createRenderStep(runNextFrame, stepName) {\n    /**\n     * We create and reuse two queues, one to queue jobs for the current frame\n     * and one for the next. We reuse to avoid triggering GC after x frames.\n     */\n    let thisFrame = new Set();\n    let nextFrame = new Set();\n    /**\n     * Track whether we're currently processing jobs in this step. This way\n     * we can decide whether to schedule new jobs for this frame or next.\n     */\n    let isProcessing = false;\n    let flushNextFrame = false;\n    /**\n     * A set of processes which were marked keepAlive when scheduled.\n     */\n    const toKeepAlive = new WeakSet();\n    let latestFrameData = {\n        delta: 0.0,\n        timestamp: 0.0,\n        isProcessing: false,\n    };\n    let numCalls = 0;\n    function triggerCallback(callback) {\n        if (toKeepAlive.has(callback)) {\n            step.schedule(callback);\n            runNextFrame();\n        }\n        numCalls++;\n        callback(latestFrameData);\n    }\n    const step = {\n        /**\n         * Schedule a process to run on the next frame.\n         */\n        schedule: (callback, keepAlive = false, immediate = false) => {\n            const addToCurrentFrame = immediate && isProcessing;\n            const queue = addToCurrentFrame ? thisFrame : nextFrame;\n            if (keepAlive)\n                toKeepAlive.add(callback);\n            if (!queue.has(callback))\n                queue.add(callback);\n            return callback;\n        },\n        /**\n         * Cancel the provided callback from running on the next frame.\n         */\n        cancel: (callback) => {\n            nextFrame.delete(callback);\n            toKeepAlive.delete(callback);\n        },\n        /**\n         * Execute all schedule callbacks.\n         */\n        process: (frameData) => {\n            latestFrameData = frameData;\n            /**\n             * If we're already processing we've probably been triggered by a flushSync\n             * inside an existing process. Instead of executing, mark flushNextFrame\n             * as true and ensure we flush the following frame at the end of this one.\n             */\n            if (isProcessing) {\n                flushNextFrame = true;\n                return;\n            }\n            isProcessing = true;\n            [thisFrame, nextFrame] = [nextFrame, thisFrame];\n            // Execute this frame\n            thisFrame.forEach(triggerCallback);\n            /**\n             * If we're recording stats then\n             */\n            if (stepName && statsBuffer.value) {\n                statsBuffer.value.frameloop[stepName].push(numCalls);\n            }\n            numCalls = 0;\n            // Clear the frame so no callbacks remain. This is to avoid\n            // memory leaks should this render step not run for a while.\n            thisFrame.clear();\n            isProcessing = false;\n            if (flushNextFrame) {\n                flushNextFrame = false;\n                step.process(frameData);\n            }\n        },\n    };\n    return step;\n}\n\nexport { createRenderStep };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,iBAAiB,YAAY,EAAE,QAAQ;IAC5C;;;KAGC,GACD,IAAI,YAAY,IAAI;IACpB,IAAI,YAAY,IAAI;IACpB;;;KAGC,GACD,IAAI,eAAe;IACnB,IAAI,iBAAiB;IACrB;;KAEC,GACD,MAAM,cAAc,IAAI;IACxB,IAAI,kBAAkB;QAClB,OAAO;QACP,WAAW;QACX,cAAc;IAClB;IACA,IAAI,WAAW;IACf,SAAS,gBAAgB,QAAQ;QAC7B,IAAI,YAAY,GAAG,CAAC,WAAW;YAC3B,KAAK,QAAQ,CAAC;YACd;QACJ;QACA;QACA,SAAS;IACb;IACA,MAAM,OAAO;QACT;;SAEC,GACD,UAAU,CAAC,UAAU,YAAY,KAAK,EAAE,YAAY,KAAK;YACrD,MAAM,oBAAoB,aAAa;YACvC,MAAM,QAAQ,oBAAoB,YAAY;YAC9C,IAAI,WACA,YAAY,GAAG,CAAC;YACpB,IAAI,CAAC,MAAM,GAAG,CAAC,WACX,MAAM,GAAG,CAAC;YACd,OAAO;QACX;QACA;;SAEC,GACD,QAAQ,CAAC;YACL,UAAU,MAAM,CAAC;YACjB,YAAY,MAAM,CAAC;QACvB;QACA;;SAEC,GACD,SAAS,CAAC;YACN,kBAAkB;YAClB;;;;aAIC,GACD,IAAI,cAAc;gBACd,iBAAiB;gBACjB;YACJ;YACA,eAAe;YACf,CAAC,WAAW,UAAU,GAAG;gBAAC;gBAAW;aAAU;YAC/C,qBAAqB;YACrB,UAAU,OAAO,CAAC;YAClB;;aAEC,GACD,IAAI,YAAY,+JAAA,CAAA,cAAW,CAAC,KAAK,EAAE;gBAC/B,+JAAA,CAAA,cAAW,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC;YAC/C;YACA,WAAW;YACX,2DAA2D;YAC3D,4DAA4D;YAC5D,UAAU,KAAK;YACf,eAAe;YACf,IAAI,gBAAgB;gBAChB,iBAAiB;gBACjB,KAAK,OAAO,CAAC;YACjB;QACJ;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 147, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/frameloop/batcher.mjs"], "sourcesContent": ["import { MotionGlobalConfig } from 'motion-utils';\nimport { stepsOrder } from './order.mjs';\nimport { createRenderStep } from './render-step.mjs';\n\nconst maxElapsed = 40;\nfunction createRenderBatcher(scheduleNextBatch, allowKeepAlive) {\n    let runNextFrame = false;\n    let useDefaultElapsed = true;\n    const state = {\n        delta: 0.0,\n        timestamp: 0.0,\n        isProcessing: false,\n    };\n    const flagRunNextFrame = () => (runNextFrame = true);\n    const steps = stepsOrder.reduce((acc, key) => {\n        acc[key] = createRenderStep(flagRunNextFrame, allowKeepAlive ? key : undefined);\n        return acc;\n    }, {});\n    const { setup, read, resolveKeyframes, preUpdate, update, preRender, render, postRender, } = steps;\n    const processBatch = () => {\n        const timestamp = MotionGlobalConfig.useManualTiming\n            ? state.timestamp\n            : performance.now();\n        runNextFrame = false;\n        if (!MotionGlobalConfig.useManualTiming) {\n            state.delta = useDefaultElapsed\n                ? 1000 / 60\n                : Math.max(Math.min(timestamp - state.timestamp, maxElapsed), 1);\n        }\n        state.timestamp = timestamp;\n        state.isProcessing = true;\n        // Unrolled render loop for better per-frame performance\n        setup.process(state);\n        read.process(state);\n        resolveKeyframes.process(state);\n        preUpdate.process(state);\n        update.process(state);\n        preRender.process(state);\n        render.process(state);\n        postRender.process(state);\n        state.isProcessing = false;\n        if (runNextFrame && allowKeepAlive) {\n            useDefaultElapsed = false;\n            scheduleNextBatch(processBatch);\n        }\n    };\n    const wake = () => {\n        runNextFrame = true;\n        useDefaultElapsed = true;\n        if (!state.isProcessing) {\n            scheduleNextBatch(processBatch);\n        }\n    };\n    const schedule = stepsOrder.reduce((acc, key) => {\n        const step = steps[key];\n        acc[key] = (process, keepAlive = false, immediate = false) => {\n            if (!runNextFrame)\n                wake();\n            return step.schedule(process, keepAlive, immediate);\n        };\n        return acc;\n    }, {});\n    const cancel = (process) => {\n        for (let i = 0; i < stepsOrder.length; i++) {\n            steps[stepsOrder[i]].cancel(process);\n        }\n    };\n    return { schedule, cancel, state, steps };\n}\n\nexport { createRenderBatcher };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,MAAM,aAAa;AACnB,SAAS,oBAAoB,iBAAiB,EAAE,cAAc;IAC1D,IAAI,eAAe;IACnB,IAAI,oBAAoB;IACxB,MAAM,QAAQ;QACV,OAAO;QACP,WAAW;QACX,cAAc;IAClB;IACA,MAAM,mBAAmB,IAAO,eAAe;IAC/C,MAAM,QAAQ,kKAAA,CAAA,aAAU,CAAC,MAAM,CAAC,CAAC,KAAK;QAClC,GAAG,CAAC,IAAI,GAAG,CAAA,GAAA,2KAAA,CAAA,mBAAgB,AAAD,EAAE,kBAAkB,iBAAiB,MAAM;QACrE,OAAO;IACX,GAAG,CAAC;IACJ,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,gBAAgB,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAG,GAAG;IAC7F,MAAM,eAAe;QACjB,MAAM,YAAY,kKAAA,CAAA,qBAAkB,CAAC,eAAe,GAC9C,MAAM,SAAS,GACf,YAAY,GAAG;QACrB,eAAe;QACf,IAAI,CAAC,kKAAA,CAAA,qBAAkB,CAAC,eAAe,EAAE;YACrC,MAAM,KAAK,GAAG,oBACR,OAAO,KACP,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,YAAY,MAAM,SAAS,EAAE,aAAa;QACtE;QACA,MAAM,SAAS,GAAG;QAClB,MAAM,YAAY,GAAG;QACrB,wDAAwD;QACxD,MAAM,OAAO,CAAC;QACd,KAAK,OAAO,CAAC;QACb,iBAAiB,OAAO,CAAC;QACzB,UAAU,OAAO,CAAC;QAClB,OAAO,OAAO,CAAC;QACf,UAAU,OAAO,CAAC;QAClB,OAAO,OAAO,CAAC;QACf,WAAW,OAAO,CAAC;QACnB,MAAM,YAAY,GAAG;QACrB,IAAI,gBAAgB,gBAAgB;YAChC,oBAAoB;YACpB,kBAAkB;QACtB;IACJ;IACA,MAAM,OAAO;QACT,eAAe;QACf,oBAAoB;QACpB,IAAI,CAAC,MAAM,YAAY,EAAE;YACrB,kBAAkB;QACtB;IACJ;IACA,MAAM,WAAW,kKAAA,CAAA,aAAU,CAAC,MAAM,CAAC,CAAC,KAAK;QACrC,MAAM,OAAO,KAAK,CAAC,IAAI;QACvB,GAAG,CAAC,IAAI,GAAG,CAAC,SAAS,YAAY,KAAK,EAAE,YAAY,KAAK;YACrD,IAAI,CAAC,cACD;YACJ,OAAO,KAAK,QAAQ,CAAC,SAAS,WAAW;QAC7C;QACA,OAAO;IACX,GAAG,CAAC;IACJ,MAAM,SAAS,CAAC;QACZ,IAAK,IAAI,IAAI,GAAG,IAAI,kKAAA,CAAA,aAAU,CAAC,MAAM,EAAE,IAAK;YACxC,KAAK,CAAC,kKAAA,CAAA,aAAU,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC;QAChC;IACJ;IACA,OAAO;QAAE;QAAU;QAAQ;QAAO;IAAM;AAC5C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/frameloop/frame.mjs"], "sourcesContent": ["import { noop } from 'motion-utils';\nimport { createRenderBatcher } from './batcher.mjs';\n\nconst { schedule: frame, cancel: cancelFrame, state: frameData, steps: frameSteps, } = /* @__PURE__ */ createRenderBatcher(typeof requestAnimationFrame !== \"undefined\" ? requestAnimationFrame : noop, true);\n\nexport { cancelFrame, frame, frameData, frameSteps };\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEA,MAAM,EAAE,UAAU,KAAK,EAAE,QAAQ,WAAW,EAAE,OAAO,SAAS,EAAE,OAAO,UAAU,EAAG,GAAG,aAAa,GAAG,CAAA,GAAA,oKAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,0BAA0B,cAAc,wBAAwB,sJAAA,CAAA,OAAI,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 246, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/render/utils/keys-transform.mjs"], "sourcesContent": ["/**\n * Generate a list of every possible transform key.\n */\nconst transformPropOrder = [\n    \"transformPerspective\",\n    \"x\",\n    \"y\",\n    \"z\",\n    \"translateX\",\n    \"translateY\",\n    \"translateZ\",\n    \"scale\",\n    \"scaleX\",\n    \"scaleY\",\n    \"rotate\",\n    \"rotateX\",\n    \"rotateY\",\n    \"rotateZ\",\n    \"skew\",\n    \"skewX\",\n    \"skewY\",\n];\n/**\n * A quick lookup for transform props.\n */\nconst transformProps = /*@__PURE__*/ (() => new Set(transformPropOrder))();\n\nexport { transformPropOrder, transformProps };\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AACD,MAAM,qBAAqB;IACvB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AACD;;CAEC,GACD,MAAM,iBAAiB,WAAW,GAAG,CAAC,IAAM,IAAI,IAAI,mBAAmB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 281, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/render/utils/keys-position.mjs"], "sourcesContent": ["import { transformPropOrder } from './keys-transform.mjs';\n\nconst positionalKeys = new Set([\n    \"width\",\n    \"height\",\n    \"top\",\n    \"left\",\n    \"right\",\n    \"bottom\",\n    ...transformPropOrder,\n]);\n\nexport { positionalKeys };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,iBAAiB,IAAI,IAAI;IAC3B;IACA;IACA;IACA;IACA;IACA;OACG,oLAAA,CAAA,qBAAkB;CACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 302, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/frameloop/sync-time.mjs"], "sourcesContent": ["import { MotionGlobalConfig } from 'motion-utils';\nimport { frameData } from './frame.mjs';\n\nlet now;\nfunction clearTime() {\n    now = undefined;\n}\n/**\n * An eventloop-synchronous alternative to performance.now().\n *\n * Ensures that time measurements remain consistent within a synchronous context.\n * Usually calling performance.now() twice within the same synchronous context\n * will return different values which isn't useful for animations when we're usually\n * trying to sync animations to the same frame.\n */\nconst time = {\n    now: () => {\n        if (now === undefined) {\n            time.set(frameData.isProcessing || MotionGlobalConfig.useManualTiming\n                ? frameData.timestamp\n                : performance.now());\n        }\n        return now;\n    },\n    set: (newTime) => {\n        now = newTime;\n        queueMicrotask(clearTime);\n    },\n};\n\nexport { time };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,IAAI;AACJ,SAAS;IACL,MAAM;AACV;AACA;;;;;;;CAOC,GACD,MAAM,OAAO;IACT,KAAK;QACD,IAAI,QAAQ,WAAW;YACnB,KAAK,GAAG,CAAC,kKAAA,CAAA,YAAS,CAAC,YAAY,IAAI,kKAAA,CAAA,qBAAkB,CAAC,eAAe,GAC/D,kKAAA,CAAA,YAAS,CAAC,SAAS,GACnB,YAAY,GAAG;QACzB;QACA,OAAO;IACX;IACA,KAAK,CAAC;QACF,MAAM;QACN,eAAe;IACnB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 339, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/value/index.mjs"], "sourcesContent": ["import { warnOnce, SubscriptionManager, velocityPerSecond } from 'motion-utils';\nimport { time } from '../frameloop/sync-time.mjs';\nimport { frame } from '../frameloop/frame.mjs';\n\n/**\n * Maximum time between the value of two frames, beyond which we\n * assume the velocity has since been 0.\n */\nconst MAX_VELOCITY_DELTA = 30;\nconst isFloat = (value) => {\n    return !isNaN(parseFloat(value));\n};\nconst collectMotionValues = {\n    current: undefined,\n};\n/**\n * `MotionValue` is used to track the state and velocity of motion values.\n *\n * @public\n */\nclass MotionValue {\n    /**\n     * @param init - The initiating value\n     * @param config - Optional configuration options\n     *\n     * -  `transformer`: A function to transform incoming values with.\n     */\n    constructor(init, options = {}) {\n        /**\n         * Tracks whether this value can output a velocity. Currently this is only true\n         * if the value is numerical, but we might be able to widen the scope here and support\n         * other value types.\n         *\n         * @internal\n         */\n        this.canTrackVelocity = null;\n        /**\n         * An object containing a SubscriptionManager for each active event.\n         */\n        this.events = {};\n        this.updateAndNotify = (v, render = true) => {\n            const currentTime = time.now();\n            /**\n             * If we're updating the value during another frame or eventloop\n             * than the previous frame, then the we set the previous frame value\n             * to current.\n             */\n            if (this.updatedAt !== currentTime) {\n                this.setPrevFrameValue();\n            }\n            this.prev = this.current;\n            this.setCurrent(v);\n            // Update update subscribers\n            if (this.current !== this.prev) {\n                this.events.change?.notify(this.current);\n                if (this.dependents) {\n                    for (const dependent of this.dependents) {\n                        dependent.dirty();\n                    }\n                }\n            }\n            // Update render subscribers\n            if (render) {\n                this.events.renderRequest?.notify(this.current);\n            }\n        };\n        this.hasAnimated = false;\n        this.setCurrent(init);\n        this.owner = options.owner;\n    }\n    setCurrent(current) {\n        this.current = current;\n        this.updatedAt = time.now();\n        if (this.canTrackVelocity === null && current !== undefined) {\n            this.canTrackVelocity = isFloat(this.current);\n        }\n    }\n    setPrevFrameValue(prevFrameValue = this.current) {\n        this.prevFrameValue = prevFrameValue;\n        this.prevUpdatedAt = this.updatedAt;\n    }\n    /**\n     * Adds a function that will be notified when the `MotionValue` is updated.\n     *\n     * It returns a function that, when called, will cancel the subscription.\n     *\n     * When calling `onChange` inside a React component, it should be wrapped with the\n     * `useEffect` hook. As it returns an unsubscribe function, this should be returned\n     * from the `useEffect` function to ensure you don't add duplicate subscribers..\n     *\n     * ```jsx\n     * export const MyComponent = () => {\n     *   const x = useMotionValue(0)\n     *   const y = useMotionValue(0)\n     *   const opacity = useMotionValue(1)\n     *\n     *   useEffect(() => {\n     *     function updateOpacity() {\n     *       const maxXY = Math.max(x.get(), y.get())\n     *       const newOpacity = transform(maxXY, [0, 100], [1, 0])\n     *       opacity.set(newOpacity)\n     *     }\n     *\n     *     const unsubscribeX = x.on(\"change\", updateOpacity)\n     *     const unsubscribeY = y.on(\"change\", updateOpacity)\n     *\n     *     return () => {\n     *       unsubscribeX()\n     *       unsubscribeY()\n     *     }\n     *   }, [])\n     *\n     *   return <motion.div style={{ x }} />\n     * }\n     * ```\n     *\n     * @param subscriber - A function that receives the latest value.\n     * @returns A function that, when called, will cancel this subscription.\n     *\n     * @deprecated\n     */\n    onChange(subscription) {\n        if (process.env.NODE_ENV !== \"production\") {\n            warnOnce(false, `value.onChange(callback) is deprecated. Switch to value.on(\"change\", callback).`);\n        }\n        return this.on(\"change\", subscription);\n    }\n    on(eventName, callback) {\n        if (!this.events[eventName]) {\n            this.events[eventName] = new SubscriptionManager();\n        }\n        const unsubscribe = this.events[eventName].add(callback);\n        if (eventName === \"change\") {\n            return () => {\n                unsubscribe();\n                /**\n                 * If we have no more change listeners by the start\n                 * of the next frame, stop active animations.\n                 */\n                frame.read(() => {\n                    if (!this.events.change.getSize()) {\n                        this.stop();\n                    }\n                });\n            };\n        }\n        return unsubscribe;\n    }\n    clearListeners() {\n        for (const eventManagers in this.events) {\n            this.events[eventManagers].clear();\n        }\n    }\n    /**\n     * Attaches a passive effect to the `MotionValue`.\n     */\n    attach(passiveEffect, stopPassiveEffect) {\n        this.passiveEffect = passiveEffect;\n        this.stopPassiveEffect = stopPassiveEffect;\n    }\n    /**\n     * Sets the state of the `MotionValue`.\n     *\n     * @remarks\n     *\n     * ```jsx\n     * const x = useMotionValue(0)\n     * x.set(10)\n     * ```\n     *\n     * @param latest - Latest value to set.\n     * @param render - Whether to notify render subscribers. Defaults to `true`\n     *\n     * @public\n     */\n    set(v, render = true) {\n        if (!render || !this.passiveEffect) {\n            this.updateAndNotify(v, render);\n        }\n        else {\n            this.passiveEffect(v, this.updateAndNotify);\n        }\n    }\n    setWithVelocity(prev, current, delta) {\n        this.set(current);\n        this.prev = undefined;\n        this.prevFrameValue = prev;\n        this.prevUpdatedAt = this.updatedAt - delta;\n    }\n    /**\n     * Set the state of the `MotionValue`, stopping any active animations,\n     * effects, and resets velocity to `0`.\n     */\n    jump(v, endAnimation = true) {\n        this.updateAndNotify(v);\n        this.prev = v;\n        this.prevUpdatedAt = this.prevFrameValue = undefined;\n        endAnimation && this.stop();\n        if (this.stopPassiveEffect)\n            this.stopPassiveEffect();\n    }\n    dirty() {\n        this.events.change?.notify(this.current);\n    }\n    addDependent(dependent) {\n        if (!this.dependents) {\n            this.dependents = new Set();\n        }\n        this.dependents.add(dependent);\n    }\n    removeDependent(dependent) {\n        if (this.dependents) {\n            this.dependents.delete(dependent);\n        }\n    }\n    /**\n     * Returns the latest state of `MotionValue`\n     *\n     * @returns - The latest state of `MotionValue`\n     *\n     * @public\n     */\n    get() {\n        if (collectMotionValues.current) {\n            collectMotionValues.current.push(this);\n        }\n        return this.current;\n    }\n    /**\n     * @public\n     */\n    getPrevious() {\n        return this.prev;\n    }\n    /**\n     * Returns the latest velocity of `MotionValue`\n     *\n     * @returns - The latest velocity of `MotionValue`. Returns `0` if the state is non-numerical.\n     *\n     * @public\n     */\n    getVelocity() {\n        const currentTime = time.now();\n        if (!this.canTrackVelocity ||\n            this.prevFrameValue === undefined ||\n            currentTime - this.updatedAt > MAX_VELOCITY_DELTA) {\n            return 0;\n        }\n        const delta = Math.min(this.updatedAt - this.prevUpdatedAt, MAX_VELOCITY_DELTA);\n        // Casts because of parseFloat's poor typing\n        return velocityPerSecond(parseFloat(this.current) -\n            parseFloat(this.prevFrameValue), delta);\n    }\n    /**\n     * Registers a new animation to control this `MotionValue`. Only one\n     * animation can drive a `MotionValue` at one time.\n     *\n     * ```jsx\n     * value.start()\n     * ```\n     *\n     * @param animation - A function that starts the provided animation\n     */\n    start(startAnimation) {\n        this.stop();\n        return new Promise((resolve) => {\n            this.hasAnimated = true;\n            this.animation = startAnimation(resolve);\n            if (this.events.animationStart) {\n                this.events.animationStart.notify();\n            }\n        }).then(() => {\n            if (this.events.animationComplete) {\n                this.events.animationComplete.notify();\n            }\n            this.clearAnimation();\n        });\n    }\n    /**\n     * Stop the currently active animation.\n     *\n     * @public\n     */\n    stop() {\n        if (this.animation) {\n            this.animation.stop();\n            if (this.events.animationCancel) {\n                this.events.animationCancel.notify();\n            }\n        }\n        this.clearAnimation();\n    }\n    /**\n     * Returns `true` if this value is currently animating.\n     *\n     * @public\n     */\n    isAnimating() {\n        return !!this.animation;\n    }\n    clearAnimation() {\n        delete this.animation;\n    }\n    /**\n     * Destroy and clean up subscribers to this `MotionValue`.\n     *\n     * The `MotionValue` hooks like `useMotionValue` and `useTransform` automatically\n     * handle the lifecycle of the returned `MotionValue`, so this method is only necessary if you've manually\n     * created a `MotionValue` via the `motionValue` function.\n     *\n     * @public\n     */\n    destroy() {\n        this.dependents?.clear();\n        this.events.destroy?.notify();\n        this.clearListeners();\n        this.stop();\n        if (this.stopPassiveEffect) {\n            this.stopPassiveEffect();\n        }\n    }\n}\nfunction motionValue(init, options) {\n    return new MotionValue(init, options);\n}\n\nexport { MotionValue, collectMotionValues, motionValue };\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;AAAA;AACA;AACA;;;;AAEA;;;CAGC,GACD,MAAM,qBAAqB;AAC3B,MAAM,UAAU,CAAC;IACb,OAAO,CAAC,MAAM,WAAW;AAC7B;AACA,MAAM,sBAAsB;IACxB,SAAS;AACb;AACA;;;;CAIC,GACD,MAAM;IACF;;;;;KAKC,GACD,YAAY,IAAI,EAAE,UAAU,CAAC,CAAC,CAAE;QAC5B;;;;;;SAMC,GACD,IAAI,CAAC,gBAAgB,GAAG;QACxB;;SAEC,GACD,IAAI,CAAC,MAAM,GAAG,CAAC;QACf,IAAI,CAAC,eAAe,GAAG,CAAC,GAAG,SAAS,IAAI;YACpC,MAAM,cAAc,yKAAA,CAAA,OAAI,CAAC,GAAG;YAC5B;;;;aAIC,GACD,IAAI,IAAI,CAAC,SAAS,KAAK,aAAa;gBAChC,IAAI,CAAC,iBAAiB;YAC1B;YACA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO;YACxB,IAAI,CAAC,UAAU,CAAC;YAChB,4BAA4B;YAC5B,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,IAAI,EAAE;gBAC5B,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,IAAI,CAAC,OAAO;gBACvC,IAAI,IAAI,CAAC,UAAU,EAAE;oBACjB,KAAK,MAAM,aAAa,IAAI,CAAC,UAAU,CAAE;wBACrC,UAAU,KAAK;oBACnB;gBACJ;YACJ;YACA,4BAA4B;YAC5B,IAAI,QAAQ;gBACR,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,OAAO,IAAI,CAAC,OAAO;YAClD;QACJ;QACA,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,UAAU,CAAC;QAChB,IAAI,CAAC,KAAK,GAAG,QAAQ,KAAK;IAC9B;IACA,WAAW,OAAO,EAAE;QAChB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,SAAS,GAAG,yKAAA,CAAA,OAAI,CAAC,GAAG;QACzB,IAAI,IAAI,CAAC,gBAAgB,KAAK,QAAQ,YAAY,WAAW;YACzD,IAAI,CAAC,gBAAgB,GAAG,QAAQ,IAAI,CAAC,OAAO;QAChD;IACJ;IACA,kBAAkB,iBAAiB,IAAI,CAAC,OAAO,EAAE;QAC7C,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,SAAS;IACvC;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAuCC,GACD,SAAS,YAAY,EAAE;QACnB,wCAA2C;YACvC,CAAA,GAAA,8JAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,+EAA+E,CAAC;QACrG;QACA,OAAO,IAAI,CAAC,EAAE,CAAC,UAAU;IAC7B;IACA,GAAG,SAAS,EAAE,QAAQ,EAAE;QACpB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;YACzB,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,IAAI,yKAAA,CAAA,sBAAmB;QACpD;QACA,MAAM,cAAc,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC;QAC/C,IAAI,cAAc,UAAU;YACxB,OAAO;gBACH;gBACA;;;iBAGC,GACD,kKAAA,CAAA,QAAK,CAAC,IAAI,CAAC;oBACP,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,IAAI;wBAC/B,IAAI,CAAC,IAAI;oBACb;gBACJ;YACJ;QACJ;QACA,OAAO;IACX;IACA,iBAAiB;QACb,IAAK,MAAM,iBAAiB,IAAI,CAAC,MAAM,CAAE;YACrC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK;QACpC;IACJ;IACA;;KAEC,GACD,OAAO,aAAa,EAAE,iBAAiB,EAAE;QACrC,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,iBAAiB,GAAG;IAC7B;IACA;;;;;;;;;;;;;;KAcC,GACD,IAAI,CAAC,EAAE,SAAS,IAAI,EAAE;QAClB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,EAAE;YAChC,IAAI,CAAC,eAAe,CAAC,GAAG;QAC5B,OACK;YACD,IAAI,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC,eAAe;QAC9C;IACJ;IACA,gBAAgB,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE;QAClC,IAAI,CAAC,GAAG,CAAC;QACT,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,SAAS,GAAG;IAC1C;IACA;;;KAGC,GACD,KAAK,CAAC,EAAE,eAAe,IAAI,EAAE;QACzB,IAAI,CAAC,eAAe,CAAC;QACrB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,GAAG;QAC3C,gBAAgB,IAAI,CAAC,IAAI;QACzB,IAAI,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,iBAAiB;IAC9B;IACA,QAAQ;QACJ,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,IAAI,CAAC,OAAO;IAC3C;IACA,aAAa,SAAS,EAAE;QACpB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,IAAI,CAAC,UAAU,GAAG,IAAI;QAC1B;QACA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;IACxB;IACA,gBAAgB,SAAS,EAAE;QACvB,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;QAC3B;IACJ;IACA;;;;;;KAMC,GACD,MAAM;QACF,IAAI,oBAAoB,OAAO,EAAE;YAC7B,oBAAoB,OAAO,CAAC,IAAI,CAAC,IAAI;QACzC;QACA,OAAO,IAAI,CAAC,OAAO;IACvB;IACA;;KAEC,GACD,cAAc;QACV,OAAO,IAAI,CAAC,IAAI;IACpB;IACA;;;;;;KAMC,GACD,cAAc;QACV,MAAM,cAAc,yKAAA,CAAA,OAAI,CAAC,GAAG;QAC5B,IAAI,CAAC,IAAI,CAAC,gBAAgB,IACtB,IAAI,CAAC,cAAc,KAAK,aACxB,cAAc,IAAI,CAAC,SAAS,GAAG,oBAAoB;YACnD,OAAO;QACX;QACA,MAAM,QAAQ,KAAK,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,aAAa,EAAE;QAC5D,4CAA4C;QAC5C,OAAO,CAAA,GAAA,2KAAA,CAAA,oBAAiB,AAAD,EAAE,WAAW,IAAI,CAAC,OAAO,IAC5C,WAAW,IAAI,CAAC,cAAc,GAAG;IACzC;IACA;;;;;;;;;KASC,GACD,MAAM,cAAc,EAAE;QAClB,IAAI,CAAC,IAAI;QACT,OAAO,IAAI,QAAQ,CAAC;YAChB,IAAI,CAAC,WAAW,GAAG;YACnB,IAAI,CAAC,SAAS,GAAG,eAAe;YAChC,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE;gBAC5B,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM;YACrC;QACJ,GAAG,IAAI,CAAC;YACJ,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE;gBAC/B,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAM;YACxC;YACA,IAAI,CAAC,cAAc;QACvB;IACJ;IACA;;;;KAIC,GACD,OAAO;QACH,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,SAAS,CAAC,IAAI;YACnB,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE;gBAC7B,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,MAAM;YACtC;QACJ;QACA,IAAI,CAAC,cAAc;IACvB;IACA;;;;KAIC,GACD,cAAc;QACV,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS;IAC3B;IACA,iBAAiB;QACb,OAAO,IAAI,CAAC,SAAS;IACzB;IACA;;;;;;;;KAQC,GACD,UAAU;QACN,IAAI,CAAC,UAAU,EAAE;QACjB,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;QACrB,IAAI,CAAC,cAAc;QACnB,IAAI,CAAC,IAAI;QACT,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,iBAAiB;QAC1B;IACJ;AACJ;AACA,SAAS,YAAY,IAAI,EAAE,OAAO;IAC9B,OAAO,IAAI,YAAY,MAAM;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 657, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/value/utils/is-motion-value.mjs"], "sourcesContent": ["const isMotionValue = (value) => Boolean(value && value.getVelocity);\n\nexport { isMotionValue };\n"], "names": [], "mappings": ";;;AAAA,MAAM,gBAAgB,CAAC,QAAU,QAAQ,SAAS,MAAM,WAAW", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 668, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/stats/animation-count.mjs"], "sourcesContent": ["const activeAnimations = {\n    layout: 0,\n    mainThread: 0,\n    waapi: 0,\n};\n\nexport { activeAnimations };\n"], "names": [], "mappings": ";;;AAAA,MAAM,mBAAmB;IACrB,QAAQ;IACR,YAAY;IACZ,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 683, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/animation/utils/is-css-variable.mjs"], "sourcesContent": ["const checkStringStartsWith = (token) => (key) => typeof key === \"string\" && key.startsWith(token);\nconst isCSSVariableName = \n/*@__PURE__*/ checkStringStartsWith(\"--\");\nconst startsAsVariableToken = \n/*@__PURE__*/ checkStringStartsWith(\"var(--\");\nconst isCSSVariableToken = (value) => {\n    const startsWithToken = startsAsVariableToken(value);\n    if (!startsWithToken)\n        return false;\n    // Ensure any comments are stripped from the value as this can harm performance of the regex.\n    return singleCssVariableRegex.test(value.split(\"/*\")[0].trim());\n};\nconst singleCssVariableRegex = /var\\(--(?:[\\w-]+\\s*|[\\w-]+\\s*,(?:\\s*[^)(\\s]|\\s*\\((?:[^)(]|\\([^)(]*\\))*\\))+\\s*)\\)$/iu;\n\nexport { isCSSVariableName, isCSSVariableToken };\n"], "names": [], "mappings": ";;;;AAAA,MAAM,wBAAwB,CAAC,QAAU,CAAC,MAAQ,OAAO,QAAQ,YAAY,IAAI,UAAU,CAAC;AAC5F,MAAM,oBACN,WAAW,GAAG,sBAAsB;AACpC,MAAM,wBACN,WAAW,GAAG,sBAAsB;AACpC,MAAM,qBAAqB,CAAC;IACxB,MAAM,kBAAkB,sBAAsB;IAC9C,IAAI,CAAC,iBACD,OAAO;IACX,6FAA6F;IAC7F,OAAO,uBAAuB,IAAI,CAAC,MAAM,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI;AAChE;AACA,MAAM,yBAAyB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 704, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/value/types/numbers/index.mjs"], "sourcesContent": ["import { clamp } from 'motion-utils';\n\nconst number = {\n    test: (v) => typeof v === \"number\",\n    parse: parseFloat,\n    transform: (v) => v,\n};\nconst alpha = {\n    ...number,\n    transform: (v) => clamp(0, 1, v),\n};\nconst scale = {\n    ...number,\n    default: 1,\n};\n\nexport { alpha, number, scale };\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,MAAM,SAAS;IACX,MAAM,CAAC,IAAM,OAAO,MAAM;IAC1B,OAAO;IACP,WAAW,CAAC,IAAM;AACtB;AACA,MAAM,QAAQ;IACV,GAAG,MAAM;IACT,WAAW,CAAC,IAAM,CAAA,GAAA,uJAAA,CAAA,QAAK,AAAD,EAAE,GAAG,GAAG;AAClC;AACA,MAAM,QAAQ;IACV,GAAG,MAAM;IACT,SAAS;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 731, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/value/types/utils/sanitize.mjs"], "sourcesContent": ["// If this number is a decimal, make it just five decimal places\n// to avoid exponents\nconst sanitize = (v) => Math.round(v * 100000) / 100000;\n\nexport { sanitize };\n"], "names": [], "mappings": "AAAA,gEAAgE;AAChE,qBAAqB;;;;AACrB,MAAM,WAAW,CAAC,IAAM,KAAK,KAAK,CAAC,IAAI,UAAU", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 744, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/value/types/utils/float-regex.mjs"], "sourcesContent": ["const floatRegex = /-?(?:\\d+(?:\\.\\d+)?|\\.\\d+)/gu;\n\nexport { floatRegex };\n"], "names": [], "mappings": ";;;AAAA,MAAM,aAAa", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 755, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/value/types/utils/is-nullish.mjs"], "sourcesContent": ["function isNullish(v) {\n    return v == null;\n}\n\nexport { isNullish };\n"], "names": [], "mappings": ";;;AAAA,SAAS,UAAU,CAAC;IAChB,OAAO,KAAK;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 768, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/value/types/utils/single-color-regex.mjs"], "sourcesContent": ["const singleColorRegex = /^(?:#[\\da-f]{3,8}|(?:rgb|hsl)a?\\((?:-?[\\d.]+%?[,\\s]+){2}-?[\\d.]+%?\\s*(?:[,/]\\s*)?(?:\\b\\d+(?:\\.\\d+)?|\\.\\d+)?%?\\))$/iu;\n\nexport { singleColorRegex };\n"], "names": [], "mappings": ";;;AAAA,MAAM,mBAAmB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 779, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/value/types/color/utils.mjs"], "sourcesContent": ["import { floatRegex } from '../utils/float-regex.mjs';\nimport { isNullish } from '../utils/is-nullish.mjs';\nimport { singleColorRegex } from '../utils/single-color-regex.mjs';\n\n/**\n * Returns true if the provided string is a color, ie rgba(0,0,0,0) or #000,\n * but false if a number or multiple colors\n */\nconst isColorString = (type, testProp) => (v) => {\n    return Boolean((typeof v === \"string\" &&\n        singleColorRegex.test(v) &&\n        v.startsWith(type)) ||\n        (testProp &&\n            !isNullish(v) &&\n            Object.prototype.hasOwnProperty.call(v, testProp)));\n};\nconst splitColor = (aName, bName, cName) => (v) => {\n    if (typeof v !== \"string\")\n        return v;\n    const [a, b, c, alpha] = v.match(floatRegex);\n    return {\n        [aName]: parseFloat(a),\n        [bName]: parseFloat(b),\n        [cName]: parseFloat(c),\n        alpha: alpha !== undefined ? parseFloat(alpha) : 1,\n    };\n};\n\nexport { isColorString, splitColor };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEA;;;CAGC,GACD,MAAM,gBAAgB,CAAC,MAAM,WAAa,CAAC;QACvC,OAAO,QAAQ,AAAC,OAAO,MAAM,YACzB,mMAAA,CAAA,mBAAgB,CAAC,IAAI,CAAC,MACtB,EAAE,UAAU,CAAC,SACZ,YACG,CAAC,CAAA,GAAA,wLAAA,CAAA,YAAS,AAAD,EAAE,MACX,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG;IACpD;AACA,MAAM,aAAa,CAAC,OAAO,OAAO,QAAU,CAAC;QACzC,IAAI,OAAO,MAAM,UACb,OAAO;QACX,MAAM,CAAC,GAAG,GAAG,GAAG,MAAM,GAAG,EAAE,KAAK,CAAC,yLAAA,CAAA,aAAU;QAC3C,OAAO;YACH,CAAC,MAAM,EAAE,WAAW;YACpB,CAAC,MAAM,EAAE,WAAW;YACpB,CAAC,MAAM,EAAE,WAAW;YACpB,OAAO,UAAU,YAAY,WAAW,SAAS;QACrD;IACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 812, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/value/types/color/rgba.mjs"], "sourcesContent": ["import { clamp } from 'motion-utils';\nimport { number, alpha } from '../numbers/index.mjs';\nimport { sanitize } from '../utils/sanitize.mjs';\nimport { isColorString, splitColor } from './utils.mjs';\n\nconst clampRgbUnit = (v) => clamp(0, 255, v);\nconst rgbUnit = {\n    ...number,\n    transform: (v) => Math.round(clampRgbUnit(v)),\n};\nconst rgba = {\n    test: /*@__PURE__*/ isColorString(\"rgb\", \"red\"),\n    parse: /*@__PURE__*/ splitColor(\"red\", \"green\", \"blue\"),\n    transform: ({ red, green, blue, alpha: alpha$1 = 1 }) => \"rgba(\" +\n        rgbUnit.transform(red) +\n        \", \" +\n        rgbUnit.transform(green) +\n        \", \" +\n        rgbUnit.transform(blue) +\n        \", \" +\n        sanitize(alpha.transform(alpha$1)) +\n        \")\",\n};\n\nexport { rgbUnit, rgba };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AAEA,MAAM,eAAe,CAAC,IAAM,CAAA,GAAA,uJAAA,CAAA,QAAK,AAAD,EAAE,GAAG,KAAK;AAC1C,MAAM,UAAU;IACZ,GAAG,kLAAA,CAAA,SAAM;IACT,WAAW,CAAC,IAAM,KAAK,KAAK,CAAC,aAAa;AAC9C;AACA,MAAM,OAAO;IACT,MAAM,WAAW,GAAG,CAAA,GAAA,gLAAA,CAAA,gBAAa,AAAD,EAAE,OAAO;IACzC,OAAO,WAAW,GAAG,CAAA,GAAA,gLAAA,CAAA,aAAU,AAAD,EAAE,OAAO,SAAS;IAChD,WAAW,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,UAAU,CAAC,EAAE,GAAK,UACrD,QAAQ,SAAS,CAAC,OAClB,OACA,QAAQ,SAAS,CAAC,SAClB,OACA,QAAQ,SAAS,CAAC,QAClB,OACA,CAAA,GAAA,mLAAA,CAAA,WAAQ,AAAD,EAAE,kLAAA,CAAA,QAAK,CAAC,SAAS,CAAC,YACzB;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 841, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/value/types/color/hex.mjs"], "sourcesContent": ["import { rgba } from './rgba.mjs';\nimport { isColorString } from './utils.mjs';\n\nfunction parseHex(v) {\n    let r = \"\";\n    let g = \"\";\n    let b = \"\";\n    let a = \"\";\n    // If we have 6 characters, ie #FF0000\n    if (v.length > 5) {\n        r = v.substring(1, 3);\n        g = v.substring(3, 5);\n        b = v.substring(5, 7);\n        a = v.substring(7, 9);\n        // Or we have 3 characters, ie #F00\n    }\n    else {\n        r = v.substring(1, 2);\n        g = v.substring(2, 3);\n        b = v.substring(3, 4);\n        a = v.substring(4, 5);\n        r += r;\n        g += g;\n        b += b;\n        a += a;\n    }\n    return {\n        red: parseInt(r, 16),\n        green: parseInt(g, 16),\n        blue: parseInt(b, 16),\n        alpha: a ? parseInt(a, 16) / 255 : 1,\n    };\n}\nconst hex = {\n    test: /*@__PURE__*/ isColorString(\"#\"),\n    parse: parseHex,\n    transform: rgba.transform,\n};\n\nexport { hex };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,SAAS,CAAC;IACf,IAAI,IAAI;IACR,IAAI,IAAI;IACR,IAAI,IAAI;IACR,IAAI,IAAI;IACR,sCAAsC;IACtC,IAAI,EAAE,MAAM,GAAG,GAAG;QACd,IAAI,EAAE,SAAS,CAAC,GAAG;QACnB,IAAI,EAAE,SAAS,CAAC,GAAG;QACnB,IAAI,EAAE,SAAS,CAAC,GAAG;QACnB,IAAI,EAAE,SAAS,CAAC,GAAG;IACnB,mCAAmC;IACvC,OACK;QACD,IAAI,EAAE,SAAS,CAAC,GAAG;QACnB,IAAI,EAAE,SAAS,CAAC,GAAG;QACnB,IAAI,EAAE,SAAS,CAAC,GAAG;QACnB,IAAI,EAAE,SAAS,CAAC,GAAG;QACnB,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACT;IACA,OAAO;QACH,KAAK,SAAS,GAAG;QACjB,OAAO,SAAS,GAAG;QACnB,MAAM,SAAS,GAAG;QAClB,OAAO,IAAI,SAAS,GAAG,MAAM,MAAM;IACvC;AACJ;AACA,MAAM,MAAM;IACR,MAAM,WAAW,GAAG,CAAA,GAAA,gLAAA,CAAA,gBAAa,AAAD,EAAE;IAClC,OAAO;IACP,WAAW,+KAAA,CAAA,OAAI,CAAC,SAAS;AAC7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 889, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/value/types/numbers/units.mjs"], "sourcesContent": ["/*#__NO_SIDE_EFFECTS__*/\nconst createUnitType = (unit) => ({\n    test: (v) => typeof v === \"string\" && v.endsWith(unit) && v.split(\" \").length === 1,\n    parse: parseFloat,\n    transform: (v) => `${v}${unit}`,\n});\nconst degrees = /*@__PURE__*/ createUnitType(\"deg\");\nconst percent = /*@__PURE__*/ createUnitType(\"%\");\nconst px = /*@__PURE__*/ createUnitType(\"px\");\nconst vh = /*@__PURE__*/ createUnitType(\"vh\");\nconst vw = /*@__PURE__*/ createUnitType(\"vw\");\nconst progressPercentage = /*@__PURE__*/ (() => ({\n    ...percent,\n    parse: (v) => percent.parse(v) / 100,\n    transform: (v) => percent.transform(v * 100),\n}))();\n\nexport { degrees, percent, progressPercentage, px, vh, vw };\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;;;;;AACtB,MAAM,iBAAiB,CAAC,OAAS,CAAC;QAC9B,MAAM,CAAC,IAAM,OAAO,MAAM,YAAY,EAAE,QAAQ,CAAC,SAAS,EAAE,KAAK,CAAC,KAAK,MAAM,KAAK;QAClF,OAAO;QACP,WAAW,CAAC,IAAM,GAAG,IAAI,MAAM;IACnC,CAAC;AACD,MAAM,UAAU,WAAW,GAAG,eAAe;AAC7C,MAAM,UAAU,WAAW,GAAG,eAAe;AAC7C,MAAM,KAAK,WAAW,GAAG,eAAe;AACxC,MAAM,KAAK,WAAW,GAAG,eAAe;AACxC,MAAM,KAAK,WAAW,GAAG,eAAe;AACxC,MAAM,qBAAqB,WAAW,GAAG,CAAC,IAAM,CAAC;QAC7C,GAAG,OAAO;QACV,OAAO,CAAC,IAAM,QAAQ,KAAK,CAAC,KAAK;QACjC,WAAW,CAAC,IAAM,QAAQ,SAAS,CAAC,IAAI;IAC5C,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 919, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/value/types/color/hsla.mjs"], "sourcesContent": ["import { alpha } from '../numbers/index.mjs';\nimport { percent } from '../numbers/units.mjs';\nimport { sanitize } from '../utils/sanitize.mjs';\nimport { isColorString, splitColor } from './utils.mjs';\n\nconst hsla = {\n    test: /*@__PURE__*/ isColorString(\"hsl\", \"hue\"),\n    parse: /*@__PURE__*/ splitColor(\"hue\", \"saturation\", \"lightness\"),\n    transform: ({ hue, saturation, lightness, alpha: alpha$1 = 1 }) => {\n        return (\"hsla(\" +\n            Math.round(hue) +\n            \", \" +\n            percent.transform(sanitize(saturation)) +\n            \", \" +\n            percent.transform(sanitize(lightness)) +\n            \", \" +\n            sanitize(alpha.transform(alpha$1)) +\n            \")\");\n    },\n};\n\nexport { hsla };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEA,MAAM,OAAO;IACT,MAAM,WAAW,GAAG,CAAA,GAAA,gLAAA,CAAA,gBAAa,AAAD,EAAE,OAAO;IACzC,OAAO,WAAW,GAAG,CAAA,GAAA,gLAAA,CAAA,aAAU,AAAD,EAAE,OAAO,cAAc;IACrD,WAAW,CAAC,EAAE,GAAG,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,UAAU,CAAC,EAAE;QAC1D,OAAQ,UACJ,KAAK,KAAK,CAAC,OACX,OACA,kLAAA,CAAA,UAAO,CAAC,SAAS,CAAC,CAAA,GAAA,mLAAA,CAAA,WAAQ,AAAD,EAAE,eAC3B,OACA,kLAAA,CAAA,UAAO,CAAC,SAAS,CAAC,CAAA,GAAA,mLAAA,CAAA,WAAQ,AAAD,EAAE,cAC3B,OACA,CAAA,GAAA,mLAAA,CAAA,WAAQ,AAAD,EAAE,kLAAA,CAAA,QAAK,CAAC,SAAS,CAAC,YACzB;IACR;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 944, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/value/types/color/index.mjs"], "sourcesContent": ["import { hex } from './hex.mjs';\nimport { hsla } from './hsla.mjs';\nimport { rgba } from './rgba.mjs';\n\nconst color = {\n    test: (v) => rgba.test(v) || hex.test(v) || hsla.test(v),\n    parse: (v) => {\n        if (rgba.test(v)) {\n            return rgba.parse(v);\n        }\n        else if (hsla.test(v)) {\n            return hsla.parse(v);\n        }\n        else {\n            return hex.parse(v);\n        }\n    },\n    transform: (v) => {\n        return typeof v === \"string\"\n            ? v\n            : v.hasOwnProperty(\"red\")\n                ? rgba.transform(v)\n                : hsla.transform(v);\n    },\n    getAnimatableNone: (v) => {\n        const parsed = color.parse(v);\n        parsed.alpha = 0;\n        return color.transform(parsed);\n    },\n};\n\nexport { color };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,MAAM,QAAQ;IACV,MAAM,CAAC,IAAM,+KAAA,CAAA,OAAI,CAAC,IAAI,CAAC,MAAM,8KAAA,CAAA,MAAG,CAAC,IAAI,CAAC,MAAM,+KAAA,CAAA,OAAI,CAAC,IAAI,CAAC;IACtD,OAAO,CAAC;QACJ,IAAI,+KAAA,CAAA,OAAI,CAAC,IAAI,CAAC,IAAI;YACd,OAAO,+KAAA,CAAA,OAAI,CAAC,KAAK,CAAC;QACtB,OACK,IAAI,+KAAA,CAAA,OAAI,CAAC,IAAI,CAAC,IAAI;YACnB,OAAO,+KAAA,CAAA,OAAI,CAAC,KAAK,CAAC;QACtB,OACK;YACD,OAAO,8KAAA,CAAA,MAAG,CAAC,KAAK,CAAC;QACrB;IACJ;IACA,WAAW,CAAC;QACR,OAAO,OAAO,MAAM,WACd,IACA,EAAE,cAAc,CAAC,SACb,+KAAA,CAAA,OAAI,CAAC,SAAS,CAAC,KACf,+KAAA,CAAA,OAAI,CAAC,SAAS,CAAC;IAC7B;IACA,mBAAmB,CAAC;QAChB,MAAM,SAAS,MAAM,KAAK,CAAC;QAC3B,OAAO,KAAK,GAAG;QACf,OAAO,MAAM,SAAS,CAAC;IAC3B;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 980, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/value/types/utils/color-regex.mjs"], "sourcesContent": ["const colorRegex = /(?:#[\\da-f]{3,8}|(?:rgb|hsl)a?\\((?:-?[\\d.]+%?[,\\s]+){2}-?[\\d.]+%?\\s*(?:[,/]\\s*)?(?:\\b\\d+(?:\\.\\d+)?|\\.\\d+)?%?\\))/giu;\n\nexport { colorRegex };\n"], "names": [], "mappings": ";;;AAAA,MAAM,aAAa", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 991, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/value/types/complex/index.mjs"], "sourcesContent": ["import { color } from '../color/index.mjs';\nimport { colorRegex } from '../utils/color-regex.mjs';\nimport { floatRegex } from '../utils/float-regex.mjs';\nimport { sanitize } from '../utils/sanitize.mjs';\n\nfunction test(v) {\n    return (isNaN(v) &&\n        typeof v === \"string\" &&\n        (v.match(floatRegex)?.length || 0) +\n            (v.match(colorRegex)?.length || 0) >\n            0);\n}\nconst NUMBER_TOKEN = \"number\";\nconst COLOR_TOKEN = \"color\";\nconst VAR_TOKEN = \"var\";\nconst VAR_FUNCTION_TOKEN = \"var(\";\nconst SPLIT_TOKEN = \"${}\";\n// this regex consists of the `singleCssVariableRegex|rgbHSLValueRegex|digitRegex`\nconst complexRegex = /var\\s*\\(\\s*--(?:[\\w-]+\\s*|[\\w-]+\\s*,(?:\\s*[^)(\\s]|\\s*\\((?:[^)(]|\\([^)(]*\\))*\\))+\\s*)\\)|#[\\da-f]{3,8}|(?:rgb|hsl)a?\\((?:-?[\\d.]+%?[,\\s]+){2}-?[\\d.]+%?\\s*(?:[,/]\\s*)?(?:\\b\\d+(?:\\.\\d+)?|\\.\\d+)?%?\\)|-?(?:\\d+(?:\\.\\d+)?|\\.\\d+)/giu;\nfunction analyseComplexValue(value) {\n    const originalValue = value.toString();\n    const values = [];\n    const indexes = {\n        color: [],\n        number: [],\n        var: [],\n    };\n    const types = [];\n    let i = 0;\n    const tokenised = originalValue.replace(complexRegex, (parsedValue) => {\n        if (color.test(parsedValue)) {\n            indexes.color.push(i);\n            types.push(COLOR_TOKEN);\n            values.push(color.parse(parsedValue));\n        }\n        else if (parsedValue.startsWith(VAR_FUNCTION_TOKEN)) {\n            indexes.var.push(i);\n            types.push(VAR_TOKEN);\n            values.push(parsedValue);\n        }\n        else {\n            indexes.number.push(i);\n            types.push(NUMBER_TOKEN);\n            values.push(parseFloat(parsedValue));\n        }\n        ++i;\n        return SPLIT_TOKEN;\n    });\n    const split = tokenised.split(SPLIT_TOKEN);\n    return { values, split, indexes, types };\n}\nfunction parseComplexValue(v) {\n    return analyseComplexValue(v).values;\n}\nfunction createTransformer(source) {\n    const { split, types } = analyseComplexValue(source);\n    const numSections = split.length;\n    return (v) => {\n        let output = \"\";\n        for (let i = 0; i < numSections; i++) {\n            output += split[i];\n            if (v[i] !== undefined) {\n                const type = types[i];\n                if (type === NUMBER_TOKEN) {\n                    output += sanitize(v[i]);\n                }\n                else if (type === COLOR_TOKEN) {\n                    output += color.transform(v[i]);\n                }\n                else {\n                    output += v[i];\n                }\n            }\n        }\n        return output;\n    };\n}\nconst convertNumbersToZero = (v) => typeof v === \"number\" ? 0 : color.test(v) ? color.getAnimatableNone(v) : v;\nfunction getAnimatableNone(v) {\n    const parsed = parseComplexValue(v);\n    const transformer = createTransformer(v);\n    return transformer(parsed.map(convertNumbersToZero));\n}\nconst complex = {\n    test,\n    parse: parseComplexValue,\n    createTransformer,\n    getAnimatableNone,\n};\n\nexport { analyseComplexValue, complex };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AAEA,SAAS,KAAK,CAAC;IACX,OAAQ,MAAM,MACV,OAAO,MAAM,YACb,CAAC,EAAE,KAAK,CAAC,yLAAA,CAAA,aAAU,GAAG,UAAU,CAAC,IAC7B,CAAC,EAAE,KAAK,CAAC,yLAAA,CAAA,aAAU,GAAG,UAAU,CAAC,IACjC;AACZ;AACA,MAAM,eAAe;AACrB,MAAM,cAAc;AACpB,MAAM,YAAY;AAClB,MAAM,qBAAqB;AAC3B,MAAM,cAAc;AACpB,kFAAkF;AAClF,MAAM,eAAe;AACrB,SAAS,oBAAoB,KAAK;IAC9B,MAAM,gBAAgB,MAAM,QAAQ;IACpC,MAAM,SAAS,EAAE;IACjB,MAAM,UAAU;QACZ,OAAO,EAAE;QACT,QAAQ,EAAE;QACV,KAAK,EAAE;IACX;IACA,MAAM,QAAQ,EAAE;IAChB,IAAI,IAAI;IACR,MAAM,YAAY,cAAc,OAAO,CAAC,cAAc,CAAC;QACnD,IAAI,gLAAA,CAAA,QAAK,CAAC,IAAI,CAAC,cAAc;YACzB,QAAQ,KAAK,CAAC,IAAI,CAAC;YACnB,MAAM,IAAI,CAAC;YACX,OAAO,IAAI,CAAC,gLAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QAC5B,OACK,IAAI,YAAY,UAAU,CAAC,qBAAqB;YACjD,QAAQ,GAAG,CAAC,IAAI,CAAC;YACjB,MAAM,IAAI,CAAC;YACX,OAAO,IAAI,CAAC;QAChB,OACK;YACD,QAAQ,MAAM,CAAC,IAAI,CAAC;YACpB,MAAM,IAAI,CAAC;YACX,OAAO,IAAI,CAAC,WAAW;QAC3B;QACA,EAAE;QACF,OAAO;IACX;IACA,MAAM,QAAQ,UAAU,KAAK,CAAC;IAC9B,OAAO;QAAE;QAAQ;QAAO;QAAS;IAAM;AAC3C;AACA,SAAS,kBAAkB,CAAC;IACxB,OAAO,oBAAoB,GAAG,MAAM;AACxC;AACA,SAAS,kBAAkB,MAAM;IAC7B,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,oBAAoB;IAC7C,MAAM,cAAc,MAAM,MAAM;IAChC,OAAO,CAAC;QACJ,IAAI,SAAS;QACb,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,IAAK;YAClC,UAAU,KAAK,CAAC,EAAE;YAClB,IAAI,CAAC,CAAC,EAAE,KAAK,WAAW;gBACpB,MAAM,OAAO,KAAK,CAAC,EAAE;gBACrB,IAAI,SAAS,cAAc;oBACvB,UAAU,CAAA,GAAA,mLAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,CAAC,EAAE;gBAC3B,OACK,IAAI,SAAS,aAAa;oBAC3B,UAAU,gLAAA,CAAA,QAAK,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE;gBAClC,OACK;oBACD,UAAU,CAAC,CAAC,EAAE;gBAClB;YACJ;QACJ;QACA,OAAO;IACX;AACJ;AACA,MAAM,uBAAuB,CAAC,IAAM,OAAO,MAAM,WAAW,IAAI,gLAAA,CAAA,QAAK,CAAC,IAAI,CAAC,KAAK,gLAAA,CAAA,QAAK,CAAC,iBAAiB,CAAC,KAAK;AAC7G,SAAS,kBAAkB,CAAC;IACxB,MAAM,SAAS,kBAAkB;IACjC,MAAM,cAAc,kBAAkB;IACtC,OAAO,YAAY,OAAO,GAAG,CAAC;AAClC;AACA,MAAM,UAAU;IACZ;IACA,OAAO;IACP;IACA;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1091, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/value/types/color/hsla-to-rgba.mjs"], "sourcesContent": ["// Adapted from https://gist.github.com/mjackson/5311256\nfunction hueToRgb(p, q, t) {\n    if (t < 0)\n        t += 1;\n    if (t > 1)\n        t -= 1;\n    if (t < 1 / 6)\n        return p + (q - p) * 6 * t;\n    if (t < 1 / 2)\n        return q;\n    if (t < 2 / 3)\n        return p + (q - p) * (2 / 3 - t) * 6;\n    return p;\n}\nfunction hslaToRgba({ hue, saturation, lightness, alpha }) {\n    hue /= 360;\n    saturation /= 100;\n    lightness /= 100;\n    let red = 0;\n    let green = 0;\n    let blue = 0;\n    if (!saturation) {\n        red = green = blue = lightness;\n    }\n    else {\n        const q = lightness < 0.5\n            ? lightness * (1 + saturation)\n            : lightness + saturation - lightness * saturation;\n        const p = 2 * lightness - q;\n        red = hueToRgb(p, q, hue + 1 / 3);\n        green = hueToRgb(p, q, hue);\n        blue = hueToRgb(p, q, hue - 1 / 3);\n    }\n    return {\n        red: Math.round(red * 255),\n        green: Math.round(green * 255),\n        blue: Math.round(blue * 255),\n        alpha,\n    };\n}\n\nexport { hslaToRgba };\n"], "names": [], "mappings": "AAAA,wDAAwD;;;;AACxD,SAAS,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC;IACrB,IAAI,IAAI,GACJ,KAAK;IACT,IAAI,IAAI,GACJ,KAAK;IACT,IAAI,IAAI,IAAI,GACR,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IAC7B,IAAI,IAAI,IAAI,GACR,OAAO;IACX,IAAI,IAAI,IAAI,GACR,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI;IACvC,OAAO;AACX;AACA,SAAS,WAAW,EAAE,GAAG,EAAE,UAAU,EAAE,SAAS,EAAE,KAAK,EAAE;IACrD,OAAO;IACP,cAAc;IACd,aAAa;IACb,IAAI,MAAM;IACV,IAAI,QAAQ;IACZ,IAAI,OAAO;IACX,IAAI,CAAC,YAAY;QACb,MAAM,QAAQ,OAAO;IACzB,OACK;QACD,MAAM,IAAI,YAAY,MAChB,YAAY,CAAC,IAAI,UAAU,IAC3B,YAAY,aAAa,YAAY;QAC3C,MAAM,IAAI,IAAI,YAAY;QAC1B,MAAM,SAAS,GAAG,GAAG,MAAM,IAAI;QAC/B,QAAQ,SAAS,GAAG,GAAG;QACvB,OAAO,SAAS,GAAG,GAAG,MAAM,IAAI;IACpC;IACA,OAAO;QACH,KAAK,KAAK,KAAK,CAAC,MAAM;QACtB,OAAO,KAAK,KAAK,CAAC,QAAQ;QAC1B,MAAM,KAAK,KAAK,CAAC,OAAO;QACxB;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1133, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/utils/mix/immediate.mjs"], "sourcesContent": ["function mixImmediate(a, b) {\n    return (p) => (p > 0 ? b : a);\n}\n\nexport { mixImmediate };\n"], "names": [], "mappings": ";;;AAAA,SAAS,aAAa,CAAC,EAAE,CAAC;IACtB,OAAO,CAAC,IAAO,IAAI,IAAI,IAAI;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1146, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/utils/mix/number.mjs"], "sourcesContent": ["/*\n  Value in range from progress\n\n  Given a lower limit and an upper limit, we return the value within\n  that range as expressed by progress (usually a number from 0 to 1)\n\n  So progress = 0.5 would change\n\n  from -------- to\n\n  to\n\n  from ---- to\n\n  E.g. from = 10, to = 20, progress = 0.5 => 15\n\n  @param [number]: Lower limit of range\n  @param [number]: Upper limit of range\n  @param [number]: The progress between lower and upper limits expressed 0-1\n  @return [number]: Value as calculated from progress within range (not limited within range)\n*/\nconst mixNumber = (from, to, progress) => {\n    return from + (to - from) * progress;\n};\n\nexport { mixNumber };\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;AAoBA;;;AACA,MAAM,YAAY,CAAC,MAAM,IAAI;IACzB,OAAO,OAAO,CAAC,KAAK,IAAI,IAAI;AAChC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1179, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/utils/mix/color.mjs"], "sourcesContent": ["import { warning } from 'motion-utils';\nimport { hex } from '../../value/types/color/hex.mjs';\nimport { hsla } from '../../value/types/color/hsla.mjs';\nimport { hslaToRgba } from '../../value/types/color/hsla-to-rgba.mjs';\nimport { rgba } from '../../value/types/color/rgba.mjs';\nimport { mixImmediate } from './immediate.mjs';\nimport { mixNumber } from './number.mjs';\n\n// Linear color space blending\n// Explained https://www.youtube.com/watch?v=LKnqECcg6Gw\n// Demonstrated http://codepen.io/osublake/pen/xGVVaN\nconst mixLinearColor = (from, to, v) => {\n    const fromExpo = from * from;\n    const expo = v * (to * to - fromExpo) + fromExpo;\n    return expo < 0 ? 0 : Math.sqrt(expo);\n};\nconst colorTypes = [hex, rgba, hsla];\nconst getColorType = (v) => colorTypes.find((type) => type.test(v));\nfunction asRGBA(color) {\n    const type = getColorType(color);\n    warning(Boolean(type), `'${color}' is not an animatable color. Use the equivalent color code instead.`, \"color-not-animatable\");\n    if (!Boolean(type))\n        return false;\n    let model = type.parse(color);\n    if (type === hsla) {\n        // TODO Remove this cast - needed since Motion's stricter typing\n        model = hslaToRgba(model);\n    }\n    return model;\n}\nconst mixColor = (from, to) => {\n    const fromRGBA = asRGBA(from);\n    const toRGBA = asRGBA(to);\n    if (!fromRGBA || !toRGBA) {\n        return mixImmediate(from, to);\n    }\n    const blended = { ...fromRGBA };\n    return (v) => {\n        blended.red = mixLinearColor(fromRGBA.red, toRGBA.red, v);\n        blended.green = mixLinearColor(fromRGBA.green, toRGBA.green, v);\n        blended.blue = mixLinearColor(fromRGBA.blue, toRGBA.blue, v);\n        blended.alpha = mixNumber(fromRGBA.alpha, toRGBA.alpha, v);\n        return rgba.transform(blended);\n    };\n};\n\nexport { mixColor, mixLinearColor };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,8BAA8B;AAC9B,wDAAwD;AACxD,qDAAqD;AACrD,MAAM,iBAAiB,CAAC,MAAM,IAAI;IAC9B,MAAM,WAAW,OAAO;IACxB,MAAM,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,IAAI;IACxC,OAAO,OAAO,IAAI,IAAI,KAAK,IAAI,CAAC;AACpC;AACA,MAAM,aAAa;IAAC,8KAAA,CAAA,MAAG;IAAE,+KAAA,CAAA,OAAI;IAAE,+KAAA,CAAA,OAAI;CAAC;AACpC,MAAM,eAAe,CAAC,IAAM,WAAW,IAAI,CAAC,CAAC,OAAS,KAAK,IAAI,CAAC;AAChE,SAAS,OAAO,KAAK;IACjB,MAAM,OAAO,aAAa;IAC1B,CAAA,GAAA,wJAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,OAAO,CAAC,CAAC,EAAE,MAAM,oEAAoE,CAAC,EAAE;IACxG,IAAI,CAAC,QAAQ,OACT,OAAO;IACX,IAAI,QAAQ,KAAK,KAAK,CAAC;IACvB,IAAI,SAAS,+KAAA,CAAA,OAAI,EAAE;QACf,gEAAgE;QAChE,QAAQ,CAAA,GAAA,6LAAA,CAAA,aAAU,AAAD,EAAE;IACvB;IACA,OAAO;AACX;AACA,MAAM,WAAW,CAAC,MAAM;IACpB,MAAM,WAAW,OAAO;IACxB,MAAM,SAAS,OAAO;IACtB,IAAI,CAAC,YAAY,CAAC,QAAQ;QACtB,OAAO,CAAA,GAAA,yKAAA,CAAA,eAAY,AAAD,EAAE,MAAM;IAC9B;IACA,MAAM,UAAU;QAAE,GAAG,QAAQ;IAAC;IAC9B,OAAO,CAAC;QACJ,QAAQ,GAAG,GAAG,eAAe,SAAS,GAAG,EAAE,OAAO,GAAG,EAAE;QACvD,QAAQ,KAAK,GAAG,eAAe,SAAS,KAAK,EAAE,OAAO,KAAK,EAAE;QAC7D,QAAQ,IAAI,GAAG,eAAe,SAAS,IAAI,EAAE,OAAO,IAAI,EAAE;QAC1D,QAAQ,KAAK,GAAG,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,SAAS,KAAK,EAAE,OAAO,KAAK,EAAE;QACxD,OAAO,+KAAA,CAAA,OAAI,CAAC,SAAS,CAAC;IAC1B;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1246, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/utils/mix/visibility.mjs"], "sourcesContent": ["const invisibleValues = new Set([\"none\", \"hidden\"]);\n/**\n * Returns a function that, when provided a progress value between 0 and 1,\n * will return the \"none\" or \"hidden\" string only when the progress is that of\n * the origin or target.\n */\nfunction mixVisibility(origin, target) {\n    if (invisibleValues.has(origin)) {\n        return (p) => (p <= 0 ? origin : target);\n    }\n    else {\n        return (p) => (p >= 1 ? target : origin);\n    }\n}\n\nexport { invisibleValues, mixVisibility };\n"], "names": [], "mappings": ";;;;AAAA,MAAM,kBAAkB,IAAI,IAAI;IAAC;IAAQ;CAAS;AAClD;;;;CAIC,GACD,SAAS,cAAc,MAAM,EAAE,MAAM;IACjC,IAAI,gBAAgB,GAAG,CAAC,SAAS;QAC7B,OAAO,CAAC,IAAO,KAAK,IAAI,SAAS;IACrC,OACK;QACD,OAAO,CAAC,IAAO,KAAK,IAAI,SAAS;IACrC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1272, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/utils/mix/complex.mjs"], "sourcesContent": ["import { pipe, warning } from 'motion-utils';\nimport { isCSSVariableToken } from '../../animation/utils/is-css-variable.mjs';\nimport { color } from '../../value/types/color/index.mjs';\nimport { complex, analyseComplexValue } from '../../value/types/complex/index.mjs';\nimport { mixColor } from './color.mjs';\nimport { mixImmediate } from './immediate.mjs';\nimport { mixNumber as mixNumber$1 } from './number.mjs';\nimport { invisibleValues, mixVisibility } from './visibility.mjs';\n\nfunction mixNumber(a, b) {\n    return (p) => mixNumber$1(a, b, p);\n}\nfunction getMixer(a) {\n    if (typeof a === \"number\") {\n        return mixNumber;\n    }\n    else if (typeof a === \"string\") {\n        return isCSSVariableToken(a)\n            ? mixImmediate\n            : color.test(a)\n                ? mixColor\n                : mixComplex;\n    }\n    else if (Array.isArray(a)) {\n        return mixArray;\n    }\n    else if (typeof a === \"object\") {\n        return color.test(a) ? mixColor : mixObject;\n    }\n    return mixImmediate;\n}\nfunction mixArray(a, b) {\n    const output = [...a];\n    const numValues = output.length;\n    const blendValue = a.map((v, i) => getMixer(v)(v, b[i]));\n    return (p) => {\n        for (let i = 0; i < numValues; i++) {\n            output[i] = blendValue[i](p);\n        }\n        return output;\n    };\n}\nfunction mixObject(a, b) {\n    const output = { ...a, ...b };\n    const blendValue = {};\n    for (const key in output) {\n        if (a[key] !== undefined && b[key] !== undefined) {\n            blendValue[key] = getMixer(a[key])(a[key], b[key]);\n        }\n    }\n    return (v) => {\n        for (const key in blendValue) {\n            output[key] = blendValue[key](v);\n        }\n        return output;\n    };\n}\nfunction matchOrder(origin, target) {\n    const orderedOrigin = [];\n    const pointers = { color: 0, var: 0, number: 0 };\n    for (let i = 0; i < target.values.length; i++) {\n        const type = target.types[i];\n        const originIndex = origin.indexes[type][pointers[type]];\n        const originValue = origin.values[originIndex] ?? 0;\n        orderedOrigin[i] = originValue;\n        pointers[type]++;\n    }\n    return orderedOrigin;\n}\nconst mixComplex = (origin, target) => {\n    const template = complex.createTransformer(target);\n    const originStats = analyseComplexValue(origin);\n    const targetStats = analyseComplexValue(target);\n    const canInterpolate = originStats.indexes.var.length === targetStats.indexes.var.length &&\n        originStats.indexes.color.length === targetStats.indexes.color.length &&\n        originStats.indexes.number.length >= targetStats.indexes.number.length;\n    if (canInterpolate) {\n        if ((invisibleValues.has(origin) &&\n            !targetStats.values.length) ||\n            (invisibleValues.has(target) &&\n                !originStats.values.length)) {\n            return mixVisibility(origin, target);\n        }\n        return pipe(mixArray(matchOrder(originStats, targetStats), targetStats.values), template);\n    }\n    else {\n        warning(true, `Complex values '${origin}' and '${target}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`, \"complex-values-different\");\n        return mixImmediate(origin, target);\n    }\n};\n\nexport { getMixer, mixArray, mixComplex, mixObject };\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAEA,SAAS,UAAU,CAAC,EAAE,CAAC;IACnB,OAAO,CAAC,IAAM,CAAA,GAAA,sKAAA,CAAA,YAAW,AAAD,EAAE,GAAG,GAAG;AACpC;AACA,SAAS,SAAS,CAAC;IACf,IAAI,OAAO,MAAM,UAAU;QACvB,OAAO;IACX,OACK,IAAI,OAAO,MAAM,UAAU;QAC5B,OAAO,CAAA,GAAA,2LAAA,CAAA,qBAAkB,AAAD,EAAE,KACpB,yKAAA,CAAA,eAAY,GACZ,gLAAA,CAAA,QAAK,CAAC,IAAI,CAAC,KACP,qKAAA,CAAA,WAAQ,GACR;IACd,OACK,IAAI,MAAM,OAAO,CAAC,IAAI;QACvB,OAAO;IACX,OACK,IAAI,OAAO,MAAM,UAAU;QAC5B,OAAO,gLAAA,CAAA,QAAK,CAAC,IAAI,CAAC,KAAK,qKAAA,CAAA,WAAQ,GAAG;IACtC;IACA,OAAO,yKAAA,CAAA,eAAY;AACvB;AACA,SAAS,SAAS,CAAC,EAAE,CAAC;IAClB,MAAM,SAAS;WAAI;KAAE;IACrB,MAAM,YAAY,OAAO,MAAM;IAC/B,MAAM,aAAa,EAAE,GAAG,CAAC,CAAC,GAAG,IAAM,SAAS,GAAG,GAAG,CAAC,CAAC,EAAE;IACtD,OAAO,CAAC;QACJ,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,IAAK;YAChC,MAAM,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,CAAC;QAC9B;QACA,OAAO;IACX;AACJ;AACA,SAAS,UAAU,CAAC,EAAE,CAAC;IACnB,MAAM,SAAS;QAAE,GAAG,CAAC;QAAE,GAAG,CAAC;IAAC;IAC5B,MAAM,aAAa,CAAC;IACpB,IAAK,MAAM,OAAO,OAAQ;QACtB,IAAI,CAAC,CAAC,IAAI,KAAK,aAAa,CAAC,CAAC,IAAI,KAAK,WAAW;YAC9C,UAAU,CAAC,IAAI,GAAG,SAAS,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI;QACrD;IACJ;IACA,OAAO,CAAC;QACJ,IAAK,MAAM,OAAO,WAAY;YAC1B,MAAM,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;QAClC;QACA,OAAO;IACX;AACJ;AACA,SAAS,WAAW,MAAM,EAAE,MAAM;IAC9B,MAAM,gBAAgB,EAAE;IACxB,MAAM,WAAW;QAAE,OAAO;QAAG,KAAK;QAAG,QAAQ;IAAE;IAC/C,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,CAAC,MAAM,EAAE,IAAK;QAC3C,MAAM,OAAO,OAAO,KAAK,CAAC,EAAE;QAC5B,MAAM,cAAc,OAAO,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC;QACxD,MAAM,cAAc,OAAO,MAAM,CAAC,YAAY,IAAI;QAClD,aAAa,CAAC,EAAE,GAAG;QACnB,QAAQ,CAAC,KAAK;IAClB;IACA,OAAO;AACX;AACA,MAAM,aAAa,CAAC,QAAQ;IACxB,MAAM,WAAW,kLAAA,CAAA,UAAO,CAAC,iBAAiB,CAAC;IAC3C,MAAM,cAAc,CAAA,GAAA,kLAAA,CAAA,sBAAmB,AAAD,EAAE;IACxC,MAAM,cAAc,CAAA,GAAA,kLAAA,CAAA,sBAAmB,AAAD,EAAE;IACxC,MAAM,iBAAiB,YAAY,OAAO,CAAC,GAAG,CAAC,MAAM,KAAK,YAAY,OAAO,CAAC,GAAG,CAAC,MAAM,IACpF,YAAY,OAAO,CAAC,KAAK,CAAC,MAAM,KAAK,YAAY,OAAO,CAAC,KAAK,CAAC,MAAM,IACrE,YAAY,OAAO,CAAC,MAAM,CAAC,MAAM,IAAI,YAAY,OAAO,CAAC,MAAM,CAAC,MAAM;IAC1E,IAAI,gBAAgB;QAChB,IAAI,AAAC,0KAAA,CAAA,kBAAe,CAAC,GAAG,CAAC,WACrB,CAAC,YAAY,MAAM,CAAC,MAAM,IACzB,0KAAA,CAAA,kBAAe,CAAC,GAAG,CAAC,WACjB,CAAC,YAAY,MAAM,CAAC,MAAM,EAAG;YACjC,OAAO,CAAA,GAAA,0KAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;QACjC;QACA,OAAO,CAAA,GAAA,sJAAA,CAAA,OAAI,AAAD,EAAE,SAAS,WAAW,aAAa,cAAc,YAAY,MAAM,GAAG;IACpF,OACK;QACD,CAAA,GAAA,wJAAA,CAAA,UAAO,AAAD,EAAE,MAAM,CAAC,gBAAgB,EAAE,OAAO,OAAO,EAAE,OAAO,wKAAwK,CAAC,EAAE;QACnO,OAAO,CAAA,GAAA,yKAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;IAChC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1379, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/utils/mix/index.mjs"], "sourcesContent": ["import { getMixer } from './complex.mjs';\nimport { mixNumber } from './number.mjs';\n\nfunction mix(from, to, p) {\n    if (typeof from === \"number\" &&\n        typeof to === \"number\" &&\n        typeof p === \"number\") {\n        return mixNumber(from, to, p);\n    }\n    const mixer = getMixer(from);\n    return mixer(from, to);\n}\n\nexport { mix };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;IACpB,IAAI,OAAO,SAAS,YAChB,OAAO,OAAO,YACd,OAAO,MAAM,UAAU;QACvB,OAAO,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,MAAM,IAAI;IAC/B;IACA,MAAM,QAAQ,CAAA,GAAA,uKAAA,CAAA,WAAQ,AAAD,EAAE;IACvB,OAAO,MAAM,MAAM;AACvB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1400, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/animation/drivers/frame.mjs"], "sourcesContent": ["import { time } from '../../frameloop/sync-time.mjs';\nimport { frame, cancelFrame, frameData } from '../../frameloop/frame.mjs';\n\nconst frameloopDriver = (update) => {\n    const passTimestamp = ({ timestamp }) => update(timestamp);\n    return {\n        start: (keepAlive = true) => frame.update(passTimestamp, keepAlive),\n        stop: () => cancelFrame(passTimestamp),\n        /**\n         * If we're processing this frame we can use the\n         * framelocked timestamp to keep things in sync.\n         */\n        now: () => (frameData.isProcessing ? frameData.timestamp : time.now()),\n    };\n};\n\nexport { frameloopDriver };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,kBAAkB,CAAC;IACrB,MAAM,gBAAgB,CAAC,EAAE,SAAS,EAAE,GAAK,OAAO;IAChD,OAAO;QACH,OAAO,CAAC,YAAY,IAAI,GAAK,kKAAA,CAAA,QAAK,CAAC,MAAM,CAAC,eAAe;QACzD,MAAM,IAAM,CAAA,GAAA,kKAAA,CAAA,cAAW,AAAD,EAAE;QACxB;;;SAGC,GACD,KAAK,IAAO,kKAAA,CAAA,YAAS,CAAC,YAAY,GAAG,kKAAA,CAAA,YAAS,CAAC,SAAS,GAAG,yKAAA,CAAA,OAAI,CAAC,GAAG;IACvE;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1425, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/animation/waapi/utils/linear.mjs"], "sourcesContent": ["const generateLinearEasing = (easing, duration, // as milliseconds\nresolution = 10 // as milliseconds\n) => {\n    let points = \"\";\n    const numPoints = Math.max(Math.round(duration / resolution), 2);\n    for (let i = 0; i < numPoints; i++) {\n        points += Math.round(easing(i / (numPoints - 1)) * 10000) / 10000 + \", \";\n    }\n    return `linear(${points.substring(0, points.length - 2)})`;\n};\n\nexport { generateLinearEasing };\n"], "names": [], "mappings": ";;;AAAA,MAAM,uBAAuB,CAAC,QAAQ,UACtC,aAAa,GAAG,kBAAkB;AAAnB;IAEX,IAAI,SAAS;IACb,MAAM,YAAY,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,WAAW,aAAa;IAC9D,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,IAAK;QAChC,UAAU,KAAK,KAAK,CAAC,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,SAAS,QAAQ;IACxE;IACA,OAAO,CAAC,OAAO,EAAE,OAAO,SAAS,CAAC,GAAG,OAAO,MAAM,GAAG,GAAG,CAAC,CAAC;AAC9D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1444, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/animation/generators/utils/calc-duration.mjs"], "sourcesContent": ["/**\n * Implement a practical max duration for keyframe generation\n * to prevent infinite loops\n */\nconst maxGeneratorDuration = 20000;\nfunction calcGeneratorDuration(generator) {\n    let duration = 0;\n    const timeStep = 50;\n    let state = generator.next(duration);\n    while (!state.done && duration < maxGeneratorDuration) {\n        duration += timeStep;\n        state = generator.next(duration);\n    }\n    return duration >= maxGeneratorDuration ? Infinity : duration;\n}\n\nexport { calcGeneratorDuration, maxGeneratorDuration };\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AACD,MAAM,uBAAuB;AAC7B,SAAS,sBAAsB,SAAS;IACpC,IAAI,WAAW;IACf,MAAM,WAAW;IACjB,IAAI,QAAQ,UAAU,IAAI,CAAC;IAC3B,MAAO,CAAC,MAAM,IAAI,IAAI,WAAW,qBAAsB;QACnD,YAAY;QACZ,QAAQ,UAAU,IAAI,CAAC;IAC3B;IACA,OAAO,YAAY,uBAAuB,WAAW;AACzD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1469, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/animation/generators/utils/create-generator-easing.mjs"], "sourcesContent": ["import { millisecondsToSeconds } from 'motion-utils';\nimport { calcGeneratorDuration, maxGeneratorDuration } from './calc-duration.mjs';\n\n/**\n * Create a progress => progress easing function from a generator.\n */\nfunction createGeneratorEasing(options, scale = 100, createGenerator) {\n    const generator = createGenerator({ ...options, keyframes: [0, scale] });\n    const duration = Math.min(calcGeneratorDuration(generator), maxGeneratorDuration);\n    return {\n        type: \"keyframes\",\n        ease: (progress) => {\n            return generator.next(duration * progress).value / scale;\n        },\n        duration: millisecondsToSeconds(duration),\n    };\n}\n\nexport { createGeneratorEasing };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA;;CAEC,GACD,SAAS,sBAAsB,OAAO,EAAE,QAAQ,GAAG,EAAE,eAAe;IAChE,MAAM,YAAY,gBAAgB;QAAE,GAAG,OAAO;QAAE,WAAW;YAAC;YAAG;SAAM;IAAC;IACtE,MAAM,WAAW,KAAK,GAAG,CAAC,CAAA,GAAA,oMAAA,CAAA,wBAAqB,AAAD,EAAE,YAAY,oMAAA,CAAA,uBAAoB;IAChF,OAAO;QACH,MAAM;QACN,MAAM,CAAC;YACH,OAAO,UAAU,IAAI,CAAC,WAAW,UAAU,KAAK,GAAG;QACvD;QACA,UAAU,CAAA,GAAA,oKAAA,CAAA,wBAAqB,AAAD,EAAE;IACpC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1502, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/animation/generators/utils/velocity.mjs"], "sourcesContent": ["import { velocityPerSecond } from 'motion-utils';\n\nconst velocitySampleDuration = 5; // ms\nfunction calcGeneratorVelocity(resolveValue, t, current) {\n    const prevT = Math.max(t - velocitySampleDuration, 0);\n    return velocityPerSecond(current - resolveValue(prevT), t - prevT);\n}\n\nexport { calcGeneratorVelocity };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,yBAAyB,GAAG,KAAK;AACvC,SAAS,sBAAsB,YAAY,EAAE,CAAC,EAAE,OAAO;IACnD,MAAM,QAAQ,KAAK,GAAG,CAAC,IAAI,wBAAwB;IACnD,OAAO,CAAA,GAAA,2KAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU,aAAa,QAAQ,IAAI;AAChE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1519, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/animation/generators/spring/defaults.mjs"], "sourcesContent": ["const springDefaults = {\n    // Default spring physics\n    stiffness: 100,\n    damping: 10,\n    mass: 1.0,\n    velocity: 0.0,\n    // Default duration/bounce-based options\n    duration: 800, // in ms\n    bounce: 0.3,\n    visualDuration: 0.3, // in seconds\n    // Rest thresholds\n    restSpeed: {\n        granular: 0.01,\n        default: 2,\n    },\n    restDelta: {\n        granular: 0.005,\n        default: 0.5,\n    },\n    // Limits\n    minDuration: 0.01, // in seconds\n    maxDuration: 10.0, // in seconds\n    minDamping: 0.05,\n    maxDamping: 1,\n};\n\nexport { springDefaults };\n"], "names": [], "mappings": ";;;AAAA,MAAM,iBAAiB;IACnB,yBAAyB;IACzB,WAAW;IACX,SAAS;IACT,MAAM;IACN,UAAU;IACV,wCAAwC;IACxC,UAAU;IACV,QAAQ;IACR,gBAAgB;IAChB,kBAAkB;IAClB,WAAW;QACP,UAAU;QACV,SAAS;IACb;IACA,WAAW;QACP,UAAU;QACV,SAAS;IACb;IACA,SAAS;IACT,aAAa;IACb,aAAa;IACb,YAAY;IACZ,YAAY;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1554, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/animation/generators/spring/find.mjs"], "sourcesContent": ["import { warning, secondsToMilliseconds, clamp, millisecondsToSeconds } from 'motion-utils';\nimport { springDefaults } from './defaults.mjs';\n\nconst safeMin = 0.001;\nfunction findSpring({ duration = springDefaults.duration, bounce = springDefaults.bounce, velocity = springDefaults.velocity, mass = springDefaults.mass, }) {\n    let envelope;\n    let derivative;\n    warning(duration <= secondsToMilliseconds(springDefaults.maxDuration), \"Spring duration must be 10 seconds or less\", \"spring-duration-limit\");\n    let dampingRatio = 1 - bounce;\n    /**\n     * Restrict dampingRatio and duration to within acceptable ranges.\n     */\n    dampingRatio = clamp(springDefaults.minDamping, springDefaults.maxDamping, dampingRatio);\n    duration = clamp(springDefaults.minDuration, springDefaults.maxDuration, millisecondsToSeconds(duration));\n    if (dampingRatio < 1) {\n        /**\n         * Underdamped spring\n         */\n        envelope = (undampedFreq) => {\n            const exponentialDecay = undampedFreq * dampingRatio;\n            const delta = exponentialDecay * duration;\n            const a = exponentialDecay - velocity;\n            const b = calcAngularFreq(undampedFreq, dampingRatio);\n            const c = Math.exp(-delta);\n            return safeMin - (a / b) * c;\n        };\n        derivative = (undampedFreq) => {\n            const exponentialDecay = undampedFreq * dampingRatio;\n            const delta = exponentialDecay * duration;\n            const d = delta * velocity + velocity;\n            const e = Math.pow(dampingRatio, 2) * Math.pow(undampedFreq, 2) * duration;\n            const f = Math.exp(-delta);\n            const g = calcAngularFreq(Math.pow(undampedFreq, 2), dampingRatio);\n            const factor = -envelope(undampedFreq) + safeMin > 0 ? -1 : 1;\n            return (factor * ((d - e) * f)) / g;\n        };\n    }\n    else {\n        /**\n         * Critically-damped spring\n         */\n        envelope = (undampedFreq) => {\n            const a = Math.exp(-undampedFreq * duration);\n            const b = (undampedFreq - velocity) * duration + 1;\n            return -safeMin + a * b;\n        };\n        derivative = (undampedFreq) => {\n            const a = Math.exp(-undampedFreq * duration);\n            const b = (velocity - undampedFreq) * (duration * duration);\n            return a * b;\n        };\n    }\n    const initialGuess = 5 / duration;\n    const undampedFreq = approximateRoot(envelope, derivative, initialGuess);\n    duration = secondsToMilliseconds(duration);\n    if (isNaN(undampedFreq)) {\n        return {\n            stiffness: springDefaults.stiffness,\n            damping: springDefaults.damping,\n            duration,\n        };\n    }\n    else {\n        const stiffness = Math.pow(undampedFreq, 2) * mass;\n        return {\n            stiffness,\n            damping: dampingRatio * 2 * Math.sqrt(mass * stiffness),\n            duration,\n        };\n    }\n}\nconst rootIterations = 12;\nfunction approximateRoot(envelope, derivative, initialGuess) {\n    let result = initialGuess;\n    for (let i = 1; i < rootIterations; i++) {\n        result = result - envelope(result) / derivative(result);\n    }\n    return result;\n}\nfunction calcAngularFreq(undampedFreq, dampingRatio) {\n    return undampedFreq * Math.sqrt(1 - dampingRatio * dampingRatio);\n}\n\nexport { calcAngularFreq, findSpring };\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AACA;;;AAEA,MAAM,UAAU;AAChB,SAAS,WAAW,EAAE,WAAW,6LAAA,CAAA,iBAAc,CAAC,QAAQ,EAAE,SAAS,6LAAA,CAAA,iBAAc,CAAC,MAAM,EAAE,WAAW,6LAAA,CAAA,iBAAc,CAAC,QAAQ,EAAE,OAAO,6LAAA,CAAA,iBAAc,CAAC,IAAI,EAAG;IACvJ,IAAI;IACJ,IAAI;IACJ,CAAA,GAAA,wJAAA,CAAA,UAAO,AAAD,EAAE,YAAY,CAAA,GAAA,oKAAA,CAAA,wBAAqB,AAAD,EAAE,6LAAA,CAAA,iBAAc,CAAC,WAAW,GAAG,8CAA8C;IACrH,IAAI,eAAe,IAAI;IACvB;;KAEC,GACD,eAAe,CAAA,GAAA,uJAAA,CAAA,QAAK,AAAD,EAAE,6LAAA,CAAA,iBAAc,CAAC,UAAU,EAAE,6LAAA,CAAA,iBAAc,CAAC,UAAU,EAAE;IAC3E,WAAW,CAAA,GAAA,uJAAA,CAAA,QAAK,AAAD,EAAE,6LAAA,CAAA,iBAAc,CAAC,WAAW,EAAE,6LAAA,CAAA,iBAAc,CAAC,WAAW,EAAE,CAAA,GAAA,oKAAA,CAAA,wBAAqB,AAAD,EAAE;IAC/F,IAAI,eAAe,GAAG;QAClB;;SAEC,GACD,WAAW,CAAC;YACR,MAAM,mBAAmB,eAAe;YACxC,MAAM,QAAQ,mBAAmB;YACjC,MAAM,IAAI,mBAAmB;YAC7B,MAAM,IAAI,gBAAgB,cAAc;YACxC,MAAM,IAAI,KAAK,GAAG,CAAC,CAAC;YACpB,OAAO,UAAU,AAAC,IAAI,IAAK;QAC/B;QACA,aAAa,CAAC;YACV,MAAM,mBAAmB,eAAe;YACxC,MAAM,QAAQ,mBAAmB;YACjC,MAAM,IAAI,QAAQ,WAAW;YAC7B,MAAM,IAAI,KAAK,GAAG,CAAC,cAAc,KAAK,KAAK,GAAG,CAAC,cAAc,KAAK;YAClE,MAAM,IAAI,KAAK,GAAG,CAAC,CAAC;YACpB,MAAM,IAAI,gBAAgB,KAAK,GAAG,CAAC,cAAc,IAAI;YACrD,MAAM,SAAS,CAAC,SAAS,gBAAgB,UAAU,IAAI,CAAC,IAAI;YAC5D,OAAO,AAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAK;QACtC;IACJ,OACK;QACD;;SAEC,GACD,WAAW,CAAC;YACR,MAAM,IAAI,KAAK,GAAG,CAAC,CAAC,eAAe;YACnC,MAAM,IAAI,CAAC,eAAe,QAAQ,IAAI,WAAW;YACjD,OAAO,CAAC,UAAU,IAAI;QAC1B;QACA,aAAa,CAAC;YACV,MAAM,IAAI,KAAK,GAAG,CAAC,CAAC,eAAe;YACnC,MAAM,IAAI,CAAC,WAAW,YAAY,IAAI,CAAC,WAAW,QAAQ;YAC1D,OAAO,IAAI;QACf;IACJ;IACA,MAAM,eAAe,IAAI;IACzB,MAAM,eAAe,gBAAgB,UAAU,YAAY;IAC3D,WAAW,CAAA,GAAA,oKAAA,CAAA,wBAAqB,AAAD,EAAE;IACjC,IAAI,MAAM,eAAe;QACrB,OAAO;YACH,WAAW,6LAAA,CAAA,iBAAc,CAAC,SAAS;YACnC,SAAS,6LAAA,CAAA,iBAAc,CAAC,OAAO;YAC/B;QACJ;IACJ,OACK;QACD,MAAM,YAAY,KAAK,GAAG,CAAC,cAAc,KAAK;QAC9C,OAAO;YACH;YACA,SAAS,eAAe,IAAI,KAAK,IAAI,CAAC,OAAO;YAC7C;QACJ;IACJ;AACJ;AACA,MAAM,iBAAiB;AACvB,SAAS,gBAAgB,QAAQ,EAAE,UAAU,EAAE,YAAY;IACvD,IAAI,SAAS;IACb,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,IAAK;QACrC,SAAS,SAAS,SAAS,UAAU,WAAW;IACpD;IACA,OAAO;AACX;AACA,SAAS,gBAAgB,YAAY,EAAE,YAAY;IAC/C,OAAO,eAAe,KAAK,IAAI,CAAC,IAAI,eAAe;AACvD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1645, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/animation/generators/spring/index.mjs"], "sourcesContent": ["import { millisecondsToSeconds, secondsToMilliseconds, clamp } from 'motion-utils';\nimport { generateLinearEasing } from '../../waapi/utils/linear.mjs';\nimport { calcGeneratorDuration, maxGeneratorDuration } from '../utils/calc-duration.mjs';\nimport { createGeneratorEasing } from '../utils/create-generator-easing.mjs';\nimport { calcGeneratorVelocity } from '../utils/velocity.mjs';\nimport { springDefaults } from './defaults.mjs';\nimport { findSpring, calcAngularFreq } from './find.mjs';\n\nconst durationKeys = [\"duration\", \"bounce\"];\nconst physicsKeys = [\"stiffness\", \"damping\", \"mass\"];\nfunction isSpringType(options, keys) {\n    return keys.some((key) => options[key] !== undefined);\n}\nfunction getSpringOptions(options) {\n    let springOptions = {\n        velocity: springDefaults.velocity,\n        stiffness: springDefaults.stiffness,\n        damping: springDefaults.damping,\n        mass: springDefaults.mass,\n        isResolvedFromDuration: false,\n        ...options,\n    };\n    // stiffness/damping/mass overrides duration/bounce\n    if (!isSpringType(options, physicsKeys) &&\n        isSpringType(options, durationKeys)) {\n        if (options.visualDuration) {\n            const visualDuration = options.visualDuration;\n            const root = (2 * Math.PI) / (visualDuration * 1.2);\n            const stiffness = root * root;\n            const damping = 2 *\n                clamp(0.05, 1, 1 - (options.bounce || 0)) *\n                Math.sqrt(stiffness);\n            springOptions = {\n                ...springOptions,\n                mass: springDefaults.mass,\n                stiffness,\n                damping,\n            };\n        }\n        else {\n            const derived = findSpring(options);\n            springOptions = {\n                ...springOptions,\n                ...derived,\n                mass: springDefaults.mass,\n            };\n            springOptions.isResolvedFromDuration = true;\n        }\n    }\n    return springOptions;\n}\nfunction spring(optionsOrVisualDuration = springDefaults.visualDuration, bounce = springDefaults.bounce) {\n    const options = typeof optionsOrVisualDuration !== \"object\"\n        ? {\n            visualDuration: optionsOrVisualDuration,\n            keyframes: [0, 1],\n            bounce,\n        }\n        : optionsOrVisualDuration;\n    let { restSpeed, restDelta } = options;\n    const origin = options.keyframes[0];\n    const target = options.keyframes[options.keyframes.length - 1];\n    /**\n     * This is the Iterator-spec return value. We ensure it's mutable rather than using a generator\n     * to reduce GC during animation.\n     */\n    const state = { done: false, value: origin };\n    const { stiffness, damping, mass, duration, velocity, isResolvedFromDuration, } = getSpringOptions({\n        ...options,\n        velocity: -millisecondsToSeconds(options.velocity || 0),\n    });\n    const initialVelocity = velocity || 0.0;\n    const dampingRatio = damping / (2 * Math.sqrt(stiffness * mass));\n    const initialDelta = target - origin;\n    const undampedAngularFreq = millisecondsToSeconds(Math.sqrt(stiffness / mass));\n    /**\n     * If we're working on a granular scale, use smaller defaults for determining\n     * when the spring is finished.\n     *\n     * These defaults have been selected emprically based on what strikes a good\n     * ratio between feeling good and finishing as soon as changes are imperceptible.\n     */\n    const isGranularScale = Math.abs(initialDelta) < 5;\n    restSpeed || (restSpeed = isGranularScale\n        ? springDefaults.restSpeed.granular\n        : springDefaults.restSpeed.default);\n    restDelta || (restDelta = isGranularScale\n        ? springDefaults.restDelta.granular\n        : springDefaults.restDelta.default);\n    let resolveSpring;\n    if (dampingRatio < 1) {\n        const angularFreq = calcAngularFreq(undampedAngularFreq, dampingRatio);\n        // Underdamped spring\n        resolveSpring = (t) => {\n            const envelope = Math.exp(-dampingRatio * undampedAngularFreq * t);\n            return (target -\n                envelope *\n                    (((initialVelocity +\n                        dampingRatio * undampedAngularFreq * initialDelta) /\n                        angularFreq) *\n                        Math.sin(angularFreq * t) +\n                        initialDelta * Math.cos(angularFreq * t)));\n        };\n    }\n    else if (dampingRatio === 1) {\n        // Critically damped spring\n        resolveSpring = (t) => target -\n            Math.exp(-undampedAngularFreq * t) *\n                (initialDelta +\n                    (initialVelocity + undampedAngularFreq * initialDelta) * t);\n    }\n    else {\n        // Overdamped spring\n        const dampedAngularFreq = undampedAngularFreq * Math.sqrt(dampingRatio * dampingRatio - 1);\n        resolveSpring = (t) => {\n            const envelope = Math.exp(-dampingRatio * undampedAngularFreq * t);\n            // When performing sinh or cosh values can hit Infinity so we cap them here\n            const freqForT = Math.min(dampedAngularFreq * t, 300);\n            return (target -\n                (envelope *\n                    ((initialVelocity +\n                        dampingRatio * undampedAngularFreq * initialDelta) *\n                        Math.sinh(freqForT) +\n                        dampedAngularFreq *\n                            initialDelta *\n                            Math.cosh(freqForT))) /\n                    dampedAngularFreq);\n        };\n    }\n    const generator = {\n        calculatedDuration: isResolvedFromDuration ? duration || null : null,\n        next: (t) => {\n            const current = resolveSpring(t);\n            if (!isResolvedFromDuration) {\n                let currentVelocity = t === 0 ? initialVelocity : 0.0;\n                /**\n                 * We only need to calculate velocity for under-damped springs\n                 * as over- and critically-damped springs can't overshoot, so\n                 * checking only for displacement is enough.\n                 */\n                if (dampingRatio < 1) {\n                    currentVelocity =\n                        t === 0\n                            ? secondsToMilliseconds(initialVelocity)\n                            : calcGeneratorVelocity(resolveSpring, t, current);\n                }\n                const isBelowVelocityThreshold = Math.abs(currentVelocity) <= restSpeed;\n                const isBelowDisplacementThreshold = Math.abs(target - current) <= restDelta;\n                state.done =\n                    isBelowVelocityThreshold && isBelowDisplacementThreshold;\n            }\n            else {\n                state.done = t >= duration;\n            }\n            state.value = state.done ? target : current;\n            return state;\n        },\n        toString: () => {\n            const calculatedDuration = Math.min(calcGeneratorDuration(generator), maxGeneratorDuration);\n            const easing = generateLinearEasing((progress) => generator.next(calculatedDuration * progress).value, calculatedDuration, 30);\n            return calculatedDuration + \"ms \" + easing;\n        },\n        toTransition: () => { },\n    };\n    return generator;\n}\nspring.applyToOptions = (options) => {\n    const generatorOptions = createGeneratorEasing(options, 100, spring);\n    options.ease = generatorOptions.ease;\n    options.duration = secondsToMilliseconds(generatorOptions.duration);\n    options.type = \"keyframes\";\n    return options;\n};\n\nexport { spring };\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,MAAM,eAAe;IAAC;IAAY;CAAS;AAC3C,MAAM,cAAc;IAAC;IAAa;IAAW;CAAO;AACpD,SAAS,aAAa,OAAO,EAAE,IAAI;IAC/B,OAAO,KAAK,IAAI,CAAC,CAAC,MAAQ,OAAO,CAAC,IAAI,KAAK;AAC/C;AACA,SAAS,iBAAiB,OAAO;IAC7B,IAAI,gBAAgB;QAChB,UAAU,6LAAA,CAAA,iBAAc,CAAC,QAAQ;QACjC,WAAW,6LAAA,CAAA,iBAAc,CAAC,SAAS;QACnC,SAAS,6LAAA,CAAA,iBAAc,CAAC,OAAO;QAC/B,MAAM,6LAAA,CAAA,iBAAc,CAAC,IAAI;QACzB,wBAAwB;QACxB,GAAG,OAAO;IACd;IACA,mDAAmD;IACnD,IAAI,CAAC,aAAa,SAAS,gBACvB,aAAa,SAAS,eAAe;QACrC,IAAI,QAAQ,cAAc,EAAE;YACxB,MAAM,iBAAiB,QAAQ,cAAc;YAC7C,MAAM,OAAO,AAAC,IAAI,KAAK,EAAE,GAAI,CAAC,iBAAiB,GAAG;YAClD,MAAM,YAAY,OAAO;YACzB,MAAM,UAAU,IACZ,CAAA,GAAA,uJAAA,CAAA,QAAK,AAAD,EAAE,MAAM,GAAG,IAAI,CAAC,QAAQ,MAAM,IAAI,CAAC,KACvC,KAAK,IAAI,CAAC;YACd,gBAAgB;gBACZ,GAAG,aAAa;gBAChB,MAAM,6LAAA,CAAA,iBAAc,CAAC,IAAI;gBACzB;gBACA;YACJ;QACJ,OACK;YACD,MAAM,UAAU,CAAA,GAAA,yLAAA,CAAA,aAAU,AAAD,EAAE;YAC3B,gBAAgB;gBACZ,GAAG,aAAa;gBAChB,GAAG,OAAO;gBACV,MAAM,6LAAA,CAAA,iBAAc,CAAC,IAAI;YAC7B;YACA,cAAc,sBAAsB,GAAG;QAC3C;IACJ;IACA,OAAO;AACX;AACA,SAAS,OAAO,0BAA0B,6LAAA,CAAA,iBAAc,CAAC,cAAc,EAAE,SAAS,6LAAA,CAAA,iBAAc,CAAC,MAAM;IACnG,MAAM,UAAU,OAAO,4BAA4B,WAC7C;QACE,gBAAgB;QAChB,WAAW;YAAC;YAAG;SAAE;QACjB;IACJ,IACE;IACN,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG;IAC/B,MAAM,SAAS,QAAQ,SAAS,CAAC,EAAE;IACnC,MAAM,SAAS,QAAQ,SAAS,CAAC,QAAQ,SAAS,CAAC,MAAM,GAAG,EAAE;IAC9D;;;KAGC,GACD,MAAM,QAAQ;QAAE,MAAM;QAAO,OAAO;IAAO;IAC3C,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,sBAAsB,EAAG,GAAG,iBAAiB;QAC/F,GAAG,OAAO;QACV,UAAU,CAAC,CAAA,GAAA,oKAAA,CAAA,wBAAqB,AAAD,EAAE,QAAQ,QAAQ,IAAI;IACzD;IACA,MAAM,kBAAkB,YAAY;IACpC,MAAM,eAAe,UAAU,CAAC,IAAI,KAAK,IAAI,CAAC,YAAY,KAAK;IAC/D,MAAM,eAAe,SAAS;IAC9B,MAAM,sBAAsB,CAAA,GAAA,oKAAA,CAAA,wBAAqB,AAAD,EAAE,KAAK,IAAI,CAAC,YAAY;IACxE;;;;;;KAMC,GACD,MAAM,kBAAkB,KAAK,GAAG,CAAC,gBAAgB;IACjD,aAAa,CAAC,YAAY,kBACpB,6LAAA,CAAA,iBAAc,CAAC,SAAS,CAAC,QAAQ,GACjC,6LAAA,CAAA,iBAAc,CAAC,SAAS,CAAC,OAAO;IACtC,aAAa,CAAC,YAAY,kBACpB,6LAAA,CAAA,iBAAc,CAAC,SAAS,CAAC,QAAQ,GACjC,6LAAA,CAAA,iBAAc,CAAC,SAAS,CAAC,OAAO;IACtC,IAAI;IACJ,IAAI,eAAe,GAAG;QAClB,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,kBAAe,AAAD,EAAE,qBAAqB;QACzD,qBAAqB;QACrB,gBAAgB,CAAC;YACb,MAAM,WAAW,KAAK,GAAG,CAAC,CAAC,eAAe,sBAAsB;YAChE,OAAQ,SACJ,WACI,CAAC,AAAC,CAAC,kBACC,eAAe,sBAAsB,YAAY,IACjD,cACA,KAAK,GAAG,CAAC,cAAc,KACvB,eAAe,KAAK,GAAG,CAAC,cAAc,EAAE;QACxD;IACJ,OACK,IAAI,iBAAiB,GAAG;QACzB,2BAA2B;QAC3B,gBAAgB,CAAC,IAAM,SACnB,KAAK,GAAG,CAAC,CAAC,sBAAsB,KAC5B,CAAC,eACG,CAAC,kBAAkB,sBAAsB,YAAY,IAAI,CAAC;IAC1E,OACK;QACD,oBAAoB;QACpB,MAAM,oBAAoB,sBAAsB,KAAK,IAAI,CAAC,eAAe,eAAe;QACxF,gBAAgB,CAAC;YACb,MAAM,WAAW,KAAK,GAAG,CAAC,CAAC,eAAe,sBAAsB;YAChE,2EAA2E;YAC3E,MAAM,WAAW,KAAK,GAAG,CAAC,oBAAoB,GAAG;YACjD,OAAQ,SACJ,AAAC,WACG,CAAC,CAAC,kBACE,eAAe,sBAAsB,YAAY,IACjD,KAAK,IAAI,CAAC,YACV,oBACI,eACA,KAAK,IAAI,CAAC,SAAS,IAC3B;QACZ;IACJ;IACA,MAAM,YAAY;QACd,oBAAoB,yBAAyB,YAAY,OAAO;QAChE,MAAM,CAAC;YACH,MAAM,UAAU,cAAc;YAC9B,IAAI,CAAC,wBAAwB;gBACzB,IAAI,kBAAkB,MAAM,IAAI,kBAAkB;gBAClD;;;;iBAIC,GACD,IAAI,eAAe,GAAG;oBAClB,kBACI,MAAM,IACA,CAAA,GAAA,oKAAA,CAAA,wBAAqB,AAAD,EAAE,mBACtB,CAAA,GAAA,4LAAA,CAAA,wBAAqB,AAAD,EAAE,eAAe,GAAG;gBACtD;gBACA,MAAM,2BAA2B,KAAK,GAAG,CAAC,oBAAoB;gBAC9D,MAAM,+BAA+B,KAAK,GAAG,CAAC,SAAS,YAAY;gBACnE,MAAM,IAAI,GACN,4BAA4B;YACpC,OACK;gBACD,MAAM,IAAI,GAAG,KAAK;YACtB;YACA,MAAM,KAAK,GAAG,MAAM,IAAI,GAAG,SAAS;YACpC,OAAO;QACX;QACA,UAAU;YACN,MAAM,qBAAqB,KAAK,GAAG,CAAC,CAAA,GAAA,oMAAA,CAAA,wBAAqB,AAAD,EAAE,YAAY,oMAAA,CAAA,uBAAoB;YAC1F,MAAM,SAAS,CAAA,GAAA,qLAAA,CAAA,uBAAoB,AAAD,EAAE,CAAC,WAAa,UAAU,IAAI,CAAC,qBAAqB,UAAU,KAAK,EAAE,oBAAoB;YAC3H,OAAO,qBAAqB,QAAQ;QACxC;QACA,cAAc,KAAQ;IAC1B;IACA,OAAO;AACX;AACA,OAAO,cAAc,GAAG,CAAC;IACrB,MAAM,mBAAmB,CAAA,GAAA,iNAAA,CAAA,wBAAqB,AAAD,EAAE,SAAS,KAAK;IAC7D,QAAQ,IAAI,GAAG,iBAAiB,IAAI;IACpC,QAAQ,QAAQ,GAAG,CAAA,GAAA,oKAAA,CAAA,wBAAqB,AAAD,EAAE,iBAAiB,QAAQ;IAClE,QAAQ,IAAI,GAAG;IACf,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1811, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/animation/generators/inertia.mjs"], "sourcesContent": ["import { spring } from './spring/index.mjs';\nimport { calcGeneratorVelocity } from './utils/velocity.mjs';\n\nfunction inertia({ keyframes, velocity = 0.0, power = 0.8, timeConstant = 325, bounceDamping = 10, bounceStiffness = 500, modifyTarget, min, max, restDelta = 0.5, restSpeed, }) {\n    const origin = keyframes[0];\n    const state = {\n        done: false,\n        value: origin,\n    };\n    const isOutOfBounds = (v) => (min !== undefined && v < min) || (max !== undefined && v > max);\n    const nearestBoundary = (v) => {\n        if (min === undefined)\n            return max;\n        if (max === undefined)\n            return min;\n        return Math.abs(min - v) < Math.abs(max - v) ? min : max;\n    };\n    let amplitude = power * velocity;\n    const ideal = origin + amplitude;\n    const target = modifyTarget === undefined ? ideal : modifyTarget(ideal);\n    /**\n     * If the target has changed we need to re-calculate the amplitude, otherwise\n     * the animation will start from the wrong position.\n     */\n    if (target !== ideal)\n        amplitude = target - origin;\n    const calcDelta = (t) => -amplitude * Math.exp(-t / timeConstant);\n    const calcLatest = (t) => target + calcDelta(t);\n    const applyFriction = (t) => {\n        const delta = calcDelta(t);\n        const latest = calcLatest(t);\n        state.done = Math.abs(delta) <= restDelta;\n        state.value = state.done ? target : latest;\n    };\n    /**\n     * Ideally this would resolve for t in a stateless way, we could\n     * do that by always precalculating the animation but as we know\n     * this will be done anyway we can assume that spring will\n     * be discovered during that.\n     */\n    let timeReachedBoundary;\n    let spring$1;\n    const checkCatchBoundary = (t) => {\n        if (!isOutOfBounds(state.value))\n            return;\n        timeReachedBoundary = t;\n        spring$1 = spring({\n            keyframes: [state.value, nearestBoundary(state.value)],\n            velocity: calcGeneratorVelocity(calcLatest, t, state.value), // TODO: This should be passing * 1000\n            damping: bounceDamping,\n            stiffness: bounceStiffness,\n            restDelta,\n            restSpeed,\n        });\n    };\n    checkCatchBoundary(0);\n    return {\n        calculatedDuration: null,\n        next: (t) => {\n            /**\n             * We need to resolve the friction to figure out if we need a\n             * spring but we don't want to do this twice per frame. So here\n             * we flag if we updated for this frame and later if we did\n             * we can skip doing it again.\n             */\n            let hasUpdatedFrame = false;\n            if (!spring$1 && timeReachedBoundary === undefined) {\n                hasUpdatedFrame = true;\n                applyFriction(t);\n                checkCatchBoundary(t);\n            }\n            /**\n             * If we have a spring and the provided t is beyond the moment the friction\n             * animation crossed the min/max boundary, use the spring.\n             */\n            if (timeReachedBoundary !== undefined && t >= timeReachedBoundary) {\n                return spring$1.next(t - timeReachedBoundary);\n            }\n            else {\n                !hasUpdatedFrame && applyFriction(t);\n                return state;\n            }\n        },\n    };\n}\n\nexport { inertia };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,QAAQ,EAAE,SAAS,EAAE,WAAW,GAAG,EAAE,QAAQ,GAAG,EAAE,eAAe,GAAG,EAAE,gBAAgB,EAAE,EAAE,kBAAkB,GAAG,EAAE,YAAY,EAAE,GAAG,EAAE,GAAG,EAAE,YAAY,GAAG,EAAE,SAAS,EAAG;IAC3K,MAAM,SAAS,SAAS,CAAC,EAAE;IAC3B,MAAM,QAAQ;QACV,MAAM;QACN,OAAO;IACX;IACA,MAAM,gBAAgB,CAAC,IAAM,AAAC,QAAQ,aAAa,IAAI,OAAS,QAAQ,aAAa,IAAI;IACzF,MAAM,kBAAkB,CAAC;QACrB,IAAI,QAAQ,WACR,OAAO;QACX,IAAI,QAAQ,WACR,OAAO;QACX,OAAO,KAAK,GAAG,CAAC,MAAM,KAAK,KAAK,GAAG,CAAC,MAAM,KAAK,MAAM;IACzD;IACA,IAAI,YAAY,QAAQ;IACxB,MAAM,QAAQ,SAAS;IACvB,MAAM,SAAS,iBAAiB,YAAY,QAAQ,aAAa;IACjE;;;KAGC,GACD,IAAI,WAAW,OACX,YAAY,SAAS;IACzB,MAAM,YAAY,CAAC,IAAM,CAAC,YAAY,KAAK,GAAG,CAAC,CAAC,IAAI;IACpD,MAAM,aAAa,CAAC,IAAM,SAAS,UAAU;IAC7C,MAAM,gBAAgB,CAAC;QACnB,MAAM,QAAQ,UAAU;QACxB,MAAM,SAAS,WAAW;QAC1B,MAAM,IAAI,GAAG,KAAK,GAAG,CAAC,UAAU;QAChC,MAAM,KAAK,GAAG,MAAM,IAAI,GAAG,SAAS;IACxC;IACA;;;;;KAKC,GACD,IAAI;IACJ,IAAI;IACJ,MAAM,qBAAqB,CAAC;QACxB,IAAI,CAAC,cAAc,MAAM,KAAK,GAC1B;QACJ,sBAAsB;QACtB,WAAW,CAAA,GAAA,0LAAA,CAAA,SAAM,AAAD,EAAE;YACd,WAAW;gBAAC,MAAM,KAAK;gBAAE,gBAAgB,MAAM,KAAK;aAAE;YACtD,UAAU,CAAA,GAAA,4LAAA,CAAA,wBAAqB,AAAD,EAAE,YAAY,GAAG,MAAM,KAAK;YAC1D,SAAS;YACT,WAAW;YACX;YACA;QACJ;IACJ;IACA,mBAAmB;IACnB,OAAO;QACH,oBAAoB;QACpB,MAAM,CAAC;YACH;;;;;aAKC,GACD,IAAI,kBAAkB;YACtB,IAAI,CAAC,YAAY,wBAAwB,WAAW;gBAChD,kBAAkB;gBAClB,cAAc;gBACd,mBAAmB;YACvB;YACA;;;aAGC,GACD,IAAI,wBAAwB,aAAa,KAAK,qBAAqB;gBAC/D,OAAO,SAAS,IAAI,CAAC,IAAI;YAC7B,OACK;gBACD,CAAC,mBAAmB,cAAc;gBAClC,OAAO;YACX;QACJ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1901, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/utils/interpolate.mjs"], "sourcesContent": ["import { invariant, clamp, MotionGlobalConfig, noop, pipe, progress } from 'motion-utils';\nimport { mix } from './mix/index.mjs';\n\nfunction createMixers(output, ease, customMixer) {\n    const mixers = [];\n    const mixerFactory = customMixer || MotionGlobalConfig.mix || mix;\n    const numMixers = output.length - 1;\n    for (let i = 0; i < numMixers; i++) {\n        let mixer = mixerFactory(output[i], output[i + 1]);\n        if (ease) {\n            const easingFunction = Array.isArray(ease) ? ease[i] || noop : ease;\n            mixer = pipe(easingFunction, mixer);\n        }\n        mixers.push(mixer);\n    }\n    return mixers;\n}\n/**\n * Create a function that maps from a numerical input array to a generic output array.\n *\n * Accepts:\n *   - Numbers\n *   - Colors (hex, hsl, hsla, rgb, rgba)\n *   - Complex (combinations of one or more numbers or strings)\n *\n * ```jsx\n * const mixColor = interpolate([0, 1], ['#fff', '#000'])\n *\n * mixColor(0.5) // 'rgba(128, 128, 128, 1)'\n * ```\n *\n * TODO Revisit this approach once we've moved to data models for values,\n * probably not needed to pregenerate mixer functions.\n *\n * @public\n */\nfunction interpolate(input, output, { clamp: isClamp = true, ease, mixer } = {}) {\n    const inputLength = input.length;\n    invariant(inputLength === output.length, \"Both input and output ranges must be the same length\", \"range-length\");\n    /**\n     * If we're only provided a single input, we can just make a function\n     * that returns the output.\n     */\n    if (inputLength === 1)\n        return () => output[0];\n    if (inputLength === 2 && output[0] === output[1])\n        return () => output[1];\n    const isZeroDeltaRange = input[0] === input[1];\n    // If input runs highest -> lowest, reverse both arrays\n    if (input[0] > input[inputLength - 1]) {\n        input = [...input].reverse();\n        output = [...output].reverse();\n    }\n    const mixers = createMixers(output, ease, mixer);\n    const numMixers = mixers.length;\n    const interpolator = (v) => {\n        if (isZeroDeltaRange && v < input[0])\n            return output[0];\n        let i = 0;\n        if (numMixers > 1) {\n            for (; i < input.length - 2; i++) {\n                if (v < input[i + 1])\n                    break;\n            }\n        }\n        const progressInRange = progress(input[i], input[i + 1], v);\n        return mixers[i](progressInRange);\n    };\n    return isClamp\n        ? (v) => interpolator(clamp(input[0], input[inputLength - 1], v))\n        : interpolator;\n}\n\nexport { interpolate };\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAEA,SAAS,aAAa,MAAM,EAAE,IAAI,EAAE,WAAW;IAC3C,MAAM,SAAS,EAAE;IACjB,MAAM,eAAe,eAAe,kKAAA,CAAA,qBAAkB,CAAC,GAAG,IAAI,qKAAA,CAAA,MAAG;IACjE,MAAM,YAAY,OAAO,MAAM,GAAG;IAClC,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,IAAK;QAChC,IAAI,QAAQ,aAAa,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,IAAI,EAAE;QACjD,IAAI,MAAM;YACN,MAAM,iBAAiB,MAAM,OAAO,CAAC,QAAQ,IAAI,CAAC,EAAE,IAAI,sJAAA,CAAA,OAAI,GAAG;YAC/D,QAAQ,CAAA,GAAA,sJAAA,CAAA,OAAI,AAAD,EAAE,gBAAgB;QACjC;QACA,OAAO,IAAI,CAAC;IAChB;IACA,OAAO;AACX;AACA;;;;;;;;;;;;;;;;;;CAkBC,GACD,SAAS,YAAY,KAAK,EAAE,MAAM,EAAE,EAAE,OAAO,UAAU,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;IAC3E,MAAM,cAAc,MAAM,MAAM;IAChC,CAAA,GAAA,wJAAA,CAAA,YAAS,AAAD,EAAE,gBAAgB,OAAO,MAAM,EAAE,wDAAwD;IACjG;;;KAGC,GACD,IAAI,gBAAgB,GAChB,OAAO,IAAM,MAAM,CAAC,EAAE;IAC1B,IAAI,gBAAgB,KAAK,MAAM,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE,EAC5C,OAAO,IAAM,MAAM,CAAC,EAAE;IAC1B,MAAM,mBAAmB,KAAK,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE;IAC9C,uDAAuD;IACvD,IAAI,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,cAAc,EAAE,EAAE;QACnC,QAAQ;eAAI;SAAM,CAAC,OAAO;QAC1B,SAAS;eAAI;SAAO,CAAC,OAAO;IAChC;IACA,MAAM,SAAS,aAAa,QAAQ,MAAM;IAC1C,MAAM,YAAY,OAAO,MAAM;IAC/B,MAAM,eAAe,CAAC;QAClB,IAAI,oBAAoB,IAAI,KAAK,CAAC,EAAE,EAChC,OAAO,MAAM,CAAC,EAAE;QACpB,IAAI,IAAI;QACR,IAAI,YAAY,GAAG;YACf,MAAO,IAAI,MAAM,MAAM,GAAG,GAAG,IAAK;gBAC9B,IAAI,IAAI,KAAK,CAAC,IAAI,EAAE,EAChB;YACR;QACJ;QACA,MAAM,kBAAkB,CAAA,GAAA,0JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,IAAI,EAAE,EAAE;QACzD,OAAO,MAAM,CAAC,EAAE,CAAC;IACrB;IACA,OAAO,UACD,CAAC,IAAM,aAAa,CAAA,GAAA,uJAAA,CAAA,QAAK,AAAD,EAAE,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,cAAc,EAAE,EAAE,MAC5D;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1985, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/animation/keyframes/offsets/fill.mjs"], "sourcesContent": ["import { progress } from 'motion-utils';\nimport { mixNumber } from '../../../utils/mix/number.mjs';\n\nfunction fillOffset(offset, remaining) {\n    const min = offset[offset.length - 1];\n    for (let i = 1; i <= remaining; i++) {\n        const offsetProgress = progress(0, remaining, i);\n        offset.push(mixNumber(min, 1, offsetProgress));\n    }\n}\n\nexport { fillOffset };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,WAAW,MAAM,EAAE,SAAS;IACjC,MAAM,MAAM,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE;IACrC,IAAK,IAAI,IAAI,GAAG,KAAK,WAAW,IAAK;QACjC,MAAM,iBAAiB,CAAA,GAAA,0JAAA,CAAA,WAAQ,AAAD,EAAE,GAAG,WAAW;QAC9C,OAAO,IAAI,CAAC,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,KAAK,GAAG;IAClC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2006, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/animation/keyframes/offsets/default.mjs"], "sourcesContent": ["import { fillOffset } from './fill.mjs';\n\nfunction defaultOffset(arr) {\n    const offset = [0];\n    fillOffset(offset, arr.length - 1);\n    return offset;\n}\n\nexport { defaultOffset };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,cAAc,GAAG;IACtB,MAAM,SAAS;QAAC;KAAE;IAClB,CAAA,GAAA,yLAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,IAAI,MAAM,GAAG;IAChC,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2025, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/animation/keyframes/offsets/time.mjs"], "sourcesContent": ["function convertOffsetToTimes(offset, duration) {\n    return offset.map((o) => o * duration);\n}\n\nexport { convertOffsetToTimes };\n"], "names": [], "mappings": ";;;AAAA,SAAS,qBAAqB,MAAM,EAAE,QAAQ;IAC1C,OAAO,OAAO,GAAG,CAAC,CAAC,IAAM,IAAI;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2038, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/animation/generators/keyframes.mjs"], "sourcesContent": ["import { easeInOut, isEasingArray, easingDefinitionToFunction } from 'motion-utils';\nimport { interpolate } from '../../utils/interpolate.mjs';\nimport { defaultOffset } from '../keyframes/offsets/default.mjs';\nimport { convertOffsetToTimes } from '../keyframes/offsets/time.mjs';\n\nfunction defaultEasing(values, easing) {\n    return values.map(() => easing || easeInOut).splice(0, values.length - 1);\n}\nfunction keyframes({ duration = 300, keyframes: keyframeValues, times, ease = \"easeInOut\", }) {\n    /**\n     * Easing functions can be externally defined as strings. Here we convert them\n     * into actual functions.\n     */\n    const easingFunctions = isEasingArray(ease)\n        ? ease.map(easingDefinitionToFunction)\n        : easingDefinitionToFunction(ease);\n    /**\n     * This is the Iterator-spec return value. We ensure it's mutable rather than using a generator\n     * to reduce GC during animation.\n     */\n    const state = {\n        done: false,\n        value: keyframeValues[0],\n    };\n    /**\n     * Create a times array based on the provided 0-1 offsets\n     */\n    const absoluteTimes = convertOffsetToTimes(\n    // Only use the provided offsets if they're the correct length\n    // TODO Maybe we should warn here if there's a length mismatch\n    times && times.length === keyframeValues.length\n        ? times\n        : defaultOffset(keyframeValues), duration);\n    const mapTimeToKeyframe = interpolate(absoluteTimes, keyframeValues, {\n        ease: Array.isArray(easingFunctions)\n            ? easingFunctions\n            : defaultEasing(keyframeValues, easingFunctions),\n    });\n    return {\n        calculatedDuration: duration,\n        next: (t) => {\n            state.value = mapTimeToKeyframe(t);\n            state.done = t >= duration;\n            return state;\n        },\n    };\n}\n\nexport { defaultEasing, keyframes };\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;AAEA,SAAS,cAAc,MAAM,EAAE,MAAM;IACjC,OAAO,OAAO,GAAG,CAAC,IAAM,UAAU,gKAAA,CAAA,YAAS,EAAE,MAAM,CAAC,GAAG,OAAO,MAAM,GAAG;AAC3E;AACA,SAAS,UAAU,EAAE,WAAW,GAAG,EAAE,WAAW,cAAc,EAAE,KAAK,EAAE,OAAO,WAAW,EAAG;IACxF;;;KAGC,GACD,MAAM,kBAAkB,CAAA,GAAA,0LAAA,CAAA,gBAAa,AAAD,EAAE,QAChC,KAAK,GAAG,CAAC,wKAAA,CAAA,6BAA0B,IACnC,CAAA,GAAA,wKAAA,CAAA,6BAA0B,AAAD,EAAE;IACjC;;;KAGC,GACD,MAAM,QAAQ;QACV,MAAM;QACN,OAAO,cAAc,CAAC,EAAE;IAC5B;IACA;;KAEC,GACD,MAAM,gBAAgB,CAAA,GAAA,yLAAA,CAAA,uBAAoB,AAAD,EACzC,8DAA8D;IAC9D,8DAA8D;IAC9D,SAAS,MAAM,MAAM,KAAK,eAAe,MAAM,GACzC,QACA,CAAA,GAAA,4LAAA,CAAA,gBAAa,AAAD,EAAE,iBAAiB;IACrC,MAAM,oBAAoB,CAAA,GAAA,oKAAA,CAAA,cAAW,AAAD,EAAE,eAAe,gBAAgB;QACjE,MAAM,MAAM,OAAO,CAAC,mBACd,kBACA,cAAc,gBAAgB;IACxC;IACA,OAAO;QACH,oBAAoB;QACpB,MAAM,CAAC;YACH,MAAM,KAAK,GAAG,kBAAkB;YAChC,MAAM,IAAI,GAAG,KAAK;YAClB,OAAO;QACX;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2091, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/animation/keyframes/get-final.mjs"], "sourcesContent": ["const isNotNull = (value) => value !== null;\nfunction getFinalKeyframe(keyframes, { repeat, repeatType = \"loop\" }, finalKeyframe, speed = 1) {\n    const resolvedKeyframes = keyframes.filter(isNotNull);\n    const useFirstKeyframe = speed < 0 || (repeat && repeatType !== \"loop\" && repeat % 2 === 1);\n    const index = useFirstKeyframe ? 0 : resolvedKeyframes.length - 1;\n    return !index || finalKeyframe === undefined\n        ? resolvedKeyframes[index]\n        : finalKeyframe;\n}\n\nexport { getFinalKeyframe };\n"], "names": [], "mappings": ";;;AAAA,MAAM,YAAY,CAAC,QAAU,UAAU;AACvC,SAAS,iBAAiB,SAAS,EAAE,EAAE,MAAM,EAAE,aAAa,MAAM,EAAE,EAAE,aAAa,EAAE,QAAQ,CAAC;IAC1F,MAAM,oBAAoB,UAAU,MAAM,CAAC;IAC3C,MAAM,mBAAmB,QAAQ,KAAM,UAAU,eAAe,UAAU,SAAS,MAAM;IACzF,MAAM,QAAQ,mBAAmB,IAAI,kBAAkB,MAAM,GAAG;IAChE,OAAO,CAAC,SAAS,kBAAkB,YAC7B,iBAAiB,CAAC,MAAM,GACxB;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2108, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/animation/utils/replace-transition-type.mjs"], "sourcesContent": ["import { inertia } from '../generators/inertia.mjs';\nimport { keyframes } from '../generators/keyframes.mjs';\nimport { spring } from '../generators/spring/index.mjs';\n\nconst transitionTypeMap = {\n    decay: inertia,\n    inertia,\n    tween: keyframes,\n    keyframes: keyframes,\n    spring,\n};\nfunction replaceTransitionType(transition) {\n    if (typeof transition.type === \"string\") {\n        transition.type = transitionTypeMap[transition.type];\n    }\n}\n\nexport { replaceTransitionType };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,MAAM,oBAAoB;IACtB,OAAO,kLAAA,CAAA,UAAO;IACd,SAAA,kLAAA,CAAA,UAAO;IACP,OAAO,oLAAA,CAAA,YAAS;IAChB,WAAW,oLAAA,CAAA,YAAS;IACpB,QAAA,0LAAA,CAAA,SAAM;AACV;AACA,SAAS,sBAAsB,UAAU;IACrC,IAAI,OAAO,WAAW,IAAI,KAAK,UAAU;QACrC,WAAW,IAAI,GAAG,iBAAiB,CAAC,WAAW,IAAI,CAAC;IACxD;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2136, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/animation/utils/WithPromise.mjs"], "sourcesContent": ["class WithPromise {\n    constructor() {\n        this.updateFinished();\n    }\n    get finished() {\n        return this._finished;\n    }\n    updateFinished() {\n        this._finished = new Promise((resolve) => {\n            this.resolve = resolve;\n        });\n    }\n    notifyFinished() {\n        this.resolve();\n    }\n    /**\n     * Allows the animation to be awaited.\n     *\n     * @deprecated Use `finished` instead.\n     */\n    then(onResolve, onReject) {\n        return this.finished.then(onResolve, onReject);\n    }\n}\n\nexport { WithPromise };\n"], "names": [], "mappings": ";;;AAAA,MAAM;IACF,aAAc;QACV,IAAI,CAAC,cAAc;IACvB;IACA,IAAI,WAAW;QACX,OAAO,IAAI,CAAC,SAAS;IACzB;IACA,iBAAiB;QACb,IAAI,CAAC,SAAS,GAAG,IAAI,QAAQ,CAAC;YAC1B,IAAI,CAAC,OAAO,GAAG;QACnB;IACJ;IACA,iBAAiB;QACb,IAAI,CAAC,OAAO;IAChB;IACA;;;;KAIC,GACD,KAAK,SAAS,EAAE,QAAQ,EAAE;QACtB,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW;IACzC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2169, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/animation/JSAnimation.mjs"], "sourcesContent": ["import { invariant, pipe, clamp, millisecondsToSeconds, secondsToMilliseconds } from 'motion-utils';\nimport { time } from '../frameloop/sync-time.mjs';\nimport { activeAnimations } from '../stats/animation-count.mjs';\nimport { mix } from '../utils/mix/index.mjs';\nimport { frameloopDriver } from './drivers/frame.mjs';\nimport { inertia } from './generators/inertia.mjs';\nimport { keyframes } from './generators/keyframes.mjs';\nimport { calcGeneratorDuration } from './generators/utils/calc-duration.mjs';\nimport { getFinalKeyframe } from './keyframes/get-final.mjs';\nimport { replaceTransitionType } from './utils/replace-transition-type.mjs';\nimport { WithPromise } from './utils/WithPromise.mjs';\n\nconst percentToProgress = (percent) => percent / 100;\nclass JSAnimation extends WithPromise {\n    constructor(options) {\n        super();\n        this.state = \"idle\";\n        this.startTime = null;\n        this.isStopped = false;\n        /**\n         * The current time of the animation.\n         */\n        this.currentTime = 0;\n        /**\n         * The time at which the animation was paused.\n         */\n        this.holdTime = null;\n        /**\n         * Playback speed as a factor. 0 would be stopped, -1 reverse and 2 double speed.\n         */\n        this.playbackSpeed = 1;\n        /**\n         * This method is bound to the instance to fix a pattern where\n         * animation.stop is returned as a reference from a useEffect.\n         */\n        this.stop = () => {\n            const { motionValue } = this.options;\n            if (motionValue && motionValue.updatedAt !== time.now()) {\n                this.tick(time.now());\n            }\n            this.isStopped = true;\n            if (this.state === \"idle\")\n                return;\n            this.teardown();\n            this.options.onStop?.();\n        };\n        activeAnimations.mainThread++;\n        this.options = options;\n        this.initAnimation();\n        this.play();\n        if (options.autoplay === false)\n            this.pause();\n    }\n    initAnimation() {\n        const { options } = this;\n        replaceTransitionType(options);\n        const { type = keyframes, repeat = 0, repeatDelay = 0, repeatType, velocity = 0, } = options;\n        let { keyframes: keyframes$1 } = options;\n        const generatorFactory = type || keyframes;\n        if (process.env.NODE_ENV !== \"production\" &&\n            generatorFactory !== keyframes) {\n            invariant(keyframes$1.length <= 2, `Only two keyframes currently supported with spring and inertia animations. Trying to animate ${keyframes$1}`, \"spring-two-frames\");\n        }\n        if (generatorFactory !== keyframes &&\n            typeof keyframes$1[0] !== \"number\") {\n            this.mixKeyframes = pipe(percentToProgress, mix(keyframes$1[0], keyframes$1[1]));\n            keyframes$1 = [0, 100];\n        }\n        const generator = generatorFactory({ ...options, keyframes: keyframes$1 });\n        /**\n         * If we have a mirror repeat type we need to create a second generator that outputs the\n         * mirrored (not reversed) animation and later ping pong between the two generators.\n         */\n        if (repeatType === \"mirror\") {\n            this.mirroredGenerator = generatorFactory({\n                ...options,\n                keyframes: [...keyframes$1].reverse(),\n                velocity: -velocity,\n            });\n        }\n        /**\n         * If duration is undefined and we have repeat options,\n         * we need to calculate a duration from the generator.\n         *\n         * We set it to the generator itself to cache the duration.\n         * Any timeline resolver will need to have already precalculated\n         * the duration by this step.\n         */\n        if (generator.calculatedDuration === null) {\n            generator.calculatedDuration = calcGeneratorDuration(generator);\n        }\n        const { calculatedDuration } = generator;\n        this.calculatedDuration = calculatedDuration;\n        this.resolvedDuration = calculatedDuration + repeatDelay;\n        this.totalDuration = this.resolvedDuration * (repeat + 1) - repeatDelay;\n        this.generator = generator;\n    }\n    updateTime(timestamp) {\n        const animationTime = Math.round(timestamp - this.startTime) * this.playbackSpeed;\n        // Update currentTime\n        if (this.holdTime !== null) {\n            this.currentTime = this.holdTime;\n        }\n        else {\n            // Rounding the time because floating point arithmetic is not always accurate, e.g. 3000.367 - 1000.367 =\n            // 2000.0000000000002. This is a problem when we are comparing the currentTime with the duration, for\n            // example.\n            this.currentTime = animationTime;\n        }\n    }\n    tick(timestamp, sample = false) {\n        const { generator, totalDuration, mixKeyframes, mirroredGenerator, resolvedDuration, calculatedDuration, } = this;\n        if (this.startTime === null)\n            return generator.next(0);\n        const { delay = 0, keyframes, repeat, repeatType, repeatDelay, type, onUpdate, finalKeyframe, } = this.options;\n        /**\n         * requestAnimationFrame timestamps can come through as lower than\n         * the startTime as set by performance.now(). Here we prevent this,\n         * though in the future it could be possible to make setting startTime\n         * a pending operation that gets resolved here.\n         */\n        if (this.speed > 0) {\n            this.startTime = Math.min(this.startTime, timestamp);\n        }\n        else if (this.speed < 0) {\n            this.startTime = Math.min(timestamp - totalDuration / this.speed, this.startTime);\n        }\n        if (sample) {\n            this.currentTime = timestamp;\n        }\n        else {\n            this.updateTime(timestamp);\n        }\n        // Rebase on delay\n        const timeWithoutDelay = this.currentTime - delay * (this.playbackSpeed >= 0 ? 1 : -1);\n        const isInDelayPhase = this.playbackSpeed >= 0\n            ? timeWithoutDelay < 0\n            : timeWithoutDelay > totalDuration;\n        this.currentTime = Math.max(timeWithoutDelay, 0);\n        // If this animation has finished, set the current time  to the total duration.\n        if (this.state === \"finished\" && this.holdTime === null) {\n            this.currentTime = totalDuration;\n        }\n        let elapsed = this.currentTime;\n        let frameGenerator = generator;\n        if (repeat) {\n            /**\n             * Get the current progress (0-1) of the animation. If t is >\n             * than duration we'll get values like 2.5 (midway through the\n             * third iteration)\n             */\n            const progress = Math.min(this.currentTime, totalDuration) / resolvedDuration;\n            /**\n             * Get the current iteration (0 indexed). For instance the floor of\n             * 2.5 is 2.\n             */\n            let currentIteration = Math.floor(progress);\n            /**\n             * Get the current progress of the iteration by taking the remainder\n             * so 2.5 is 0.5 through iteration 2\n             */\n            let iterationProgress = progress % 1.0;\n            /**\n             * If iteration progress is 1 we count that as the end\n             * of the previous iteration.\n             */\n            if (!iterationProgress && progress >= 1) {\n                iterationProgress = 1;\n            }\n            iterationProgress === 1 && currentIteration--;\n            currentIteration = Math.min(currentIteration, repeat + 1);\n            /**\n             * Reverse progress if we're not running in \"normal\" direction\n             */\n            const isOddIteration = Boolean(currentIteration % 2);\n            if (isOddIteration) {\n                if (repeatType === \"reverse\") {\n                    iterationProgress = 1 - iterationProgress;\n                    if (repeatDelay) {\n                        iterationProgress -= repeatDelay / resolvedDuration;\n                    }\n                }\n                else if (repeatType === \"mirror\") {\n                    frameGenerator = mirroredGenerator;\n                }\n            }\n            elapsed = clamp(0, 1, iterationProgress) * resolvedDuration;\n        }\n        /**\n         * If we're in negative time, set state as the initial keyframe.\n         * This prevents delay: x, duration: 0 animations from finishing\n         * instantly.\n         */\n        const state = isInDelayPhase\n            ? { done: false, value: keyframes[0] }\n            : frameGenerator.next(elapsed);\n        if (mixKeyframes) {\n            state.value = mixKeyframes(state.value);\n        }\n        let { done } = state;\n        if (!isInDelayPhase && calculatedDuration !== null) {\n            done =\n                this.playbackSpeed >= 0\n                    ? this.currentTime >= totalDuration\n                    : this.currentTime <= 0;\n        }\n        const isAnimationFinished = this.holdTime === null &&\n            (this.state === \"finished\" || (this.state === \"running\" && done));\n        // TODO: The exception for inertia could be cleaner here\n        if (isAnimationFinished && type !== inertia) {\n            state.value = getFinalKeyframe(keyframes, this.options, finalKeyframe, this.speed);\n        }\n        if (onUpdate) {\n            onUpdate(state.value);\n        }\n        if (isAnimationFinished) {\n            this.finish();\n        }\n        return state;\n    }\n    /**\n     * Allows the returned animation to be awaited or promise-chained. Currently\n     * resolves when the animation finishes at all but in a future update could/should\n     * reject if its cancels.\n     */\n    then(resolve, reject) {\n        return this.finished.then(resolve, reject);\n    }\n    get duration() {\n        return millisecondsToSeconds(this.calculatedDuration);\n    }\n    get time() {\n        return millisecondsToSeconds(this.currentTime);\n    }\n    set time(newTime) {\n        newTime = secondsToMilliseconds(newTime);\n        this.currentTime = newTime;\n        if (this.startTime === null ||\n            this.holdTime !== null ||\n            this.playbackSpeed === 0) {\n            this.holdTime = newTime;\n        }\n        else if (this.driver) {\n            this.startTime = this.driver.now() - newTime / this.playbackSpeed;\n        }\n        this.driver?.start(false);\n    }\n    get speed() {\n        return this.playbackSpeed;\n    }\n    set speed(newSpeed) {\n        this.updateTime(time.now());\n        const hasChanged = this.playbackSpeed !== newSpeed;\n        this.playbackSpeed = newSpeed;\n        if (hasChanged) {\n            this.time = millisecondsToSeconds(this.currentTime);\n        }\n    }\n    play() {\n        if (this.isStopped)\n            return;\n        const { driver = frameloopDriver, startTime } = this.options;\n        if (!this.driver) {\n            this.driver = driver((timestamp) => this.tick(timestamp));\n        }\n        this.options.onPlay?.();\n        const now = this.driver.now();\n        if (this.state === \"finished\") {\n            this.updateFinished();\n            this.startTime = now;\n        }\n        else if (this.holdTime !== null) {\n            this.startTime = now - this.holdTime;\n        }\n        else if (!this.startTime) {\n            this.startTime = startTime ?? now;\n        }\n        if (this.state === \"finished\" && this.speed < 0) {\n            this.startTime += this.calculatedDuration;\n        }\n        this.holdTime = null;\n        /**\n         * Set playState to running only after we've used it in\n         * the previous logic.\n         */\n        this.state = \"running\";\n        this.driver.start();\n    }\n    pause() {\n        this.state = \"paused\";\n        this.updateTime(time.now());\n        this.holdTime = this.currentTime;\n    }\n    complete() {\n        if (this.state !== \"running\") {\n            this.play();\n        }\n        this.state = \"finished\";\n        this.holdTime = null;\n    }\n    finish() {\n        this.notifyFinished();\n        this.teardown();\n        this.state = \"finished\";\n        this.options.onComplete?.();\n    }\n    cancel() {\n        this.holdTime = null;\n        this.startTime = 0;\n        this.tick(0);\n        this.teardown();\n        this.options.onCancel?.();\n    }\n    teardown() {\n        this.state = \"idle\";\n        this.stopDriver();\n        this.startTime = this.holdTime = null;\n        activeAnimations.mainThread--;\n    }\n    stopDriver() {\n        if (!this.driver)\n            return;\n        this.driver.stop();\n        this.driver = undefined;\n    }\n    sample(sampleTime) {\n        this.startTime = 0;\n        return this.tick(sampleTime, true);\n    }\n    attachTimeline(timeline) {\n        if (this.options.allowFlatten) {\n            this.options.type = \"keyframes\";\n            this.options.ease = \"linear\";\n            this.initAnimation();\n        }\n        this.driver?.stop();\n        return timeline.observe(this);\n    }\n}\n// Legacy function support\nfunction animateValue(options) {\n    return new JSAnimation(options);\n}\n\nexport { JSAnimation, animateValue };\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AAEA,MAAM,oBAAoB,CAAC,UAAY,UAAU;AACjD,MAAM,oBAAoB,iLAAA,CAAA,cAAW;IACjC,YAAY,OAAO,CAAE;QACjB,KAAK;QACL,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,SAAS,GAAG;QACjB;;SAEC,GACD,IAAI,CAAC,WAAW,GAAG;QACnB;;SAEC,GACD,IAAI,CAAC,QAAQ,GAAG;QAChB;;SAEC,GACD,IAAI,CAAC,aAAa,GAAG;QACrB;;;SAGC,GACD,IAAI,CAAC,IAAI,GAAG;YACR,MAAM,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC,OAAO;YACpC,IAAI,eAAe,YAAY,SAAS,KAAK,yKAAA,CAAA,OAAI,CAAC,GAAG,IAAI;gBACrD,IAAI,CAAC,IAAI,CAAC,yKAAA,CAAA,OAAI,CAAC,GAAG;YACtB;YACA,IAAI,CAAC,SAAS,GAAG;YACjB,IAAI,IAAI,CAAC,KAAK,KAAK,QACf;YACJ,IAAI,CAAC,QAAQ;YACb,IAAI,CAAC,OAAO,CAAC,MAAM;QACvB;QACA,2KAAA,CAAA,mBAAgB,CAAC,UAAU;QAC3B,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,aAAa;QAClB,IAAI,CAAC,IAAI;QACT,IAAI,QAAQ,QAAQ,KAAK,OACrB,IAAI,CAAC,KAAK;IAClB;IACA,gBAAgB;QACZ,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI;QACxB,CAAA,GAAA,mMAAA,CAAA,wBAAqB,AAAD,EAAE;QACtB,MAAM,EAAE,OAAO,oLAAA,CAAA,YAAS,EAAE,SAAS,CAAC,EAAE,cAAc,CAAC,EAAE,UAAU,EAAE,WAAW,CAAC,EAAG,GAAG;QACrF,IAAI,EAAE,WAAW,WAAW,EAAE,GAAG;QACjC,MAAM,mBAAmB,QAAQ,oLAAA,CAAA,YAAS;QAC1C,IAAI,oDAAyB,gBACzB,qBAAqB,oLAAA,CAAA,YAAS,EAAE;YAChC,CAAA,GAAA,wJAAA,CAAA,YAAS,AAAD,EAAE,YAAY,MAAM,IAAI,GAAG,CAAC,6FAA6F,EAAE,aAAa,EAAE;QACtJ;QACA,IAAI,qBAAqB,oLAAA,CAAA,YAAS,IAC9B,OAAO,WAAW,CAAC,EAAE,KAAK,UAAU;YACpC,IAAI,CAAC,YAAY,GAAG,CAAA,GAAA,sJAAA,CAAA,OAAI,AAAD,EAAE,mBAAmB,CAAA,GAAA,qKAAA,CAAA,MAAG,AAAD,EAAE,WAAW,CAAC,EAAE,EAAE,WAAW,CAAC,EAAE;YAC9E,cAAc;gBAAC;gBAAG;aAAI;QAC1B;QACA,MAAM,YAAY,iBAAiB;YAAE,GAAG,OAAO;YAAE,WAAW;QAAY;QACxE;;;SAGC,GACD,IAAI,eAAe,UAAU;YACzB,IAAI,CAAC,iBAAiB,GAAG,iBAAiB;gBACtC,GAAG,OAAO;gBACV,WAAW;uBAAI;iBAAY,CAAC,OAAO;gBACnC,UAAU,CAAC;YACf;QACJ;QACA;;;;;;;SAOC,GACD,IAAI,UAAU,kBAAkB,KAAK,MAAM;YACvC,UAAU,kBAAkB,GAAG,CAAA,GAAA,oMAAA,CAAA,wBAAqB,AAAD,EAAE;QACzD;QACA,MAAM,EAAE,kBAAkB,EAAE,GAAG;QAC/B,IAAI,CAAC,kBAAkB,GAAG;QAC1B,IAAI,CAAC,gBAAgB,GAAG,qBAAqB;QAC7C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,gBAAgB,GAAG,CAAC,SAAS,CAAC,IAAI;QAC5D,IAAI,CAAC,SAAS,GAAG;IACrB;IACA,WAAW,SAAS,EAAE;QAClB,MAAM,gBAAgB,KAAK,KAAK,CAAC,YAAY,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,aAAa;QACjF,qBAAqB;QACrB,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM;YACxB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ;QACpC,OACK;YACD,yGAAyG;YACzG,qGAAqG;YACrG,WAAW;YACX,IAAI,CAAC,WAAW,GAAG;QACvB;IACJ;IACA,KAAK,SAAS,EAAE,SAAS,KAAK,EAAE;QAC5B,MAAM,EAAE,SAAS,EAAE,aAAa,EAAE,YAAY,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,kBAAkB,EAAG,GAAG,IAAI;QACjH,IAAI,IAAI,CAAC,SAAS,KAAK,MACnB,OAAO,UAAU,IAAI,CAAC;QAC1B,MAAM,EAAE,QAAQ,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,QAAQ,EAAE,aAAa,EAAG,GAAG,IAAI,CAAC,OAAO;QAC9G;;;;;SAKC,GACD,IAAI,IAAI,CAAC,KAAK,GAAG,GAAG;YAChB,IAAI,CAAC,SAAS,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE;QAC9C,OACK,IAAI,IAAI,CAAC,KAAK,GAAG,GAAG;YACrB,IAAI,CAAC,SAAS,GAAG,KAAK,GAAG,CAAC,YAAY,gBAAgB,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS;QACpF;QACA,IAAI,QAAQ;YACR,IAAI,CAAC,WAAW,GAAG;QACvB,OACK;YACD,IAAI,CAAC,UAAU,CAAC;QACpB;QACA,kBAAkB;QAClB,MAAM,mBAAmB,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,IAAI,CAAC,CAAC;QACrF,MAAM,iBAAiB,IAAI,CAAC,aAAa,IAAI,IACvC,mBAAmB,IACnB,mBAAmB;QACzB,IAAI,CAAC,WAAW,GAAG,KAAK,GAAG,CAAC,kBAAkB;QAC9C,+EAA+E;QAC/E,IAAI,IAAI,CAAC,KAAK,KAAK,cAAc,IAAI,CAAC,QAAQ,KAAK,MAAM;YACrD,IAAI,CAAC,WAAW,GAAG;QACvB;QACA,IAAI,UAAU,IAAI,CAAC,WAAW;QAC9B,IAAI,iBAAiB;QACrB,IAAI,QAAQ;YACR;;;;aAIC,GACD,MAAM,WAAW,KAAK,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,iBAAiB;YAC7D;;;aAGC,GACD,IAAI,mBAAmB,KAAK,KAAK,CAAC;YAClC;;;aAGC,GACD,IAAI,oBAAoB,WAAW;YACnC;;;aAGC,GACD,IAAI,CAAC,qBAAqB,YAAY,GAAG;gBACrC,oBAAoB;YACxB;YACA,sBAAsB,KAAK;YAC3B,mBAAmB,KAAK,GAAG,CAAC,kBAAkB,SAAS;YACvD;;aAEC,GACD,MAAM,iBAAiB,QAAQ,mBAAmB;YAClD,IAAI,gBAAgB;gBAChB,IAAI,eAAe,WAAW;oBAC1B,oBAAoB,IAAI;oBACxB,IAAI,aAAa;wBACb,qBAAqB,cAAc;oBACvC;gBACJ,OACK,IAAI,eAAe,UAAU;oBAC9B,iBAAiB;gBACrB;YACJ;YACA,UAAU,CAAA,GAAA,uJAAA,CAAA,QAAK,AAAD,EAAE,GAAG,GAAG,qBAAqB;QAC/C;QACA;;;;SAIC,GACD,MAAM,QAAQ,iBACR;YAAE,MAAM;YAAO,OAAO,SAAS,CAAC,EAAE;QAAC,IACnC,eAAe,IAAI,CAAC;QAC1B,IAAI,cAAc;YACd,MAAM,KAAK,GAAG,aAAa,MAAM,KAAK;QAC1C;QACA,IAAI,EAAE,IAAI,EAAE,GAAG;QACf,IAAI,CAAC,kBAAkB,uBAAuB,MAAM;YAChD,OACI,IAAI,CAAC,aAAa,IAAI,IAChB,IAAI,CAAC,WAAW,IAAI,gBACpB,IAAI,CAAC,WAAW,IAAI;QAClC;QACA,MAAM,sBAAsB,IAAI,CAAC,QAAQ,KAAK,QAC1C,CAAC,IAAI,CAAC,KAAK,KAAK,cAAe,IAAI,CAAC,KAAK,KAAK,aAAa,IAAK;QACpE,wDAAwD;QACxD,IAAI,uBAAuB,SAAS,kLAAA,CAAA,UAAO,EAAE;YACzC,MAAM,KAAK,GAAG,CAAA,GAAA,sLAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,IAAI,CAAC,OAAO,EAAE,eAAe,IAAI,CAAC,KAAK;QACrF;QACA,IAAI,UAAU;YACV,SAAS,MAAM,KAAK;QACxB;QACA,IAAI,qBAAqB;YACrB,IAAI,CAAC,MAAM;QACf;QACA,OAAO;IACX;IACA;;;;KAIC,GACD,KAAK,OAAO,EAAE,MAAM,EAAE;QAClB,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS;IACvC;IACA,IAAI,WAAW;QACX,OAAO,CAAA,GAAA,oKAAA,CAAA,wBAAqB,AAAD,EAAE,IAAI,CAAC,kBAAkB;IACxD;IACA,IAAI,OAAO;QACP,OAAO,CAAA,GAAA,oKAAA,CAAA,wBAAqB,AAAD,EAAE,IAAI,CAAC,WAAW;IACjD;IACA,IAAI,KAAK,OAAO,EAAE;QACd,UAAU,CAAA,GAAA,oKAAA,CAAA,wBAAqB,AAAD,EAAE;QAChC,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,IAAI,CAAC,SAAS,KAAK,QACnB,IAAI,CAAC,QAAQ,KAAK,QAClB,IAAI,CAAC,aAAa,KAAK,GAAG;YAC1B,IAAI,CAAC,QAAQ,GAAG;QACpB,OACK,IAAI,IAAI,CAAC,MAAM,EAAE;YAClB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,KAAK,UAAU,IAAI,CAAC,aAAa;QACrE;QACA,IAAI,CAAC,MAAM,EAAE,MAAM;IACvB;IACA,IAAI,QAAQ;QACR,OAAO,IAAI,CAAC,aAAa;IAC7B;IACA,IAAI,MAAM,QAAQ,EAAE;QAChB,IAAI,CAAC,UAAU,CAAC,yKAAA,CAAA,OAAI,CAAC,GAAG;QACxB,MAAM,aAAa,IAAI,CAAC,aAAa,KAAK;QAC1C,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,YAAY;YACZ,IAAI,CAAC,IAAI,GAAG,CAAA,GAAA,oKAAA,CAAA,wBAAqB,AAAD,EAAE,IAAI,CAAC,WAAW;QACtD;IACJ;IACA,OAAO;QACH,IAAI,IAAI,CAAC,SAAS,EACd;QACJ,MAAM,EAAE,SAAS,6KAAA,CAAA,kBAAe,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,OAAO;QAC5D,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACd,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,YAAc,IAAI,CAAC,IAAI,CAAC;QAClD;QACA,IAAI,CAAC,OAAO,CAAC,MAAM;QACnB,MAAM,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG;QAC3B,IAAI,IAAI,CAAC,KAAK,KAAK,YAAY;YAC3B,IAAI,CAAC,cAAc;YACnB,IAAI,CAAC,SAAS,GAAG;QACrB,OACK,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM;YAC7B,IAAI,CAAC,SAAS,GAAG,MAAM,IAAI,CAAC,QAAQ;QACxC,OACK,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACtB,IAAI,CAAC,SAAS,GAAG,aAAa;QAClC;QACA,IAAI,IAAI,CAAC,KAAK,KAAK,cAAc,IAAI,CAAC,KAAK,GAAG,GAAG;YAC7C,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,kBAAkB;QAC7C;QACA,IAAI,CAAC,QAAQ,GAAG;QAChB;;;SAGC,GACD,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,MAAM,CAAC,KAAK;IACrB;IACA,QAAQ;QACJ,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,UAAU,CAAC,yKAAA,CAAA,OAAI,CAAC,GAAG;QACxB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,WAAW;IACpC;IACA,WAAW;QACP,IAAI,IAAI,CAAC,KAAK,KAAK,WAAW;YAC1B,IAAI,CAAC,IAAI;QACb;QACA,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA,SAAS;QACL,IAAI,CAAC,cAAc;QACnB,IAAI,CAAC,QAAQ;QACb,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,OAAO,CAAC,UAAU;IAC3B;IACA,SAAS;QACL,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,IAAI,CAAC;QACV,IAAI,CAAC,QAAQ;QACb,IAAI,CAAC,OAAO,CAAC,QAAQ;IACzB;IACA,WAAW;QACP,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,UAAU;QACf,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,GAAG;QACjC,2KAAA,CAAA,mBAAgB,CAAC,UAAU;IAC/B;IACA,aAAa;QACT,IAAI,CAAC,IAAI,CAAC,MAAM,EACZ;QACJ,IAAI,CAAC,MAAM,CAAC,IAAI;QAChB,IAAI,CAAC,MAAM,GAAG;IAClB;IACA,OAAO,UAAU,EAAE;QACf,IAAI,CAAC,SAAS,GAAG;QACjB,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY;IACjC;IACA,eAAe,QAAQ,EAAE;QACrB,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE;YAC3B,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG;YACpB,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG;YACpB,IAAI,CAAC,aAAa;QACtB;QACA,IAAI,CAAC,MAAM,EAAE;QACb,OAAO,SAAS,OAAO,CAAC,IAAI;IAChC;AACJ;AACA,0BAA0B;AAC1B,SAAS,aAAa,OAAO;IACzB,OAAO,IAAI,YAAY;AAC3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2508, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/animation/keyframes/utils/fill-wildcards.mjs"], "sourcesContent": ["function fillWildcards(keyframes) {\n    for (let i = 1; i < keyframes.length; i++) {\n        keyframes[i] ?? (keyframes[i] = keyframes[i - 1]);\n    }\n}\n\nexport { fillWildcards };\n"], "names": [], "mappings": ";;;AAAA,SAAS,cAAc,SAAS;IAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QACvC,SAAS,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,IAAI,EAAE;IACpD;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2523, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/render/dom/parse-transform.mjs"], "sourcesContent": ["const radToDeg = (rad) => (rad * 180) / Math.PI;\nconst rotate = (v) => {\n    const angle = radToDeg(Math.atan2(v[1], v[0]));\n    return rebaseAngle(angle);\n};\nconst matrix2dParsers = {\n    x: 4,\n    y: 5,\n    translateX: 4,\n    translateY: 5,\n    scaleX: 0,\n    scaleY: 3,\n    scale: (v) => (Math.abs(v[0]) + Math.abs(v[3])) / 2,\n    rotate,\n    rotateZ: rotate,\n    skewX: (v) => radToDeg(Math.atan(v[1])),\n    skewY: (v) => radToDeg(Math.atan(v[2])),\n    skew: (v) => (Math.abs(v[1]) + Math.abs(v[2])) / 2,\n};\nconst rebaseAngle = (angle) => {\n    angle = angle % 360;\n    if (angle < 0)\n        angle += 360;\n    return angle;\n};\nconst rotateZ = rotate;\nconst scaleX = (v) => Math.sqrt(v[0] * v[0] + v[1] * v[1]);\nconst scaleY = (v) => Math.sqrt(v[4] * v[4] + v[5] * v[5]);\nconst matrix3dParsers = {\n    x: 12,\n    y: 13,\n    z: 14,\n    translateX: 12,\n    translateY: 13,\n    translateZ: 14,\n    scaleX,\n    scaleY,\n    scale: (v) => (scaleX(v) + scaleY(v)) / 2,\n    rotateX: (v) => rebaseAngle(radToDeg(Math.atan2(v[6], v[5]))),\n    rotateY: (v) => rebaseAngle(radToDeg(Math.atan2(-v[2], v[0]))),\n    rotateZ,\n    rotate: rotateZ,\n    skewX: (v) => radToDeg(Math.atan(v[4])),\n    skewY: (v) => radToDeg(Math.atan(v[1])),\n    skew: (v) => (Math.abs(v[1]) + Math.abs(v[4])) / 2,\n};\nfunction defaultTransformValue(name) {\n    return name.includes(\"scale\") ? 1 : 0;\n}\nfunction parseValueFromTransform(transform, name) {\n    if (!transform || transform === \"none\") {\n        return defaultTransformValue(name);\n    }\n    const matrix3dMatch = transform.match(/^matrix3d\\(([-\\d.e\\s,]+)\\)$/u);\n    let parsers;\n    let match;\n    if (matrix3dMatch) {\n        parsers = matrix3dParsers;\n        match = matrix3dMatch;\n    }\n    else {\n        const matrix2dMatch = transform.match(/^matrix\\(([-\\d.e\\s,]+)\\)$/u);\n        parsers = matrix2dParsers;\n        match = matrix2dMatch;\n    }\n    if (!match) {\n        return defaultTransformValue(name);\n    }\n    const valueParser = parsers[name];\n    const values = match[1].split(\",\").map(convertTransformToNumber);\n    return typeof valueParser === \"function\"\n        ? valueParser(values)\n        : values[valueParser];\n}\nconst readTransformValue = (instance, name) => {\n    const { transform = \"none\" } = getComputedStyle(instance);\n    return parseValueFromTransform(transform, name);\n};\nfunction convertTransformToNumber(value) {\n    return parseFloat(value.trim());\n}\n\nexport { defaultTransformValue, parseValueFromTransform, readTransformValue };\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,WAAW,CAAC,MAAQ,AAAC,MAAM,MAAO,KAAK,EAAE;AAC/C,MAAM,SAAS,CAAC;IACZ,MAAM,QAAQ,SAAS,KAAK,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;IAC5C,OAAO,YAAY;AACvB;AACA,MAAM,kBAAkB;IACpB,GAAG;IACH,GAAG;IACH,YAAY;IACZ,YAAY;IACZ,QAAQ;IACR,QAAQ;IACR,OAAO,CAAC,IAAM,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI;IAClD;IACA,SAAS;IACT,OAAO,CAAC,IAAM,SAAS,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE;IACrC,OAAO,CAAC,IAAM,SAAS,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE;IACrC,MAAM,CAAC,IAAM,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI;AACrD;AACA,MAAM,cAAc,CAAC;IACjB,QAAQ,QAAQ;IAChB,IAAI,QAAQ,GACR,SAAS;IACb,OAAO;AACX;AACA,MAAM,UAAU;AAChB,MAAM,SAAS,CAAC,IAAM,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;AACzD,MAAM,SAAS,CAAC,IAAM,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;AACzD,MAAM,kBAAkB;IACpB,GAAG;IACH,GAAG;IACH,GAAG;IACH,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ;IACA;IACA,OAAO,CAAC,IAAM,CAAC,OAAO,KAAK,OAAO,EAAE,IAAI;IACxC,SAAS,CAAC,IAAM,YAAY,SAAS,KAAK,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;IAC1D,SAAS,CAAC,IAAM,YAAY,SAAS,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;IAC3D;IACA,QAAQ;IACR,OAAO,CAAC,IAAM,SAAS,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE;IACrC,OAAO,CAAC,IAAM,SAAS,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE;IACrC,MAAM,CAAC,IAAM,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI;AACrD;AACA,SAAS,sBAAsB,IAAI;IAC/B,OAAO,KAAK,QAAQ,CAAC,WAAW,IAAI;AACxC;AACA,SAAS,wBAAwB,SAAS,EAAE,IAAI;IAC5C,IAAI,CAAC,aAAa,cAAc,QAAQ;QACpC,OAAO,sBAAsB;IACjC;IACA,MAAM,gBAAgB,UAAU,KAAK,CAAC;IACtC,IAAI;IACJ,IAAI;IACJ,IAAI,eAAe;QACf,UAAU;QACV,QAAQ;IACZ,OACK;QACD,MAAM,gBAAgB,UAAU,KAAK,CAAC;QACtC,UAAU;QACV,QAAQ;IACZ;IACA,IAAI,CAAC,OAAO;QACR,OAAO,sBAAsB;IACjC;IACA,MAAM,cAAc,OAAO,CAAC,KAAK;IACjC,MAAM,SAAS,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC;IACvC,OAAO,OAAO,gBAAgB,aACxB,YAAY,UACZ,MAAM,CAAC,YAAY;AAC7B;AACA,MAAM,qBAAqB,CAAC,UAAU;IAClC,MAAM,EAAE,YAAY,MAAM,EAAE,GAAG,iBAAiB;IAChD,OAAO,wBAAwB,WAAW;AAC9C;AACA,SAAS,yBAAyB,KAAK;IACnC,OAAO,WAAW,MAAM,IAAI;AAChC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2612, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/animation/keyframes/utils/unit-conversion.mjs"], "sourcesContent": ["import { parseValueFromTransform } from '../../../render/dom/parse-transform.mjs';\nimport { transformPropOrder } from '../../../render/utils/keys-transform.mjs';\nimport { number } from '../../../value/types/numbers/index.mjs';\nimport { px } from '../../../value/types/numbers/units.mjs';\n\nconst isNumOrPxType = (v) => v === number || v === px;\nconst transformKeys = new Set([\"x\", \"y\", \"z\"]);\nconst nonTranslationalTransformKeys = transformPropOrder.filter((key) => !transformKeys.has(key));\nfunction removeNonTranslationalTransform(visualElement) {\n    const removedTransforms = [];\n    nonTranslationalTransformKeys.forEach((key) => {\n        const value = visualElement.getValue(key);\n        if (value !== undefined) {\n            removedTransforms.push([key, value.get()]);\n            value.set(key.startsWith(\"scale\") ? 1 : 0);\n        }\n    });\n    return removedTransforms;\n}\nconst positionalValues = {\n    // Dimensions\n    width: ({ x }, { paddingLeft = \"0\", paddingRight = \"0\" }) => x.max - x.min - parseFloat(paddingLeft) - parseFloat(paddingRight),\n    height: ({ y }, { paddingTop = \"0\", paddingBottom = \"0\" }) => y.max - y.min - parseFloat(paddingTop) - parseFloat(paddingBottom),\n    top: (_bbox, { top }) => parseFloat(top),\n    left: (_bbox, { left }) => parseFloat(left),\n    bottom: ({ y }, { top }) => parseFloat(top) + (y.max - y.min),\n    right: ({ x }, { left }) => parseFloat(left) + (x.max - x.min),\n    // Transform\n    x: (_bbox, { transform }) => parseValueFromTransform(transform, \"x\"),\n    y: (_bbox, { transform }) => parseValueFromTransform(transform, \"y\"),\n};\n// Alias translate longform names\npositionalValues.translateX = positionalValues.x;\npositionalValues.translateY = positionalValues.y;\n\nexport { isNumOrPxType, positionalValues, removeNonTranslationalTransform };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,CAAC,IAAM,MAAM,kLAAA,CAAA,SAAM,IAAI,MAAM,kLAAA,CAAA,KAAE;AACrD,MAAM,gBAAgB,IAAI,IAAI;IAAC;IAAK;IAAK;CAAI;AAC7C,MAAM,gCAAgC,oLAAA,CAAA,qBAAkB,CAAC,MAAM,CAAC,CAAC,MAAQ,CAAC,cAAc,GAAG,CAAC;AAC5F,SAAS,gCAAgC,aAAa;IAClD,MAAM,oBAAoB,EAAE;IAC5B,8BAA8B,OAAO,CAAC,CAAC;QACnC,MAAM,QAAQ,cAAc,QAAQ,CAAC;QACrC,IAAI,UAAU,WAAW;YACrB,kBAAkB,IAAI,CAAC;gBAAC;gBAAK,MAAM,GAAG;aAAG;YACzC,MAAM,GAAG,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI;QAC5C;IACJ;IACA,OAAO;AACX;AACA,MAAM,mBAAmB;IACrB,aAAa;IACb,OAAO,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,cAAc,GAAG,EAAE,eAAe,GAAG,EAAE,GAAK,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,WAAW,eAAe,WAAW;IAClH,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,aAAa,GAAG,EAAE,gBAAgB,GAAG,EAAE,GAAK,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,WAAW,cAAc,WAAW;IAClH,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,GAAK,WAAW;IACpC,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,GAAK,WAAW;IACtC,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,GAAK,WAAW,OAAO,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG;IAC5D,OAAO,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,GAAK,WAAW,QAAQ,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG;IAC7D,YAAY;IACZ,GAAG,CAAC,OAAO,EAAE,SAAS,EAAE,GAAK,CAAA,GAAA,mLAAA,CAAA,0BAAuB,AAAD,EAAE,WAAW;IAChE,GAAG,CAAC,OAAO,EAAE,SAAS,EAAE,GAAK,CAAA,GAAA,mLAAA,CAAA,0BAAuB,AAAD,EAAE,WAAW;AACpE;AACA,iCAAiC;AACjC,iBAAiB,UAAU,GAAG,iBAAiB,CAAC;AAChD,iBAAiB,UAAU,GAAG,iBAAiB,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2668, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/animation/keyframes/KeyframesResolver.mjs"], "sourcesContent": ["import { fillWildcards } from './utils/fill-wildcards.mjs';\nimport { removeNonTranslationalTransform } from './utils/unit-conversion.mjs';\nimport { frame } from '../../frameloop/frame.mjs';\n\nconst toResolve = new Set();\nlet isScheduled = false;\nlet anyNeedsMeasurement = false;\nlet isForced = false;\nfunction measureAllKeyframes() {\n    if (anyNeedsMeasurement) {\n        const resolversToMeasure = Array.from(toResolve).filter((resolver) => resolver.needsMeasurement);\n        const elementsToMeasure = new Set(resolversToMeasure.map((resolver) => resolver.element));\n        const transformsToRestore = new Map();\n        /**\n         * Write pass\n         * If we're measuring elements we want to remove bounding box-changing transforms.\n         */\n        elementsToMeasure.forEach((element) => {\n            const removedTransforms = removeNonTranslationalTransform(element);\n            if (!removedTransforms.length)\n                return;\n            transformsToRestore.set(element, removedTransforms);\n            element.render();\n        });\n        // Read\n        resolversToMeasure.forEach((resolver) => resolver.measureInitialState());\n        // Write\n        elementsToMeasure.forEach((element) => {\n            element.render();\n            const restore = transformsToRestore.get(element);\n            if (restore) {\n                restore.forEach(([key, value]) => {\n                    element.getValue(key)?.set(value);\n                });\n            }\n        });\n        // Read\n        resolversToMeasure.forEach((resolver) => resolver.measureEndState());\n        // Write\n        resolversToMeasure.forEach((resolver) => {\n            if (resolver.suspendedScrollY !== undefined) {\n                window.scrollTo(0, resolver.suspendedScrollY);\n            }\n        });\n    }\n    anyNeedsMeasurement = false;\n    isScheduled = false;\n    toResolve.forEach((resolver) => resolver.complete(isForced));\n    toResolve.clear();\n}\nfunction readAllKeyframes() {\n    toResolve.forEach((resolver) => {\n        resolver.readKeyframes();\n        if (resolver.needsMeasurement) {\n            anyNeedsMeasurement = true;\n        }\n    });\n}\nfunction flushKeyframeResolvers() {\n    isForced = true;\n    readAllKeyframes();\n    measureAllKeyframes();\n    isForced = false;\n}\nclass KeyframeResolver {\n    constructor(unresolvedKeyframes, onComplete, name, motionValue, element, isAsync = false) {\n        this.state = \"pending\";\n        /**\n         * Track whether this resolver is async. If it is, it'll be added to the\n         * resolver queue and flushed in the next frame. Resolvers that aren't going\n         * to trigger read/write thrashing don't need to be async.\n         */\n        this.isAsync = false;\n        /**\n         * Track whether this resolver needs to perform a measurement\n         * to resolve its keyframes.\n         */\n        this.needsMeasurement = false;\n        this.unresolvedKeyframes = [...unresolvedKeyframes];\n        this.onComplete = onComplete;\n        this.name = name;\n        this.motionValue = motionValue;\n        this.element = element;\n        this.isAsync = isAsync;\n    }\n    scheduleResolve() {\n        this.state = \"scheduled\";\n        if (this.isAsync) {\n            toResolve.add(this);\n            if (!isScheduled) {\n                isScheduled = true;\n                frame.read(readAllKeyframes);\n                frame.resolveKeyframes(measureAllKeyframes);\n            }\n        }\n        else {\n            this.readKeyframes();\n            this.complete();\n        }\n    }\n    readKeyframes() {\n        const { unresolvedKeyframes, name, element, motionValue } = this;\n        // If initial keyframe is null we need to read it from the DOM\n        if (unresolvedKeyframes[0] === null) {\n            const currentValue = motionValue?.get();\n            // TODO: This doesn't work if the final keyframe is a wildcard\n            const finalKeyframe = unresolvedKeyframes[unresolvedKeyframes.length - 1];\n            if (currentValue !== undefined) {\n                unresolvedKeyframes[0] = currentValue;\n            }\n            else if (element && name) {\n                const valueAsRead = element.readValue(name, finalKeyframe);\n                if (valueAsRead !== undefined && valueAsRead !== null) {\n                    unresolvedKeyframes[0] = valueAsRead;\n                }\n            }\n            if (unresolvedKeyframes[0] === undefined) {\n                unresolvedKeyframes[0] = finalKeyframe;\n            }\n            if (motionValue && currentValue === undefined) {\n                motionValue.set(unresolvedKeyframes[0]);\n            }\n        }\n        fillWildcards(unresolvedKeyframes);\n    }\n    setFinalKeyframe() { }\n    measureInitialState() { }\n    renderEndStyles() { }\n    measureEndState() { }\n    complete(isForcedComplete = false) {\n        this.state = \"complete\";\n        this.onComplete(this.unresolvedKeyframes, this.finalKeyframe, isForcedComplete);\n        toResolve.delete(this);\n    }\n    cancel() {\n        if (this.state === \"scheduled\") {\n            toResolve.delete(this);\n            this.state = \"pending\";\n        }\n    }\n    resume() {\n        if (this.state === \"pending\")\n            this.scheduleResolve();\n    }\n}\n\nexport { KeyframeResolver, flushKeyframeResolvers };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEA,MAAM,YAAY,IAAI;AACtB,IAAI,cAAc;AAClB,IAAI,sBAAsB;AAC1B,IAAI,WAAW;AACf,SAAS;IACL,IAAI,qBAAqB;QACrB,MAAM,qBAAqB,MAAM,IAAI,CAAC,WAAW,MAAM,CAAC,CAAC,WAAa,SAAS,gBAAgB;QAC/F,MAAM,oBAAoB,IAAI,IAAI,mBAAmB,GAAG,CAAC,CAAC,WAAa,SAAS,OAAO;QACvF,MAAM,sBAAsB,IAAI;QAChC;;;SAGC,GACD,kBAAkB,OAAO,CAAC,CAAC;YACvB,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,kCAA+B,AAAD,EAAE;YAC1D,IAAI,CAAC,kBAAkB,MAAM,EACzB;YACJ,oBAAoB,GAAG,CAAC,SAAS;YACjC,QAAQ,MAAM;QAClB;QACA,OAAO;QACP,mBAAmB,OAAO,CAAC,CAAC,WAAa,SAAS,mBAAmB;QACrE,QAAQ;QACR,kBAAkB,OAAO,CAAC,CAAC;YACvB,QAAQ,MAAM;YACd,MAAM,UAAU,oBAAoB,GAAG,CAAC;YACxC,IAAI,SAAS;gBACT,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;oBACzB,QAAQ,QAAQ,CAAC,MAAM,IAAI;gBAC/B;YACJ;QACJ;QACA,OAAO;QACP,mBAAmB,OAAO,CAAC,CAAC,WAAa,SAAS,eAAe;QACjE,QAAQ;QACR,mBAAmB,OAAO,CAAC,CAAC;YACxB,IAAI,SAAS,gBAAgB,KAAK,WAAW;gBACzC,OAAO,QAAQ,CAAC,GAAG,SAAS,gBAAgB;YAChD;QACJ;IACJ;IACA,sBAAsB;IACtB,cAAc;IACd,UAAU,OAAO,CAAC,CAAC,WAAa,SAAS,QAAQ,CAAC;IAClD,UAAU,KAAK;AACnB;AACA,SAAS;IACL,UAAU,OAAO,CAAC,CAAC;QACf,SAAS,aAAa;QACtB,IAAI,SAAS,gBAAgB,EAAE;YAC3B,sBAAsB;QAC1B;IACJ;AACJ;AACA,SAAS;IACL,WAAW;IACX;IACA;IACA,WAAW;AACf;AACA,MAAM;IACF,YAAY,mBAAmB,EAAE,UAAU,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,KAAK,CAAE;QACtF,IAAI,CAAC,KAAK,GAAG;QACb;;;;SAIC,GACD,IAAI,CAAC,OAAO,GAAG;QACf;;;SAGC,GACD,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,mBAAmB,GAAG;eAAI;SAAoB;QACnD,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,OAAO,GAAG;IACnB;IACA,kBAAkB;QACd,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,UAAU,GAAG,CAAC,IAAI;YAClB,IAAI,CAAC,aAAa;gBACd,cAAc;gBACd,kKAAA,CAAA,QAAK,CAAC,IAAI,CAAC;gBACX,kKAAA,CAAA,QAAK,CAAC,gBAAgB,CAAC;YAC3B;QACJ,OACK;YACD,IAAI,CAAC,aAAa;YAClB,IAAI,CAAC,QAAQ;QACjB;IACJ;IACA,gBAAgB;QACZ,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,IAAI;QAChE,8DAA8D;QAC9D,IAAI,mBAAmB,CAAC,EAAE,KAAK,MAAM;YACjC,MAAM,eAAe,aAAa;YAClC,8DAA8D;YAC9D,MAAM,gBAAgB,mBAAmB,CAAC,oBAAoB,MAAM,GAAG,EAAE;YACzE,IAAI,iBAAiB,WAAW;gBAC5B,mBAAmB,CAAC,EAAE,GAAG;YAC7B,OACK,IAAI,WAAW,MAAM;gBACtB,MAAM,cAAc,QAAQ,SAAS,CAAC,MAAM;gBAC5C,IAAI,gBAAgB,aAAa,gBAAgB,MAAM;oBACnD,mBAAmB,CAAC,EAAE,GAAG;gBAC7B;YACJ;YACA,IAAI,mBAAmB,CAAC,EAAE,KAAK,WAAW;gBACtC,mBAAmB,CAAC,EAAE,GAAG;YAC7B;YACA,IAAI,eAAe,iBAAiB,WAAW;gBAC3C,YAAY,GAAG,CAAC,mBAAmB,CAAC,EAAE;YAC1C;QACJ;QACA,CAAA,GAAA,oMAAA,CAAA,gBAAa,AAAD,EAAE;IAClB;IACA,mBAAmB,CAAE;IACrB,sBAAsB,CAAE;IACxB,kBAAkB,CAAE;IACpB,kBAAkB,CAAE;IACpB,SAAS,mBAAmB,KAAK,EAAE;QAC/B,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,aAAa,EAAE;QAC9D,UAAU,MAAM,CAAC,IAAI;IACzB;IACA,SAAS;QACL,IAAI,IAAI,CAAC,KAAK,KAAK,aAAa;YAC5B,UAAU,MAAM,CAAC,IAAI;YACrB,IAAI,CAAC,KAAK,GAAG;QACjB;IACJ;IACA,SAAS;QACL,IAAI,IAAI,CAAC,KAAK,KAAK,WACf,IAAI,CAAC,eAAe;IAC5B;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2821, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/render/dom/is-css-var.mjs"], "sourcesContent": ["const isCSSVar = (name) => name.startsWith(\"--\");\n\nexport { isCSSVar };\n"], "names": [], "mappings": ";;;AAAA,MAAM,WAAW,CAAC,OAAS,KAAK,UAAU,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2832, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/render/dom/style-set.mjs"], "sourcesContent": ["import { isCSSVar } from './is-css-var.mjs';\n\nfunction setStyle(element, name, value) {\n    isCSSVar(name)\n        ? element.style.setProperty(name, value)\n        : (element.style[name] = value);\n}\n\nexport { setStyle };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,SAAS,OAAO,EAAE,IAAI,EAAE,KAAK;IAClC,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD,EAAE,QACH,QAAQ,KAAK,CAAC,WAAW,CAAC,MAAM,SAC/B,QAAQ,KAAK,CAAC,KAAK,GAAG;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2847, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/utils/supports/scroll-timeline.mjs"], "sourcesContent": ["import { memo } from 'motion-utils';\n\nconst supportsScrollTimeline = /* @__PURE__ */ memo(() => window.ScrollTimeline !== undefined);\n\nexport { supportsScrollTimeline };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,yBAAyB,aAAa,GAAG,CAAA,GAAA,sJAAA,CAAA,OAAI,AAAD,EAAE,IAAM,OAAO,cAAc,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2860, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/utils/supports/flags.mjs"], "sourcesContent": ["/**\n * Add the ability for test suites to manually set support flags\n * to better test more environments.\n */\nconst supportsFlags = {};\n\nexport { supportsFlags };\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AACD,MAAM,gBAAgB,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2874, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/utils/supports/memo.mjs"], "sourcesContent": ["import { memo } from 'motion-utils';\nimport { supportsFlags } from './flags.mjs';\n\nfunction memoSupports(callback, supportsFlag) {\n    const memoized = memo(callback);\n    return () => supportsFlags[supportsFlag] ?? memoized();\n}\n\nexport { memoSupports };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,aAAa,QAAQ,EAAE,YAAY;IACxC,MAAM,WAAW,CAAA,GAAA,sJAAA,CAAA,OAAI,AAAD,EAAE;IACtB,OAAO,IAAM,0KAAA,CAAA,gBAAa,CAAC,aAAa,IAAI;AAChD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2892, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/utils/supports/linear-easing.mjs"], "sourcesContent": ["import { memoSupports } from './memo.mjs';\n\nconst supportsLinearEasing = /*@__PURE__*/ memoSupports(() => {\n    try {\n        document\n            .createElement(\"div\")\n            .animate({ opacity: 0 }, { easing: \"linear(0, 1)\" });\n    }\n    catch (e) {\n        return false;\n    }\n    return true;\n}, \"linearEasing\");\n\nexport { supportsLinearEasing };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,uBAAuB,WAAW,GAAG,CAAA,GAAA,yKAAA,CAAA,eAAY,AAAD,EAAE;IACpD,IAAI;QACA,SACK,aAAa,CAAC,OACd,OAAO,CAAC;YAAE,SAAS;QAAE,GAAG;YAAE,QAAQ;QAAe;IAC1D,EACA,OAAO,GAAG;QACN,OAAO;IACX;IACA,OAAO;AACX,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2916, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/animation/waapi/easing/cubic-bezier.mjs"], "sourcesContent": ["const cubicBezierAsString = ([a, b, c, d]) => `cubic-bezier(${a}, ${b}, ${c}, ${d})`;\n\nexport { cubicBezierAsString };\n"], "names": [], "mappings": ";;;AAAA,MAAM,sBAAsB,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,GAAK,CAAC,aAAa,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2927, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/animation/waapi/easing/supported.mjs"], "sourcesContent": ["import { cubicBezierAsString } from './cubic-bezier.mjs';\n\nconst supportedWaapiEasing = {\n    linear: \"linear\",\n    ease: \"ease\",\n    easeIn: \"ease-in\",\n    easeOut: \"ease-out\",\n    easeInOut: \"ease-in-out\",\n    circIn: /*@__PURE__*/ cubicBezierAsString([0, 0.65, 0.55, 1]),\n    circOut: /*@__PURE__*/ cubicBezierAsString([0.55, 0, 1, 0.45]),\n    backIn: /*@__PURE__*/ cubicBezierAsString([0.31, 0.01, 0.66, -0.59]),\n    backOut: /*@__PURE__*/ cubicBezierAsString([0.33, 1.53, 0.69, 0.99]),\n};\n\nexport { supportedWaapiEasing };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,uBAAuB;IACzB,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,SAAS;IACT,WAAW;IACX,QAAQ,WAAW,GAAG,CAAA,GAAA,+LAAA,CAAA,sBAAmB,AAAD,EAAE;QAAC;QAAG;QAAM;QAAM;KAAE;IAC5D,SAAS,WAAW,GAAG,CAAA,GAAA,+LAAA,CAAA,sBAAmB,AAAD,EAAE;QAAC;QAAM;QAAG;QAAG;KAAK;IAC7D,QAAQ,WAAW,GAAG,CAAA,GAAA,+LAAA,CAAA,sBAAmB,AAAD,EAAE;QAAC;QAAM;QAAM;QAAM,CAAC;KAAK;IACnE,SAAS,WAAW,GAAG,CAAA,GAAA,+LAAA,CAAA,sBAAmB,AAAD,EAAE;QAAC;QAAM;QAAM;QAAM;KAAK;AACvE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2970, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/animation/waapi/easing/map-easing.mjs"], "sourcesContent": ["import { isBezierDefinition } from 'motion-utils';\nimport { supportsLinearEasing } from '../../../utils/supports/linear-easing.mjs';\nimport { generateLinearEasing } from '../utils/linear.mjs';\nimport { cubicBezierAsString } from './cubic-bezier.mjs';\nimport { supportedWaapiEasing } from './supported.mjs';\n\nfunction mapEasingToNativeEasing(easing, duration) {\n    if (!easing) {\n        return undefined;\n    }\n    else if (typeof easing === \"function\") {\n        return supportsLinearEasing()\n            ? generateLinearEasing(easing, duration)\n            : \"ease-out\";\n    }\n    else if (isBezierDefinition(easing)) {\n        return cubicBezierAsString(easing);\n    }\n    else if (Array.isArray(easing)) {\n        return easing.map((segmentEasing) => mapEasingToNativeEasing(segmentEasing, duration) ||\n            supportedWaapiEasing.easeOut);\n    }\n    else {\n        return supportedWaapiEasing[easing];\n    }\n}\n\nexport { mapEasingToNativeEasing };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEA,SAAS,wBAAwB,MAAM,EAAE,QAAQ;IAC7C,IAAI,CAAC,QAAQ;QACT,OAAO;IACX,OACK,IAAI,OAAO,WAAW,YAAY;QACnC,OAAO,CAAA,GAAA,qLAAA,CAAA,uBAAoB,AAAD,MACpB,CAAA,GAAA,qLAAA,CAAA,uBAAoB,AAAD,EAAE,QAAQ,YAC7B;IACV,OACK,IAAI,CAAA,GAAA,+LAAA,CAAA,qBAAkB,AAAD,EAAE,SAAS;QACjC,OAAO,CAAA,GAAA,+LAAA,CAAA,sBAAmB,AAAD,EAAE;IAC/B,OACK,IAAI,MAAM,OAAO,CAAC,SAAS;QAC5B,OAAO,OAAO,GAAG,CAAC,CAAC,gBAAkB,wBAAwB,eAAe,aACxE,yLAAA,CAAA,uBAAoB,CAAC,OAAO;IACpC,OACK;QACD,OAAO,yLAAA,CAAA,uBAAoB,CAAC,OAAO;IACvC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3003, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/animation/waapi/start-waapi-animation.mjs"], "sourcesContent": ["import { activeAnimations } from '../../stats/animation-count.mjs';\nimport { statsBuffer } from '../../stats/buffer.mjs';\nimport { mapEasingToNativeEasing } from './easing/map-easing.mjs';\n\nfunction startWaapiAnimation(element, valueName, keyframes, { delay = 0, duration = 300, repeat = 0, repeatType = \"loop\", ease = \"easeOut\", times, } = {}, pseudoElement = undefined) {\n    const keyframeOptions = {\n        [valueName]: keyframes,\n    };\n    if (times)\n        keyframeOptions.offset = times;\n    const easing = mapEasingToNativeEasing(ease, duration);\n    /**\n     * If this is an easing array, apply to keyframes, not animation as a whole\n     */\n    if (Array.isArray(easing))\n        keyframeOptions.easing = easing;\n    if (statsBuffer.value) {\n        activeAnimations.waapi++;\n    }\n    const options = {\n        delay,\n        duration,\n        easing: !Array.isArray(easing) ? easing : \"linear\",\n        fill: \"both\",\n        iterations: repeat + 1,\n        direction: repeatType === \"reverse\" ? \"alternate\" : \"normal\",\n    };\n    if (pseudoElement)\n        options.pseudoElement = pseudoElement;\n    const animation = element.animate(keyframeOptions, options);\n    if (statsBuffer.value) {\n        animation.finished.finally(() => {\n            activeAnimations.waapi--;\n        });\n    }\n    return animation;\n}\n\nexport { startWaapiAnimation };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,SAAS,oBAAoB,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,EAAE,QAAQ,CAAC,EAAE,WAAW,GAAG,EAAE,SAAS,CAAC,EAAE,aAAa,MAAM,EAAE,OAAO,SAAS,EAAE,KAAK,EAAG,GAAG,CAAC,CAAC,EAAE,gBAAgB,SAAS;IAChL,MAAM,kBAAkB;QACpB,CAAC,UAAU,EAAE;IACjB;IACA,IAAI,OACA,gBAAgB,MAAM,GAAG;IAC7B,MAAM,SAAS,CAAA,GAAA,6LAAA,CAAA,0BAAuB,AAAD,EAAE,MAAM;IAC7C;;KAEC,GACD,IAAI,MAAM,OAAO,CAAC,SACd,gBAAgB,MAAM,GAAG;IAC7B,IAAI,+JAAA,CAAA,cAAW,CAAC,KAAK,EAAE;QACnB,2KAAA,CAAA,mBAAgB,CAAC,KAAK;IAC1B;IACA,MAAM,UAAU;QACZ;QACA;QACA,QAAQ,CAAC,MAAM,OAAO,CAAC,UAAU,SAAS;QAC1C,MAAM;QACN,YAAY,SAAS;QACrB,WAAW,eAAe,YAAY,cAAc;IACxD;IACA,IAAI,eACA,QAAQ,aAAa,GAAG;IAC5B,MAAM,YAAY,QAAQ,OAAO,CAAC,iBAAiB;IACnD,IAAI,+JAAA,CAAA,cAAW,CAAC,KAAK,EAAE;QACnB,UAAU,QAAQ,CAAC,OAAO,CAAC;YACvB,2KAAA,CAAA,mBAAgB,CAAC,KAAK;QAC1B;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3048, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/animation/generators/utils/is-generator.mjs"], "sourcesContent": ["function isGenerator(type) {\n    return typeof type === \"function\" && \"applyToOptions\" in type;\n}\n\nexport { isGenerator };\n"], "names": [], "mappings": ";;;AAAA,SAAS,YAAY,IAAI;IACrB,OAAO,OAAO,SAAS,cAAc,oBAAoB;AAC7D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3061, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/animation/waapi/utils/apply-generator.mjs"], "sourcesContent": ["import { supportsLinearEasing } from '../../../utils/supports/linear-easing.mjs';\nimport { isGenerator } from '../../generators/utils/is-generator.mjs';\n\nfunction applyGeneratorOptions({ type, ...options }) {\n    if (isGenerator(type) && supportsLinearEasing()) {\n        return type.applyToOptions(options);\n    }\n    else {\n        options.duration ?? (options.duration = 300);\n        options.ease ?? (options.ease = \"easeOut\");\n    }\n    return options;\n}\n\nexport { applyGeneratorOptions };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,sBAAsB,EAAE,IAAI,EAAE,GAAG,SAAS;IAC/C,IAAI,CAAA,GAAA,mMAAA,CAAA,cAAW,AAAD,EAAE,SAAS,CAAA,GAAA,qLAAA,CAAA,uBAAoB,AAAD,KAAK;QAC7C,OAAO,KAAK,cAAc,CAAC;IAC/B,OACK;QACD,QAAQ,QAAQ,IAAI,CAAC,QAAQ,QAAQ,GAAG,GAAG;QAC3C,QAAQ,IAAI,IAAI,CAAC,QAAQ,IAAI,GAAG,SAAS;IAC7C;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3084, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/animation/NativeAnimation.mjs"], "sourcesContent": ["import { invariant, millisecondsToSeconds, secondsToMilliseconds, noop } from 'motion-utils';\nimport { setStyle } from '../render/dom/style-set.mjs';\nimport { supportsScrollTimeline } from '../utils/supports/scroll-timeline.mjs';\nimport { getFinalKeyframe } from './keyframes/get-final.mjs';\nimport { WithPromise } from './utils/WithPromise.mjs';\nimport { startWaapiAnimation } from './waapi/start-waapi-animation.mjs';\nimport { applyGeneratorOptions } from './waapi/utils/apply-generator.mjs';\n\n/**\n * NativeAnimation implements AnimationPlaybackControls for the browser's Web Animations API.\n */\nclass NativeAnimation extends WithPromise {\n    constructor(options) {\n        super();\n        this.finishedTime = null;\n        this.isStopped = false;\n        if (!options)\n            return;\n        const { element, name, keyframes, pseudoElement, allowFlatten = false, finalKeyframe, onComplete, } = options;\n        this.isPseudoElement = Boolean(pseudoElement);\n        this.allowFlatten = allowFlatten;\n        this.options = options;\n        invariant(typeof options.type !== \"string\", `Mini animate() doesn't support \"type\" as a string.`, \"mini-spring\");\n        const transition = applyGeneratorOptions(options);\n        this.animation = startWaapiAnimation(element, name, keyframes, transition, pseudoElement);\n        if (transition.autoplay === false) {\n            this.animation.pause();\n        }\n        this.animation.onfinish = () => {\n            this.finishedTime = this.time;\n            if (!pseudoElement) {\n                const keyframe = getFinalKeyframe(keyframes, this.options, finalKeyframe, this.speed);\n                if (this.updateMotionValue) {\n                    this.updateMotionValue(keyframe);\n                }\n                else {\n                    /**\n                     * If we can, we want to commit the final style as set by the user,\n                     * rather than the computed keyframe value supplied by the animation.\n                     */\n                    setStyle(element, name, keyframe);\n                }\n                this.animation.cancel();\n            }\n            onComplete?.();\n            this.notifyFinished();\n        };\n    }\n    play() {\n        if (this.isStopped)\n            return;\n        this.animation.play();\n        if (this.state === \"finished\") {\n            this.updateFinished();\n        }\n    }\n    pause() {\n        this.animation.pause();\n    }\n    complete() {\n        this.animation.finish?.();\n    }\n    cancel() {\n        try {\n            this.animation.cancel();\n        }\n        catch (e) { }\n    }\n    stop() {\n        if (this.isStopped)\n            return;\n        this.isStopped = true;\n        const { state } = this;\n        if (state === \"idle\" || state === \"finished\") {\n            return;\n        }\n        if (this.updateMotionValue) {\n            this.updateMotionValue();\n        }\n        else {\n            this.commitStyles();\n        }\n        if (!this.isPseudoElement)\n            this.cancel();\n    }\n    /**\n     * WAAPI doesn't natively have any interruption capabilities.\n     *\n     * In this method, we commit styles back to the DOM before cancelling\n     * the animation.\n     *\n     * This is designed to be overridden by NativeAnimationExtended, which\n     * will create a renderless JS animation and sample it twice to calculate\n     * its current value, \"previous\" value, and therefore allow\n     * Motion to also correctly calculate velocity for any subsequent animation\n     * while deferring the commit until the next animation frame.\n     */\n    commitStyles() {\n        if (!this.isPseudoElement) {\n            this.animation.commitStyles?.();\n        }\n    }\n    get duration() {\n        const duration = this.animation.effect?.getComputedTiming?.().duration || 0;\n        return millisecondsToSeconds(Number(duration));\n    }\n    get time() {\n        return millisecondsToSeconds(Number(this.animation.currentTime) || 0);\n    }\n    set time(newTime) {\n        this.finishedTime = null;\n        this.animation.currentTime = secondsToMilliseconds(newTime);\n    }\n    /**\n     * The playback speed of the animation.\n     * 1 = normal speed, 2 = double speed, 0.5 = half speed.\n     */\n    get speed() {\n        return this.animation.playbackRate;\n    }\n    set speed(newSpeed) {\n        // Allow backwards playback after finishing\n        if (newSpeed < 0)\n            this.finishedTime = null;\n        this.animation.playbackRate = newSpeed;\n    }\n    get state() {\n        return this.finishedTime !== null\n            ? \"finished\"\n            : this.animation.playState;\n    }\n    get startTime() {\n        return Number(this.animation.startTime);\n    }\n    set startTime(newStartTime) {\n        this.animation.startTime = newStartTime;\n    }\n    /**\n     * Attaches a timeline to the animation, for instance the `ScrollTimeline`.\n     */\n    attachTimeline({ timeline, observe }) {\n        if (this.allowFlatten) {\n            this.animation.effect?.updateTiming({ easing: \"linear\" });\n        }\n        this.animation.onfinish = null;\n        if (timeline && supportsScrollTimeline()) {\n            this.animation.timeline = timeline;\n            return noop;\n        }\n        else {\n            return observe(this);\n        }\n    }\n}\n\nexport { NativeAnimation };\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEA;;CAEC,GACD,MAAM,wBAAwB,iLAAA,CAAA,cAAW;IACrC,YAAY,OAAO,CAAE;QACjB,KAAK;QACL,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,SACD;QACJ,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,aAAa,EAAE,eAAe,KAAK,EAAE,aAAa,EAAE,UAAU,EAAG,GAAG;QACtG,IAAI,CAAC,eAAe,GAAG,QAAQ;QAC/B,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,OAAO,GAAG;QACf,CAAA,GAAA,wJAAA,CAAA,YAAS,AAAD,EAAE,OAAO,QAAQ,IAAI,KAAK,UAAU,CAAC,kDAAkD,CAAC,EAAE;QAClG,MAAM,aAAa,CAAA,GAAA,iMAAA,CAAA,wBAAqB,AAAD,EAAE;QACzC,IAAI,CAAC,SAAS,GAAG,CAAA,GAAA,iMAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS,MAAM,WAAW,YAAY;QAC3E,IAAI,WAAW,QAAQ,KAAK,OAAO;YAC/B,IAAI,CAAC,SAAS,CAAC,KAAK;QACxB;QACA,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG;YACtB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,IAAI;YAC7B,IAAI,CAAC,eAAe;gBAChB,MAAM,WAAW,CAAA,GAAA,sLAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,IAAI,CAAC,OAAO,EAAE,eAAe,IAAI,CAAC,KAAK;gBACpF,IAAI,IAAI,CAAC,iBAAiB,EAAE;oBACxB,IAAI,CAAC,iBAAiB,CAAC;gBAC3B,OACK;oBACD;;;qBAGC,GACD,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,MAAM;gBAC5B;gBACA,IAAI,CAAC,SAAS,CAAC,MAAM;YACzB;YACA;YACA,IAAI,CAAC,cAAc;QACvB;IACJ;IACA,OAAO;QACH,IAAI,IAAI,CAAC,SAAS,EACd;QACJ,IAAI,CAAC,SAAS,CAAC,IAAI;QACnB,IAAI,IAAI,CAAC,KAAK,KAAK,YAAY;YAC3B,IAAI,CAAC,cAAc;QACvB;IACJ;IACA,QAAQ;QACJ,IAAI,CAAC,SAAS,CAAC,KAAK;IACxB;IACA,WAAW;QACP,IAAI,CAAC,SAAS,CAAC,MAAM;IACzB;IACA,SAAS;QACL,IAAI;YACA,IAAI,CAAC,SAAS,CAAC,MAAM;QACzB,EACA,OAAO,GAAG,CAAE;IAChB;IACA,OAAO;QACH,IAAI,IAAI,CAAC,SAAS,EACd;QACJ,IAAI,CAAC,SAAS,GAAG;QACjB,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI;QACtB,IAAI,UAAU,UAAU,UAAU,YAAY;YAC1C;QACJ;QACA,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,iBAAiB;QAC1B,OACK;YACD,IAAI,CAAC,YAAY;QACrB;QACA,IAAI,CAAC,IAAI,CAAC,eAAe,EACrB,IAAI,CAAC,MAAM;IACnB;IACA;;;;;;;;;;;KAWC,GACD,eAAe;QACX,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACvB,IAAI,CAAC,SAAS,CAAC,YAAY;QAC/B;IACJ;IACA,IAAI,WAAW;QACX,MAAM,WAAW,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,sBAAsB,YAAY;QAC1E,OAAO,CAAA,GAAA,oKAAA,CAAA,wBAAqB,AAAD,EAAE,OAAO;IACxC;IACA,IAAI,OAAO;QACP,OAAO,CAAA,GAAA,oKAAA,CAAA,wBAAqB,AAAD,EAAE,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,KAAK;IACvE;IACA,IAAI,KAAK,OAAO,EAAE;QACd,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,CAAA,GAAA,oKAAA,CAAA,wBAAqB,AAAD,EAAE;IACvD;IACA;;;KAGC,GACD,IAAI,QAAQ;QACR,OAAO,IAAI,CAAC,SAAS,CAAC,YAAY;IACtC;IACA,IAAI,MAAM,QAAQ,EAAE;QAChB,2CAA2C;QAC3C,IAAI,WAAW,GACX,IAAI,CAAC,YAAY,GAAG;QACxB,IAAI,CAAC,SAAS,CAAC,YAAY,GAAG;IAClC;IACA,IAAI,QAAQ;QACR,OAAO,IAAI,CAAC,YAAY,KAAK,OACvB,aACA,IAAI,CAAC,SAAS,CAAC,SAAS;IAClC;IACA,IAAI,YAAY;QACZ,OAAO,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS;IAC1C;IACA,IAAI,UAAU,YAAY,EAAE;QACxB,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG;IAC/B;IACA;;KAEC,GACD,eAAe,EAAE,QAAQ,EAAE,OAAO,EAAE,EAAE;QAClC,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,aAAa;gBAAE,QAAQ;YAAS;QAC3D;QACA,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG;QAC1B,IAAI,YAAY,CAAA,GAAA,uLAAA,CAAA,yBAAsB,AAAD,KAAK;YACtC,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG;YAC1B,OAAO,sJAAA,CAAA,OAAI;QACf,OACK;YACD,OAAO,QAAQ,IAAI;QACvB;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3242, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/animation/waapi/utils/unsupported-easing.mjs"], "sourcesContent": ["import { anticipate, backInOut, circInOut } from 'motion-utils';\n\nconst unsupportedEasingFunctions = {\n    anticipate,\n    backInOut,\n    circInOut,\n};\nfunction isUnsupportedEase(key) {\n    return key in unsupportedEasingFunctions;\n}\nfunction replaceStringEasing(transition) {\n    if (typeof transition.ease === \"string\" &&\n        isUnsupportedEase(transition.ease)) {\n        transition.ease = unsupportedEasingFunctions[transition.ease];\n    }\n}\n\nexport { replaceStringEasing };\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;;AAEA,MAAM,6BAA6B;IAC/B,YAAA,sKAAA,CAAA,aAAU;IACV,WAAA,gKAAA,CAAA,YAAS;IACT,WAAA,gKAAA,CAAA,YAAS;AACb;AACA,SAAS,kBAAkB,GAAG;IAC1B,OAAO,OAAO;AAClB;AACA,SAAS,oBAAoB,UAAU;IACnC,IAAI,OAAO,WAAW,IAAI,KAAK,YAC3B,kBAAkB,WAAW,IAAI,GAAG;QACpC,WAAW,IAAI,GAAG,0BAA0B,CAAC,WAAW,IAAI,CAAC;IACjE;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3269, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/animation/NativeAnimationExtended.mjs"], "sourcesContent": ["import { secondsToMilliseconds } from 'motion-utils';\nimport { JSAnimation } from './JSAnimation.mjs';\nimport { NativeAnimation } from './NativeAnimation.mjs';\nimport { replaceTransitionType } from './utils/replace-transition-type.mjs';\nimport { replaceStringEasing } from './waapi/utils/unsupported-easing.mjs';\n\n/**\n * 10ms is chosen here as it strikes a balance between smooth\n * results (more than one keyframe per frame at 60fps) and\n * keyframe quantity.\n */\nconst sampleDelta = 10; //ms\nclass NativeAnimationExtended extends NativeAnimation {\n    constructor(options) {\n        /**\n         * The base NativeAnimation function only supports a subset\n         * of Motion easings, and WAAPI also only supports some\n         * easing functions via string/cubic-bezier definitions.\n         *\n         * This function replaces those unsupported easing functions\n         * with a JS easing function. This will later get compiled\n         * to a linear() easing function.\n         */\n        replaceStringEasing(options);\n        /**\n         * Ensure we replace the transition type with a generator function\n         * before passing to WAAPI.\n         *\n         * TODO: Does this have a better home? It could be shared with\n         * JSAnimation.\n         */\n        replaceTransitionType(options);\n        super(options);\n        if (options.startTime) {\n            this.startTime = options.startTime;\n        }\n        this.options = options;\n    }\n    /**\n     * WAAPI doesn't natively have any interruption capabilities.\n     *\n     * Rather than read commited styles back out of the DOM, we can\n     * create a renderless JS animation and sample it twice to calculate\n     * its current value, \"previous\" value, and therefore allow\n     * Motion to calculate velocity for any subsequent animation.\n     */\n    updateMotionValue(value) {\n        const { motionValue, onUpdate, onComplete, element, ...options } = this.options;\n        if (!motionValue)\n            return;\n        if (value !== undefined) {\n            motionValue.set(value);\n            return;\n        }\n        const sampleAnimation = new JSAnimation({\n            ...options,\n            autoplay: false,\n        });\n        const sampleTime = secondsToMilliseconds(this.finishedTime ?? this.time);\n        motionValue.setWithVelocity(sampleAnimation.sample(sampleTime - sampleDelta).value, sampleAnimation.sample(sampleTime).value, sampleDelta);\n        sampleAnimation.stop();\n    }\n}\n\nexport { NativeAnimationExtended };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEA;;;;CAIC,GACD,MAAM,cAAc,IAAI,IAAI;AAC5B,MAAM,gCAAgC,4KAAA,CAAA,kBAAe;IACjD,YAAY,OAAO,CAAE;QACjB;;;;;;;;SAQC,GACD,CAAA,GAAA,oMAAA,CAAA,sBAAmB,AAAD,EAAE;QACpB;;;;;;SAMC,GACD,CAAA,GAAA,mMAAA,CAAA,wBAAqB,AAAD,EAAE;QACtB,KAAK,CAAC;QACN,IAAI,QAAQ,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,QAAQ,SAAS;QACtC;QACA,IAAI,CAAC,OAAO,GAAG;IACnB;IACA;;;;;;;KAOC,GACD,kBAAkB,KAAK,EAAE;QACrB,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,SAAS,GAAG,IAAI,CAAC,OAAO;QAC/E,IAAI,CAAC,aACD;QACJ,IAAI,UAAU,WAAW;YACrB,YAAY,GAAG,CAAC;YAChB;QACJ;QACA,MAAM,kBAAkB,IAAI,wKAAA,CAAA,cAAW,CAAC;YACpC,GAAG,OAAO;YACV,UAAU;QACd;QACA,MAAM,aAAa,CAAA,GAAA,oKAAA,CAAA,wBAAqB,AAAD,EAAE,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,IAAI;QACvE,YAAY,eAAe,CAAC,gBAAgB,MAAM,CAAC,aAAa,aAAa,KAAK,EAAE,gBAAgB,MAAM,CAAC,YAAY,KAAK,EAAE;QAC9H,gBAAgB,IAAI;IACxB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3341, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/animation/utils/is-animatable.mjs"], "sourcesContent": ["import { complex } from '../../value/types/complex/index.mjs';\n\n/**\n * Check if a value is animatable. Examples:\n *\n * ✅: 100, \"100px\", \"#fff\"\n * ❌: \"block\", \"url(2.jpg)\"\n * @param value\n *\n * @internal\n */\nconst isAnimatable = (value, name) => {\n    // If the list of keys that might be non-animatable grows, replace with Set\n    if (name === \"zIndex\")\n        return false;\n    // If it's a number or a keyframes array, we can animate it. We might at some point\n    // need to do a deep isAnimatable check of keyframes, or let Popmotion handle this,\n    // but for now lets leave it like this for performance reasons\n    if (typeof value === \"number\" || Array.isArray(value))\n        return true;\n    if (typeof value === \"string\" && // It's animatable if we have a string\n        (complex.test(value) || value === \"0\") && // And it contains numbers and/or colors\n        !value.startsWith(\"url(\") // Unless it starts with \"url(\"\n    ) {\n        return true;\n    }\n    return false;\n};\n\nexport { isAnimatable };\n"], "names": [], "mappings": ";;;AAAA;;AAEA;;;;;;;;CAQC,GACD,MAAM,eAAe,CAAC,OAAO;IACzB,2EAA2E;IAC3E,IAAI,SAAS,UACT,OAAO;IACX,mFAAmF;IACnF,mFAAmF;IACnF,8DAA8D;IAC9D,IAAI,OAAO,UAAU,YAAY,MAAM,OAAO,CAAC,QAC3C,OAAO;IACX,IAAI,OAAO,UAAU,YAAY,sCAAsC;IACnE,CAAC,kLAAA,CAAA,UAAO,CAAC,IAAI,CAAC,UAAU,UAAU,GAAG,KAAK,wCAAwC;IAClF,CAAC,MAAM,UAAU,CAAC,QAAQ,+BAA+B;MAC3D;QACE,OAAO;IACX;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3376, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/animation/utils/can-animate.mjs"], "sourcesContent": ["import { warning } from 'motion-utils';\nimport { isGenerator } from '../generators/utils/is-generator.mjs';\nimport { isAnimatable } from './is-animatable.mjs';\n\nfunction hasKeyframesChanged(keyframes) {\n    const current = keyframes[0];\n    if (keyframes.length === 1)\n        return true;\n    for (let i = 0; i < keyframes.length; i++) {\n        if (keyframes[i] !== current)\n            return true;\n    }\n}\nfunction canAnimate(keyframes, name, type, velocity) {\n    /**\n     * Check if we're able to animate between the start and end keyframes,\n     * and throw a warning if we're attempting to animate between one that's\n     * animatable and another that isn't.\n     */\n    const originKeyframe = keyframes[0];\n    if (originKeyframe === null)\n        return false;\n    /**\n     * These aren't traditionally animatable but we do support them.\n     * In future we could look into making this more generic or replacing\n     * this function with mix() === mixImmediate\n     */\n    if (name === \"display\" || name === \"visibility\")\n        return true;\n    const targetKeyframe = keyframes[keyframes.length - 1];\n    const isOriginAnimatable = isAnimatable(originKeyframe, name);\n    const isTargetAnimatable = isAnimatable(targetKeyframe, name);\n    warning(isOriginAnimatable === isTargetAnimatable, `You are trying to animate ${name} from \"${originKeyframe}\" to \"${targetKeyframe}\". \"${isOriginAnimatable ? targetKeyframe : originKeyframe}\" is not an animatable value.`, \"value-not-animatable\");\n    // Always skip if any of these are true\n    if (!isOriginAnimatable || !isTargetAnimatable) {\n        return false;\n    }\n    return (hasKeyframesChanged(keyframes) ||\n        ((type === \"spring\" || isGenerator(type)) && velocity));\n}\n\nexport { canAnimate };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,SAAS,oBAAoB,SAAS;IAClC,MAAM,UAAU,SAAS,CAAC,EAAE;IAC5B,IAAI,UAAU,MAAM,KAAK,GACrB,OAAO;IACX,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QACvC,IAAI,SAAS,CAAC,EAAE,KAAK,SACjB,OAAO;IACf;AACJ;AACA,SAAS,WAAW,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ;IAC/C;;;;KAIC,GACD,MAAM,iBAAiB,SAAS,CAAC,EAAE;IACnC,IAAI,mBAAmB,MACnB,OAAO;IACX;;;;KAIC,GACD,IAAI,SAAS,aAAa,SAAS,cAC/B,OAAO;IACX,MAAM,iBAAiB,SAAS,CAAC,UAAU,MAAM,GAAG,EAAE;IACtD,MAAM,qBAAqB,CAAA,GAAA,sLAAA,CAAA,eAAY,AAAD,EAAE,gBAAgB;IACxD,MAAM,qBAAqB,CAAA,GAAA,sLAAA,CAAA,eAAY,AAAD,EAAE,gBAAgB;IACxD,CAAA,GAAA,wJAAA,CAAA,UAAO,AAAD,EAAE,uBAAuB,oBAAoB,CAAC,0BAA0B,EAAE,KAAK,OAAO,EAAE,eAAe,MAAM,EAAE,eAAe,IAAI,EAAE,qBAAqB,iBAAiB,eAAe,6BAA6B,CAAC,EAAE;IAC/N,uCAAuC;IACvC,IAAI,CAAC,sBAAsB,CAAC,oBAAoB;QAC5C,OAAO;IACX;IACA,OAAQ,oBAAoB,cACvB,CAAC,SAAS,YAAY,CAAA,GAAA,mMAAA,CAAA,cAAW,AAAD,EAAE,KAAK,KAAK;AACrD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3421, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/utils/is-html-element.mjs"], "sourcesContent": ["import { isObject } from 'motion-utils';\n\n/**\n * Checks if an element is an HTML element in a way\n * that works across iframes\n */\nfunction isHTMLElement(element) {\n    return isObject(element) && \"offsetHeight\" in element;\n}\n\nexport { isHTMLElement };\n"], "names": [], "mappings": ";;;AAAA;;AAEA;;;CAGC,GACD,SAAS,cAAc,OAAO;IAC1B,OAAO,CAAA,GAAA,8JAAA,CAAA,WAAQ,AAAD,EAAE,YAAY,kBAAkB;AAClD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3439, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/animation/waapi/supports/waapi.mjs"], "sourcesContent": ["import { memo } from 'motion-utils';\nimport { isHTMLElement } from '../../../utils/is-html-element.mjs';\n\n/**\n * A list of values that can be hardware-accelerated.\n */\nconst acceleratedValues = new Set([\n    \"opacity\",\n    \"clipPath\",\n    \"filter\",\n    \"transform\",\n    // TODO: Could be re-enabled now we have support for linear() easing\n    // \"background-color\"\n]);\nconst supportsWaapi = /*@__PURE__*/ memo(() => Object.hasOwnProperty.call(Element.prototype, \"animate\"));\nfunction supportsBrowserAnimation(options) {\n    const { motionValue, name, repeatDelay, repeatType, damping, type } = options;\n    if (!isHTMLElement(motionValue?.owner?.current)) {\n        return false;\n    }\n    const { onUpdate, transformTemplate } = motionValue.owner.getProps();\n    return (supportsWaapi() &&\n        name &&\n        acceleratedValues.has(name) &&\n        (name !== \"transform\" || !transformTemplate) &&\n        /**\n         * If we're outputting values to onUpdate then we can't use WAAPI as there's\n         * no way to read the value from WAAPI every frame.\n         */\n        !onUpdate &&\n        !repeatDelay &&\n        repeatType !== \"mirror\" &&\n        damping !== 0 &&\n        type !== \"inertia\");\n}\n\nexport { supportsBrowserAnimation };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA;;CAEC,GACD,MAAM,oBAAoB,IAAI,IAAI;IAC9B;IACA;IACA;IACA;CAGH;AACD,MAAM,gBAAgB,WAAW,GAAG,CAAA,GAAA,sJAAA,CAAA,OAAI,AAAD,EAAE,IAAM,OAAO,cAAc,CAAC,IAAI,CAAC,QAAQ,SAAS,EAAE;AAC7F,SAAS,yBAAyB,OAAO;IACrC,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG;IACtE,IAAI,CAAC,CAAA,GAAA,8KAAA,CAAA,gBAAa,AAAD,EAAE,aAAa,OAAO,UAAU;QAC7C,OAAO;IACX;IACA,MAAM,EAAE,QAAQ,EAAE,iBAAiB,EAAE,GAAG,YAAY,KAAK,CAAC,QAAQ;IAClE,OAAQ,mBACJ,QACA,kBAAkB,GAAG,CAAC,SACtB,CAAC,SAAS,eAAe,CAAC,iBAAiB,KAC3C;;;SAGC,GACD,CAAC,YACD,CAAC,eACD,eAAe,YACf,YAAY,KACZ,SAAS;AACjB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3473, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/animation/AsyncMotionValueAnimation.mjs"], "sourcesContent": ["import { MotionGlobalConfig, noop } from 'motion-utils';\nimport { time } from '../frameloop/sync-time.mjs';\nimport { JSAnimation } from './JSAnimation.mjs';\nimport { getFinalKeyframe } from './keyframes/get-final.mjs';\nimport { KeyframeResolver, flushKeyframeResolvers } from './keyframes/KeyframesResolver.mjs';\nimport { NativeAnimationExtended } from './NativeAnimationExtended.mjs';\nimport { canAnimate } from './utils/can-animate.mjs';\nimport { WithPromise } from './utils/WithPromise.mjs';\nimport { supportsBrowserAnimation } from './waapi/supports/waapi.mjs';\n\n/**\n * Maximum time allowed between an animation being created and it being\n * resolved for us to use the latter as the start time.\n *\n * This is to ensure that while we prefer to \"start\" an animation as soon\n * as it's triggered, we also want to avoid a visual jump if there's a big delay\n * between these two moments.\n */\nconst MAX_RESOLVE_DELAY = 40;\nclass AsyncMotionValueAnimation extends WithPromise {\n    constructor({ autoplay = true, delay = 0, type = \"keyframes\", repeat = 0, repeatDelay = 0, repeatType = \"loop\", keyframes, name, motionValue, element, ...options }) {\n        super();\n        /**\n         * Bound to support return animation.stop pattern\n         */\n        this.stop = () => {\n            if (this._animation) {\n                this._animation.stop();\n                this.stopTimeline?.();\n            }\n            this.keyframeResolver?.cancel();\n        };\n        this.createdAt = time.now();\n        const optionsWithDefaults = {\n            autoplay,\n            delay,\n            type,\n            repeat,\n            repeatDelay,\n            repeatType,\n            name,\n            motionValue,\n            element,\n            ...options,\n        };\n        const KeyframeResolver$1 = element?.KeyframeResolver || KeyframeResolver;\n        this.keyframeResolver = new KeyframeResolver$1(keyframes, (resolvedKeyframes, finalKeyframe, forced) => this.onKeyframesResolved(resolvedKeyframes, finalKeyframe, optionsWithDefaults, !forced), name, motionValue, element);\n        this.keyframeResolver?.scheduleResolve();\n    }\n    onKeyframesResolved(keyframes, finalKeyframe, options, sync) {\n        this.keyframeResolver = undefined;\n        const { name, type, velocity, delay, isHandoff, onUpdate } = options;\n        this.resolvedAt = time.now();\n        /**\n         * If we can't animate this value with the resolved keyframes\n         * then we should complete it immediately.\n         */\n        if (!canAnimate(keyframes, name, type, velocity)) {\n            if (MotionGlobalConfig.instantAnimations || !delay) {\n                onUpdate?.(getFinalKeyframe(keyframes, options, finalKeyframe));\n            }\n            keyframes[0] = keyframes[keyframes.length - 1];\n            options.duration = 0;\n            options.repeat = 0;\n        }\n        /**\n         * Resolve startTime for the animation.\n         *\n         * This method uses the createdAt and resolvedAt to calculate the\n         * animation startTime. *Ideally*, we would use the createdAt time as t=0\n         * as the following frame would then be the first frame of the animation in\n         * progress, which would feel snappier.\n         *\n         * However, if there's a delay (main thread work) between the creation of\n         * the animation and the first commited frame, we prefer to use resolvedAt\n         * to avoid a sudden jump into the animation.\n         */\n        const startTime = sync\n            ? !this.resolvedAt\n                ? this.createdAt\n                : this.resolvedAt - this.createdAt > MAX_RESOLVE_DELAY\n                    ? this.resolvedAt\n                    : this.createdAt\n            : undefined;\n        const resolvedOptions = {\n            startTime,\n            finalKeyframe,\n            ...options,\n            keyframes,\n        };\n        /**\n         * Animate via WAAPI if possible. If this is a handoff animation, the optimised animation will be running via\n         * WAAPI. Therefore, this animation must be JS to ensure it runs \"under\" the\n         * optimised animation.\n         */\n        const animation = !isHandoff && supportsBrowserAnimation(resolvedOptions)\n            ? new NativeAnimationExtended({\n                ...resolvedOptions,\n                element: resolvedOptions.motionValue.owner.current,\n            })\n            : new JSAnimation(resolvedOptions);\n        animation.finished.then(() => this.notifyFinished()).catch(noop);\n        if (this.pendingTimeline) {\n            this.stopTimeline = animation.attachTimeline(this.pendingTimeline);\n            this.pendingTimeline = undefined;\n        }\n        this._animation = animation;\n    }\n    get finished() {\n        if (!this._animation) {\n            return this._finished;\n        }\n        else {\n            return this.animation.finished;\n        }\n    }\n    then(onResolve, _onReject) {\n        return this.finished.finally(onResolve).then(() => { });\n    }\n    get animation() {\n        if (!this._animation) {\n            this.keyframeResolver?.resume();\n            flushKeyframeResolvers();\n        }\n        return this._animation;\n    }\n    get duration() {\n        return this.animation.duration;\n    }\n    get time() {\n        return this.animation.time;\n    }\n    set time(newTime) {\n        this.animation.time = newTime;\n    }\n    get speed() {\n        return this.animation.speed;\n    }\n    get state() {\n        return this.animation.state;\n    }\n    set speed(newSpeed) {\n        this.animation.speed = newSpeed;\n    }\n    get startTime() {\n        return this.animation.startTime;\n    }\n    attachTimeline(timeline) {\n        if (this._animation) {\n            this.stopTimeline = this.animation.attachTimeline(timeline);\n        }\n        else {\n            this.pendingTimeline = timeline;\n        }\n        return () => this.stop();\n    }\n    play() {\n        this.animation.play();\n    }\n    pause() {\n        this.animation.pause();\n    }\n    complete() {\n        this.animation.complete();\n    }\n    cancel() {\n        if (this._animation) {\n            this.animation.cancel();\n        }\n        this.keyframeResolver?.cancel();\n    }\n}\n\nexport { AsyncMotionValueAnimation };\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAEA;;;;;;;CAOC,GACD,MAAM,oBAAoB;AAC1B,MAAM,kCAAkC,iLAAA,CAAA,cAAW;IAC/C,YAAY,EAAE,WAAW,IAAI,EAAE,QAAQ,CAAC,EAAE,OAAO,WAAW,EAAE,SAAS,CAAC,EAAE,cAAc,CAAC,EAAE,aAAa,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,SAAS,CAAE;QACjK,KAAK;QACL;;SAEC,GACD,IAAI,CAAC,IAAI,GAAG;YACR,IAAI,IAAI,CAAC,UAAU,EAAE;gBACjB,IAAI,CAAC,UAAU,CAAC,IAAI;gBACpB,IAAI,CAAC,YAAY;YACrB;YACA,IAAI,CAAC,gBAAgB,EAAE;QAC3B;QACA,IAAI,CAAC,SAAS,GAAG,yKAAA,CAAA,OAAI,CAAC,GAAG;QACzB,MAAM,sBAAsB;YACxB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,GAAG,OAAO;QACd;QACA,MAAM,qBAAqB,SAAS,oBAAoB,2LAAA,CAAA,mBAAgB;QACxE,IAAI,CAAC,gBAAgB,GAAG,IAAI,mBAAmB,WAAW,CAAC,mBAAmB,eAAe,SAAW,IAAI,CAAC,mBAAmB,CAAC,mBAAmB,eAAe,qBAAqB,CAAC,SAAS,MAAM,aAAa;QACrN,IAAI,CAAC,gBAAgB,EAAE;IAC3B;IACA,oBAAoB,SAAS,EAAE,aAAa,EAAE,OAAO,EAAE,IAAI,EAAE;QACzD,IAAI,CAAC,gBAAgB,GAAG;QACxB,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG;QAC7D,IAAI,CAAC,UAAU,GAAG,yKAAA,CAAA,OAAI,CAAC,GAAG;QAC1B;;;SAGC,GACD,IAAI,CAAC,CAAA,GAAA,oLAAA,CAAA,aAAU,AAAD,EAAE,WAAW,MAAM,MAAM,WAAW;YAC9C,IAAI,kKAAA,CAAA,qBAAkB,CAAC,iBAAiB,IAAI,CAAC,OAAO;gBAChD,WAAW,CAAA,GAAA,sLAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,SAAS;YACpD;YACA,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,UAAU,MAAM,GAAG,EAAE;YAC9C,QAAQ,QAAQ,GAAG;YACnB,QAAQ,MAAM,GAAG;QACrB;QACA;;;;;;;;;;;SAWC,GACD,MAAM,YAAY,OACZ,CAAC,IAAI,CAAC,UAAU,GACZ,IAAI,CAAC,SAAS,GACd,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,GAAG,oBAC/B,IAAI,CAAC,UAAU,GACf,IAAI,CAAC,SAAS,GACtB;QACN,MAAM,kBAAkB;YACpB;YACA;YACA,GAAG,OAAO;YACV;QACJ;QACA;;;;SAIC,GACD,MAAM,YAAY,CAAC,aAAa,CAAA,GAAA,uLAAA,CAAA,2BAAwB,AAAD,EAAE,mBACnD,IAAI,oLAAA,CAAA,0BAAuB,CAAC;YAC1B,GAAG,eAAe;YAClB,SAAS,gBAAgB,WAAW,CAAC,KAAK,CAAC,OAAO;QACtD,KACE,IAAI,wKAAA,CAAA,cAAW,CAAC;QACtB,UAAU,QAAQ,CAAC,IAAI,CAAC,IAAM,IAAI,CAAC,cAAc,IAAI,KAAK,CAAC,sJAAA,CAAA,OAAI;QAC/D,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,YAAY,GAAG,UAAU,cAAc,CAAC,IAAI,CAAC,eAAe;YACjE,IAAI,CAAC,eAAe,GAAG;QAC3B;QACA,IAAI,CAAC,UAAU,GAAG;IACtB;IACA,IAAI,WAAW;QACX,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,OAAO,IAAI,CAAC,SAAS;QACzB,OACK;YACD,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ;QAClC;IACJ;IACA,KAAK,SAAS,EAAE,SAAS,EAAE;QACvB,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,KAAQ;IACzD;IACA,IAAI,YAAY;QACZ,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,IAAI,CAAC,gBAAgB,EAAE;YACvB,CAAA,GAAA,2LAAA,CAAA,yBAAsB,AAAD;QACzB;QACA,OAAO,IAAI,CAAC,UAAU;IAC1B;IACA,IAAI,WAAW;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ;IAClC;IACA,IAAI,OAAO;QACP,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI;IAC9B;IACA,IAAI,KAAK,OAAO,EAAE;QACd,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG;IAC1B;IACA,IAAI,QAAQ;QACR,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK;IAC/B;IACA,IAAI,QAAQ;QACR,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK;IAC/B;IACA,IAAI,MAAM,QAAQ,EAAE;QAChB,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG;IAC3B;IACA,IAAI,YAAY;QACZ,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS;IACnC;IACA,eAAe,QAAQ,EAAE;QACrB,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC;QACtD,OACK;YACD,IAAI,CAAC,eAAe,GAAG;QAC3B;QACA,OAAO,IAAM,IAAI,CAAC,IAAI;IAC1B;IACA,OAAO;QACH,IAAI,CAAC,SAAS,CAAC,IAAI;IACvB;IACA,QAAQ;QACJ,IAAI,CAAC,SAAS,CAAC,KAAK;IACxB;IACA,WAAW;QACP,IAAI,CAAC,SAAS,CAAC,QAAQ;IAC3B;IACA,SAAS;QACL,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,SAAS,CAAC,MAAM;QACzB;QACA,IAAI,CAAC,gBAAgB,EAAE;IAC3B;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3649, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs"], "sourcesContent": ["const isDragging = {\n    x: false,\n    y: false,\n};\nfunction isDragActive() {\n    return isDragging.x || isDragging.y;\n}\n\nexport { isDragActive, isDragging };\n"], "names": [], "mappings": ";;;;AAAA,MAAM,aAAa;IACf,GAAG;IACH,GAAG;AACP;AACA,SAAS;IACL,OAAO,WAAW,CAAC,IAAI,WAAW,CAAC;AACvC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3667, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/gestures/drag/state/set-active.mjs"], "sourcesContent": ["import { isDragging } from './is-active.mjs';\n\nfunction setDragLock(axis) {\n    if (axis === \"x\" || axis === \"y\") {\n        if (isDragging[axis]) {\n            return null;\n        }\n        else {\n            isDragging[axis] = true;\n            return () => {\n                isDragging[axis] = false;\n            };\n        }\n    }\n    else {\n        if (isDragging.x || isDragging.y) {\n            return null;\n        }\n        else {\n            isDragging.x = isDragging.y = true;\n            return () => {\n                isDragging.x = isDragging.y = false;\n            };\n        }\n    }\n}\n\nexport { setDragLock };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,YAAY,IAAI;IACrB,IAAI,SAAS,OAAO,SAAS,KAAK;QAC9B,IAAI,yLAAA,CAAA,aAAU,CAAC,KAAK,EAAE;YAClB,OAAO;QACX,OACK;YACD,yLAAA,CAAA,aAAU,CAAC,KAAK,GAAG;YACnB,OAAO;gBACH,yLAAA,CAAA,aAAU,CAAC,KAAK,GAAG;YACvB;QACJ;IACJ,OACK;QACD,IAAI,yLAAA,CAAA,aAAU,CAAC,CAAC,IAAI,yLAAA,CAAA,aAAU,CAAC,CAAC,EAAE;YAC9B,OAAO;QACX,OACK;YACD,yLAAA,CAAA,aAAU,CAAC,CAAC,GAAG,yLAAA,CAAA,aAAU,CAAC,CAAC,GAAG;YAC9B,OAAO;gBACH,yLAAA,CAAA,aAAU,CAAC,CAAC,GAAG,yLAAA,CAAA,aAAU,CAAC,CAAC,GAAG;YAClC;QACJ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3700, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/gestures/utils/is-primary-pointer.mjs"], "sourcesContent": ["const isPrimaryPointer = (event) => {\n    if (event.pointerType === \"mouse\") {\n        return typeof event.button !== \"number\" || event.button <= 0;\n    }\n    else {\n        /**\n         * isPrimary is true for all mice buttons, whereas every touch point\n         * is regarded as its own input. So subsequent concurrent touch points\n         * will be false.\n         *\n         * Specifically match against false here as incomplete versions of\n         * PointerEvents in very old browser might have it set as undefined.\n         */\n        return event.isPrimary !== false;\n    }\n};\n\nexport { isPrimaryPointer };\n"], "names": [], "mappings": ";;;AAAA,MAAM,mBAAmB,CAAC;IACtB,IAAI,MAAM,WAAW,KAAK,SAAS;QAC/B,OAAO,OAAO,MAAM,MAAM,KAAK,YAAY,MAAM,MAAM,IAAI;IAC/D,OACK;QACD;;;;;;;SAOC,GACD,OAAO,MAAM,SAAS,KAAK;IAC/B;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3724, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/frameloop/microtask.mjs"], "sourcesContent": ["import { createRenderBatcher } from './batcher.mjs';\n\nconst { schedule: microtask, cancel: cancelMicrotask } = \n/* @__PURE__ */ createRenderBatcher(queueMicrotask, false);\n\nexport { cancelMicrotask, microtask };\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,EAAE,UAAU,SAAS,EAAE,QAAQ,eAAe,EAAE,GACtD,aAAa,GAAG,CAAA,GAAA,oKAAA,CAAA,sBAAmB,AAAD,EAAE,gBAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3738, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/utils/is-svg-element.mjs"], "sourcesContent": ["import { isObject } from 'motion-utils';\n\n/**\n * Checks if an element is an SVG element in a way\n * that works across iframes\n */\nfunction isSVGElement(element) {\n    return isObject(element) && \"ownerSVGElement\" in element;\n}\n\nexport { isSVGElement };\n"], "names": [], "mappings": ";;;AAAA;;AAEA;;;CAGC,GACD,SAAS,aAAa,OAAO;IACzB,OAAO,CAAA,GAAA,8JAAA,CAAA,WAAQ,AAAD,EAAE,YAAY,qBAAqB;AACrD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3756, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/utils/is-svg-svg-element.mjs"], "sourcesContent": ["import { isSVGElement } from './is-svg-element.mjs';\n\n/**\n * Checks if an element is specifically an SVGSVGElement (the root SVG element)\n * in a way that works across iframes\n */\nfunction isSVGSVGElement(element) {\n    return isSVGElement(element) && element.tagName === \"svg\";\n}\n\nexport { isSVGSVGElement };\n"], "names": [], "mappings": ";;;AAAA;;AAEA;;;CAGC,GACD,SAAS,gBAAgB,OAAO;IAC5B,OAAO,CAAA,GAAA,6KAAA,CAAA,eAAY,AAAD,EAAE,YAAY,QAAQ,OAAO,KAAK;AACxD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3774, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/utils/resolve-elements.mjs"], "sourcesContent": ["function resolveElements(elementOrSelector, scope, selectorCache) {\n    if (elementOrSelector instanceof EventTarget) {\n        return [elementOrSelector];\n    }\n    else if (typeof elementOrSelector === \"string\") {\n        let root = document;\n        if (scope) {\n            root = scope.current;\n        }\n        const elements = selectorCache?.[elementOrSelector] ??\n            root.querySelectorAll(elementOrSelector);\n        return elements ? Array.from(elements) : [];\n    }\n    return Array.from(elementOrSelector);\n}\n\nexport { resolveElements };\n"], "names": [], "mappings": ";;;AAAA,SAAS,gBAAgB,iBAAiB,EAAE,KAAK,EAAE,aAAa;IAC5D,IAAI,6BAA6B,aAAa;QAC1C,OAAO;YAAC;SAAkB;IAC9B,OACK,IAAI,OAAO,sBAAsB,UAAU;QAC5C,IAAI,OAAO;QACX,IAAI,OAAO;YACP,OAAO,MAAM,OAAO;QACxB;QACA,MAAM,WAAW,eAAe,CAAC,kBAAkB,IAC/C,KAAK,gBAAgB,CAAC;QAC1B,OAAO,WAAW,MAAM,IAAI,CAAC,YAAY,EAAE;IAC/C;IACA,OAAO,MAAM,IAAI,CAAC;AACtB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3799, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/gestures/utils/setup.mjs"], "sourcesContent": ["import { resolveElements } from '../../utils/resolve-elements.mjs';\n\nfunction setupGesture(elementOrSelector, options) {\n    const elements = resolveElements(elementOrSelector);\n    const gestureAbortController = new AbortController();\n    const eventOptions = {\n        passive: true,\n        ...options,\n        signal: gestureAbortController.signal,\n    };\n    const cancel = () => gestureAbortController.abort();\n    return [elements, eventOptions, cancel];\n}\n\nexport { setupGesture };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,aAAa,iBAAiB,EAAE,OAAO;IAC5C,MAAM,WAAW,CAAA,GAAA,4KAAA,CAAA,kBAAe,AAAD,EAAE;IACjC,MAAM,yBAAyB,IAAI;IACnC,MAAM,eAAe;QACjB,SAAS;QACT,GAAG,OAAO;QACV,QAAQ,uBAAuB,MAAM;IACzC;IACA,MAAM,SAAS,IAAM,uBAAuB,KAAK;IACjD,OAAO;QAAC;QAAU;QAAc;KAAO;AAC3C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3826, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/gestures/hover.mjs"], "sourcesContent": ["import { isDragActive } from './drag/state/is-active.mjs';\nimport { setupGesture } from './utils/setup.mjs';\n\nfunction isValidHover(event) {\n    return !(event.pointerType === \"touch\" || isDragActive());\n}\n/**\n * Create a hover gesture. hover() is different to .addEventListener(\"pointerenter\")\n * in that it has an easier syntax, filters out polyfilled touch events, interoperates\n * with drag gestures, and automatically removes the \"pointerennd\" event listener when the hover ends.\n *\n * @public\n */\nfunction hover(elementOrSelector, onHoverStart, options = {}) {\n    const [elements, eventOptions, cancel] = setupGesture(elementOrSelector, options);\n    const onPointerEnter = (enterEvent) => {\n        if (!isValidHover(enterEvent))\n            return;\n        const { target } = enterEvent;\n        const onHoverEnd = onHoverStart(target, enterEvent);\n        if (typeof onHoverEnd !== \"function\" || !target)\n            return;\n        const onPointerLeave = (leaveEvent) => {\n            if (!isValidHover(leaveEvent))\n                return;\n            onHoverEnd(leaveEvent);\n            target.removeEventListener(\"pointerleave\", onPointerLeave);\n        };\n        target.addEventListener(\"pointerleave\", onPointerLeave, eventOptions);\n    };\n    elements.forEach((element) => {\n        element.addEventListener(\"pointerenter\", onPointerEnter, eventOptions);\n    });\n    return cancel;\n}\n\nexport { hover };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,aAAa,KAAK;IACvB,OAAO,CAAC,CAAC,MAAM,WAAW,KAAK,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,GAAG;AAC5D;AACA;;;;;;CAMC,GACD,SAAS,MAAM,iBAAiB,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;IACxD,MAAM,CAAC,UAAU,cAAc,OAAO,GAAG,CAAA,GAAA,0KAAA,CAAA,eAAY,AAAD,EAAE,mBAAmB;IACzE,MAAM,iBAAiB,CAAC;QACpB,IAAI,CAAC,aAAa,aACd;QACJ,MAAM,EAAE,MAAM,EAAE,GAAG;QACnB,MAAM,aAAa,aAAa,QAAQ;QACxC,IAAI,OAAO,eAAe,cAAc,CAAC,QACrC;QACJ,MAAM,iBAAiB,CAAC;YACpB,IAAI,CAAC,aAAa,aACd;YACJ,WAAW;YACX,OAAO,mBAAmB,CAAC,gBAAgB;QAC/C;QACA,OAAO,gBAAgB,CAAC,gBAAgB,gBAAgB;IAC5D;IACA,SAAS,OAAO,CAAC,CAAC;QACd,QAAQ,gBAAgB,CAAC,gBAAgB,gBAAgB;IAC7D;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3868, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/gestures/utils/is-node-or-child.mjs"], "sourcesContent": ["/**\n * Recursively traverse up the tree to check whether the provided child node\n * is the parent or a descendant of it.\n *\n * @param parent - Element to find\n * @param child - Element to test against parent\n */\nconst isNodeOrChild = (parent, child) => {\n    if (!child) {\n        return false;\n    }\n    else if (parent === child) {\n        return true;\n    }\n    else {\n        return isNodeOrChild(parent, child.parentElement);\n    }\n};\n\nexport { isNodeOrChild };\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;AACD,MAAM,gBAAgB,CAAC,QAAQ;IAC3B,IAAI,CAAC,OAAO;QACR,OAAO;IACX,OACK,IAAI,WAAW,OAAO;QACvB,OAAO;IACX,OACK;QACD,OAAO,cAAc,QAAQ,MAAM,aAAa;IACpD;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3893, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/gestures/press/utils/is-keyboard-accessible.mjs"], "sourcesContent": ["const focusableElements = new Set([\n    \"BUTTON\",\n    \"INPUT\",\n    \"SELECT\",\n    \"TEXTAREA\",\n    \"A\",\n]);\nfunction isElementKeyboardAccessible(element) {\n    return (focusableElements.has(element.tagName) ||\n        element.tabIndex !== -1);\n}\n\nexport { isElementKeyboardAccessible };\n"], "names": [], "mappings": ";;;AAAA,MAAM,oBAAoB,IAAI,IAAI;IAC9B;IACA;IACA;IACA;IACA;CACH;AACD,SAAS,4BAA4B,OAAO;IACxC,OAAQ,kBAAkB,GAAG,CAAC,QAAQ,OAAO,KACzC,QAAQ,QAAQ,KAAK,CAAC;AAC9B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3913, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/gestures/press/utils/state.mjs"], "sourcesContent": ["const isPressing = new WeakSet();\n\nexport { isPressing };\n"], "names": [], "mappings": ";;;AAAA,MAAM,aAAa,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3924, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/gestures/press/utils/keyboard.mjs"], "sourcesContent": ["import { isPressing } from './state.mjs';\n\n/**\n * Filter out events that are not \"Enter\" keys.\n */\nfunction filterEvents(callback) {\n    return (event) => {\n        if (event.key !== \"Enter\")\n            return;\n        callback(event);\n    };\n}\nfunction firePointerEvent(target, type) {\n    target.dispatchEvent(new PointerEvent(\"pointer\" + type, { isPrimary: true, bubbles: true }));\n}\nconst enableKeyboardPress = (focusEvent, eventOptions) => {\n    const element = focusEvent.currentTarget;\n    if (!element)\n        return;\n    const handleKeydown = filterEvents(() => {\n        if (isPressing.has(element))\n            return;\n        firePointerEvent(element, \"down\");\n        const handleKeyup = filterEvents(() => {\n            firePointerEvent(element, \"up\");\n        });\n        const handleBlur = () => firePointerEvent(element, \"cancel\");\n        element.addEventListener(\"keyup\", handleKeyup, eventOptions);\n        element.addEventListener(\"blur\", handleBlur, eventOptions);\n    });\n    element.addEventListener(\"keydown\", handleKeydown, eventOptions);\n    /**\n     * Add an event listener that fires on blur to remove the keydown events.\n     */\n    element.addEventListener(\"blur\", () => element.removeEventListener(\"keydown\", handleKeydown), eventOptions);\n};\n\nexport { enableKeyboardPress };\n"], "names": [], "mappings": ";;;AAAA;;AAEA;;CAEC,GACD,SAAS,aAAa,QAAQ;IAC1B,OAAO,CAAC;QACJ,IAAI,MAAM,GAAG,KAAK,SACd;QACJ,SAAS;IACb;AACJ;AACA,SAAS,iBAAiB,MAAM,EAAE,IAAI;IAClC,OAAO,aAAa,CAAC,IAAI,aAAa,YAAY,MAAM;QAAE,WAAW;QAAM,SAAS;IAAK;AAC7F;AACA,MAAM,sBAAsB,CAAC,YAAY;IACrC,MAAM,UAAU,WAAW,aAAa;IACxC,IAAI,CAAC,SACD;IACJ,MAAM,gBAAgB,aAAa;QAC/B,IAAI,mLAAA,CAAA,aAAU,CAAC,GAAG,CAAC,UACf;QACJ,iBAAiB,SAAS;QAC1B,MAAM,cAAc,aAAa;YAC7B,iBAAiB,SAAS;QAC9B;QACA,MAAM,aAAa,IAAM,iBAAiB,SAAS;QACnD,QAAQ,gBAAgB,CAAC,SAAS,aAAa;QAC/C,QAAQ,gBAAgB,CAAC,QAAQ,YAAY;IACjD;IACA,QAAQ,gBAAgB,CAAC,WAAW,eAAe;IACnD;;KAEC,GACD,QAAQ,gBAAgB,CAAC,QAAQ,IAAM,QAAQ,mBAAmB,CAAC,WAAW,gBAAgB;AAClG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3968, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/gestures/press/index.mjs"], "sourcesContent": ["import { isHTMLElement } from '../../utils/is-html-element.mjs';\nimport { isDragActive } from '../drag/state/is-active.mjs';\nimport { isNodeOrChild } from '../utils/is-node-or-child.mjs';\nimport { isPrimaryPointer } from '../utils/is-primary-pointer.mjs';\nimport { setupGesture } from '../utils/setup.mjs';\nimport { isElementKeyboardAccessible } from './utils/is-keyboard-accessible.mjs';\nimport { enableKeyboardPress } from './utils/keyboard.mjs';\nimport { isPressing } from './utils/state.mjs';\n\n/**\n * Filter out events that are not primary pointer events, or are triggering\n * while a Motion gesture is active.\n */\nfunction isValidPressEvent(event) {\n    return isPrimaryPointer(event) && !isDragActive();\n}\n/**\n * Create a press gesture.\n *\n * Press is different to `\"pointerdown\"`, `\"pointerup\"` in that it\n * automatically filters out secondary pointer events like right\n * click and multitouch.\n *\n * It also adds accessibility support for keyboards, where\n * an element with a press gesture will receive focus and\n *  trigger on Enter `\"keydown\"` and `\"keyup\"` events.\n *\n * This is different to a browser's `\"click\"` event, which does\n * respond to keyboards but only for the `\"click\"` itself, rather\n * than the press start and end/cancel. The element also needs\n * to be focusable for this to work, whereas a press gesture will\n * make an element focusable by default.\n *\n * @public\n */\nfunction press(targetOrSelector, onPressStart, options = {}) {\n    const [targets, eventOptions, cancelEvents] = setupGesture(targetOrSelector, options);\n    const startPress = (startEvent) => {\n        const target = startEvent.currentTarget;\n        if (!isValidPressEvent(startEvent))\n            return;\n        isPressing.add(target);\n        const onPressEnd = onPressStart(target, startEvent);\n        const onPointerEnd = (endEvent, success) => {\n            window.removeEventListener(\"pointerup\", onPointerUp);\n            window.removeEventListener(\"pointercancel\", onPointerCancel);\n            if (isPressing.has(target)) {\n                isPressing.delete(target);\n            }\n            if (!isValidPressEvent(endEvent)) {\n                return;\n            }\n            if (typeof onPressEnd === \"function\") {\n                onPressEnd(endEvent, { success });\n            }\n        };\n        const onPointerUp = (upEvent) => {\n            onPointerEnd(upEvent, target === window ||\n                target === document ||\n                options.useGlobalTarget ||\n                isNodeOrChild(target, upEvent.target));\n        };\n        const onPointerCancel = (cancelEvent) => {\n            onPointerEnd(cancelEvent, false);\n        };\n        window.addEventListener(\"pointerup\", onPointerUp, eventOptions);\n        window.addEventListener(\"pointercancel\", onPointerCancel, eventOptions);\n    };\n    targets.forEach((target) => {\n        const pointerDownTarget = options.useGlobalTarget ? window : target;\n        pointerDownTarget.addEventListener(\"pointerdown\", startPress, eventOptions);\n        if (isHTMLElement(target)) {\n            target.addEventListener(\"focus\", (event) => enableKeyboardPress(event, eventOptions));\n            if (!isElementKeyboardAccessible(target) &&\n                !target.hasAttribute(\"tabindex\")) {\n                target.tabIndex = 0;\n            }\n        }\n    });\n    return cancelEvents;\n}\n\nexport { press };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAEA;;;CAGC,GACD,SAAS,kBAAkB,KAAK;IAC5B,OAAO,CAAA,GAAA,6LAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU,CAAC,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD;AAClD;AACA;;;;;;;;;;;;;;;;;;CAkBC,GACD,SAAS,MAAM,gBAAgB,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;IACvD,MAAM,CAAC,SAAS,cAAc,aAAa,GAAG,CAAA,GAAA,0KAAA,CAAA,eAAY,AAAD,EAAE,kBAAkB;IAC7E,MAAM,aAAa,CAAC;QAChB,MAAM,SAAS,WAAW,aAAa;QACvC,IAAI,CAAC,kBAAkB,aACnB;QACJ,mLAAA,CAAA,aAAU,CAAC,GAAG,CAAC;QACf,MAAM,aAAa,aAAa,QAAQ;QACxC,MAAM,eAAe,CAAC,UAAU;YAC5B,OAAO,mBAAmB,CAAC,aAAa;YACxC,OAAO,mBAAmB,CAAC,iBAAiB;YAC5C,IAAI,mLAAA,CAAA,aAAU,CAAC,GAAG,CAAC,SAAS;gBACxB,mLAAA,CAAA,aAAU,CAAC,MAAM,CAAC;YACtB;YACA,IAAI,CAAC,kBAAkB,WAAW;gBAC9B;YACJ;YACA,IAAI,OAAO,eAAe,YAAY;gBAClC,WAAW,UAAU;oBAAE;gBAAQ;YACnC;QACJ;QACA,MAAM,cAAc,CAAC;YACjB,aAAa,SAAS,WAAW,UAC7B,WAAW,YACX,QAAQ,eAAe,IACvB,CAAA,GAAA,8LAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ,QAAQ,MAAM;QAC5C;QACA,MAAM,kBAAkB,CAAC;YACrB,aAAa,aAAa;QAC9B;QACA,OAAO,gBAAgB,CAAC,aAAa,aAAa;QAClD,OAAO,gBAAgB,CAAC,iBAAiB,iBAAiB;IAC9D;IACA,QAAQ,OAAO,CAAC,CAAC;QACb,MAAM,oBAAoB,QAAQ,eAAe,GAAG,SAAS;QAC7D,kBAAkB,gBAAgB,CAAC,eAAe,YAAY;QAC9D,IAAI,CAAA,GAAA,8KAAA,CAAA,gBAAa,AAAD,EAAE,SAAS;YACvB,OAAO,gBAAgB,CAAC,SAAS,CAAC,QAAU,CAAA,GAAA,sLAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO;YACvE,IAAI,CAAC,CAAA,GAAA,0MAAA,CAAA,8BAA2B,AAAD,EAAE,WAC7B,CAAC,OAAO,YAAY,CAAC,aAAa;gBAClC,OAAO,QAAQ,GAAG;YACtB;QACJ;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4061, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/value/types/utils/get-as-type.mjs"], "sourcesContent": ["/**\n * Provided a value and a ValueType, returns the value as that value type.\n */\nconst getValueAsType = (value, type) => {\n    return type && typeof value === \"number\"\n        ? type.transform(value)\n        : value;\n};\n\nexport { getValueAsType };\n"], "names": [], "mappings": "AAAA;;CAEC;;;AACD,MAAM,iBAAiB,CAAC,OAAO;IAC3B,OAAO,QAAQ,OAAO,UAAU,WAC1B,KAAK,SAAS,CAAC,SACf;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4076, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/value/types/int.mjs"], "sourcesContent": ["import { number } from './numbers/index.mjs';\n\nconst int = {\n    ...number,\n    transform: Math.round,\n};\n\nexport { int };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,MAAM;IACR,GAAG,kLAAA,CAAA,SAAM;IACT,WAAW,KAAK,KAAK;AACzB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4092, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/value/types/maps/transform.mjs"], "sourcesContent": ["import { scale, alpha } from '../numbers/index.mjs';\nimport { degrees, px, progressPercentage } from '../numbers/units.mjs';\n\nconst transformValueTypes = {\n    rotate: degrees,\n    rotateX: degrees,\n    rotateY: degrees,\n    rotateZ: degrees,\n    scale,\n    scaleX: scale,\n    scaleY: scale,\n    scaleZ: scale,\n    skew: degrees,\n    skewX: degrees,\n    skewY: degrees,\n    distance: px,\n    translateX: px,\n    translateY: px,\n    translateZ: px,\n    x: px,\n    y: px,\n    z: px,\n    perspective: px,\n    transformPerspective: px,\n    opacity: alpha,\n    originX: progressPercentage,\n    originY: progressPercentage,\n    originZ: px,\n};\n\nexport { transformValueTypes };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,sBAAsB;IACxB,QAAQ,kLAAA,CAAA,UAAO;IACf,SAAS,kLAAA,CAAA,UAAO;IAChB,SAAS,kLAAA,CAAA,UAAO;IAChB,SAAS,kLAAA,CAAA,UAAO;IAChB,OAAA,kLAAA,CAAA,QAAK;IACL,QAAQ,kLAAA,CAAA,QAAK;IACb,QAAQ,kLAAA,CAAA,QAAK;IACb,QAAQ,kLAAA,CAAA,QAAK;IACb,MAAM,kLAAA,CAAA,UAAO;IACb,OAAO,kLAAA,CAAA,UAAO;IACd,OAAO,kLAAA,CAAA,UAAO;IACd,UAAU,kLAAA,CAAA,KAAE;IACZ,YAAY,kLAAA,CAAA,KAAE;IACd,YAAY,kLAAA,CAAA,KAAE;IACd,YAAY,kLAAA,CAAA,KAAE;IACd,GAAG,kLAAA,CAAA,KAAE;IACL,GAAG,kLAAA,CAAA,KAAE;IACL,GAAG,kLAAA,CAAA,KAAE;IACL,aAAa,kLAAA,CAAA,KAAE;IACf,sBAAsB,kLAAA,CAAA,KAAE;IACxB,SAAS,kLAAA,CAAA,QAAK;IACd,SAAS,kLAAA,CAAA,qBAAkB;IAC3B,SAAS,kLAAA,CAAA,qBAAkB;IAC3B,SAAS,kLAAA,CAAA,KAAE;AACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4132, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/value/types/maps/number.mjs"], "sourcesContent": ["import { int } from '../int.mjs';\nimport { alpha } from '../numbers/index.mjs';\nimport { px } from '../numbers/units.mjs';\nimport { transformValueTypes } from './transform.mjs';\n\nconst numberValueTypes = {\n    // Border props\n    borderWidth: px,\n    borderTopWidth: px,\n    borderRightWidth: px,\n    borderBottomWidth: px,\n    borderLeftWidth: px,\n    borderRadius: px,\n    radius: px,\n    borderTopLeftRadius: px,\n    borderTopRightRadius: px,\n    borderBottomRightRadius: px,\n    borderBottomLeftRadius: px,\n    // Positioning props\n    width: px,\n    maxWidth: px,\n    height: px,\n    maxHeight: px,\n    top: px,\n    right: px,\n    bottom: px,\n    left: px,\n    // Spacing props\n    padding: px,\n    paddingTop: px,\n    paddingRight: px,\n    paddingBottom: px,\n    paddingLeft: px,\n    margin: px,\n    marginTop: px,\n    marginRight: px,\n    marginBottom: px,\n    marginLeft: px,\n    // Misc\n    backgroundPositionX: px,\n    backgroundPositionY: px,\n    ...transformValueTypes,\n    zIndex: int,\n    // SVG\n    fillOpacity: alpha,\n    strokeOpacity: alpha,\n    numOctaves: int,\n};\n\nexport { numberValueTypes };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEA,MAAM,mBAAmB;IACrB,eAAe;IACf,aAAa,kLAAA,CAAA,KAAE;IACf,gBAAgB,kLAAA,CAAA,KAAE;IAClB,kBAAkB,kLAAA,CAAA,KAAE;IACpB,mBAAmB,kLAAA,CAAA,KAAE;IACrB,iBAAiB,kLAAA,CAAA,KAAE;IACnB,cAAc,kLAAA,CAAA,KAAE;IAChB,QAAQ,kLAAA,CAAA,KAAE;IACV,qBAAqB,kLAAA,CAAA,KAAE;IACvB,sBAAsB,kLAAA,CAAA,KAAE;IACxB,yBAAyB,kLAAA,CAAA,KAAE;IAC3B,wBAAwB,kLAAA,CAAA,KAAE;IAC1B,oBAAoB;IACpB,OAAO,kLAAA,CAAA,KAAE;IACT,UAAU,kLAAA,CAAA,KAAE;IACZ,QAAQ,kLAAA,CAAA,KAAE;IACV,WAAW,kLAAA,CAAA,KAAE;IACb,KAAK,kLAAA,CAAA,KAAE;IACP,OAAO,kLAAA,CAAA,KAAE;IACT,QAAQ,kLAAA,CAAA,KAAE;IACV,MAAM,kLAAA,CAAA,KAAE;IACR,gBAAgB;IAChB,SAAS,kLAAA,CAAA,KAAE;IACX,YAAY,kLAAA,CAAA,KAAE;IACd,cAAc,kLAAA,CAAA,KAAE;IAChB,eAAe,kLAAA,CAAA,KAAE;IACjB,aAAa,kLAAA,CAAA,KAAE;IACf,QAAQ,kLAAA,CAAA,KAAE;IACV,WAAW,kLAAA,CAAA,KAAE;IACb,aAAa,kLAAA,CAAA,KAAE;IACf,cAAc,kLAAA,CAAA,KAAE;IAChB,YAAY,kLAAA,CAAA,KAAE;IACd,OAAO;IACP,qBAAqB,kLAAA,CAAA,KAAE;IACvB,qBAAqB,kLAAA,CAAA,KAAE;IACvB,GAAG,mLAAA,CAAA,sBAAmB;IACtB,QAAQ,qKAAA,CAAA,MAAG;IACX,MAAM;IACN,aAAa,kLAAA,CAAA,QAAK;IAClB,eAAe,kLAAA,CAAA,QAAK;IACpB,YAAY,qKAAA,CAAA,MAAG;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4193, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/value/types/auto.mjs"], "sourcesContent": ["/**\n * ValueType for \"auto\"\n */\nconst auto = {\n    test: (v) => v === \"auto\",\n    parse: (v) => v,\n};\n\nexport { auto };\n"], "names": [], "mappings": "AAAA;;CAEC;;;AACD,MAAM,OAAO;IACT,MAAM,CAAC,IAAM,MAAM;IACnB,OAAO,CAAC,IAAM;AAClB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4209, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/value/types/test.mjs"], "sourcesContent": ["/**\n * Tests a provided value against a ValueType\n */\nconst testValueType = (v) => (type) => type.test(v);\n\nexport { testValueType };\n"], "names": [], "mappings": "AAAA;;CAEC;;;AACD,MAAM,gBAAgB,CAAC,IAAM,CAAC,OAAS,KAAK,IAAI,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4222, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/value/types/dimensions.mjs"], "sourcesContent": ["import { auto } from './auto.mjs';\nimport { number } from './numbers/index.mjs';\nimport { px, percent, degrees, vw, vh } from './numbers/units.mjs';\nimport { testValueType } from './test.mjs';\n\n/**\n * A list of value types commonly used for dimensions\n */\nconst dimensionValueTypes = [number, px, percent, degrees, vw, vh, auto];\n/**\n * Tests a dimensional value against the list of dimension ValueTypes\n */\nconst findDimensionValueType = (v) => dimensionValueTypes.find(testValueType(v));\n\nexport { dimensionValueTypes, findDimensionValueType };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AAEA;;CAEC,GACD,MAAM,sBAAsB;IAAC,kLAAA,CAAA,SAAM;IAAE,kLAAA,CAAA,KAAE;IAAE,kLAAA,CAAA,UAAO;IAAE,kLAAA,CAAA,UAAO;IAAE,kLAAA,CAAA,KAAE;IAAE,kLAAA,CAAA,KAAE;IAAE,sKAAA,CAAA,OAAI;CAAC;AACxE;;CAEC,GACD,MAAM,yBAAyB,CAAC,IAAM,oBAAoB,IAAI,CAAC,CAAA,GAAA,sKAAA,CAAA,gBAAa,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4255, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/animation/utils/css-variables-conversion.mjs"], "sourcesContent": ["import { invariant, isNumericalString } from 'motion-utils';\nimport { isCSSVariableToken } from './is-css-variable.mjs';\n\n/**\n * Parse <PERSON><PERSON><PERSON>'s special CSS variable format into a CSS token and a fallback.\n *\n * ```\n * `var(--foo, #fff)` => [`--foo`, '#fff']\n * ```\n *\n * @param current\n */\nconst splitCSSVariableRegex = \n// eslint-disable-next-line redos-detector/no-unsafe-regex -- false positive, as it can match a lot of words\n/^var\\(--(?:([\\w-]+)|([\\w-]+), ?([a-zA-Z\\d ()%#.,-]+))\\)/u;\nfunction parseCSSVariable(current) {\n    const match = splitCSSVariableRegex.exec(current);\n    if (!match)\n        return [,];\n    const [, token1, token2, fallback] = match;\n    return [`--${token1 ?? token2}`, fallback];\n}\nconst maxDepth = 4;\nfunction getVariableValue(current, element, depth = 1) {\n    invariant(depth <= maxDepth, `Max CSS variable fallback depth detected in property \"${current}\". This may indicate a circular fallback dependency.`, \"max-css-var-depth\");\n    const [token, fallback] = parseCSSVariable(current);\n    // No CSS variable detected\n    if (!token)\n        return;\n    // Attempt to read this CSS variable off the element\n    const resolved = window.getComputedStyle(element).getPropertyValue(token);\n    if (resolved) {\n        const trimmed = resolved.trim();\n        return isNumericalString(trimmed) ? parseFloat(trimmed) : trimmed;\n    }\n    return isCSSVariableToken(fallback)\n        ? getVariableValue(fallback, element, depth + 1)\n        : fallback;\n}\n\nexport { getVariableValue, parseCSSVariable };\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;;;AAEA;;;;;;;;CAQC,GACD,MAAM,wBACN,4GAA4G;AAC5G;AACA,SAAS,iBAAiB,OAAO;IAC7B,MAAM,QAAQ,sBAAsB,IAAI,CAAC;IACzC,IAAI,CAAC,OACD,OAAO;;KAAG;IACd,MAAM,GAAG,QAAQ,QAAQ,SAAS,GAAG;IACrC,OAAO;QAAC,CAAC,EAAE,EAAE,UAAU,QAAQ;QAAE;KAAS;AAC9C;AACA,MAAM,WAAW;AACjB,SAAS,iBAAiB,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC;IACjD,CAAA,GAAA,wJAAA,CAAA,YAAS,AAAD,EAAE,SAAS,UAAU,CAAC,sDAAsD,EAAE,QAAQ,oDAAoD,CAAC,EAAE;IACrJ,MAAM,CAAC,OAAO,SAAS,GAAG,iBAAiB;IAC3C,2BAA2B;IAC3B,IAAI,CAAC,OACD;IACJ,oDAAoD;IACpD,MAAM,WAAW,OAAO,gBAAgB,CAAC,SAAS,gBAAgB,CAAC;IACnE,IAAI,UAAU;QACV,MAAM,UAAU,SAAS,IAAI;QAC7B,OAAO,CAAA,GAAA,2KAAA,CAAA,oBAAiB,AAAD,EAAE,WAAW,WAAW,WAAW;IAC9D;IACA,OAAO,CAAA,GAAA,2LAAA,CAAA,qBAAkB,AAAD,EAAE,YACpB,iBAAiB,UAAU,SAAS,QAAQ,KAC5C;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4306, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/animation/keyframes/utils/is-none.mjs"], "sourcesContent": ["import { isZeroValueString } from 'motion-utils';\n\nfunction isNone(value) {\n    if (typeof value === \"number\") {\n        return value === 0;\n    }\n    else if (value !== null) {\n        return value === \"none\" || value === \"0\" || isZeroValueString(value);\n    }\n    else {\n        return true;\n    }\n}\n\nexport { isNone };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,OAAO,KAAK;IACjB,IAAI,OAAO,UAAU,UAAU;QAC3B,OAAO,UAAU;IACrB,OACK,IAAI,UAAU,MAAM;QACrB,OAAO,UAAU,UAAU,UAAU,OAAO,CAAA,GAAA,+KAAA,CAAA,oBAAiB,AAAD,EAAE;IAClE,OACK;QACD,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4327, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/value/types/complex/filter.mjs"], "sourcesContent": ["import { complex } from './index.mjs';\nimport { floatRegex } from '../utils/float-regex.mjs';\n\n/**\n * Properties that should default to 1 or 100%\n */\nconst maxDefaults = new Set([\"brightness\", \"contrast\", \"saturate\", \"opacity\"]);\nfunction applyDefaultFilter(v) {\n    const [name, value] = v.slice(0, -1).split(\"(\");\n    if (name === \"drop-shadow\")\n        return v;\n    const [number] = value.match(floatRegex) || [];\n    if (!number)\n        return v;\n    const unit = value.replace(number, \"\");\n    let defaultValue = maxDefaults.has(name) ? 1 : 0;\n    if (number !== value)\n        defaultValue *= 100;\n    return name + \"(\" + defaultValue + unit + \")\";\n}\nconst functionRegex = /\\b([a-z-]*)\\(.*?\\)/gu;\nconst filter = {\n    ...complex,\n    getAnimatableNone: (v) => {\n        const functions = v.match(functionRegex);\n        return functions ? functions.map(applyDefaultFilter).join(\" \") : v;\n    },\n};\n\nexport { filter };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA;;CAEC,GACD,MAAM,cAAc,IAAI,IAAI;IAAC;IAAc;IAAY;IAAY;CAAU;AAC7E,SAAS,mBAAmB,CAAC;IACzB,MAAM,CAAC,MAAM,MAAM,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;IAC3C,IAAI,SAAS,eACT,OAAO;IACX,MAAM,CAAC,OAAO,GAAG,MAAM,KAAK,CAAC,yLAAA,CAAA,aAAU,KAAK,EAAE;IAC9C,IAAI,CAAC,QACD,OAAO;IACX,MAAM,OAAO,MAAM,OAAO,CAAC,QAAQ;IACnC,IAAI,eAAe,YAAY,GAAG,CAAC,QAAQ,IAAI;IAC/C,IAAI,WAAW,OACX,gBAAgB;IACpB,OAAO,OAAO,MAAM,eAAe,OAAO;AAC9C;AACA,MAAM,gBAAgB;AACtB,MAAM,SAAS;IACX,GAAG,kLAAA,CAAA,UAAO;IACV,mBAAmB,CAAC;QAChB,MAAM,YAAY,EAAE,KAAK,CAAC;QAC1B,OAAO,YAAY,UAAU,GAAG,CAAC,oBAAoB,IAAI,CAAC,OAAO;IACrE;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4367, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/value/types/maps/defaults.mjs"], "sourcesContent": ["import { color } from '../color/index.mjs';\nimport { filter } from '../complex/filter.mjs';\nimport { numberValueTypes } from './number.mjs';\n\n/**\n * A map of default value types for common values\n */\nconst defaultValueTypes = {\n    ...numberValueTypes,\n    // Color props\n    color,\n    backgroundColor: color,\n    outlineColor: color,\n    fill: color,\n    stroke: color,\n    // Border props\n    borderColor: color,\n    borderTopColor: color,\n    borderRightColor: color,\n    borderBottomColor: color,\n    borderLeftColor: color,\n    filter,\n    WebkitFilter: filter,\n};\n/**\n * Gets the default ValueType for the provided value key\n */\nconst getDefaultValueType = (key) => defaultValueTypes[key];\n\nexport { defaultValueTypes, getDefaultValueType };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEA;;CAEC,GACD,MAAM,oBAAoB;IACtB,GAAG,gLAAA,CAAA,mBAAgB;IACnB,cAAc;IACd,OAAA,gLAAA,CAAA,QAAK;IACL,iBAAiB,gLAAA,CAAA,QAAK;IACtB,cAAc,gLAAA,CAAA,QAAK;IACnB,MAAM,gLAAA,CAAA,QAAK;IACX,QAAQ,gLAAA,CAAA,QAAK;IACb,eAAe;IACf,aAAa,gLAAA,CAAA,QAAK;IAClB,gBAAgB,gLAAA,CAAA,QAAK;IACrB,kBAAkB,gLAAA,CAAA,QAAK;IACvB,mBAAmB,gLAAA,CAAA,QAAK;IACxB,iBAAiB,gLAAA,CAAA,QAAK;IACtB,QAAA,mLAAA,CAAA,SAAM;IACN,cAAc,mLAAA,CAAA,SAAM;AACxB;AACA;;CAEC,GACD,MAAM,sBAAsB,CAAC,MAAQ,iBAAiB,CAAC,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4406, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/value/types/utils/animatable-none.mjs"], "sourcesContent": ["import { complex } from '../complex/index.mjs';\nimport { filter } from '../complex/filter.mjs';\nimport { getDefaultValueType } from '../maps/defaults.mjs';\n\nfunction getAnimatableNone(key, value) {\n    let defaultValueType = getDefaultValueType(key);\n    if (defaultValueType !== filter)\n        defaultValueType = complex;\n    // If value is not recognised as animatable, ie \"none\", create an animatable version origin based on the target\n    return defaultValueType.getAnimatableNone\n        ? defaultValueType.getAnimatableNone(value)\n        : undefined;\n}\n\nexport { getAnimatableNone };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,SAAS,kBAAkB,GAAG,EAAE,KAAK;IACjC,IAAI,mBAAmB,CAAA,GAAA,kLAAA,CAAA,sBAAmB,AAAD,EAAE;IAC3C,IAAI,qBAAqB,mLAAA,CAAA,SAAM,EAC3B,mBAAmB,kLAAA,CAAA,UAAO;IAC9B,+GAA+G;IAC/G,OAAO,iBAAiB,iBAAiB,GACnC,iBAAiB,iBAAiB,CAAC,SACnC;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4428, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/animation/keyframes/utils/make-none-animatable.mjs"], "sourcesContent": ["import { analyseComplexValue } from '../../../value/types/complex/index.mjs';\nimport { getAnimatableNone } from '../../../value/types/utils/animatable-none.mjs';\n\n/**\n * If we encounter keyframes like \"none\" or \"0\" and we also have keyframes like\n * \"#fff\" or \"200px 200px\" we want to find a keyframe to serve as a template for\n * the \"none\" keyframes. In this case \"#fff\" or \"200px 200px\" - then these get turned into\n * zero equivalents, i.e. \"#fff0\" or \"0px 0px\".\n */\nconst invalidTemplates = new Set([\"auto\", \"none\", \"0\"]);\nfunction makeNoneKeyframesAnimatable(unresolvedKeyframes, noneKeyframeIndexes, name) {\n    let i = 0;\n    let animatableTemplate = undefined;\n    while (i < unresolvedKeyframes.length && !animatableTemplate) {\n        const keyframe = unresolvedKeyframes[i];\n        if (typeof keyframe === \"string\" &&\n            !invalidTemplates.has(keyframe) &&\n            analyseComplexValue(keyframe).values.length) {\n            animatableTemplate = unresolvedKeyframes[i];\n        }\n        i++;\n    }\n    if (animatableTemplate && name) {\n        for (const noneIndex of noneKeyframeIndexes) {\n            unresolvedKeyframes[noneIndex] = getAnimatableNone(name, animatableTemplate);\n        }\n    }\n}\n\nexport { makeNoneKeyframesAnimatable };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA;;;;;CAKC,GACD,MAAM,mBAAmB,IAAI,IAAI;IAAC;IAAQ;IAAQ;CAAI;AACtD,SAAS,4BAA4B,mBAAmB,EAAE,mBAAmB,EAAE,IAAI;IAC/E,IAAI,IAAI;IACR,IAAI,qBAAqB;IACzB,MAAO,IAAI,oBAAoB,MAAM,IAAI,CAAC,mBAAoB;QAC1D,MAAM,WAAW,mBAAmB,CAAC,EAAE;QACvC,IAAI,OAAO,aAAa,YACpB,CAAC,iBAAiB,GAAG,CAAC,aACtB,CAAA,GAAA,kLAAA,CAAA,sBAAmB,AAAD,EAAE,UAAU,MAAM,CAAC,MAAM,EAAE;YAC7C,qBAAqB,mBAAmB,CAAC,EAAE;QAC/C;QACA;IACJ;IACA,IAAI,sBAAsB,MAAM;QAC5B,KAAK,MAAM,aAAa,oBAAqB;YACzC,mBAAmB,CAAC,UAAU,GAAG,CAAA,GAAA,6LAAA,CAAA,oBAAiB,AAAD,EAAE,MAAM;QAC7D;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4468, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/animation/keyframes/DOMKeyframesResolver.mjs"], "sourcesContent": ["import { positionalKeys } from '../../render/utils/keys-position.mjs';\nimport { findDimensionValueType } from '../../value/types/dimensions.mjs';\nimport { getVariableValue } from '../utils/css-variables-conversion.mjs';\nimport { isCSSVariableToken } from '../utils/is-css-variable.mjs';\nimport { KeyframeResolver } from './KeyframesResolver.mjs';\nimport { isNone } from './utils/is-none.mjs';\nimport { makeNoneKeyframesAnimatable } from './utils/make-none-animatable.mjs';\nimport { isNumOrPxType, positionalValues } from './utils/unit-conversion.mjs';\n\nclass DOMKeyframesResolver extends KeyframeResolver {\n    constructor(unresolvedKeyframes, onComplete, name, motionValue, element) {\n        super(unresolvedKeyframes, onComplete, name, motionValue, element, true);\n    }\n    readKeyframes() {\n        const { unresolvedKeyframes, element, name } = this;\n        if (!element || !element.current)\n            return;\n        super.readKeyframes();\n        /**\n         * If any keyframe is a CSS variable, we need to find its value by sampling the element\n         */\n        for (let i = 0; i < unresolvedKeyframes.length; i++) {\n            let keyframe = unresolvedKeyframes[i];\n            if (typeof keyframe === \"string\") {\n                keyframe = keyframe.trim();\n                if (isCSSVariableToken(keyframe)) {\n                    const resolved = getVariableValue(keyframe, element.current);\n                    if (resolved !== undefined) {\n                        unresolvedKeyframes[i] = resolved;\n                    }\n                    if (i === unresolvedKeyframes.length - 1) {\n                        this.finalKeyframe = keyframe;\n                    }\n                }\n            }\n        }\n        /**\n         * Resolve \"none\" values. We do this potentially twice - once before and once after measuring keyframes.\n         * This could be seen as inefficient but it's a trade-off to avoid measurements in more situations, which\n         * have a far bigger performance impact.\n         */\n        this.resolveNoneKeyframes();\n        /**\n         * Check to see if unit type has changed. If so schedule jobs that will\n         * temporarily set styles to the destination keyframes.\n         * Skip if we have more than two keyframes or this isn't a positional value.\n         * TODO: We can throw if there are multiple keyframes and the value type changes.\n         */\n        if (!positionalKeys.has(name) || unresolvedKeyframes.length !== 2) {\n            return;\n        }\n        const [origin, target] = unresolvedKeyframes;\n        const originType = findDimensionValueType(origin);\n        const targetType = findDimensionValueType(target);\n        /**\n         * Either we don't recognise these value types or we can animate between them.\n         */\n        if (originType === targetType)\n            return;\n        /**\n         * If both values are numbers or pixels, we can animate between them by\n         * converting them to numbers.\n         */\n        if (isNumOrPxType(originType) && isNumOrPxType(targetType)) {\n            for (let i = 0; i < unresolvedKeyframes.length; i++) {\n                const value = unresolvedKeyframes[i];\n                if (typeof value === \"string\") {\n                    unresolvedKeyframes[i] = parseFloat(value);\n                }\n            }\n        }\n        else if (positionalValues[name]) {\n            /**\n             * Else, the only way to resolve this is by measuring the element.\n             */\n            this.needsMeasurement = true;\n        }\n    }\n    resolveNoneKeyframes() {\n        const { unresolvedKeyframes, name } = this;\n        const noneKeyframeIndexes = [];\n        for (let i = 0; i < unresolvedKeyframes.length; i++) {\n            if (unresolvedKeyframes[i] === null ||\n                isNone(unresolvedKeyframes[i])) {\n                noneKeyframeIndexes.push(i);\n            }\n        }\n        if (noneKeyframeIndexes.length) {\n            makeNoneKeyframesAnimatable(unresolvedKeyframes, noneKeyframeIndexes, name);\n        }\n    }\n    measureInitialState() {\n        const { element, unresolvedKeyframes, name } = this;\n        if (!element || !element.current)\n            return;\n        if (name === \"height\") {\n            this.suspendedScrollY = window.pageYOffset;\n        }\n        this.measuredOrigin = positionalValues[name](element.measureViewportBox(), window.getComputedStyle(element.current));\n        unresolvedKeyframes[0] = this.measuredOrigin;\n        // Set final key frame to measure after next render\n        const measureKeyframe = unresolvedKeyframes[unresolvedKeyframes.length - 1];\n        if (measureKeyframe !== undefined) {\n            element.getValue(name, measureKeyframe).jump(measureKeyframe, false);\n        }\n    }\n    measureEndState() {\n        const { element, name, unresolvedKeyframes } = this;\n        if (!element || !element.current)\n            return;\n        const value = element.getValue(name);\n        value && value.jump(this.measuredOrigin, false);\n        const finalKeyframeIndex = unresolvedKeyframes.length - 1;\n        const finalKeyframe = unresolvedKeyframes[finalKeyframeIndex];\n        unresolvedKeyframes[finalKeyframeIndex] = positionalValues[name](element.measureViewportBox(), window.getComputedStyle(element.current));\n        if (finalKeyframe !== null && this.finalKeyframe === undefined) {\n            this.finalKeyframe = finalKeyframe;\n        }\n        // If we removed transform values, reapply them before the next render\n        if (this.removedTransforms?.length) {\n            this.removedTransforms.forEach(([unsetTransformName, unsetTransformValue]) => {\n                element\n                    .getValue(unsetTransformName)\n                    .set(unsetTransformValue);\n            });\n        }\n        this.resolveNoneKeyframes();\n    }\n}\n\nexport { DOMKeyframesResolver };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAEA,MAAM,6BAA6B,2LAAA,CAAA,mBAAgB;IAC/C,YAAY,mBAAmB,EAAE,UAAU,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,CAAE;QACrE,KAAK,CAAC,qBAAqB,YAAY,MAAM,aAAa,SAAS;IACvE;IACA,gBAAgB;QACZ,MAAM,EAAE,mBAAmB,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,IAAI;QACnD,IAAI,CAAC,WAAW,CAAC,QAAQ,OAAO,EAC5B;QACJ,KAAK,CAAC;QACN;;SAEC,GACD,IAAK,IAAI,IAAI,GAAG,IAAI,oBAAoB,MAAM,EAAE,IAAK;YACjD,IAAI,WAAW,mBAAmB,CAAC,EAAE;YACrC,IAAI,OAAO,aAAa,UAAU;gBAC9B,WAAW,SAAS,IAAI;gBACxB,IAAI,CAAA,GAAA,2LAAA,CAAA,qBAAkB,AAAD,EAAE,WAAW;oBAC9B,MAAM,WAAW,CAAA,GAAA,oMAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU,QAAQ,OAAO;oBAC3D,IAAI,aAAa,WAAW;wBACxB,mBAAmB,CAAC,EAAE,GAAG;oBAC7B;oBACA,IAAI,MAAM,oBAAoB,MAAM,GAAG,GAAG;wBACtC,IAAI,CAAC,aAAa,GAAG;oBACzB;gBACJ;YACJ;QACJ;QACA;;;;SAIC,GACD,IAAI,CAAC,oBAAoB;QACzB;;;;;SAKC,GACD,IAAI,CAAC,mLAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,SAAS,oBAAoB,MAAM,KAAK,GAAG;YAC/D;QACJ;QACA,MAAM,CAAC,QAAQ,OAAO,GAAG;QACzB,MAAM,aAAa,CAAA,GAAA,4KAAA,CAAA,yBAAsB,AAAD,EAAE;QAC1C,MAAM,aAAa,CAAA,GAAA,4KAAA,CAAA,yBAAsB,AAAD,EAAE;QAC1C;;SAEC,GACD,IAAI,eAAe,YACf;QACJ;;;SAGC,GACD,IAAI,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE,eAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE,aAAa;YACxD,IAAK,IAAI,IAAI,GAAG,IAAI,oBAAoB,MAAM,EAAE,IAAK;gBACjD,MAAM,QAAQ,mBAAmB,CAAC,EAAE;gBACpC,IAAI,OAAO,UAAU,UAAU;oBAC3B,mBAAmB,CAAC,EAAE,GAAG,WAAW;gBACxC;YACJ;QACJ,OACK,IAAI,qMAAA,CAAA,mBAAgB,CAAC,KAAK,EAAE;YAC7B;;aAEC,GACD,IAAI,CAAC,gBAAgB,GAAG;QAC5B;IACJ;IACA,uBAAuB;QACnB,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,GAAG,IAAI;QAC1C,MAAM,sBAAsB,EAAE;QAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,oBAAoB,MAAM,EAAE,IAAK;YACjD,IAAI,mBAAmB,CAAC,EAAE,KAAK,QAC3B,CAAA,GAAA,6LAAA,CAAA,SAAM,AAAD,EAAE,mBAAmB,CAAC,EAAE,GAAG;gBAChC,oBAAoB,IAAI,CAAC;YAC7B;QACJ;QACA,IAAI,oBAAoB,MAAM,EAAE;YAC5B,CAAA,GAAA,6MAAA,CAAA,8BAA2B,AAAD,EAAE,qBAAqB,qBAAqB;QAC1E;IACJ;IACA,sBAAsB;QAClB,MAAM,EAAE,OAAO,EAAE,mBAAmB,EAAE,IAAI,EAAE,GAAG,IAAI;QACnD,IAAI,CAAC,WAAW,CAAC,QAAQ,OAAO,EAC5B;QACJ,IAAI,SAAS,UAAU;YACnB,IAAI,CAAC,gBAAgB,GAAG,OAAO,WAAW;QAC9C;QACA,IAAI,CAAC,cAAc,GAAG,qMAAA,CAAA,mBAAgB,CAAC,KAAK,CAAC,QAAQ,kBAAkB,IAAI,OAAO,gBAAgB,CAAC,QAAQ,OAAO;QAClH,mBAAmB,CAAC,EAAE,GAAG,IAAI,CAAC,cAAc;QAC5C,mDAAmD;QACnD,MAAM,kBAAkB,mBAAmB,CAAC,oBAAoB,MAAM,GAAG,EAAE;QAC3E,IAAI,oBAAoB,WAAW;YAC/B,QAAQ,QAAQ,CAAC,MAAM,iBAAiB,IAAI,CAAC,iBAAiB;QAClE;IACJ;IACA,kBAAkB;QACd,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,mBAAmB,EAAE,GAAG,IAAI;QACnD,IAAI,CAAC,WAAW,CAAC,QAAQ,OAAO,EAC5B;QACJ,MAAM,QAAQ,QAAQ,QAAQ,CAAC;QAC/B,SAAS,MAAM,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;QACzC,MAAM,qBAAqB,oBAAoB,MAAM,GAAG;QACxD,MAAM,gBAAgB,mBAAmB,CAAC,mBAAmB;QAC7D,mBAAmB,CAAC,mBAAmB,GAAG,qMAAA,CAAA,mBAAgB,CAAC,KAAK,CAAC,QAAQ,kBAAkB,IAAI,OAAO,gBAAgB,CAAC,QAAQ,OAAO;QACtI,IAAI,kBAAkB,QAAQ,IAAI,CAAC,aAAa,KAAK,WAAW;YAC5D,IAAI,CAAC,aAAa,GAAG;QACzB;QACA,sEAAsE;QACtE,IAAI,IAAI,CAAC,iBAAiB,EAAE,QAAQ;YAChC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,oBAAoB,oBAAoB;gBACrE,QACK,QAAQ,CAAC,oBACT,GAAG,CAAC;YACb;QACJ;QACA,IAAI,CAAC,oBAAoB;IAC7B;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4600, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/node_modules/motion-dom/dist/es/value/types/utils/find.mjs"], "sourcesContent": ["import { color } from '../color/index.mjs';\nimport { complex } from '../complex/index.mjs';\nimport { dimensionValueTypes } from '../dimensions.mjs';\nimport { testValueType } from '../test.mjs';\n\n/**\n * A list of all ValueTypes\n */\nconst valueTypes = [...dimensionValueTypes, color, complex];\n/**\n * Tests a value against the list of ValueTypes\n */\nconst findValueType = (v) => valueTypes.find(testValueType(v));\n\nexport { findValueType };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEA;;CAEC,GACD,MAAM,aAAa;OAAI,4KAAA,CAAA,sBAAmB;IAAE,gLAAA,CAAA,QAAK;IAAE,kLAAA,CAAA,UAAO;CAAC;AAC3D;;CAEC,GACD,MAAM,gBAAgB,CAAC,IAAM,WAAW,IAAI,CAAC,CAAA,GAAA,sKAAA,CAAA,gBAAa,AAAD,EAAE", "ignoreList": [0], "debugId": null}}]}