import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { Toaster } from 'sonner'
import AuthProvider from '@/components/providers/AuthProvider'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Pro Matos Afrique Ouest - Écosystème Professionnel Électrique',
  description: 'La plateforme de référence pour les professionnels de l\'électrique en Afrique de l\'Ouest. Hub d\'information, validation technique, kits de prescription et club exclusif.',
  keywords: 'électrique, Afrique Ouest, professionnel, validation, prescription, club, réseau',
  authors: [{ name: 'Pro Matos Afrique Ouest' }],
  openGraph: {
    title: 'Pro Matos Afrique Ouest',
    description: 'L\'écosystème professionnel de l\'électrique en Afrique de l\'Ouest',
    type: 'website',
    locale: 'fr_FR',
  },
  robots: {
    index: true,
    follow: true,
  },
  viewport: {
    width: 'device-width',
    initialScale: 1,
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="fr" className="h-full">
      <body className={`${inter.className} h-full antialiased`}>
        <AuthProvider>
          {children}
        </AuthProvider>
        <Toaster 
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: 'white',
              border: '1px solid #e5e7eb',
              color: '#374151',
            },
          }}
        />
      </body>
    </html>
  )
}
