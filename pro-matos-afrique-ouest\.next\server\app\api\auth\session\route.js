/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/session/route";
exports.ids = ["app/api/auth/session/route"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fsession%2Froute&page=%2Fapi%2Fauth%2Fsession%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fsession%2Froute.ts&appDir=C%3A%5CUsers%5CMartial%5CDocuments%5CPro%20Matos%20Afrique%20Ouest%5Cpro-matos-afrique-ouest%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CMartial%5CDocuments%5CPro%20Matos%20Afrique%20Ouest%5Cpro-matos-afrique-ouest&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fsession%2Froute&page=%2Fapi%2Fauth%2Fsession%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fsession%2Froute.ts&appDir=C%3A%5CUsers%5CMartial%5CDocuments%5CPro%20Matos%20Afrique%20Ouest%5Cpro-matos-afrique-ouest%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CMartial%5CDocuments%5CPro%20Matos%20Afrique%20Ouest%5Cpro-matos-afrique-ouest&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Martial_Documents_Pro_Matos_Afrique_Ouest_pro_matos_afrique_ouest_src_app_api_auth_session_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/session/route.ts */ \"(rsc)/./src/app/api/auth/session/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/session/route\",\n        pathname: \"/api/auth/session\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/session/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\app\\\\api\\\\auth\\\\session\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Martial_Documents_Pro_Matos_Afrique_Ouest_pro_matos_afrique_ouest_src_app_api_auth_session_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/auth/session/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fsession%2Froute&page=%2Fapi%2Fauth%2Fsession%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fsession%2Froute.ts&appDir=C%3A%5CUsers%5CMartial%5CDocuments%5CPro%20Matos%20Afrique%20Ouest%5Cpro-matos-afrique-ouest%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CMartial%5CDocuments%5CPro%20Matos%20Afrique%20Ouest%5Cpro-matos-afrique-ouest&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/auth/session/route.ts":
/*!*******************************************!*\
  !*** ./src/app/api/auth/session/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(rsc)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n\n\nasync function POST(request) {\n    try {\n        const { access_token, refresh_token } = await request.json();\n        console.log(\"=== API SESSION CREATE ===\");\n        console.log(\"Access token re\\xe7u:\", access_token ? \"pr\\xe9sent\" : \"absent\");\n        console.log(\"Refresh token re\\xe7u:\", refresh_token ? \"pr\\xe9sent\" : \"absent\");\n        if (!access_token || !refresh_token) {\n            return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n                error: \"Tokens manquants\"\n            }, {\n                status: 400\n            });\n        }\n        const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n        const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createRouteHandlerClient)({\n            cookies: ()=>cookieStore\n        });\n        // Créer la session côté serveur\n        const { data, error } = await supabase.auth.setSession({\n            access_token,\n            refresh_token\n        });\n        if (error) {\n            console.error(\"Erreur cr\\xe9ation session serveur:\", error);\n            return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n                error: error.message\n            }, {\n                status: 400\n            });\n        }\n        if (data.session) {\n            console.log(\"✅ Session serveur cr\\xe9\\xe9e pour:\", data.session.user.email);\n            // Vérifier que la session est bien établie\n            const { data: { session: verifySession } } = await supabase.auth.getSession();\n            if (verifySession) {\n                console.log(\"✅ Session serveur v\\xe9rifi\\xe9e\");\n                return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n                    success: true,\n                    user: data.session.user\n                });\n            } else {\n                console.log(\"❌ Session serveur non v\\xe9rifi\\xe9e\");\n                return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n                    error: \"Session non \\xe9tablie c\\xf4t\\xe9 serveur\"\n                }, {\n                    status: 500\n                });\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n            error: \"Aucune session cr\\xe9\\xe9e\"\n        }, {\n            status: 500\n        });\n    } catch (error) {\n        console.error(\"Exception API session:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n            error: \"Erreur serveur\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9hdXRoL3Nlc3Npb24vcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBd0U7QUFDbEM7QUFDaUI7QUFFaEQsZUFBZUcsS0FBS0MsT0FBb0I7SUFDN0MsSUFBSTtRQUNGLE1BQU0sRUFBRUMsWUFBWSxFQUFFQyxhQUFhLEVBQUUsR0FBRyxNQUFNRixRQUFRRyxJQUFJO1FBRTFEQyxRQUFRQyxHQUFHLENBQUM7UUFDWkQsUUFBUUMsR0FBRyxDQUFDLHlCQUFzQkosZUFBZSxlQUFZO1FBQzdERyxRQUFRQyxHQUFHLENBQUMsMEJBQXVCSCxnQkFBZ0IsZUFBWTtRQUUvRCxJQUFJLENBQUNELGdCQUFnQixDQUFDQyxlQUFlO1lBQ25DLE9BQU9KLHFEQUFZQSxDQUFDSyxJQUFJLENBQ3RCO2dCQUFFRyxPQUFPO1lBQW1CLEdBQzVCO2dCQUFFQyxRQUFRO1lBQUk7UUFFbEI7UUFFQSxNQUFNQyxjQUFjWCxxREFBT0E7UUFDM0IsTUFBTVksV0FBV2IsdUZBQXdCQSxDQUFDO1lBQUVDLFNBQVMsSUFBTVc7UUFBWTtRQUV2RSxnQ0FBZ0M7UUFDaEMsTUFBTSxFQUFFRSxJQUFJLEVBQUVKLEtBQUssRUFBRSxHQUFHLE1BQU1HLFNBQVNFLElBQUksQ0FBQ0MsVUFBVSxDQUFDO1lBQ3JEWDtZQUNBQztRQUNGO1FBRUEsSUFBSUksT0FBTztZQUNURixRQUFRRSxLQUFLLENBQUMsdUNBQW9DQTtZQUNsRCxPQUFPUixxREFBWUEsQ0FBQ0ssSUFBSSxDQUN0QjtnQkFBRUcsT0FBT0EsTUFBTU8sT0FBTztZQUFDLEdBQ3ZCO2dCQUFFTixRQUFRO1lBQUk7UUFFbEI7UUFFQSxJQUFJRyxLQUFLSSxPQUFPLEVBQUU7WUFDaEJWLFFBQVFDLEdBQUcsQ0FBQyx1Q0FBaUNLLEtBQUtJLE9BQU8sQ0FBQ0MsSUFBSSxDQUFDQyxLQUFLO1lBRXBFLDJDQUEyQztZQUMzQyxNQUFNLEVBQUVOLE1BQU0sRUFBRUksU0FBU0csYUFBYSxFQUFFLEVBQUUsR0FBRyxNQUFNUixTQUFTRSxJQUFJLENBQUNPLFVBQVU7WUFFM0UsSUFBSUQsZUFBZTtnQkFDakJiLFFBQVFDLEdBQUcsQ0FBQztnQkFDWixPQUFPUCxxREFBWUEsQ0FBQ0ssSUFBSSxDQUFDO29CQUN2QmdCLFNBQVM7b0JBQ1RKLE1BQU1MLEtBQUtJLE9BQU8sQ0FBQ0MsSUFBSTtnQkFDekI7WUFDRixPQUFPO2dCQUNMWCxRQUFRQyxHQUFHLENBQUM7Z0JBQ1osT0FBT1AscURBQVlBLENBQUNLLElBQUksQ0FDdEI7b0JBQUVHLE9BQU87Z0JBQW1DLEdBQzVDO29CQUFFQyxRQUFRO2dCQUFJO1lBRWxCO1FBQ0Y7UUFFQSxPQUFPVCxxREFBWUEsQ0FBQ0ssSUFBSSxDQUN0QjtZQUFFRyxPQUFPO1FBQXVCLEdBQ2hDO1lBQUVDLFFBQVE7UUFBSTtJQUdsQixFQUFFLE9BQU9ELE9BQU87UUFDZEYsUUFBUUUsS0FBSyxDQUFDLDBCQUEwQkE7UUFDeEMsT0FBT1IscURBQVlBLENBQUNLLElBQUksQ0FDdEI7WUFBRUcsT0FBTztRQUFpQixHQUMxQjtZQUFFQyxRQUFRO1FBQUk7SUFFbEI7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL3Byby1tYXRvcy1hZnJpcXVlLW91ZXN0Ly4vc3JjL2FwcC9hcGkvYXV0aC9zZXNzaW9uL3JvdXRlLnRzP2MyYjYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlUm91dGVIYW5kbGVyQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL2F1dGgtaGVscGVycy1uZXh0anMnXG5pbXBvcnQgeyBjb29raWVzIH0gZnJvbSAnbmV4dC9oZWFkZXJzJ1xuaW1wb3J0IHsgTmV4dFJlcXVlc3QsIE5leHRSZXNwb25zZSB9IGZyb20gJ25leHQvc2VydmVyJ1xuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gUE9TVChyZXF1ZXN0OiBOZXh0UmVxdWVzdCkge1xuICB0cnkge1xuICAgIGNvbnN0IHsgYWNjZXNzX3Rva2VuLCByZWZyZXNoX3Rva2VuIH0gPSBhd2FpdCByZXF1ZXN0Lmpzb24oKVxuICAgIFxuICAgIGNvbnNvbGUubG9nKCc9PT0gQVBJIFNFU1NJT04gQ1JFQVRFID09PScpXG4gICAgY29uc29sZS5sb2coJ0FjY2VzcyB0b2tlbiByZcOndTonLCBhY2Nlc3NfdG9rZW4gPyAncHLDqXNlbnQnIDogJ2Fic2VudCcpXG4gICAgY29uc29sZS5sb2coJ1JlZnJlc2ggdG9rZW4gcmXDp3U6JywgcmVmcmVzaF90b2tlbiA/ICdwcsOpc2VudCcgOiAnYWJzZW50JylcblxuICAgIGlmICghYWNjZXNzX3Rva2VuIHx8ICFyZWZyZXNoX3Rva2VuKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHsgZXJyb3I6ICdUb2tlbnMgbWFucXVhbnRzJyB9LFxuICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgIClcbiAgICB9XG5cbiAgICBjb25zdCBjb29raWVTdG9yZSA9IGNvb2tpZXMoKVxuICAgIGNvbnN0IHN1cGFiYXNlID0gY3JlYXRlUm91dGVIYW5kbGVyQ2xpZW50KHsgY29va2llczogKCkgPT4gY29va2llU3RvcmUgfSlcblxuICAgIC8vIENyw6llciBsYSBzZXNzaW9uIGPDtHTDqSBzZXJ2ZXVyXG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5zZXRTZXNzaW9uKHtcbiAgICAgIGFjY2Vzc190b2tlbixcbiAgICAgIHJlZnJlc2hfdG9rZW5cbiAgICB9KVxuXG4gICAgaWYgKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJldXIgY3LDqWF0aW9uIHNlc3Npb24gc2VydmV1cjonLCBlcnJvcilcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBlcnJvcjogZXJyb3IubWVzc2FnZSB9LFxuICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgIClcbiAgICB9XG5cbiAgICBpZiAoZGF0YS5zZXNzaW9uKSB7XG4gICAgICBjb25zb2xlLmxvZygn4pyFIFNlc3Npb24gc2VydmV1ciBjcsOpw6llIHBvdXI6JywgZGF0YS5zZXNzaW9uLnVzZXIuZW1haWwpXG4gICAgICBcbiAgICAgIC8vIFbDqXJpZmllciBxdWUgbGEgc2Vzc2lvbiBlc3QgYmllbiDDqXRhYmxpZVxuICAgICAgY29uc3QgeyBkYXRhOiB7IHNlc3Npb246IHZlcmlmeVNlc3Npb24gfSB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5nZXRTZXNzaW9uKClcbiAgICAgIFxuICAgICAgaWYgKHZlcmlmeVNlc3Npb24pIHtcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSBTZXNzaW9uIHNlcnZldXIgdsOpcmlmacOpZScpXG4gICAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgICAgICB1c2VyOiBkYXRhLnNlc3Npb24udXNlclxuICAgICAgICB9KVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS5sb2coJ+KdjCBTZXNzaW9uIHNlcnZldXIgbm9uIHbDqXJpZmnDqWUnKVxuICAgICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgICAgeyBlcnJvcjogJ1Nlc3Npb24gbm9uIMOpdGFibGllIGPDtHTDqSBzZXJ2ZXVyJyB9LFxuICAgICAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgICAgICApXG4gICAgICB9XG4gICAgfVxuXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgeyBlcnJvcjogJ0F1Y3VuZSBzZXNzaW9uIGNyw6nDqWUnIH0sXG4gICAgICB7IHN0YXR1czogNTAwIH1cbiAgICApXG5cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFeGNlcHRpb24gQVBJIHNlc3Npb246JywgZXJyb3IpXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgeyBlcnJvcjogJ0VycmV1ciBzZXJ2ZXVyJyB9LFxuICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgKVxuICB9XG59XG4iXSwibmFtZXMiOlsiY3JlYXRlUm91dGVIYW5kbGVyQ2xpZW50IiwiY29va2llcyIsIk5leHRSZXNwb25zZSIsIlBPU1QiLCJyZXF1ZXN0IiwiYWNjZXNzX3Rva2VuIiwicmVmcmVzaF90b2tlbiIsImpzb24iLCJjb25zb2xlIiwibG9nIiwiZXJyb3IiLCJzdGF0dXMiLCJjb29raWVTdG9yZSIsInN1cGFiYXNlIiwiZGF0YSIsImF1dGgiLCJzZXRTZXNzaW9uIiwibWVzc2FnZSIsInNlc3Npb24iLCJ1c2VyIiwiZW1haWwiLCJ2ZXJpZnlTZXNzaW9uIiwiZ2V0U2Vzc2lvbiIsInN1Y2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/session/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows","vendor-chunks/jose","vendor-chunks/set-cookie-parser"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fsession%2Froute&page=%2Fapi%2Fauth%2Fsession%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fsession%2Froute.ts&appDir=C%3A%5CUsers%5CMartial%5CDocuments%5CPro%20Matos%20Afrique%20Ouest%5Cpro-matos-afrique-ouest%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CMartial%5CDocuments%5CPro%20Matos%20Afrique%20Ouest%5Cpro-matos-afrique-ouest&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();