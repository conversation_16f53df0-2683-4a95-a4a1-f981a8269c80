{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/store/useStore.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { persist } from 'zustand/middleware'\nimport {\n  User,\n  Product,\n  Alert,\n  MarketAlert,\n  StockUpdate,\n  TrainingEvent,\n  NewsUpdate,\n  TechnicalDocument,\n  CompatibilityCheck,\n  ExpertConsultation,\n  ExpertProfile,\n  TechnicalResource,\n  TechnicalReport,\n  PrescriptionTemplate,\n  PrescriptionProject,\n  TechnicalNote,\n  ComplianceCertificate,\n  CalculationEngine\n} from '@/lib/supabase'\n\ninterface AppState {\n  // User state\n  user: User | null\n  isAuthenticated: boolean\n  \n  // Products state\n  products: Product[]\n  filteredProducts: Product[]\n  searchQuery: string\n  selectedCategory: string\n  \n  // Alerts state\n  alerts: Alert[]\n  unreadAlertsCount: number\n\n  // Hub d'Information state\n  marketAlerts: MarketAlert[]\n  stockUpdates: StockUpdate[]\n  trainingEvents: TrainingEvent[]\n  newsUpdates: NewsUpdate[]\n  realTimeConnected: boolean\n  lastUpdateTime: string\n\n  // Espace Conseil Technique state\n  technicalDocuments: TechnicalDocument[]\n  compatibilityChecks: CompatibilityCheck[]\n  expertConsultations: ExpertConsultation[]\n  expertProfiles: ExpertProfile[]\n  technicalResources: TechnicalResource[]\n  technicalReports: TechnicalReport[]\n  selectedExpert: ExpertProfile | null\n  activeConsultation: ExpertConsultation | null\n\n  // Module Prescripteur Professionnel state\n  prescriptionTemplates: PrescriptionTemplate[]\n  prescriptionProjects: PrescriptionProject[]\n  technicalNotes: TechnicalNote[]\n  complianceCertificates: ComplianceCertificate[]\n  calculationEngines: CalculationEngine[]\n  activeProject: PrescriptionProject | null\n  selectedTemplate: PrescriptionTemplate | null\n\n  // UI state\n  isLoading: boolean\n  isDarkMode: boolean\n  sidebarOpen: boolean\n  \n  // Actions\n  setUser: (user: User | null) => void\n  setProducts: (products: Product[]) => void\n  setSearchQuery: (query: string) => void\n  setSelectedCategory: (category: string) => void\n  setAlerts: (alerts: Alert[]) => void\n  markAlertAsRead: (alertId: string) => void\n\n  // Hub d'Information actions\n  setMarketAlerts: (alerts: MarketAlert[]) => void\n  addMarketAlert: (alert: MarketAlert) => void\n  setStockUpdates: (updates: StockUpdate[]) => void\n  addStockUpdate: (update: StockUpdate) => void\n  setTrainingEvents: (events: TrainingEvent[]) => void\n  setNewsUpdates: (news: NewsUpdate[]) => void\n  addNewsUpdate: (news: NewsUpdate) => void\n  setRealTimeConnected: (connected: boolean) => void\n  updateLastUpdateTime: () => void\n\n  // Espace Conseil Technique actions\n  setTechnicalDocuments: (documents: TechnicalDocument[]) => void\n  addTechnicalDocument: (document: TechnicalDocument) => void\n  updateTechnicalDocument: (id: string, updates: Partial<TechnicalDocument>) => void\n  setCompatibilityChecks: (checks: CompatibilityCheck[]) => void\n  addCompatibilityCheck: (check: CompatibilityCheck) => void\n  setExpertConsultations: (consultations: ExpertConsultation[]) => void\n  addExpertConsultation: (consultation: ExpertConsultation) => void\n  updateExpertConsultation: (id: string, updates: Partial<ExpertConsultation>) => void\n  setExpertProfiles: (profiles: ExpertProfile[]) => void\n  setTechnicalResources: (resources: TechnicalResource[]) => void\n  setTechnicalReports: (reports: TechnicalReport[]) => void\n  addTechnicalReport: (report: TechnicalReport) => void\n  setSelectedExpert: (expert: ExpertProfile | null) => void\n  setActiveConsultation: (consultation: ExpertConsultation | null) => void\n\n  // Module Prescripteur Professionnel actions\n  setPrescriptionTemplates: (templates: PrescriptionTemplate[]) => void\n  setPrescriptionProjects: (projects: PrescriptionProject[]) => void\n  addPrescriptionProject: (project: PrescriptionProject) => void\n  updatePrescriptionProject: (id: string, updates: Partial<PrescriptionProject>) => void\n  setTechnicalNotes: (notes: TechnicalNote[]) => void\n  addTechnicalNote: (note: TechnicalNote) => void\n  setComplianceCertificates: (certificates: ComplianceCertificate[]) => void\n  addComplianceCertificate: (certificate: ComplianceCertificate) => void\n  setCalculationEngines: (engines: CalculationEngine[]) => void\n  setActiveProject: (project: PrescriptionProject | null) => void\n  setSelectedTemplate: (template: PrescriptionTemplate | null) => void\n\n  setLoading: (loading: boolean) => void\n  toggleDarkMode: () => void\n  toggleSidebar: () => void\n  filterProducts: () => void\n}\n\nexport const useStore = create<AppState>()(\n  persist(\n    (set, get) => ({\n      // Initial state\n      user: null,\n      isAuthenticated: false,\n      products: [],\n      filteredProducts: [],\n      searchQuery: '',\n      selectedCategory: '',\n      alerts: [],\n      unreadAlertsCount: 0,\n\n      // Hub d'Information initial state\n      marketAlerts: [],\n      stockUpdates: [],\n      trainingEvents: [],\n      newsUpdates: [],\n      realTimeConnected: false,\n      lastUpdateTime: new Date().toISOString(),\n\n      // Espace Conseil Technique initial state\n      technicalDocuments: [],\n      compatibilityChecks: [],\n      expertConsultations: [],\n      expertProfiles: [],\n      technicalResources: [],\n      technicalReports: [],\n      selectedExpert: null,\n      activeConsultation: null,\n\n      // Module Prescripteur Professionnel initial state\n      prescriptionTemplates: [],\n      prescriptionProjects: [],\n      technicalNotes: [],\n      complianceCertificates: [],\n      calculationEngines: [],\n      activeProject: null,\n      selectedTemplate: null,\n\n      isLoading: false,\n      isDarkMode: false,\n      sidebarOpen: false,\n\n      // Actions\n      setUser: (user) => set({ \n        user, \n        isAuthenticated: !!user \n      }),\n\n      setProducts: (products) => {\n        set({ products })\n        get().filterProducts()\n      },\n\n      setSearchQuery: (searchQuery) => {\n        set({ searchQuery })\n        get().filterProducts()\n      },\n\n      setSelectedCategory: (selectedCategory) => {\n        set({ selectedCategory })\n        get().filterProducts()\n      },\n\n      setAlerts: (alerts) => {\n        const unreadAlertsCount = alerts.filter(alert => !alert.is_read).length\n        set({ alerts, unreadAlertsCount })\n      },\n\n      markAlertAsRead: (alertId) => {\n        const alerts = get().alerts.map(alert =>\n          alert.id === alertId ? { ...alert, is_read: true } : alert\n        )\n        const unreadAlertsCount = alerts.filter(alert => !alert.is_read).length\n        set({ alerts, unreadAlertsCount })\n      },\n\n      // Hub d'Information actions\n      setMarketAlerts: (marketAlerts) => {\n        set({ marketAlerts })\n        get().updateLastUpdateTime()\n      },\n\n      addMarketAlert: (alert) => {\n        const marketAlerts = [alert, ...get().marketAlerts].slice(0, 50) // Garder les 50 plus récentes\n        set({ marketAlerts })\n        get().updateLastUpdateTime()\n      },\n\n      setStockUpdates: (stockUpdates) => {\n        set({ stockUpdates })\n        get().updateLastUpdateTime()\n      },\n\n      addStockUpdate: (update) => {\n        const stockUpdates = [update, ...get().stockUpdates].slice(0, 100) // Garder les 100 plus récentes\n        set({ stockUpdates })\n        get().updateLastUpdateTime()\n      },\n\n      setTrainingEvents: (trainingEvents) => set({ trainingEvents }),\n\n      setNewsUpdates: (newsUpdates) => set({ newsUpdates }),\n\n      addNewsUpdate: (news) => {\n        const newsUpdates = [news, ...get().newsUpdates].slice(0, 20) // Garder les 20 plus récentes\n        set({ newsUpdates })\n        get().updateLastUpdateTime()\n      },\n\n      setRealTimeConnected: (realTimeConnected) => set({ realTimeConnected }),\n\n      updateLastUpdateTime: () => set({ lastUpdateTime: new Date().toISOString() }),\n\n      // Espace Conseil Technique actions\n      setTechnicalDocuments: (technicalDocuments) => set({ technicalDocuments }),\n\n      addTechnicalDocument: (document) => {\n        const technicalDocuments = [document, ...get().technicalDocuments]\n        set({ technicalDocuments })\n      },\n\n      updateTechnicalDocument: (id, updates) => {\n        const technicalDocuments = get().technicalDocuments.map(doc =>\n          doc.id === id ? { ...doc, ...updates } : doc\n        )\n        set({ technicalDocuments })\n      },\n\n      setCompatibilityChecks: (compatibilityChecks) => set({ compatibilityChecks }),\n\n      addCompatibilityCheck: (check) => {\n        const compatibilityChecks = [check, ...get().compatibilityChecks]\n        set({ compatibilityChecks })\n      },\n\n      setExpertConsultations: (expertConsultations) => set({ expertConsultations }),\n\n      addExpertConsultation: (consultation) => {\n        const expertConsultations = [consultation, ...get().expertConsultations]\n        set({ expertConsultations })\n      },\n\n      updateExpertConsultation: (id, updates) => {\n        const expertConsultations = get().expertConsultations.map(consult =>\n          consult.id === id ? { ...consult, ...updates } : consult\n        )\n        set({ expertConsultations })\n      },\n\n      setExpertProfiles: (expertProfiles) => set({ expertProfiles }),\n\n      setTechnicalResources: (technicalResources) => set({ technicalResources }),\n\n      setTechnicalReports: (technicalReports) => set({ technicalReports }),\n\n      addTechnicalReport: (report) => {\n        const technicalReports = [report, ...get().technicalReports]\n        set({ technicalReports })\n      },\n\n      setSelectedExpert: (selectedExpert) => set({ selectedExpert }),\n\n      setActiveConsultation: (activeConsultation) => set({ activeConsultation }),\n\n      // Module Prescripteur Professionnel actions\n      setPrescriptionTemplates: (prescriptionTemplates) => set({ prescriptionTemplates }),\n\n      setPrescriptionProjects: (prescriptionProjects) => set({ prescriptionProjects }),\n\n      addPrescriptionProject: (project) => {\n        const prescriptionProjects = [project, ...get().prescriptionProjects]\n        set({ prescriptionProjects })\n      },\n\n      updatePrescriptionProject: (id, updates) => {\n        const prescriptionProjects = get().prescriptionProjects.map(project =>\n          project.id === id ? { ...project, ...updates } : project\n        )\n        set({ prescriptionProjects })\n      },\n\n      setTechnicalNotes: (technicalNotes) => set({ technicalNotes }),\n\n      addTechnicalNote: (note) => {\n        const technicalNotes = [note, ...get().technicalNotes]\n        set({ technicalNotes })\n      },\n\n      setComplianceCertificates: (complianceCertificates) => set({ complianceCertificates }),\n\n      addComplianceCertificate: (certificate) => {\n        const complianceCertificates = [certificate, ...get().complianceCertificates]\n        set({ complianceCertificates })\n      },\n\n      setCalculationEngines: (calculationEngines) => set({ calculationEngines }),\n\n      setActiveProject: (activeProject) => set({ activeProject }),\n\n      setSelectedTemplate: (selectedTemplate) => set({ selectedTemplate }),\n\n      setLoading: (isLoading) => set({ isLoading }),\n\n      toggleDarkMode: () => set((state) => ({ \n        isDarkMode: !state.isDarkMode \n      })),\n\n      toggleSidebar: () => set((state) => ({ \n        sidebarOpen: !state.sidebarOpen \n      })),\n\n      filterProducts: () => {\n        const { products, searchQuery, selectedCategory } = get()\n        \n        let filtered = products\n\n        if (selectedCategory) {\n          filtered = filtered.filter(product => \n            product.category === selectedCategory\n          )\n        }\n\n        if (searchQuery) {\n          const query = searchQuery.toLowerCase()\n          filtered = filtered.filter(product =>\n            product.name.toLowerCase().includes(query) ||\n            product.description.toLowerCase().includes(query) ||\n            product.brand.toLowerCase().includes(query) ||\n            product.model.toLowerCase().includes(query)\n          )\n        }\n\n        set({ filteredProducts: filtered })\n      },\n    }),\n    {\n      name: 'pro-matos-storage',\n      partialize: (state) => ({\n        user: state.user,\n        isAuthenticated: state.isAuthenticated,\n        isDarkMode: state.isDarkMode,\n      }),\n    }\n  )\n)\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AA2HO,MAAM,WAAW,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC3B,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,MAAM;QACN,iBAAiB;QACjB,UAAU,EAAE;QACZ,kBAAkB,EAAE;QACpB,aAAa;QACb,kBAAkB;QAClB,QAAQ,EAAE;QACV,mBAAmB;QAEnB,kCAAkC;QAClC,cAAc,EAAE;QAChB,cAAc,EAAE;QAChB,gBAAgB,EAAE;QAClB,aAAa,EAAE;QACf,mBAAmB;QACnB,gBAAgB,IAAI,OAAO,WAAW;QAEtC,yCAAyC;QACzC,oBAAoB,EAAE;QACtB,qBAAqB,EAAE;QACvB,qBAAqB,EAAE;QACvB,gBAAgB,EAAE;QAClB,oBAAoB,EAAE;QACtB,kBAAkB,EAAE;QACpB,gBAAgB;QAChB,oBAAoB;QAEpB,kDAAkD;QAClD,uBAAuB,EAAE;QACzB,sBAAsB,EAAE;QACxB,gBAAgB,EAAE;QAClB,wBAAwB,EAAE;QAC1B,oBAAoB,EAAE;QACtB,eAAe;QACf,kBAAkB;QAElB,WAAW;QACX,YAAY;QACZ,aAAa;QAEb,UAAU;QACV,SAAS,CAAC,OAAS,IAAI;gBACrB;gBACA,iBAAiB,CAAC,CAAC;YACrB;QAEA,aAAa,CAAC;YACZ,IAAI;gBAAE;YAAS;YACf,MAAM,cAAc;QACtB;QAEA,gBAAgB,CAAC;YACf,IAAI;gBAAE;YAAY;YAClB,MAAM,cAAc;QACtB;QAEA,qBAAqB,CAAC;YACpB,IAAI;gBAAE;YAAiB;YACvB,MAAM,cAAc;QACtB;QAEA,WAAW,CAAC;YACV,MAAM,oBAAoB,OAAO,MAAM,CAAC,CAAA,QAAS,CAAC,MAAM,OAAO,EAAE,MAAM;YACvE,IAAI;gBAAE;gBAAQ;YAAkB;QAClC;QAEA,iBAAiB,CAAC;YAChB,MAAM,SAAS,MAAM,MAAM,CAAC,GAAG,CAAC,CAAA,QAC9B,MAAM,EAAE,KAAK,UAAU;oBAAE,GAAG,KAAK;oBAAE,SAAS;gBAAK,IAAI;YAEvD,MAAM,oBAAoB,OAAO,MAAM,CAAC,CAAA,QAAS,CAAC,MAAM,OAAO,EAAE,MAAM;YACvE,IAAI;gBAAE;gBAAQ;YAAkB;QAClC;QAEA,4BAA4B;QAC5B,iBAAiB,CAAC;YAChB,IAAI;gBAAE;YAAa;YACnB,MAAM,oBAAoB;QAC5B;QAEA,gBAAgB,CAAC;YACf,MAAM,eAAe;gBAAC;mBAAU,MAAM,YAAY;aAAC,CAAC,KAAK,CAAC,GAAG,IAAI,8BAA8B;;YAC/F,IAAI;gBAAE;YAAa;YACnB,MAAM,oBAAoB;QAC5B;QAEA,iBAAiB,CAAC;YAChB,IAAI;gBAAE;YAAa;YACnB,MAAM,oBAAoB;QAC5B;QAEA,gBAAgB,CAAC;YACf,MAAM,eAAe;gBAAC;mBAAW,MAAM,YAAY;aAAC,CAAC,KAAK,CAAC,GAAG,KAAK,+BAA+B;;YAClG,IAAI;gBAAE;YAAa;YACnB,MAAM,oBAAoB;QAC5B;QAEA,mBAAmB,CAAC,iBAAmB,IAAI;gBAAE;YAAe;QAE5D,gBAAgB,CAAC,cAAgB,IAAI;gBAAE;YAAY;QAEnD,eAAe,CAAC;YACd,MAAM,cAAc;gBAAC;mBAAS,MAAM,WAAW;aAAC,CAAC,KAAK,CAAC,GAAG,IAAI,8BAA8B;;YAC5F,IAAI;gBAAE;YAAY;YAClB,MAAM,oBAAoB;QAC5B;QAEA,sBAAsB,CAAC,oBAAsB,IAAI;gBAAE;YAAkB;QAErE,sBAAsB,IAAM,IAAI;gBAAE,gBAAgB,IAAI,OAAO,WAAW;YAAG;QAE3E,mCAAmC;QACnC,uBAAuB,CAAC,qBAAuB,IAAI;gBAAE;YAAmB;QAExE,sBAAsB,CAAC;YACrB,MAAM,qBAAqB;gBAAC;mBAAa,MAAM,kBAAkB;aAAC;YAClE,IAAI;gBAAE;YAAmB;QAC3B;QAEA,yBAAyB,CAAC,IAAI;YAC5B,MAAM,qBAAqB,MAAM,kBAAkB,CAAC,GAAG,CAAC,CAAA,MACtD,IAAI,EAAE,KAAK,KAAK;oBAAE,GAAG,GAAG;oBAAE,GAAG,OAAO;gBAAC,IAAI;YAE3C,IAAI;gBAAE;YAAmB;QAC3B;QAEA,wBAAwB,CAAC,sBAAwB,IAAI;gBAAE;YAAoB;QAE3E,uBAAuB,CAAC;YACtB,MAAM,sBAAsB;gBAAC;mBAAU,MAAM,mBAAmB;aAAC;YACjE,IAAI;gBAAE;YAAoB;QAC5B;QAEA,wBAAwB,CAAC,sBAAwB,IAAI;gBAAE;YAAoB;QAE3E,uBAAuB,CAAC;YACtB,MAAM,sBAAsB;gBAAC;mBAAiB,MAAM,mBAAmB;aAAC;YACxE,IAAI;gBAAE;YAAoB;QAC5B;QAEA,0BAA0B,CAAC,IAAI;YAC7B,MAAM,sBAAsB,MAAM,mBAAmB,CAAC,GAAG,CAAC,CAAA,UACxD,QAAQ,EAAE,KAAK,KAAK;oBAAE,GAAG,OAAO;oBAAE,GAAG,OAAO;gBAAC,IAAI;YAEnD,IAAI;gBAAE;YAAoB;QAC5B;QAEA,mBAAmB,CAAC,iBAAmB,IAAI;gBAAE;YAAe;QAE5D,uBAAuB,CAAC,qBAAuB,IAAI;gBAAE;YAAmB;QAExE,qBAAqB,CAAC,mBAAqB,IAAI;gBAAE;YAAiB;QAElE,oBAAoB,CAAC;YACnB,MAAM,mBAAmB;gBAAC;mBAAW,MAAM,gBAAgB;aAAC;YAC5D,IAAI;gBAAE;YAAiB;QACzB;QAEA,mBAAmB,CAAC,iBAAmB,IAAI;gBAAE;YAAe;QAE5D,uBAAuB,CAAC,qBAAuB,IAAI;gBAAE;YAAmB;QAExE,4CAA4C;QAC5C,0BAA0B,CAAC,wBAA0B,IAAI;gBAAE;YAAsB;QAEjF,yBAAyB,CAAC,uBAAyB,IAAI;gBAAE;YAAqB;QAE9E,wBAAwB,CAAC;YACvB,MAAM,uBAAuB;gBAAC;mBAAY,MAAM,oBAAoB;aAAC;YACrE,IAAI;gBAAE;YAAqB;QAC7B;QAEA,2BAA2B,CAAC,IAAI;YAC9B,MAAM,uBAAuB,MAAM,oBAAoB,CAAC,GAAG,CAAC,CAAA,UAC1D,QAAQ,EAAE,KAAK,KAAK;oBAAE,GAAG,OAAO;oBAAE,GAAG,OAAO;gBAAC,IAAI;YAEnD,IAAI;gBAAE;YAAqB;QAC7B;QAEA,mBAAmB,CAAC,iBAAmB,IAAI;gBAAE;YAAe;QAE5D,kBAAkB,CAAC;YACjB,MAAM,iBAAiB;gBAAC;mBAAS,MAAM,cAAc;aAAC;YACtD,IAAI;gBAAE;YAAe;QACvB;QAEA,2BAA2B,CAAC,yBAA2B,IAAI;gBAAE;YAAuB;QAEpF,0BAA0B,CAAC;YACzB,MAAM,yBAAyB;gBAAC;mBAAgB,MAAM,sBAAsB;aAAC;YAC7E,IAAI;gBAAE;YAAuB;QAC/B;QAEA,uBAAuB,CAAC,qBAAuB,IAAI;gBAAE;YAAmB;QAExE,kBAAkB,CAAC,gBAAkB,IAAI;gBAAE;YAAc;QAEzD,qBAAqB,CAAC,mBAAqB,IAAI;gBAAE;YAAiB;QAElE,YAAY,CAAC,YAAc,IAAI;gBAAE;YAAU;QAE3C,gBAAgB,IAAM,IAAI,CAAC,QAAU,CAAC;oBACpC,YAAY,CAAC,MAAM,UAAU;gBAC/B,CAAC;QAED,eAAe,IAAM,IAAI,CAAC,QAAU,CAAC;oBACnC,aAAa,CAAC,MAAM,WAAW;gBACjC,CAAC;QAED,gBAAgB;YACd,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,gBAAgB,EAAE,GAAG;YAEpD,IAAI,WAAW;YAEf,IAAI,kBAAkB;gBACpB,WAAW,SAAS,MAAM,CAAC,CAAA,UACzB,QAAQ,QAAQ,KAAK;YAEzB;YAEA,IAAI,aAAa;gBACf,MAAM,QAAQ,YAAY,WAAW;gBACrC,WAAW,SAAS,MAAM,CAAC,CAAA,UACzB,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,UACpC,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,UAC3C,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,UACrC,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC;YAEzC;YAEA,IAAI;gBAAE,kBAAkB;YAAS;QACnC;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,iBAAiB,MAAM,eAAe;YACtC,YAAY,MAAM,UAAU;QAC9B,CAAC;AACH", "debugId": null}}, {"offset": {"line": 356, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/services/expertService.ts"], "sourcesContent": ["import { \n  TechnicalDocument, \n  CompatibilityCheck, \n  ExpertConsultation, \n  ExpertProfile, \n  TechnicalResource, \n  TechnicalReport,\n  Product \n} from '@/lib/supabase'\n\n// Service pour l'Espace Conseil Technique Expert\nexport class ExpertService {\n  \n  // Simulation des experts disponibles\n  static generateExpertProfiles(): ExpertProfile[] {\n    return [\n      {\n        id: 'expert_001',\n        user_id: 'user_expert_001',\n        display_name: 'Ing. <PERSON><PERSON><PERSON>',\n        title: 'Ingénieur Électricien Senior',\n        company: 'Schneider Electric Afrique',\n        specialties: ['Installations BT', 'Protection électrique', 'Normes NF C 15-100'],\n        certifications: ['Schneider Electric Certified', 'IEC 61439 Expert', 'NF C 15-100'],\n        experience_years: 15,\n        rating: 4.9,\n        total_consultations: 247,\n        hourly_rate: 75000, // 75,000 FCFA/heure\n        availability_status: 'available',\n        languages: ['Français', 'Anglais'],\n        bio: 'Expert en installations électriques basse tension avec 15 ans d\\'expérience chez Schneider Electric. Spécialisé dans la mise en conformité selon les normes internationales.',\n        profile_image: '/images/experts/kouassi-yao.jpg',\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString(),\n      },\n      {\n        id: 'expert_002',\n        user_id: 'user_expert_002',\n        display_name: 'Dr. Aminata Traoré',\n        title: 'Experte Énergie Renouvelable',\n        company: 'SolarTech Solutions',\n        specialties: ['Photovoltaïque', 'Stockage énergie', 'Micro-réseaux'],\n        certifications: ['PV System Design', 'Battery Storage Expert', 'Grid Integration'],\n        experience_years: 12,\n        rating: 4.8,\n        total_consultations: 189,\n        hourly_rate: 85000, // 85,000 FCFA/heure\n        availability_status: 'available',\n        languages: ['Français', 'Anglais', 'Bambara'],\n        bio: 'Docteure en énergies renouvelables, spécialisée dans les systèmes photovoltaïques et le stockage d\\'énergie pour l\\'Afrique de l\\'Ouest.',\n        profile_image: '/images/experts/aminata-traore.jpg',\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString(),\n      },\n      {\n        id: 'expert_003',\n        user_id: 'user_expert_003',\n        display_name: 'Ing. Jean-Baptiste Kone',\n        title: 'Spécialiste Automatismes Industriels',\n        company: 'ABB Côte d\\'Ivoire',\n        specialties: ['Automatismes', 'Variateurs de vitesse', 'Supervision SCADA'],\n        certifications: ['ABB Certified', 'Siemens TIA Portal', 'Schneider Unity Pro'],\n        experience_years: 18,\n        rating: 4.7,\n        total_consultations: 312,\n        hourly_rate: 90000, // 90,000 FCFA/heure\n        availability_status: 'busy',\n        languages: ['Français', 'Anglais'],\n        bio: 'Ingénieur spécialisé en automatismes industriels avec une expertise approfondie des systèmes de contrôle et supervision.',\n        profile_image: '/images/experts/jean-baptiste-kone.jpg',\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString(),\n      }\n    ]\n  }\n\n  // Simulation des ressources techniques\n  static generateTechnicalResources(): TechnicalResource[] {\n    return [\n      {\n        id: 'resource_001',\n        title: 'Guide d\\'Installation NF C 15-100',\n        description: 'Guide complet pour les installations électriques selon la norme NF C 15-100',\n        type: 'guide',\n        category: 'Installation',\n        difficulty_level: 'intermediate',\n        duration_minutes: 45,\n        file_url: '/resources/guide-nfc15100.pdf',\n        thumbnail_url: '/images/resources/nfc15100-thumb.jpg',\n        tags: ['norme', 'installation', 'basse tension'],\n        author: 'Ing. Kouassi Yao',\n        views_count: 1247,\n        downloads_count: 892,\n        rating: 4.8,\n        membership_required: 'silver',\n        is_featured: true,\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString(),\n      },\n      {\n        id: 'resource_002',\n        title: 'Tutoriel Dimensionnement Photovoltaïque',\n        description: 'Méthode de calcul pour dimensionner une installation photovoltaïque',\n        type: 'tutorial',\n        category: 'Énergie Renouvelable',\n        difficulty_level: 'advanced',\n        duration_minutes: 60,\n        video_url: '/videos/dimensionnement-pv.mp4',\n        thumbnail_url: '/images/resources/pv-sizing-thumb.jpg',\n        tags: ['photovoltaïque', 'dimensionnement', 'calcul'],\n        author: 'Dr. Aminata Traoré',\n        views_count: 2156,\n        downloads_count: 0,\n        rating: 4.9,\n        membership_required: 'gold',\n        is_featured: true,\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString(),\n      },\n      {\n        id: 'resource_003',\n        title: 'Template Rapport de Conformité',\n        description: 'Modèle de rapport de conformité pour installations électriques',\n        type: 'template',\n        category: 'Documentation',\n        difficulty_level: 'beginner',\n        file_url: '/templates/rapport-conformite.docx',\n        thumbnail_url: '/images/resources/template-thumb.jpg',\n        tags: ['template', 'conformité', 'rapport'],\n        author: 'Pro Matos Team',\n        views_count: 3421,\n        downloads_count: 2156,\n        rating: 4.6,\n        membership_required: 'bronze',\n        is_featured: false,\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString(),\n      }\n    ]\n  }\n\n  // Simulation des vérifications de compatibilité\n  static generateCompatibilityChecks(): CompatibilityCheck[] {\n    return [\n      {\n        id: 'compat_001',\n        user_id: 'user_001',\n        primary_product_id: 'prod_001',\n        secondary_product_id: 'prod_002',\n        compatibility_status: 'compatible',\n        compatibility_score: 95,\n        notes: 'Parfaitement compatible. Section de câble adaptée au courant nominal du disjoncteur.',\n        conditions: ['Respecter la longueur maximale de 50m', 'Utiliser des bornes adaptées'],\n        warnings: [],\n        recommendations: ['Prévoir une protection différentielle en amont'],\n        verified_by: 'expert_001',\n        created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // Il y a 2 heures\n        updated_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n      },\n      {\n        id: 'compat_002',\n        user_id: 'user_002',\n        primary_product_id: 'prod_003',\n        secondary_product_id: 'prod_004',\n        compatibility_status: 'conditional',\n        compatibility_score: 75,\n        notes: 'Compatible sous certaines conditions. Vérifier la tension nominale.',\n        conditions: ['Tension d\\'alimentation 400V obligatoire', 'Température ambiante < 40°C'],\n        warnings: ['Risque de surchauffe si mal ventilé'],\n        recommendations: ['Installer un système de ventilation forcée', 'Prévoir un contrôle de température'],\n        verified_by: 'expert_002',\n        created_at: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // Il y a 4 heures\n        updated_at: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),\n      }\n    ]\n  }\n\n  // Simulation des consultations d'experts\n  static generateExpertConsultations(): ExpertConsultation[] {\n    return [\n      {\n        id: 'consult_001',\n        user_id: 'user_001',\n        expert_id: 'expert_001',\n        title: 'Validation schéma électrique industriel',\n        description: 'Besoin de validation d\\'un schéma électrique pour une installation industrielle de 400kVA',\n        category: 'design',\n        priority: 'high',\n        status: 'in_progress',\n        attachments: ['/uploads/schema-industriel.pdf', '/uploads/liste-materiel.xlsx'],\n        estimated_duration: 120, // 2 heures\n        actual_duration: 90,\n        cost: 150000, // 150,000 FCFA\n        created_at: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(), // Il y a 3 heures\n        updated_at: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(), // Il y a 1 heure\n      },\n      {\n        id: 'consult_002',\n        user_id: 'user_003',\n        expert_id: 'expert_002',\n        title: 'Dimensionnement installation solaire',\n        description: 'Aide pour dimensionner une installation photovoltaïque de 50kWc avec stockage',\n        category: 'technical',\n        priority: 'medium',\n        status: 'resolved',\n        attachments: ['/uploads/consommation-electrique.pdf'],\n        estimated_duration: 90,\n        actual_duration: 85,\n        cost: 127500, // 127,500 FCFA\n        created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // Il y a 24 heures\n        updated_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // Il y a 2 heures\n        resolved_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n      }\n    ]\n  }\n\n  // Simulation des documents techniques\n  static generateTechnicalDocuments(): TechnicalDocument[] {\n    return [\n      {\n        id: 'doc_001',\n        user_id: 'user_001',\n        title: 'Schéma unifilaire installation industrielle',\n        description: 'Schéma électrique unifilaire pour usine textile 2MW',\n        file_url: '/uploads/schema-unifilaire-textile.pdf',\n        file_type: 'pdf',\n        file_size: 2456789, // ~2.5MB\n        validation_status: 'approved',\n        validation_notes: 'Schéma conforme aux normes. Quelques recommandations d\\'amélioration ajoutées.',\n        validated_by: 'expert_001',\n        validation_date: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),\n        category: 'design',\n        tags: ['schéma', 'industriel', 'textile', '2MW'],\n        is_public: false,\n        created_at: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),\n        updated_at: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),\n      },\n      {\n        id: 'doc_002',\n        user_id: 'user_002',\n        title: 'Plan d\\'installation photovoltaïque',\n        description: 'Plans détaillés pour installation PV 100kWc sur toiture industrielle',\n        file_url: '/uploads/plan-pv-100kwc.dwg',\n        file_type: 'cad',\n        file_size: 5234567, // ~5.2MB\n        validation_status: 'in_review',\n        validation_notes: 'En cours de révision par l\\'expert énergie renouvelable',\n        category: 'installation',\n        tags: ['photovoltaïque', 'toiture', '100kWc', 'industriel'],\n        is_public: false,\n        created_at: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),\n        updated_at: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),\n      }\n    ]\n  }\n\n  // Méthode pour vérifier la compatibilité entre deux produits\n  static async checkCompatibility(productA: Product, productB: Product): Promise<CompatibilityCheck> {\n    // Simulation d'une vérification de compatibilité intelligente\n    const compatibilityRules = [\n      {\n        condition: (a: Product, b: Product) => \n          a.category === 'Protection électrique' && b.category === 'Câblage',\n        score: 90,\n        status: 'compatible' as const,\n        notes: 'Disjoncteur et câble sont compatibles'\n      },\n      {\n        condition: (a: Product, b: Product) => \n          a.category === 'Énergie renouvelable' && b.category === 'Stockage',\n        score: 85,\n        status: 'compatible' as const,\n        notes: 'Système solaire et batterie compatibles'\n      }\n    ]\n\n    const rule = compatibilityRules.find(r => r.condition(productA, productB))\n    \n    return {\n      id: `compat_${Date.now()}`,\n      user_id: 'current_user',\n      primary_product_id: productA.id,\n      secondary_product_id: productB.id,\n      compatibility_status: rule?.status || 'unknown',\n      compatibility_score: rule?.score || 50,\n      notes: rule?.notes || 'Compatibilité à vérifier manuellement',\n      conditions: rule ? [] : ['Vérification manuelle requise'],\n      warnings: [],\n      recommendations: ['Consulter un expert pour validation'],\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString(),\n    }\n  }\n\n  // Méthode pour générer un rapport technique\n  static generateTechnicalReport(\n    type: 'validation' | 'compatibility' | 'installation',\n    data: any\n  ): TechnicalReport {\n    return {\n      id: `report_${Date.now()}`,\n      user_id: 'current_user',\n      title: `Rapport ${type} - ${new Date().toLocaleDateString('fr-FR')}`,\n      report_type: type,\n      content: data,\n      products: data.products || [],\n      recommendations: data.recommendations || [],\n      warnings: data.warnings || [],\n      compliance_status: data.compliance_status || 'unknown',\n      generated_by: 'system',\n      is_certified: false,\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString(),\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAWO,MAAM;IAEX,qCAAqC;IACrC,OAAO,yBAA0C;QAC/C,OAAO;YACL;gBACE,IAAI;gBACJ,SAAS;gBACT,cAAc;gBACd,OAAO;gBACP,SAAS;gBACT,aAAa;oBAAC;oBAAoB;oBAAyB;iBAAqB;gBAChF,gBAAgB;oBAAC;oBAAgC;oBAAoB;iBAAc;gBACnF,kBAAkB;gBAClB,QAAQ;gBACR,qBAAqB;gBACrB,aAAa;gBACb,qBAAqB;gBACrB,WAAW;oBAAC;oBAAY;iBAAU;gBAClC,KAAK;gBACL,eAAe;gBACf,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,cAAc;gBACd,OAAO;gBACP,SAAS;gBACT,aAAa;oBAAC;oBAAkB;oBAAoB;iBAAgB;gBACpE,gBAAgB;oBAAC;oBAAoB;oBAA0B;iBAAmB;gBAClF,kBAAkB;gBAClB,QAAQ;gBACR,qBAAqB;gBACrB,aAAa;gBACb,qBAAqB;gBACrB,WAAW;oBAAC;oBAAY;oBAAW;iBAAU;gBAC7C,KAAK;gBACL,eAAe;gBACf,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,cAAc;gBACd,OAAO;gBACP,SAAS;gBACT,aAAa;oBAAC;oBAAgB;oBAAyB;iBAAoB;gBAC3E,gBAAgB;oBAAC;oBAAiB;oBAAsB;iBAAsB;gBAC9E,kBAAkB;gBAClB,QAAQ;gBACR,qBAAqB;gBACrB,aAAa;gBACb,qBAAqB;gBACrB,WAAW;oBAAC;oBAAY;iBAAU;gBAClC,KAAK;gBACL,eAAe;gBACf,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;IACH;IAEA,uCAAuC;IACvC,OAAO,6BAAkD;QACvD,OAAO;YACL;gBACE,IAAI;gBACJ,OAAO;gBACP,aAAa;gBACb,MAAM;gBACN,UAAU;gBACV,kBAAkB;gBAClB,kBAAkB;gBAClB,UAAU;gBACV,eAAe;gBACf,MAAM;oBAAC;oBAAS;oBAAgB;iBAAgB;gBAChD,QAAQ;gBACR,aAAa;gBACb,iBAAiB;gBACjB,QAAQ;gBACR,qBAAqB;gBACrB,aAAa;gBACb,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,aAAa;gBACb,MAAM;gBACN,UAAU;gBACV,kBAAkB;gBAClB,kBAAkB;gBAClB,WAAW;gBACX,eAAe;gBACf,MAAM;oBAAC;oBAAkB;oBAAmB;iBAAS;gBACrD,QAAQ;gBACR,aAAa;gBACb,iBAAiB;gBACjB,QAAQ;gBACR,qBAAqB;gBACrB,aAAa;gBACb,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,aAAa;gBACb,MAAM;gBACN,UAAU;gBACV,kBAAkB;gBAClB,UAAU;gBACV,eAAe;gBACf,MAAM;oBAAC;oBAAY;oBAAc;iBAAU;gBAC3C,QAAQ;gBACR,aAAa;gBACb,iBAAiB;gBACjB,QAAQ;gBACR,qBAAqB;gBACrB,aAAa;gBACb,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;IACH;IAEA,gDAAgD;IAChD,OAAO,8BAAoD;QACzD,OAAO;YACL;gBACE,IAAI;gBACJ,SAAS;gBACT,oBAAoB;gBACpB,sBAAsB;gBACtB,sBAAsB;gBACtB,qBAAqB;gBACrB,OAAO;gBACP,YAAY;oBAAC;oBAAyC;iBAA+B;gBACrF,UAAU,EAAE;gBACZ,iBAAiB;oBAAC;iBAAiD;gBACnE,aAAa;gBACb,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;gBACjE,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;YACnE;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,oBAAoB;gBACpB,sBAAsB;gBACtB,sBAAsB;gBACtB,qBAAqB;gBACrB,OAAO;gBACP,YAAY;oBAAC;oBAA4C;iBAA8B;gBACvF,UAAU;oBAAC;iBAAsC;gBACjD,iBAAiB;oBAAC;oBAA8C;iBAAqC;gBACrG,aAAa;gBACb,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;gBACjE,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;YACnE;SACD;IACH;IAEA,yCAAyC;IACzC,OAAO,8BAAoD;QACzD,OAAO;YACL;gBACE,IAAI;gBACJ,SAAS;gBACT,WAAW;gBACX,OAAO;gBACP,aAAa;gBACb,UAAU;gBACV,UAAU;gBACV,QAAQ;gBACR,aAAa;oBAAC;oBAAkC;iBAA+B;gBAC/E,oBAAoB;gBACpB,iBAAiB;gBACjB,MAAM;gBACN,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;gBACjE,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;YACnE;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,WAAW;gBACX,OAAO;gBACP,aAAa;gBACb,UAAU;gBACV,UAAU;gBACV,QAAQ;gBACR,aAAa;oBAAC;iBAAuC;gBACrD,oBAAoB;gBACpB,iBAAiB;gBACjB,MAAM;gBACN,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW;gBAClE,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;gBACjE,aAAa,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;YACpE;SACD;IACH;IAEA,sCAAsC;IACtC,OAAO,6BAAkD;QACvD,OAAO;YACL;gBACE,IAAI;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa;gBACb,UAAU;gBACV,WAAW;gBACX,WAAW;gBACX,mBAAmB;gBACnB,kBAAkB;gBAClB,cAAc;gBACd,iBAAiB,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;gBACtE,UAAU;gBACV,MAAM;oBAAC;oBAAU;oBAAc;oBAAW;iBAAM;gBAChD,WAAW;gBACX,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW;gBAClE,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;YACnE;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa;gBACb,UAAU;gBACV,WAAW;gBACX,WAAW;gBACX,mBAAmB;gBACnB,kBAAkB;gBAClB,UAAU;gBACV,MAAM;oBAAC;oBAAkB;oBAAW;oBAAU;iBAAa;gBAC3D,WAAW;gBACX,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;gBACjE,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;YACnE;SACD;IACH;IAEA,6DAA6D;IAC7D,aAAa,mBAAmB,QAAiB,EAAE,QAAiB,EAA+B;QACjG,8DAA8D;QAC9D,MAAM,qBAAqB;YACzB;gBACE,WAAW,CAAC,GAAY,IACtB,EAAE,QAAQ,KAAK,2BAA2B,EAAE,QAAQ,KAAK;gBAC3D,OAAO;gBACP,QAAQ;gBACR,OAAO;YACT;YACA;gBACE,WAAW,CAAC,GAAY,IACtB,EAAE,QAAQ,KAAK,0BAA0B,EAAE,QAAQ,KAAK;gBAC1D,OAAO;gBACP,QAAQ;gBACR,OAAO;YACT;SACD;QAED,MAAM,OAAO,mBAAmB,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,CAAC,UAAU;QAEhE,OAAO;YACL,IAAI,CAAC,OAAO,EAAE,KAAK,GAAG,IAAI;YAC1B,SAAS;YACT,oBAAoB,SAAS,EAAE;YAC/B,sBAAsB,SAAS,EAAE;YACjC,sBAAsB,MAAM,UAAU;YACtC,qBAAqB,MAAM,SAAS;YACpC,OAAO,MAAM,SAAS;YACtB,YAAY,OAAO,EAAE,GAAG;gBAAC;aAAgC;YACzD,UAAU,EAAE;YACZ,iBAAiB;gBAAC;aAAsC;YACxD,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY,IAAI,OAAO,WAAW;QACpC;IACF;IAEA,4CAA4C;IAC5C,OAAO,wBACL,IAAqD,EACrD,IAAS,EACQ;QACjB,OAAO;YACL,IAAI,CAAC,OAAO,EAAE,KAAK,GAAG,IAAI;YAC1B,SAAS;YACT,OAAO,CAAC,QAAQ,EAAE,KAAK,GAAG,EAAE,IAAI,OAAO,kBAAkB,CAAC,UAAU;YACpE,aAAa;YACb,SAAS;YACT,UAAU,KAAK,QAAQ,IAAI,EAAE;YAC7B,iBAAiB,KAAK,eAAe,IAAI,EAAE;YAC3C,UAAU,KAAK,QAAQ,IAAI,EAAE;YAC7B,mBAAmB,KAAK,iBAAiB,IAAI;YAC7C,cAAc;YACd,cAAc;YACd,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY,IAAI,OAAO,WAAW;QACpC;IACF;AACF", "debugId": null}}, {"offset": {"line": 734, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPrice(price: number): string {\n  return new Intl.NumberFormat('fr-FR', {\n    style: 'currency',\n    currency: 'XOF',\n    minimumFractionDigits: 0,\n  }).format(price)\n}\n\nexport function formatDate(date: string | Date): string {\n  return new Intl.DateTimeFormat('fr-FR', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(new Date(date))\n}\n\nexport function getMembershipColor(level: string): string {\n  switch (level) {\n    case 'bronze':\n      return 'text-amber-600 bg-amber-50'\n    case 'silver':\n      return 'text-gray-600 bg-gray-50'\n    case 'gold':\n      return 'text-yellow-600 bg-yellow-50'\n    case 'platinum':\n      return 'text-purple-600 bg-purple-50'\n    default:\n      return 'text-gray-600 bg-gray-50'\n  }\n}\n\nexport function generateQRCode(productId: string): string {\n  // Simulation d'un QR code - à remplacer par une vraie génération\n  return `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(productId)}`\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa;IACvC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,mBAAmB,KAAa;IAC9C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,eAAe,SAAiB;IAC9C,iEAAiE;IACjE,OAAO,CAAC,8DAA8D,EAAE,mBAAmB,YAAY;AACzG;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 796, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/components/expert/ExpertSpace.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { \n  FileCheck, \n  Users, \n  MessageSquare, \n  BookOpen,\n  Upload,\n  Search,\n  Filter,\n  Clock,\n  Star,\n  CheckCircle,\n  AlertCircle,\n  FileText,\n  Zap\n} from 'lucide-react'\nimport { useStore } from '@/store/useStore'\nimport { ExpertService } from '@/services/expertService'\nimport { formatDate, getMembershipColor } from '@/lib/utils'\n\ninterface ExpertSpaceProps {\n  className?: string\n}\n\nexport default function ExpertSpace({ className = '' }: ExpertSpaceProps) {\n  const {\n    technicalDocuments,\n    compatibilityChecks,\n    expertConsultations,\n    expertProfiles,\n    technicalResources,\n    selectedExpert,\n    setTechnicalDocuments,\n    setCompatibilityChecks,\n    setExpertConsultations,\n    setExpertProfiles,\n    setTechnicalResources,\n    setSelectedExpert\n  } = useStore()\n\n  const [activeTab, setActiveTab] = useState<'validation' | 'compatibility' | 'consultation' | 'resources'>('validation')\n  const [searchQuery, setSearchQuery] = useState('')\n  const [filterStatus, setFilterStatus] = useState<'all' | 'pending' | 'approved' | 'rejected'>('all')\n\n  // Initialisation des données\n  useEffect(() => {\n    const loadInitialData = () => {\n      setTechnicalDocuments(ExpertService.generateTechnicalDocuments())\n      setCompatibilityChecks(ExpertService.generateCompatibilityChecks())\n      setExpertConsultations(ExpertService.generateExpertConsultations())\n      setExpertProfiles(ExpertService.generateExpertProfiles())\n      setTechnicalResources(ExpertService.generateTechnicalResources())\n    }\n\n    loadInitialData()\n  }, [])\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'approved':\n      case 'resolved':\n      case 'compatible':\n        return 'bg-green-100 text-green-800 border-green-200'\n      case 'rejected':\n      case 'incompatible':\n        return 'bg-red-100 text-red-800 border-red-200'\n      case 'pending':\n      case 'open':\n        return 'bg-yellow-100 text-yellow-800 border-yellow-200'\n      case 'in_review':\n      case 'in_progress':\n        return 'bg-blue-100 text-blue-800 border-blue-200'\n      case 'conditional':\n        return 'bg-orange-100 text-orange-800 border-orange-200'\n      default:\n        return 'bg-gray-100 text-gray-800 border-gray-200'\n    }\n  }\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'approved':\n      case 'resolved':\n      case 'compatible':\n        return <CheckCircle className=\"h-4 w-4\" />\n      case 'rejected':\n      case 'incompatible':\n        return <AlertCircle className=\"h-4 w-4\" />\n      case 'pending':\n      case 'open':\n        return <Clock className=\"h-4 w-4\" />\n      case 'in_review':\n      case 'in_progress':\n        return <Zap className=\"h-4 w-4\" />\n      default:\n        return <FileText className=\"h-4 w-4\" />\n    }\n  }\n\n  const tabs = [\n    { \n      id: 'validation', \n      label: 'Validation Technique', \n      icon: FileCheck, \n      count: technicalDocuments.length,\n      description: 'Upload et validation de documents techniques'\n    },\n    { \n      id: 'compatibility', \n      label: 'Compatibilité', \n      icon: Zap, \n      count: compatibilityChecks.length,\n      description: 'Vérification de compatibilité entre composants'\n    },\n    { \n      id: 'consultation', \n      label: 'Consultation Expert', \n      icon: MessageSquare, \n      count: expertConsultations.length,\n      description: 'Chat et conseils avec nos experts certifiés'\n    },\n    { \n      id: 'resources', \n      label: 'Bibliothèque', \n      icon: BookOpen, \n      count: technicalResources.length,\n      description: 'Ressources techniques et formations'\n    }\n  ]\n\n  return (\n    <div className={`bg-white rounded-lg shadow-lg ${className}`}>\n      {/* Header */}\n      <div className=\"p-6 border-b border-gray-200\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h2 className=\"text-2xl font-bold text-gray-900\">Espace Conseil Technique Expert</h2>\n            <p className=\"text-gray-600 mt-1\">Validation, compatibilité et expertise technique professionnelle</p>\n          </div>\n          \n          <div className=\"flex items-center space-x-4\">\n            <div className=\"text-right\">\n              <div className=\"text-sm text-gray-600\">Experts disponibles</div>\n              <div className=\"text-lg font-bold text-green-600\">\n                {expertProfiles.filter(e => e.availability_status === 'available').length}\n              </div>\n            </div>\n            \n            <button className=\"btn-premium\">\n              Nouvelle Consultation\n            </button>\n          </div>\n        </div>\n\n        {/* Barre de recherche et filtres */}\n        <div className=\"mt-4 flex items-center space-x-4\">\n          <div className=\"flex-1 relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n            <input\n              type=\"text\"\n              placeholder=\"Rechercher dans l'espace expert...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400 focus:border-transparent\"\n            />\n          </div>\n          \n          <select\n            value={filterStatus}\n            onChange={(e) => setFilterStatus(e.target.value as any)}\n            className=\"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400 focus:border-transparent\"\n          >\n            <option value=\"all\">Tous les statuts</option>\n            <option value=\"pending\">En attente</option>\n            <option value=\"approved\">Approuvé</option>\n            <option value=\"rejected\">Rejeté</option>\n          </select>\n        </div>\n      </div>\n\n      {/* Onglets */}\n      <div className=\"border-b border-gray-200\">\n        <nav className=\"flex space-x-8 px-6\">\n          {tabs.map((tab) => {\n            const Icon = tab.icon\n            const isActive = activeTab === tab.id\n            \n            return (\n              <button\n                key={tab.id}\n                onClick={() => setActiveTab(tab.id as any)}\n                className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${\n                  isActive\n                    ? 'border-amber-500 text-amber-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                <Icon className=\"h-5 w-5\" />\n                <span>{tab.label}</span>\n                {tab.count > 0 && (\n                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                    isActive ? 'bg-amber-100 text-amber-800' : 'bg-gray-100 text-gray-600'\n                  }`}>\n                    {tab.count}\n                  </span>\n                )}\n              </button>\n            )\n          })}\n        </nav>\n      </div>\n\n      {/* Description de l'onglet actif */}\n      <div className=\"px-6 py-3 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-200\">\n        <p className=\"text-sm text-blue-800\">\n          {tabs.find(tab => tab.id === activeTab)?.description}\n        </p>\n      </div>\n\n      {/* Contenu des onglets */}\n      <div className=\"p-6\">\n        <AnimatePresence mode=\"wait\">\n          {activeTab === 'validation' && (\n            <motion.div\n              key=\"validation\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -20 }}\n              className=\"space-y-6\"\n            >\n              {/* Zone d'upload */}\n              <div className=\"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-amber-400 transition-colors\">\n                <Upload className=\"h-12 w-12 mx-auto text-gray-400 mb-4\" />\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                  Uploader un Document Technique\n                </h3>\n                <p className=\"text-gray-600 mb-4\">\n                  Glissez-déposez vos fichiers ou cliquez pour sélectionner\n                </p>\n                <button className=\"btn-premium\">\n                  Sélectionner des fichiers\n                </button>\n                <p className=\"text-xs text-gray-500 mt-2\">\n                  Formats acceptés: PDF, DOC, CAD, Excel • Max 10MB\n                </p>\n              </div>\n\n              {/* Liste des documents */}\n              <div className=\"space-y-4\">\n                <h3 className=\"text-lg font-semibold text-gray-900\">Documents en Validation</h3>\n                {technicalDocuments.length === 0 ? (\n                  <div className=\"text-center py-8 text-gray-500\">\n                    <FileCheck className=\"h-12 w-12 mx-auto mb-4 text-gray-300\" />\n                    <p>Aucun document en cours de validation</p>\n                  </div>\n                ) : (\n                  technicalDocuments.map((doc) => (\n                    <motion.div\n                      key={doc.id}\n                      initial={{ opacity: 0, x: -20 }}\n                      animate={{ opacity: 1, x: 0 }}\n                      className=\"industrial-card p-4\"\n                    >\n                      <div className=\"flex items-start justify-between\">\n                        <div className=\"flex-1\">\n                          <div className=\"flex items-center space-x-2 mb-2\">\n                            <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(doc.validation_status)}`}>\n                              {getStatusIcon(doc.validation_status)}\n                              <span className=\"ml-1\">{doc.validation_status}</span>\n                            </span>\n                            <span className=\"text-xs text-gray-500\">{doc.category}</span>\n                          </div>\n                          <h4 className=\"font-semibold text-gray-900 mb-1\">{doc.title}</h4>\n                          <p className=\"text-sm text-gray-600 mb-2\">{doc.description}</p>\n                          <div className=\"flex items-center space-x-4 text-xs text-gray-500\">\n                            <span>Taille: {(doc.file_size / 1024 / 1024).toFixed(1)} MB</span>\n                            <span>Type: {doc.file_type.toUpperCase()}</span>\n                            <span>Créé: {formatDate(doc.created_at)}</span>\n                          </div>\n                          {doc.validation_notes && (\n                            <div className=\"mt-2 p-2 bg-blue-50 rounded text-sm text-blue-800\">\n                              <strong>Notes:</strong> {doc.validation_notes}\n                            </div>\n                          )}\n                        </div>\n                        <div className=\"ml-4\">\n                          <button className=\"text-amber-600 hover:text-amber-700 text-sm font-medium\">\n                            Voir détails →\n                          </button>\n                        </div>\n                      </div>\n                    </motion.div>\n                  ))\n                )}\n              </div>\n            </motion.div>\n          )}\n\n          {activeTab === 'compatibility' && (\n            <motion.div\n              key=\"compatibility\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -20 }}\n              className=\"space-y-6\"\n            >\n              {/* Simulateur de compatibilité */}\n              <div className=\"bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-6 border border-green-200\">\n                <h3 className=\"text-lg font-semibold text-green-900 mb-4\">\n                  Simulateur de Compatibilité\n                </h3>\n                <div className=\"grid md:grid-cols-2 gap-4 mb-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-green-800 mb-2\">\n                      Produit Principal\n                    </label>\n                    <select className=\"w-full px-3 py-2 border border-green-300 rounded-lg focus:ring-2 focus:ring-green-400\">\n                      <option>Sélectionner un produit...</option>\n                      <option>Disjoncteur C60N 32A</option>\n                      <option>Câble U1000R2V 3x2.5mm²</option>\n                    </select>\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-green-800 mb-2\">\n                      Produit Secondaire\n                    </label>\n                    <select className=\"w-full px-3 py-2 border border-green-300 rounded-lg focus:ring-2 focus:ring-green-400\">\n                      <option>Sélectionner un produit...</option>\n                      <option>Câble U1000R2V 3x2.5mm²</option>\n                      <option>Contacteur LC1D32</option>\n                    </select>\n                  </div>\n                </div>\n                <button className=\"btn-premium\">\n                  Vérifier la Compatibilité\n                </button>\n              </div>\n\n              {/* Résultats de compatibilité */}\n              <div className=\"space-y-4\">\n                <h3 className=\"text-lg font-semibold text-gray-900\">Vérifications Récentes</h3>\n                {compatibilityChecks.length === 0 ? (\n                  <div className=\"text-center py-8 text-gray-500\">\n                    <Zap className=\"h-12 w-12 mx-auto mb-4 text-gray-300\" />\n                    <p>Aucune vérification de compatibilité</p>\n                  </div>\n                ) : (\n                  compatibilityChecks.map((check) => (\n                    <motion.div\n                      key={check.id}\n                      initial={{ opacity: 0, x: -20 }}\n                      animate={{ opacity: 1, x: 0 }}\n                      className=\"industrial-card p-4\"\n                    >\n                      <div className=\"flex items-start justify-between\">\n                        <div className=\"flex-1\">\n                          <div className=\"flex items-center space-x-2 mb-2\">\n                            <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(check.compatibility_status)}`}>\n                              {getStatusIcon(check.compatibility_status)}\n                              <span className=\"ml-1\">{check.compatibility_status}</span>\n                            </span>\n                            <div className=\"flex items-center space-x-1\">\n                              <Star className=\"h-4 w-4 text-yellow-500\" />\n                              <span className=\"text-sm font-medium\">{check.compatibility_score}%</span>\n                            </div>\n                          </div>\n                          <h4 className=\"font-semibold text-gray-900 mb-1\">\n                            Produit #{check.primary_product_id} ↔ Produit #{check.secondary_product_id}\n                          </h4>\n                          <p className=\"text-sm text-gray-600 mb-2\">{check.notes}</p>\n\n                          {check.conditions && check.conditions.length > 0 && (\n                            <div className=\"mb-2\">\n                              <h5 className=\"text-xs font-medium text-blue-800 mb-1\">Conditions:</h5>\n                              <ul className=\"text-xs text-blue-700 space-y-1\">\n                                {check.conditions.map((condition, idx) => (\n                                  <li key={idx}>• {condition}</li>\n                                ))}\n                              </ul>\n                            </div>\n                          )}\n\n                          {check.warnings && check.warnings.length > 0 && (\n                            <div className=\"mb-2\">\n                              <h5 className=\"text-xs font-medium text-orange-800 mb-1\">Avertissements:</h5>\n                              <ul className=\"text-xs text-orange-700 space-y-1\">\n                                {check.warnings.map((warning, idx) => (\n                                  <li key={idx}>⚠ {warning}</li>\n                                ))}\n                              </ul>\n                            </div>\n                          )}\n\n                          <div className=\"flex items-center space-x-4 text-xs text-gray-500\">\n                            <span>Vérifié: {formatDate(check.created_at)}</span>\n                            {check.verified_by && <span>Par: Expert #{check.verified_by}</span>}\n                          </div>\n                        </div>\n                        <div className=\"ml-4\">\n                          <button className=\"text-amber-600 hover:text-amber-700 text-sm font-medium\">\n                            Rapport détaillé →\n                          </button>\n                        </div>\n                      </div>\n                    </motion.div>\n                  ))\n                )}\n              </div>\n            </motion.div>\n          )}\n\n          {activeTab === 'consultation' && (\n            <motion.div\n              key=\"consultation\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -20 }}\n              className=\"space-y-6\"\n            >\n              {/* Experts disponibles */}\n              <div className=\"grid md:grid-cols-3 gap-4\">\n                {expertProfiles.map((expert) => (\n                  <motion.div\n                    key={expert.id}\n                    initial={{ opacity: 0, scale: 0.9 }}\n                    animate={{ opacity: 1, scale: 1 }}\n                    className={`industrial-card p-4 cursor-pointer transition-all ${\n                      selectedExpert?.id === expert.id ? 'ring-2 ring-amber-400' : ''\n                    }`}\n                    onClick={() => setSelectedExpert(expert)}\n                  >\n                    <div className=\"text-center\">\n                      <div className=\"w-16 h-16 bg-gradient-to-r from-amber-400 to-amber-500 rounded-full flex items-center justify-center mx-auto mb-3\">\n                        <Users className=\"h-8 w-8 text-white\" />\n                      </div>\n                      <h4 className=\"font-semibold text-gray-900 mb-1\">{expert.display_name}</h4>\n                      <p className=\"text-sm text-gray-600 mb-2\">{expert.title}</p>\n                      <div className=\"flex items-center justify-center space-x-1 mb-2\">\n                        <Star className=\"h-4 w-4 text-yellow-500\" />\n                        <span className=\"text-sm font-medium\">{expert.rating}</span>\n                        <span className=\"text-xs text-gray-500\">({expert.total_consultations})</span>\n                      </div>\n                      <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${\n                        expert.availability_status === 'available' ? 'bg-green-100 text-green-800' :\n                        expert.availability_status === 'busy' ? 'bg-orange-100 text-orange-800' :\n                        'bg-gray-100 text-gray-800'\n                      }`}>\n                        {expert.availability_status}\n                      </div>\n                      <div className=\"mt-2 text-sm font-medium text-amber-600\">\n                        {expert.hourly_rate.toLocaleString()} FCFA/h\n                      </div>\n                    </div>\n                  </motion.div>\n                ))}\n              </div>\n\n              {/* Consultations actives */}\n              <div className=\"space-y-4\">\n                <h3 className=\"text-lg font-semibold text-gray-900\">Consultations en Cours</h3>\n                {expertConsultations.length === 0 ? (\n                  <div className=\"text-center py-8 text-gray-500\">\n                    <MessageSquare className=\"h-12 w-12 mx-auto mb-4 text-gray-300\" />\n                    <p>Aucune consultation active</p>\n                  </div>\n                ) : (\n                  expertConsultations.map((consultation) => (\n                    <motion.div\n                      key={consultation.id}\n                      initial={{ opacity: 0, x: -20 }}\n                      animate={{ opacity: 1, x: 0 }}\n                      className=\"industrial-card p-4\"\n                    >\n                      <div className=\"flex items-start justify-between\">\n                        <div className=\"flex-1\">\n                          <div className=\"flex items-center space-x-2 mb-2\">\n                            <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(consultation.status)}`}>\n                              {getStatusIcon(consultation.status)}\n                              <span className=\"ml-1\">{consultation.status}</span>\n                            </span>\n                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                              consultation.priority === 'urgent' ? 'bg-red-100 text-red-800' :\n                              consultation.priority === 'high' ? 'bg-orange-100 text-orange-800' :\n                              consultation.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :\n                              'bg-blue-100 text-blue-800'\n                            }`}>\n                              {consultation.priority}\n                            </span>\n                          </div>\n                          <h4 className=\"font-semibold text-gray-900 mb-1\">{consultation.title}</h4>\n                          <p className=\"text-sm text-gray-600 mb-2\">{consultation.description}</p>\n                          <div className=\"flex items-center space-x-4 text-xs text-gray-500\">\n                            <span>Expert: #{consultation.expert_id}</span>\n                            <span>Durée estimée: {consultation.estimated_duration}min</span>\n                            <span>Coût: {consultation.cost.toLocaleString()} FCFA</span>\n                            <span>Créé: {formatDate(consultation.created_at)}</span>\n                          </div>\n                        </div>\n                        <div className=\"ml-4\">\n                          <button className=\"text-amber-600 hover:text-amber-700 text-sm font-medium\">\n                            Rejoindre →\n                          </button>\n                        </div>\n                      </div>\n                    </motion.div>\n                  ))\n                )}\n              </div>\n            </motion.div>\n          )}\n\n          {activeTab === 'resources' && (\n            <motion.div\n              key=\"resources\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -20 }}\n              className=\"space-y-6\"\n            >\n              {/* Filtres par niveau */}\n              <div className=\"flex items-center space-x-4\">\n                <span className=\"text-sm font-medium text-gray-700\">Filtrer par niveau:</span>\n                {['beginner', 'intermediate', 'advanced', 'expert'].map((level) => (\n                  <button\n                    key={level}\n                    className=\"px-3 py-1 rounded-full text-xs font-medium border border-gray-300 hover:border-amber-400 hover:text-amber-600 transition-colors\"\n                  >\n                    {level}\n                  </button>\n                ))}\n              </div>\n\n              {/* Ressources techniques */}\n              <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6\">\n                {technicalResources.map((resource) => (\n                  <motion.div\n                    key={resource.id}\n                    initial={{ opacity: 0, scale: 0.9 }}\n                    animate={{ opacity: 1, scale: 1 }}\n                    className=\"industrial-card p-4\"\n                  >\n                    <div className=\"mb-3\">\n                      <div className=\"w-full h-32 bg-gray-200 rounded-lg mb-3 flex items-center justify-center\">\n                        <BookOpen className=\"h-8 w-8 text-gray-400\" />\n                      </div>\n                      <div className=\"flex items-center space-x-2 mb-2\">\n                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                          resource.type === 'tutorial' ? 'bg-blue-100 text-blue-800' :\n                          resource.type === 'guide' ? 'bg-green-100 text-green-800' :\n                          resource.type === 'video' ? 'bg-purple-100 text-purple-800' :\n                          'bg-gray-100 text-gray-800'\n                        }`}>\n                          {resource.type}\n                        </span>\n                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                          resource.difficulty_level === 'expert' ? 'bg-red-100 text-red-800' :\n                          resource.difficulty_level === 'advanced' ? 'bg-orange-100 text-orange-800' :\n                          resource.difficulty_level === 'intermediate' ? 'bg-yellow-100 text-yellow-800' :\n                          'bg-green-100 text-green-800'\n                        }`}>\n                          {resource.difficulty_level}\n                        </span>\n                      </div>\n                      <h4 className=\"font-semibold text-gray-900 mb-1\">{resource.title}</h4>\n                      <p className=\"text-sm text-gray-600 mb-2\">{resource.description}</p>\n                      <div className=\"flex items-center space-x-4 text-xs text-gray-500 mb-2\">\n                        {resource.duration_minutes && <span>{resource.duration_minutes}min</span>}\n                        <span>{resource.views_count} vues</span>\n                        <div className=\"flex items-center space-x-1\">\n                          <Star className=\"h-3 w-3 text-yellow-500\" />\n                          <span>{resource.rating}</span>\n                        </div>\n                      </div>\n                      {resource.membership_required && (\n                        <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getMembershipColor(resource.membership_required)}`}>\n                          {resource.membership_required}+ requis\n                        </div>\n                      )}\n                    </div>\n                    <button className=\"w-full btn-premium text-sm py-2\">\n                      Accéder à la ressource\n                    </button>\n                  </motion.div>\n                ))}\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AACA;AACA;AArBA;;;;;;;;AA2Be,SAAS,YAAY,EAAE,YAAY,EAAE,EAAoB;IACtE,MAAM,EACJ,kBAAkB,EAClB,mBAAmB,EACnB,mBAAmB,EACnB,cAAc,EACd,kBAAkB,EAClB,cAAc,EACd,qBAAqB,EACrB,sBAAsB,EACtB,sBAAsB,EACtB,iBAAiB,EACjB,qBAAqB,EACrB,iBAAiB,EAClB,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAEX,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiE;IAC1G,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA+C;IAE9F,6BAA6B;IAC7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB;YACtB,sBAAsB,gIAAA,CAAA,gBAAa,CAAC,0BAA0B;YAC9D,uBAAuB,gIAAA,CAAA,gBAAa,CAAC,2BAA2B;YAChE,uBAAuB,gIAAA,CAAA,gBAAa,CAAC,2BAA2B;YAChE,kBAAkB,gIAAA,CAAA,gBAAa,CAAC,sBAAsB;YACtD,sBAAsB,gIAAA,CAAA,gBAAa,CAAC,0BAA0B;QAChE;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;YACL,KAAK;gBACH,qBAAO,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;YACL,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;YACL,KAAK;gBACH,qBAAO,8OAAC,gMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;YACxB;gBACE,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;QAC/B;IACF;IAEA,MAAM,OAAO;QACX;YACE,IAAI;YACJ,OAAO;YACP,MAAM,gNAAA,CAAA,YAAS;YACf,OAAO,mBAAmB,MAAM;YAChC,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,gMAAA,CAAA,MAAG;YACT,OAAO,oBAAoB,MAAM;YACjC,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,wNAAA,CAAA,gBAAa;YACnB,OAAO,oBAAoB,MAAM;YACjC,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,8MAAA,CAAA,WAAQ;YACd,OAAO,mBAAmB,MAAM;YAChC,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAI,WAAW,CAAC,8BAA8B,EAAE,WAAW;;0BAE1D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;;0CAGpC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;0DACvC,8OAAC;gDAAI,WAAU;0DACZ,eAAe,MAAM,CAAC,CAAA,IAAK,EAAE,mBAAmB,KAAK,aAAa,MAAM;;;;;;;;;;;;kDAI7E,8OAAC;wCAAO,WAAU;kDAAc;;;;;;;;;;;;;;;;;;kCAOpC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;;;;;;;0CAId,8OAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gCAC/C,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAM;;;;;;kDACpB,8OAAC;wCAAO,OAAM;kDAAU;;;;;;kDACxB,8OAAC;wCAAO,OAAM;kDAAW;;;;;;kDACzB,8OAAC;wCAAO,OAAM;kDAAW;;;;;;;;;;;;;;;;;;;;;;;;0BAM/B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC;wBACT,MAAM,OAAO,IAAI,IAAI;wBACrB,MAAM,WAAW,cAAc,IAAI,EAAE;wBAErC,qBACE,8OAAC;4BAEC,SAAS,IAAM,aAAa,IAAI,EAAE;4BAClC,WAAW,CAAC,uFAAuF,EACjG,WACI,oCACA,8EACJ;;8CAEF,8OAAC;oCAAK,WAAU;;;;;;8CAChB,8OAAC;8CAAM,IAAI,KAAK;;;;;;gCACf,IAAI,KAAK,GAAG,mBACX,8OAAC;oCAAK,WAAW,CAAC,2CAA2C,EAC3D,WAAW,gCAAgC,6BAC3C;8CACC,IAAI,KAAK;;;;;;;2BAdT,IAAI,EAAE;;;;;oBAmBjB;;;;;;;;;;;0BAKJ,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BACV,KAAK,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,YAAY;;;;;;;;;;;0BAK7C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,yLAAA,CAAA,kBAAe;oBAAC,MAAK;;wBACnB,cAAc,8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,MAAM;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC3B,WAAU;;8CAGV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAGzD,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,8OAAC;4CAAO,WAAU;sDAAc;;;;;;sDAGhC,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAM5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAsC;;;;;;wCACnD,mBAAmB,MAAM,KAAK,kBAC7B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,8OAAC;8DAAE;;;;;;;;;;;mDAGL,mBAAmB,GAAG,CAAC,CAAC,oBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC9B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,WAAU;0DAEV,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAW,CAAC,kDAAkD,EAAE,eAAe,IAAI,iBAAiB,GAAG;;gFAC1G,cAAc,IAAI,iBAAiB;8FACpC,8OAAC;oFAAK,WAAU;8FAAQ,IAAI,iBAAiB;;;;;;;;;;;;sFAE/C,8OAAC;4EAAK,WAAU;sFAAyB,IAAI,QAAQ;;;;;;;;;;;;8EAEvD,8OAAC;oEAAG,WAAU;8EAAoC,IAAI,KAAK;;;;;;8EAC3D,8OAAC;oEAAE,WAAU;8EAA8B,IAAI,WAAW;;;;;;8EAC1D,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;;gFAAK;gFAAS,CAAC,IAAI,SAAS,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;gFAAG;;;;;;;sFACxD,8OAAC;;gFAAK;gFAAO,IAAI,SAAS,CAAC,WAAW;;;;;;;sFACtC,8OAAC;;gFAAK;gFAAO,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,IAAI,UAAU;;;;;;;;;;;;;gEAEvC,IAAI,gBAAgB,kBACnB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;sFAAO;;;;;;wEAAe;wEAAE,IAAI,gBAAgB;;;;;;;;;;;;;sEAInD,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAO,WAAU;0EAA0D;;;;;;;;;;;;;;;;;+CA5B3E,IAAI,EAAE;;;;;;;;;;;;2BAlCf;;;;;wBA0EP,cAAc,iCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,MAAM;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC3B,WAAU;;8CAGV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA4C;;;;;;sDAG1D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAAgD;;;;;;sEAGjE,8OAAC;4DAAO,WAAU;;8EAChB,8OAAC;8EAAO;;;;;;8EACR,8OAAC;8EAAO;;;;;;8EACR,8OAAC;8EAAO;;;;;;;;;;;;;;;;;;8DAGZ,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAAgD;;;;;;sEAGjE,8OAAC;4DAAO,WAAU;;8EAChB,8OAAC;8EAAO;;;;;;8EACR,8OAAC;8EAAO;;;;;;8EACR,8OAAC;8EAAO;;;;;;;;;;;;;;;;;;;;;;;;sDAId,8OAAC;4CAAO,WAAU;sDAAc;;;;;;;;;;;;8CAMlC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAsC;;;;;;wCACnD,oBAAoB,MAAM,KAAK,kBAC9B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;8DACf,8OAAC;8DAAE;;;;;;;;;;;mDAGL,oBAAoB,GAAG,CAAC,CAAC,sBACvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC9B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,WAAU;0DAEV,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAW,CAAC,kDAAkD,EAAE,eAAe,MAAM,oBAAoB,GAAG;;gFAC/G,cAAc,MAAM,oBAAoB;8FACzC,8OAAC;oFAAK,WAAU;8FAAQ,MAAM,oBAAoB;;;;;;;;;;;;sFAEpD,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,kMAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;8FAChB,8OAAC;oFAAK,WAAU;;wFAAuB,MAAM,mBAAmB;wFAAC;;;;;;;;;;;;;;;;;;;8EAGrE,8OAAC;oEAAG,WAAU;;wEAAmC;wEACrC,MAAM,kBAAkB;wEAAC;wEAAa,MAAM,oBAAoB;;;;;;;8EAE5E,8OAAC;oEAAE,WAAU;8EAA8B,MAAM,KAAK;;;;;;gEAErD,MAAM,UAAU,IAAI,MAAM,UAAU,CAAC,MAAM,GAAG,mBAC7C,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAU;sFAAyC;;;;;;sFACvD,8OAAC;4EAAG,WAAU;sFACX,MAAM,UAAU,CAAC,GAAG,CAAC,CAAC,WAAW,oBAChC,8OAAC;;wFAAa;wFAAG;;mFAAR;;;;;;;;;;;;;;;;gEAMhB,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC,MAAM,GAAG,mBACzC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAU;sFAA2C;;;;;;sFACzD,8OAAC;4EAAG,WAAU;sFACX,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,oBAC5B,8OAAC;;wFAAa;wFAAG;;mFAAR;;;;;;;;;;;;;;;;8EAMjB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;;gFAAK;gFAAU,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,MAAM,UAAU;;;;;;;wEAC1C,MAAM,WAAW,kBAAI,8OAAC;;gFAAK;gFAAc,MAAM,WAAW;;;;;;;;;;;;;;;;;;;sEAG/D,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAO,WAAU;0EAA0D;;;;;;;;;;;;;;;;;+CAlD3E,MAAM,EAAE;;;;;;;;;;;;2BAjDjB;;;;;wBA+GP,cAAc,gCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,MAAM;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC3B,WAAU;;8CAGV,8OAAC;oCAAI,WAAU;8CACZ,eAAe,GAAG,CAAC,CAAC,uBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAI;4CAClC,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CAChC,WAAW,CAAC,kDAAkD,EAC5D,gBAAgB,OAAO,OAAO,EAAE,GAAG,0BAA0B,IAC7D;4CACF,SAAS,IAAM,kBAAkB;sDAEjC,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;kEAEnB,8OAAC;wDAAG,WAAU;kEAAoC,OAAO,YAAY;;;;;;kEACrE,8OAAC;wDAAE,WAAU;kEAA8B,OAAO,KAAK;;;;;;kEACvD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;gEAAK,WAAU;0EAAuB,OAAO,MAAM;;;;;;0EACpD,8OAAC;gEAAK,WAAU;;oEAAwB;oEAAE,OAAO,mBAAmB;oEAAC;;;;;;;;;;;;;kEAEvE,8OAAC;wDAAI,WAAW,CAAC,oEAAoE,EACnF,OAAO,mBAAmB,KAAK,cAAc,gCAC7C,OAAO,mBAAmB,KAAK,SAAS,kCACxC,6BACA;kEACC,OAAO,mBAAmB;;;;;;kEAE7B,8OAAC;wDAAI,WAAU;;4DACZ,OAAO,WAAW,CAAC,cAAc;4DAAG;;;;;;;;;;;;;2CA3BpC,OAAO,EAAE;;;;;;;;;;8CAmCpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAsC;;;;;;wCACnD,oBAAoB,MAAM,KAAK,kBAC9B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,wNAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;8DACzB,8OAAC;8DAAE;;;;;;;;;;;mDAGL,oBAAoB,GAAG,CAAC,CAAC,6BACvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC9B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,WAAU;0DAEV,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAW,CAAC,kDAAkD,EAAE,eAAe,aAAa,MAAM,GAAG;;gFACxG,cAAc,aAAa,MAAM;8FAClC,8OAAC;oFAAK,WAAU;8FAAQ,aAAa,MAAM;;;;;;;;;;;;sFAE7C,8OAAC;4EAAK,WAAW,CAAC,2CAA2C,EAC3D,aAAa,QAAQ,KAAK,WAAW,4BACrC,aAAa,QAAQ,KAAK,SAAS,kCACnC,aAAa,QAAQ,KAAK,WAAW,kCACrC,6BACA;sFACC,aAAa,QAAQ;;;;;;;;;;;;8EAG1B,8OAAC;oEAAG,WAAU;8EAAoC,aAAa,KAAK;;;;;;8EACpE,8OAAC;oEAAE,WAAU;8EAA8B,aAAa,WAAW;;;;;;8EACnE,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;;gFAAK;gFAAU,aAAa,SAAS;;;;;;;sFACtC,8OAAC;;gFAAK;gFAAgB,aAAa,kBAAkB;gFAAC;;;;;;;sFACtD,8OAAC;;gFAAK;gFAAO,aAAa,IAAI,CAAC,cAAc;gFAAG;;;;;;;sFAChD,8OAAC;;gFAAK;gFAAO,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,aAAa,UAAU;;;;;;;;;;;;;;;;;;;sEAGnD,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAO,WAAU;0EAA0D;;;;;;;;;;;;;;;;;+CA/B3E,aAAa,EAAE;;;;;;;;;;;;2BAvDxB;;;;;wBAkGP,cAAc,6BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,MAAM;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC3B,WAAU;;8CAGV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAoC;;;;;;wCACnD;4CAAC;4CAAY;4CAAgB;4CAAY;yCAAS,CAAC,GAAG,CAAC,CAAC,sBACvD,8OAAC;gDAEC,WAAU;0DAET;+CAHI;;;;;;;;;;;8CASX,8OAAC;oCAAI,WAAU;8CACZ,mBAAmB,GAAG,CAAC,CAAC,yBACvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAI;4CAClC,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CAChC,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;sEAEtB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAW,CAAC,2CAA2C,EAC3D,SAAS,IAAI,KAAK,aAAa,8BAC/B,SAAS,IAAI,KAAK,UAAU,gCAC5B,SAAS,IAAI,KAAK,UAAU,kCAC5B,6BACA;8EACC,SAAS,IAAI;;;;;;8EAEhB,8OAAC;oEAAK,WAAW,CAAC,2CAA2C,EAC3D,SAAS,gBAAgB,KAAK,WAAW,4BACzC,SAAS,gBAAgB,KAAK,aAAa,kCAC3C,SAAS,gBAAgB,KAAK,iBAAiB,kCAC/C,+BACA;8EACC,SAAS,gBAAgB;;;;;;;;;;;;sEAG9B,8OAAC;4DAAG,WAAU;sEAAoC,SAAS,KAAK;;;;;;sEAChE,8OAAC;4DAAE,WAAU;sEAA8B,SAAS,WAAW;;;;;;sEAC/D,8OAAC;4DAAI,WAAU;;gEACZ,SAAS,gBAAgB,kBAAI,8OAAC;;wEAAM,SAAS,gBAAgB;wEAAC;;;;;;;8EAC/D,8OAAC;;wEAAM,SAAS,WAAW;wEAAC;;;;;;;8EAC5B,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,kMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;sFAChB,8OAAC;sFAAM,SAAS,MAAM;;;;;;;;;;;;;;;;;;wDAGzB,SAAS,mBAAmB,kBAC3B,8OAAC;4DAAI,WAAW,CAAC,oEAAoE,EAAE,CAAA,GAAA,mHAAA,CAAA,qBAAkB,AAAD,EAAE,SAAS,mBAAmB,GAAG;;gEACtI,SAAS,mBAAmB;gEAAC;;;;;;;;;;;;;8DAIpC,8OAAC;oDAAO,WAAU;8DAAkC;;;;;;;2CA3C/C,SAAS,EAAE;;;;;;;;;;;2BAvBlB;;;;;;;;;;;;;;;;;;;;;;AA8ElB", "debugId": null}}, {"offset": {"line": 2410, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/components/expert/ExpertChat.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useRef } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { \n  MessageSquare,\n  Send,\n  Paperclip,\n  X,\n  User,\n  Bot,\n  Clock,\n  CheckCircle,\n  AlertCircle,\n  Minimize2,\n  Maximize2\n} from 'lucide-react'\nimport { useStore } from '@/store/useStore'\nimport { formatDate } from '@/lib/utils'\n\ninterface ChatMessage {\n  id: string\n  sender: 'user' | 'expert' | 'system'\n  content: string\n  timestamp: string\n  attachments?: string[]\n  status?: 'sending' | 'sent' | 'read'\n}\n\ninterface ExpertChatProps {\n  expertId?: string\n  consultationId?: string\n  onClose?: () => void\n}\n\nexport default function ExpertChat({ expertId, consultationId, onClose }: ExpertChatProps) {\n  const { selectedExpert, expertProfiles } = useStore()\n  const [isOpen, setIsOpen] = useState(false)\n  const [isMinimized, setIsMinimized] = useState(false)\n  const [messages, setMessages] = useState<ChatMessage[]>([])\n  const [newMessage, setNewMessage] = useState('')\n  const [isTyping, setIsTyping] = useState(false)\n  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('connecting')\n  const messagesEndRef = useRef<HTMLDivElement>(null)\n\n  const expert = selectedExpert || expertProfiles.find(e => e.id === expertId)\n\n  // Simulation de messages initiaux\n  useEffect(() => {\n    if (expert) {\n      const initialMessages: ChatMessage[] = [\n        {\n          id: 'msg_1',\n          sender: 'system',\n          content: `Connexion établie avec ${expert.display_name}. La consultation commence maintenant.`,\n          timestamp: new Date().toISOString(),\n          status: 'read'\n        },\n        {\n          id: 'msg_2',\n          sender: 'expert',\n          content: `Bonjour ! Je suis ${expert.display_name}, ${expert.title}. Comment puis-je vous aider aujourd'hui ?`,\n          timestamp: new Date(Date.now() + 1000).toISOString(),\n          status: 'read'\n        }\n      ]\n      setMessages(initialMessages)\n      setConnectionStatus('connected')\n    }\n  }, [expert])\n\n  // Auto-scroll vers le bas\n  useEffect(() => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })\n  }, [messages])\n\n  // Simulation de réponses d'expert\n  const simulateExpertResponse = (userMessage: string) => {\n    setIsTyping(true)\n    \n    setTimeout(() => {\n      const responses = [\n        \"Excellente question ! Laissez-moi analyser votre situation...\",\n        \"D'après votre description, je recommande de vérifier les points suivants :\",\n        \"C'est un cas intéressant. Voici mon analyse technique :\",\n        \"Pour résoudre ce problème, nous devons considérer plusieurs aspects :\",\n        \"Basé sur mon expérience, voici la meilleure approche :\"\n      ]\n      \n      const randomResponse = responses[Math.floor(Math.random() * responses.length)]\n      \n      const expertMessage: ChatMessage = {\n        id: `msg_${Date.now()}`,\n        sender: 'expert',\n        content: randomResponse,\n        timestamp: new Date().toISOString(),\n        status: 'sent'\n      }\n      \n      setMessages(prev => [...prev, expertMessage])\n      setIsTyping(false)\n    }, 2000 + Math.random() * 3000) // 2-5 secondes\n  }\n\n  const sendMessage = () => {\n    if (!newMessage.trim()) return\n\n    const userMessage: ChatMessage = {\n      id: `msg_${Date.now()}`,\n      sender: 'user',\n      content: newMessage,\n      timestamp: new Date().toISOString(),\n      status: 'sending'\n    }\n\n    setMessages(prev => [...prev, userMessage])\n    setNewMessage('')\n\n    // Marquer comme envoyé après un délai\n    setTimeout(() => {\n      setMessages(prev => \n        prev.map(msg => \n          msg.id === userMessage.id ? { ...msg, status: 'sent' } : msg\n        )\n      )\n    }, 1000)\n\n    // Simuler une réponse d'expert\n    simulateExpertResponse(newMessage)\n  }\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault()\n      sendMessage()\n    }\n  }\n\n  const getMessageIcon = (sender: string) => {\n    switch (sender) {\n      case 'expert':\n        return <User className=\"h-4 w-4\" />\n      case 'system':\n        return <Bot className=\"h-4 w-4\" />\n      default:\n        return null\n    }\n  }\n\n  const getStatusIcon = (status?: string) => {\n    switch (status) {\n      case 'sending':\n        return <Clock className=\"h-3 w-3 text-gray-400\" />\n      case 'sent':\n        return <CheckCircle className=\"h-3 w-3 text-blue-500\" />\n      case 'read':\n        return <CheckCircle className=\"h-3 w-3 text-green-500\" />\n      default:\n        return null\n    }\n  }\n\n  if (!expert) {\n    return (\n      <div className=\"fixed bottom-4 right-4 bg-red-100 border border-red-300 rounded-lg p-4 text-red-800\">\n        <AlertCircle className=\"h-5 w-5 inline mr-2\" />\n        Expert non trouvé\n      </div>\n    )\n  }\n\n  return (\n    <>\n      {/* Bouton d'ouverture du chat */}\n      {!isOpen && (\n        <motion.button\n          initial={{ scale: 0 }}\n          animate={{ scale: 1 }}\n          onClick={() => setIsOpen(true)}\n          className=\"fixed bottom-6 right-6 bg-gradient-to-r from-amber-400 to-amber-500 text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 z-50\"\n        >\n          <MessageSquare className=\"h-6 w-6\" />\n          <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\">\n            1\n          </span>\n        </motion.button>\n      )}\n\n      {/* Fenêtre de chat */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            initial={{ opacity: 0, y: 100, scale: 0.8 }}\n            animate={{ \n              opacity: 1, \n              y: 0, \n              scale: 1,\n              height: isMinimized ? 60 : 500\n            }}\n            exit={{ opacity: 0, y: 100, scale: 0.8 }}\n            className=\"fixed bottom-6 right-6 w-96 bg-white rounded-lg shadow-2xl border border-gray-200 z-50 overflow-hidden\"\n          >\n            {/* Header du chat */}\n            <div className=\"bg-gradient-to-r from-amber-400 to-amber-500 text-white p-4 flex items-center justify-between\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-10 h-10 bg-white/20 rounded-full flex items-center justify-center\">\n                  <User className=\"h-5 w-5\" />\n                </div>\n                <div>\n                  <h3 className=\"font-semibold\">{expert.display_name}</h3>\n                  <div className=\"flex items-center space-x-2 text-xs\">\n                    <div className={`w-2 h-2 rounded-full ${\n                      connectionStatus === 'connected' ? 'bg-green-300' :\n                      connectionStatus === 'connecting' ? 'bg-yellow-300' :\n                      'bg-red-300'\n                    }`}></div>\n                    <span>{connectionStatus}</span>\n                  </div>\n                </div>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <button\n                  onClick={() => setIsMinimized(!isMinimized)}\n                  className=\"p-1 hover:bg-white/20 rounded\"\n                >\n                  {isMinimized ? <Maximize2 className=\"h-4 w-4\" /> : <Minimize2 className=\"h-4 w-4\" />}\n                </button>\n                <button\n                  onClick={() => {\n                    setIsOpen(false)\n                    onClose?.()\n                  }}\n                  className=\"p-1 hover:bg-white/20 rounded\"\n                >\n                  <X className=\"h-4 w-4\" />\n                </button>\n              </div>\n            </div>\n\n            {/* Corps du chat */}\n            {!isMinimized && (\n              <>\n                {/* Messages */}\n                <div className=\"h-80 overflow-y-auto p-4 space-y-4\">\n                  {messages.map((message) => (\n                    <motion.div\n                      key={message.id}\n                      initial={{ opacity: 0, y: 10 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}\n                    >\n                      <div className={`max-w-xs px-3 py-2 rounded-lg ${\n                        message.sender === 'user' \n                          ? 'bg-amber-500 text-white' \n                          : message.sender === 'system'\n                          ? 'bg-gray-100 text-gray-700'\n                          : 'bg-gray-200 text-gray-900'\n                      }`}>\n                        {message.sender !== 'user' && (\n                          <div className=\"flex items-center space-x-1 mb-1\">\n                            {getMessageIcon(message.sender)}\n                            <span className=\"text-xs font-medium\">\n                              {message.sender === 'expert' ? expert.display_name : 'Système'}\n                            </span>\n                          </div>\n                        )}\n                        <p className=\"text-sm\">{message.content}</p>\n                        <div className=\"flex items-center justify-between mt-1\">\n                          <span className=\"text-xs opacity-70\">\n                            {new Date(message.timestamp).toLocaleTimeString('fr-FR', { \n                              hour: '2-digit', \n                              minute: '2-digit' \n                            })}\n                          </span>\n                          {message.sender === 'user' && getStatusIcon(message.status)}\n                        </div>\n                      </div>\n                    </motion.div>\n                  ))}\n                  \n                  {/* Indicateur de frappe */}\n                  {isTyping && (\n                    <motion.div\n                      initial={{ opacity: 0 }}\n                      animate={{ opacity: 1 }}\n                      className=\"flex justify-start\"\n                    >\n                      <div className=\"bg-gray-200 text-gray-900 px-3 py-2 rounded-lg\">\n                        <div className=\"flex items-center space-x-1\">\n                          <User className=\"h-3 w-3\" />\n                          <span className=\"text-xs\">{expert.display_name} écrit...</span>\n                          <div className=\"flex space-x-1\">\n                            <div className=\"w-1 h-1 bg-gray-500 rounded-full animate-bounce\"></div>\n                            <div className=\"w-1 h-1 bg-gray-500 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n                            <div className=\"w-1 h-1 bg-gray-500 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n                          </div>\n                        </div>\n                      </div>\n                    </motion.div>\n                  )}\n                  \n                  <div ref={messagesEndRef} />\n                </div>\n\n                {/* Zone de saisie */}\n                <div className=\"border-t border-gray-200 p-4\">\n                  <div className=\"flex items-center space-x-2\">\n                    <button className=\"p-2 text-gray-400 hover:text-gray-600\">\n                      <Paperclip className=\"h-4 w-4\" />\n                    </button>\n                    <input\n                      type=\"text\"\n                      value={newMessage}\n                      onChange={(e) => setNewMessage(e.target.value)}\n                      onKeyPress={handleKeyPress}\n                      placeholder=\"Tapez votre message...\"\n                      className=\"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400 focus:border-transparent text-sm\"\n                    />\n                    <button\n                      onClick={sendMessage}\n                      disabled={!newMessage.trim()}\n                      className=\"p-2 bg-amber-500 text-white rounded-lg hover:bg-amber-600 disabled:opacity-50 disabled:cursor-not-allowed\"\n                    >\n                      <Send className=\"h-4 w-4\" />\n                    </button>\n                  </div>\n                </div>\n              </>\n            )}\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AAjBA;;;;;;AAmCe,SAAS,WAAW,EAAE,QAAQ,EAAE,cAAc,EAAE,OAAO,EAAmB;IACvF,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAClD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAC1D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA+C;IACtG,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE9C,MAAM,SAAS,kBAAkB,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAEnE,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV,MAAM,kBAAiC;gBACrC;oBACE,IAAI;oBACJ,QAAQ;oBACR,SAAS,CAAC,uBAAuB,EAAE,OAAO,YAAY,CAAC,sCAAsC,CAAC;oBAC9F,WAAW,IAAI,OAAO,WAAW;oBACjC,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,QAAQ;oBACR,SAAS,CAAC,kBAAkB,EAAE,OAAO,YAAY,CAAC,EAAE,EAAE,OAAO,KAAK,CAAC,0CAA0C,CAAC;oBAC9G,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,MAAM,WAAW;oBAClD,QAAQ;gBACV;aACD;YACD,YAAY;YACZ,oBAAoB;QACtB;IACF,GAAG;QAAC;KAAO;IAEX,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D,GAAG;QAAC;KAAS;IAEb,kCAAkC;IAClC,MAAM,yBAAyB,CAAC;QAC9B,YAAY;QAEZ,WAAW;YACT,MAAM,YAAY;gBAChB;gBACA;gBACA;gBACA;gBACA;aACD;YAED,MAAM,iBAAiB,SAAS,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,UAAU,MAAM,EAAE;YAE9E,MAAM,gBAA6B;gBACjC,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI;gBACvB,QAAQ;gBACR,SAAS;gBACT,WAAW,IAAI,OAAO,WAAW;gBACjC,QAAQ;YACV;YAEA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAc;YAC5C,YAAY;QACd,GAAG,OAAO,KAAK,MAAM,KAAK,MAAM,eAAe;;IACjD;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,WAAW,IAAI,IAAI;QAExB,MAAM,cAA2B;YAC/B,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI;YACvB,QAAQ;YACR,SAAS;YACT,WAAW,IAAI,OAAO,WAAW;YACjC,QAAQ;QACV;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,cAAc;QAEd,sCAAsC;QACtC,WAAW;YACT,YAAY,CAAA,OACV,KAAK,GAAG,CAAC,CAAA,MACP,IAAI,EAAE,KAAK,YAAY,EAAE,GAAG;wBAAE,GAAG,GAAG;wBAAE,QAAQ;oBAAO,IAAI;QAG/D,GAAG;QAEH,+BAA+B;QAC/B,uBAAuB;IACzB;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,8OAAC,gMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;YACxB;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,OAAO;QACX;IACF;IAEA,IAAI,CAAC,QAAQ;QACX,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;gBAAwB;;;;;;;IAIrD;IAEA,qBACE;;YAEG,CAAC,wBACA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS;oBAAE,OAAO;gBAAE;gBACpB,SAAS;oBAAE,OAAO;gBAAE;gBACpB,SAAS,IAAM,UAAU;gBACzB,WAAU;;kCAEV,8OAAC,wNAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;kCACzB,8OAAC;wBAAK,WAAU;kCAA+G;;;;;;;;;;;;0BAOnI,8OAAC,yLAAA,CAAA,kBAAe;0BACb,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;wBAAK,OAAO;oBAAI;oBAC1C,SAAS;wBACP,SAAS;wBACT,GAAG;wBACH,OAAO;wBACP,QAAQ,cAAc,KAAK;oBAC7B;oBACA,MAAM;wBAAE,SAAS;wBAAG,GAAG;wBAAK,OAAO;oBAAI;oBACvC,WAAU;;sCAGV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAiB,OAAO,YAAY;;;;;;8DAClD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAW,CAAC,qBAAqB,EACpC,qBAAqB,cAAc,iBACnC,qBAAqB,eAAe,kBACpC,cACA;;;;;;sEACF,8OAAC;sEAAM;;;;;;;;;;;;;;;;;;;;;;;;8CAIb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,eAAe,CAAC;4CAC/B,WAAU;sDAET,4BAAc,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;qEAAe,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAE1E,8OAAC;4CACC,SAAS;gDACP,UAAU;gDACV;4CACF;4CACA,WAAU;sDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;wBAMlB,CAAC,6BACA;;8CAEE,8OAAC;oCAAI,WAAU;;wCACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,WAAW,CAAC,KAAK,EAAE,QAAQ,MAAM,KAAK,SAAS,gBAAgB,iBAAiB;0DAEhF,cAAA,8OAAC;oDAAI,WAAW,CAAC,8BAA8B,EAC7C,QAAQ,MAAM,KAAK,SACf,4BACA,QAAQ,MAAM,KAAK,WACnB,8BACA,6BACJ;;wDACC,QAAQ,MAAM,KAAK,wBAClB,8OAAC;4DAAI,WAAU;;gEACZ,eAAe,QAAQ,MAAM;8EAC9B,8OAAC;oEAAK,WAAU;8EACb,QAAQ,MAAM,KAAK,WAAW,OAAO,YAAY,GAAG;;;;;;;;;;;;sEAI3D,8OAAC;4DAAE,WAAU;sEAAW,QAAQ,OAAO;;;;;;sEACvC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EACb,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB,CAAC,SAAS;wEACvD,MAAM;wEACN,QAAQ;oEACV;;;;;;gEAED,QAAQ,MAAM,KAAK,UAAU,cAAc,QAAQ,MAAM;;;;;;;;;;;;;+CA5BzD,QAAQ,EAAE;;;;;wCAmClB,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;4CAAE;4CACtB,SAAS;gDAAE,SAAS;4CAAE;4CACtB,WAAU;sDAEV,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;4DAAK,WAAU;;gEAAW,OAAO,YAAY;gEAAC;;;;;;;sEAC/C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAI,WAAU;oEAAkD,OAAO;wEAAE,gBAAgB;oEAAO;;;;;;8EACjG,8OAAC;oEAAI,WAAU;oEAAkD,OAAO;wEAAE,gBAAgB;oEAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAO3G,8OAAC;4CAAI,KAAK;;;;;;;;;;;;8CAIZ,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAO,WAAU;0DAChB,cAAA,8OAAC,4MAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;0DAEvB,8OAAC;gDACC,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,YAAY;gDACZ,aAAY;gDACZ,WAAU;;;;;;0DAEZ,8OAAC;gDACC,SAAS;gDACT,UAAU,CAAC,WAAW,IAAI;gDAC1B,WAAU;0DAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWtC", "debugId": null}}, {"offset": {"line": 3030, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/app/expert/page.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { \n  ArrowLeft,\n  Shield,\n  Users,\n  Award,\n  Zap,\n  FileCheck,\n  MessageSquare,\n  BookOpen,\n  TrendingUp\n} from 'lucide-react'\nimport Link from 'next/link'\nimport ExpertSpace from '@/components/expert/ExpertSpace'\nimport ExpertChat from '@/components/expert/ExpertChat'\n\nexport default function ExpertPage() {\n  const stats = [\n    {\n      label: 'Documents Validés',\n      value: '1,247',\n      change: '+23',\n      changeType: 'increase',\n      icon: <FileCheck className=\"h-6 w-6\" />\n    },\n    {\n      label: 'Vérifications Compatibilité',\n      value: '892',\n      change: '+15',\n      changeType: 'increase',\n      icon: <Zap className=\"h-6 w-6\" />\n    },\n    {\n      label: 'Consultations Résolues',\n      value: '456',\n      change: '+8',\n      changeType: 'increase',\n      icon: <MessageSquare className=\"h-6 w-6\" />\n    },\n    {\n      label: 'Ressources Disponibles',\n      value: '234',\n      change: '+12',\n      changeType: 'increase',\n      icon: <BookOpen className=\"h-6 w-6\" />\n    }\n  ]\n\n  const expertiseAreas = [\n    {\n      title: 'Validation Technique',\n      description: 'Upload et validation de vos documents techniques par nos experts certifiés',\n      icon: <Shield className=\"h-8 w-8\" />,\n      features: ['Analyse de conformité', 'Recommandations d\\'amélioration', 'Certification officielle'],\n      color: 'from-blue-500 to-blue-600'\n    },\n    {\n      title: 'Compatibilité Produits',\n      description: 'Vérification automatique et experte de la compatibilité entre composants',\n      icon: <Zap className=\"h-8 w-8\" />,\n      features: ['Simulation intelligente', 'Alertes de sécurité', 'Recommandations alternatives'],\n      color: 'from-green-500 to-green-600'\n    },\n    {\n      title: 'Consultation Expert',\n      description: 'Accès direct à nos experts pour conseils personnalisés en temps réel',\n      icon: <Users className=\"h-8 w-8\" />,\n      features: ['Chat en direct', 'Visioconférence', 'Rapports détaillés'],\n      color: 'from-purple-500 to-purple-600'\n    },\n    {\n      title: 'Bibliothèque Technique',\n      description: 'Ressources exclusives, tutoriels et formations selon votre niveau',\n      icon: <BookOpen className=\"h-8 w-8\" />,\n      features: ['Guides détaillés', 'Vidéos tutoriels', 'Templates professionnels'],\n      color: 'from-amber-500 to-amber-600'\n    }\n  ]\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100\">\n      {/* Header */}\n      <header className=\"bg-white/80 backdrop-blur-md border-b border-slate-200 sticky top-0 z-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-16\">\n            <div className=\"flex items-center space-x-4\">\n              <Link \n                href=\"/\"\n                className=\"flex items-center space-x-2 text-slate-700 hover:text-amber-600 transition-colors\"\n              >\n                <ArrowLeft className=\"h-5 w-5\" />\n                <span>Retour à l'accueil</span>\n              </Link>\n            </div>\n            \n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-amber-400 to-amber-500 rounded-lg flex items-center justify-center\">\n                <Shield className=\"h-5 w-5 text-slate-900\" />\n              </div>\n              <div>\n                <h1 className=\"text-lg font-bold text-slate-900\">Pro Matos</h1>\n                <p className=\"text-xs text-slate-600\">Conseil Technique Expert</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Contenu principal */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Titre et description */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"mb-8\"\n        >\n          <h1 className=\"text-3xl font-bold text-slate-900 mb-2\">\n            Espace Conseil Technique Expert\n          </h1>\n          <p className=\"text-lg text-slate-600\">\n            Votre expertise technique validée par les meilleurs spécialistes d'Afrique de l'Ouest\n          </p>\n        </motion.div>\n\n        {/* Statistiques rapides */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.1 }}\n          className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\"\n        >\n          {stats.map((stat, index) => (\n            <motion.div\n              key={stat.label}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.1 + index * 0.05 }}\n              className=\"industrial-card p-6\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-slate-600\">{stat.label}</p>\n                  <div className=\"flex items-baseline space-x-2\">\n                    <p className=\"text-2xl font-bold text-slate-900\">{stat.value}</p>\n                    <span className={`text-sm font-medium ${\n                      stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'\n                    }`}>\n                      {stat.change}\n                    </span>\n                  </div>\n                </div>\n                <div className=\"p-3 bg-amber-100 rounded-lg text-amber-600\">\n                  {stat.icon}\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* Domaines d'expertise */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.2 }}\n          className=\"mb-8\"\n        >\n          <h2 className=\"text-2xl font-bold text-slate-900 mb-6\">Nos Domaines d'Expertise</h2>\n          <div className=\"grid md:grid-cols-2 gap-6\">\n            {expertiseAreas.map((area, index) => (\n              <motion.div\n                key={area.title}\n                initial={{ opacity: 0, x: index % 2 === 0 ? -20 : 20 }}\n                animate={{ opacity: 1, x: 0 }}\n                transition={{ delay: 0.3 + index * 0.1 }}\n                className=\"industrial-card p-6\"\n              >\n                <div className={`w-16 h-16 bg-gradient-to-r ${area.color} rounded-lg flex items-center justify-center text-white mb-4`}>\n                  {area.icon}\n                </div>\n                <h3 className=\"text-xl font-bold text-slate-900 mb-2\">{area.title}</h3>\n                <p className=\"text-slate-600 mb-4\">{area.description}</p>\n                <ul className=\"space-y-2\">\n                  {area.features.map((feature, idx) => (\n                    <li key={idx} className=\"flex items-center space-x-2 text-sm text-slate-700\">\n                      <div className=\"w-1.5 h-1.5 bg-amber-500 rounded-full\"></div>\n                      <span>{feature}</span>\n                    </li>\n                  ))}\n                </ul>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n\n        {/* Espace Expert principal */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.4 }}\n        >\n          <ExpertSpace />\n        </motion.div>\n\n        {/* Section avantages membres */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.5 }}\n          className=\"mt-8 bg-gradient-to-r from-amber-50 to-yellow-50 rounded-lg p-6 border border-amber-200\"\n        >\n          <div className=\"flex items-center space-x-4 mb-4\">\n            <Award className=\"h-8 w-8 text-amber-600\" />\n            <h3 className=\"text-xl font-bold text-amber-900\">Avantages Membres Exclusifs</h3>\n          </div>\n          <div className=\"grid md:grid-cols-3 gap-6\">\n            <div>\n              <h4 className=\"font-semibold text-amber-800 mb-2\">🥉 Bronze</h4>\n              <ul className=\"text-sm text-amber-700 space-y-1\">\n                <li>• Validation documents basique</li>\n                <li>• Ressources publiques</li>\n                <li>• Support par email</li>\n              </ul>\n            </div>\n            <div>\n              <h4 className=\"font-semibold text-amber-800 mb-2\">🥈 Silver</h4>\n              <ul className=\"text-sm text-amber-700 space-y-1\">\n                <li>• Validation prioritaire</li>\n                <li>• Simulateur compatibilité</li>\n                <li>• Chat expert (limité)</li>\n                <li>• Guides techniques avancés</li>\n              </ul>\n            </div>\n            <div>\n              <h4 className=\"font-semibold text-amber-800 mb-2\">🥇 Gold & Platinum</h4>\n              <ul className=\"text-sm text-amber-700 space-y-1\">\n                <li>• Validation express 24h</li>\n                <li>• Consultation illimitée</li>\n                <li>• Rapports certifiés</li>\n                <li>• Formations exclusives</li>\n                <li>• Support téléphonique</li>\n              </ul>\n            </div>\n          </div>\n        </motion.div>\n      </main>\n\n      {/* Chat Expert */}\n      <ExpertChat />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AAhBA;;;;;;;AAkBe,SAAS;IACtB,MAAM,QAAQ;QACZ;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,oBAAM,8OAAC,gNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;QAC7B;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,oBAAM,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;QACvB;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,oBAAM,8OAAC,wNAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;QACjC;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;QAC5B;KACD;IAED,MAAM,iBAAiB;QACrB;YACE,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,UAAU;gBAAC;gBAAyB;gBAAmC;aAA2B;YAClG,OAAO;QACT;QACA;YACE,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;YACrB,UAAU;gBAAC;gBAA2B;gBAAuB;aAA+B;YAC5F,OAAO;QACT;QACA;YACE,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,UAAU;gBAAC;gBAAkB;gBAAmB;aAAqB;YACrE,OAAO;QACT;QACA;YACE,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,UAAU;gBAAC;gBAAoB;gBAAoB;aAA2B;YAC9E,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;0CAIV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAEpB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhD,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CAGvD,8OAAC;gCAAE,WAAU;0CAAyB;;;;;;;;;;;;kCAMxC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;wBACzB,WAAU;kCAET,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO,MAAM,QAAQ;gCAAK;gCACxC,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAsC,KAAK,KAAK;;;;;;8DAC7D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAqC,KAAK,KAAK;;;;;;sEAC5D,8OAAC;4DAAK,WAAW,CAAC,oBAAoB,EACpC,KAAK,UAAU,KAAK,aAAa,mBAAmB,gBACpD;sEACC,KAAK,MAAM;;;;;;;;;;;;;;;;;;sDAIlB,8OAAC;4CAAI,WAAU;sDACZ,KAAK,IAAI;;;;;;;;;;;;+BAnBT,KAAK,KAAK;;;;;;;;;;kCA2BrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;wBACzB,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAI,WAAU;0CACZ,eAAe,GAAG,CAAC,CAAC,MAAM,sBACzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG,QAAQ,MAAM,IAAI,CAAC,KAAK;wCAAG;wCACrD,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO,MAAM,QAAQ;wCAAI;wCACvC,WAAU;;0DAEV,8OAAC;gDAAI,WAAW,CAAC,2BAA2B,EAAE,KAAK,KAAK,CAAC,4DAA4D,CAAC;0DACnH,KAAK,IAAI;;;;;;0DAEZ,8OAAC;gDAAG,WAAU;0DAAyC,KAAK,KAAK;;;;;;0DACjE,8OAAC;gDAAE,WAAU;0DAAuB,KAAK,WAAW;;;;;;0DACpD,8OAAC;gDAAG,WAAU;0DACX,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,oBAC3B,8OAAC;wDAAa,WAAU;;0EACtB,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;0EAAM;;;;;;;uDAFA;;;;;;;;;;;uCAbR,KAAK,KAAK;;;;;;;;;;;;;;;;kCAyBvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;kCAEzB,cAAA,8OAAC,2IAAA,CAAA,UAAW;;;;;;;;;;kCAId,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;wBACzB,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;;;;;;;0CAEnD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;;;;;;;kDAGR,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;;;;;;;kDAGR,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQd,8OAAC,0IAAA,CAAA,UAAU;;;;;;;;;;;AAGjB", "debugId": null}}]}