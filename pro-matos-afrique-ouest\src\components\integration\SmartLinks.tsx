'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  ArrowRight,
  ExternalLink,
  Zap,
  Database,
  Users,
  FileText,
  Crown,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  Star
} from 'lucide-react'
import Link from 'next/link'

interface SmartLinksProps {
  context: {
    module: 'hub' | 'expert' | 'prescriptor' | 'club'
    data?: any
    action?: string
  }
  className?: string
}

export default function SmartLinks({ context, className = '' }: SmartLinksProps) {
  const [hoveredLink, setHoveredLink] = useState<string | null>(null)

  const generateSmartLinks = () => {
    const { module, data, action } = context

    switch (module) {
      case 'hub':
        return [
          {
            id: 'hub-to-expert',
            title: 'Consulter un Expert',
            description: 'Obtenir une validation technique sur cette alerte',
            href: `/expert?source=hub&alert=${data?.alertId || 'current'}`,
            icon: <Users className="h-5 w-5" />,
            color: 'from-green-500 to-green-600',
            priority: 'high',
            estimatedTime: '15 min',
            cost: '25,000 FCFA'
          },
          {
            id: 'hub-to-prescriptor',
            title: 'Créer Template',
            description: 'Transformer cette information en template de prescription',
            href: `/prescriptor?action=create&source=hub&data=${data?.type || 'alert'}`,
            icon: <FileText className="h-5 w-5" />,
            color: 'from-purple-500 to-purple-600',
            priority: 'medium',
            estimatedTime: '30 min',
            cost: 'Inclus'
          },
          {
            id: 'hub-to-club',
            title: 'Partager au Réseau',
            description: 'Diffuser cette information aux membres du club',
            href: `/club?action=share&content=${data?.id || 'current'}`,
            icon: <Crown className="h-5 w-5" />,
            color: 'from-amber-500 to-amber-600',
            priority: 'low',
            estimatedTime: '5 min',
            cost: 'Membre requis'
          }
        ]

      case 'expert':
        return [
          {
            id: 'expert-to-hub',
            title: 'Voir Alertes Techniques',
            description: 'Consulter les dernières alertes nécessitant expertise',
            href: `/hub?filter=technical&priority=expert`,
            icon: <Database className="h-5 w-5" />,
            color: 'from-blue-500 to-blue-600',
            priority: 'high',
            estimatedTime: '10 min',
            cost: 'Gratuit'
          },
          {
            id: 'expert-to-prescriptor',
            title: 'Générer Template',
            description: 'Créer un template basé sur cette consultation',
            href: `/prescriptor?action=generate&consultation=${data?.consultationId || 'current'}`,
            icon: <FileText className="h-5 w-5" />,
            color: 'from-purple-500 to-purple-600',
            priority: 'high',
            estimatedTime: '20 min',
            cost: 'Inclus'
          },
          {
            id: 'expert-to-club',
            title: 'Présenter au Club',
            description: 'Partager cette expertise avec le réseau professionnel',
            href: `/club?action=present&expertise=${data?.expertiseId || 'current'}`,
            icon: <Crown className="h-5 w-5" />,
            color: 'from-amber-500 to-amber-600',
            priority: 'medium',
            estimatedTime: '15 min',
            cost: 'Membre requis'
          }
        ]

      case 'prescriptor':
        return [
          {
            id: 'prescriptor-to-expert',
            title: 'Valider avec Expert',
            description: 'Faire valider ce template par un expert certifié',
            href: `/expert?mode=validation&template=${data?.templateId || 'current'}`,
            icon: <Users className="h-5 w-5" />,
            color: 'from-green-500 to-green-600',
            priority: 'high',
            estimatedTime: '30 min',
            cost: '35,000 FCFA'
          },
          {
            id: 'prescriptor-to-hub',
            title: 'Surveiller Tendances',
            description: 'Suivre les tendances liées à ce type de projet',
            href: `/hub?track=${data?.category || 'electrical'}&project=${data?.projectId || 'current'}`,
            icon: <Database className="h-5 w-5" />,
            color: 'from-blue-500 to-blue-600',
            priority: 'medium',
            estimatedTime: '5 min',
            cost: 'Gratuit'
          },
          {
            id: 'prescriptor-to-club',
            title: 'Showcase Projet',
            description: 'Présenter ce projet certifié au réseau membre',
            href: `/club?action=showcase&project=${data?.projectId || 'current'}`,
            icon: <Crown className="h-5 w-5" />,
            color: 'from-amber-500 to-amber-600',
            priority: 'high',
            estimatedTime: '10 min',
            cost: 'Membre requis'
          }
        ]

      case 'club':
        return [
          {
            id: 'club-to-hub',
            title: 'Alertes Exclusives',
            description: 'Accéder aux informations réservées aux membres',
            href: `/hub?level=premium&member=true`,
            icon: <Database className="h-5 w-5" />,
            color: 'from-blue-500 to-blue-600',
            priority: 'high',
            estimatedTime: '5 min',
            cost: 'Inclus membre'
          },
          {
            id: 'club-to-expert',
            title: 'Expert Dédié',
            description: 'Consultation prioritaire avec expert membre',
            href: `/expert?priority=member&dedicated=true`,
            icon: <Users className="h-5 w-5" />,
            color: 'from-green-500 to-green-600',
            priority: 'high',
            estimatedTime: '15 min',
            cost: 'Tarif préférentiel'
          },
          {
            id: 'club-to-prescriptor',
            title: 'Templates VIP',
            description: 'Accéder aux templates exclusifs membres',
            href: `/prescriptor?level=vip&member=true`,
            icon: <FileText className="h-5 w-5" />,
            color: 'from-purple-500 to-purple-600',
            priority: 'medium',
            estimatedTime: '10 min',
            cost: 'Inclus membre'
          }
        ]

      default:
        return []
    }
  }

  const smartLinks = generateSmartLinks()

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'border-red-200 bg-red-50'
      case 'medium':
        return 'border-yellow-200 bg-yellow-50'
      case 'low':
        return 'border-green-200 bg-green-50'
      default:
        return 'border-gray-200 bg-gray-50'
    }
  }

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'high':
        return <AlertTriangle className="h-4 w-4 text-red-600" />
      case 'medium':
        return <Clock className="h-4 w-4 text-yellow-600" />
      case 'low':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      default:
        return <Star className="h-4 w-4 text-gray-600" />
    }
  }

  if (smartLinks.length === 0) return null

  return (
    <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
      <div className="flex items-center space-x-3 mb-6">
        <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center text-white">
          <Zap className="h-5 w-5" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Actions Intelligentes</h3>
          <p className="text-sm text-gray-600">Suggestions basées sur votre contexte actuel</p>
        </div>
      </div>

      <div className="space-y-4">
        {smartLinks.map((link, index) => (
          <motion.div
            key={link.id}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.1 }}
            onMouseEnter={() => setHoveredLink(link.id)}
            onMouseLeave={() => setHoveredLink(null)}
          >
            <Link
              href={link.href}
              className={`block p-4 rounded-lg border-2 transition-all duration-300 hover:shadow-md ${
                hoveredLink === link.id 
                  ? 'border-indigo-400 bg-indigo-50 transform scale-[1.02]' 
                  : getPriorityColor(link.priority)
              }`}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-4 flex-1">
                  <div className={`w-12 h-12 bg-gradient-to-r ${link.color} rounded-lg flex items-center justify-center text-white flex-shrink-0`}>
                    {link.icon}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <h4 className="font-semibold text-gray-900 truncate">{link.title}</h4>
                      {getPriorityIcon(link.priority)}
                    </div>
                    <p className="text-sm text-gray-600 mb-3 line-clamp-2">{link.description}</p>
                    
                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <div className="flex items-center space-x-1">
                        <Clock className="h-3 w-3" />
                        <span>{link.estimatedTime}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <span className="font-medium">Coût:</span>
                        <span>{link.cost}</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2 ml-4">
                  <motion.div
                    animate={{
                      x: hoveredLink === link.id ? 5 : 0
                    }}
                    transition={{ duration: 0.2 }}
                  >
                    <ArrowRight className="h-5 w-5 text-gray-400" />
                  </motion.div>
                  <ExternalLink className="h-4 w-4 text-gray-400" />
                </div>
              </div>
            </Link>
          </motion.div>
        ))}
      </div>

      {/* Statistiques d'utilisation */}
      <div className="mt-6 pt-6 border-t border-gray-200">
        <div className="flex items-center justify-between text-sm text-gray-500">
          <span>Actions suggérées aujourd'hui</span>
          <div className="flex items-center space-x-4">
            <span className="flex items-center space-x-1">
              <TrendingUp className="h-4 w-4 text-green-500" />
              <span className="text-green-600 font-medium">+23%</span>
            </span>
            <span>47 utilisées</span>
          </div>
        </div>
      </div>
    </div>
  )
}
