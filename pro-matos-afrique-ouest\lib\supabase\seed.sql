-- Script de seed pour Pro Matos Afrique Ouest
-- Exécuter après avoir créé le schéma

-- 1. Insérer des alertes d'exemple
INSERT INTO public.alerts (title, body, type, category, is_active) VALUES
(
  'Nouvelle norme NF C 15-100 - Amendement A6',
  'Mise à jour importante des règles d''installation électrique résidentielle. Les nouvelles exigences concernent notamment la protection différentielle et les circuits spécialisés.',
  'warning',
  'Réglementation',
  true
),
(
  'Formation Schneider Electric - Nouveaux produits',
  'Session de formation gratuite sur la nouvelle gamme Acti9 nouvelle génération. Inscription obligatoire avant le 30 janvier.',
  'info',
  'Formation',
  true
),
(
  'Rupture de stock - Disjoncteurs ABB',
  'Rupture temporaire sur les disjoncteurs ABB série S200. Livraison prévue dans 2 semaines. Stock alternatif disponible.',
  'critical',
  'Stock',
  true
),
(
  'Promotion exceptionnelle - Câbles Nexans',
  'Remise de 15% sur toute la gamme de câbles Nexans jusqu''au 15 février. Profitez-en pour vos projets en cours.',
  'promo',
  'Commercial',
  true
),
(
  'Mise à jour logiciel Caneco BT',
  'Nouvelle version 5.8 disponible avec support des nouvelles normes IEC. Mise à jour recommandée pour tous les utilisateurs.',
  'info',
  'Logiciel',
  true
);

-- 2. Insérer des kits d'exemple
INSERT INTO public.kits (title, description, file_url, file_name, category, downloads, is_premium) VALUES
(
  'Kit Installation Résidentielle Standard',
  'Guide complet pour l''installation électrique d''une maison individuelle selon la norme NF C 15-100. Inclut schémas types et liste de matériel.',
  'kits/kit-residentiel-standard.pdf',
  'kit-residentiel-standard.pdf',
  'Résidentiel',
  245,
  false
),
(
  'Kit Tableau Électrique Tertiaire',
  'Documentation technique pour la conception de tableaux électriques en milieu tertiaire. Calculs de dimensionnement inclus.',
  'kits/kit-tableau-tertiaire.pdf',
  'kit-tableau-tertiaire.pdf',
  'Tertiaire',
  156,
  true
),
(
  'Kit Éclairage LED Industriel',
  'Guide de migration vers l''éclairage LED en milieu industriel. Études de cas et calculs de rentabilité.',
  'kits/kit-eclairage-led.pdf',
  'kit-eclairage-led.pdf',
  'Industriel',
  89,
  true
),
(
  'Kit Sécurité Électrique Chantier',
  'Procédures de sécurité et équipements obligatoires pour les installations électriques sur chantier.',
  'kits/kit-securite-chantier.pdf',
  'kit-securite-chantier.pdf',
  'Sécurité',
  312,
  false
),
(
  'Kit Domotique Résidentielle',
  'Guide d''installation des systèmes domotiques modernes. Protocoles KNX, Z-Wave et solutions connectées.',
  'kits/kit-domotique.pdf',
  'kit-domotique.pdf',
  'Domotique',
  178,
  true
);

-- 3. Insérer des événements club
INSERT INTO public.club_events (title, description, event_date, location, max_participants, current_participants, is_vip_only) VALUES
(
  'Webinaire NF C 15-100 - Nouveautés 2024',
  'Présentation des dernières évolutions de la norme NF C 15-100 par un expert AFNOR. Session interactive avec questions/réponses.',
  '2024-02-15 14:00:00+00',
  'En ligne',
  100,
  67,
  false
),
(
  'Formation Photovoltaïque Avancée',
  'Formation technique approfondie sur les installations photovoltaïques. Dimensionnement, raccordement et maintenance.',
  '2024-02-22 09:00:00+00',
  'Abidjan - Centre de formation',
  25,
  18,
  true
),
(
  'Networking Pro Matos - Abidjan',
  'Soirée networking exclusive pour les membres VIP. Rencontres professionnelles et présentation des nouveautés.',
  '2024-03-05 18:00:00+00',
  'Hôtel Ivoire - Abidjan',
  50,
  32,
  true
),
(
  'Conférence Transition Énergétique',
  'Table ronde sur les enjeux de la transition énergétique en Afrique de l''Ouest. Intervenants experts et institutionnels.',
  '2024-03-12 10:00:00+00',
  'Dakar - Centre de conférences',
  200,
  145,
  false
);

-- 4. Créer un utilisateur admin (à adapter avec un vrai UUID)
-- Note: En production, cet utilisateur sera créé via l'interface d'authentification
INSERT INTO public.users (id, email, role, full_name, company, phone, devis_demandes, achats, statut) VALUES
(
  '00000000-0000-0000-0000-000000000001', -- UUID fictif - remplacer par un vrai
  '<EMAIL>',
  'admin',
  'Administrateur Pro Matos',
  'Pro Matos Afrique Ouest',
  '+225 01 02 03 04 05',
  0,
  0,
  'white'
);

-- 5. Insérer quelques utilisateurs d'exemple
INSERT INTO public.users (id, email, role, full_name, company, phone, devis_demandes, achats, statut) VALUES
(
  '00000000-0000-0000-0000-000000000002',
  '<EMAIL>',
  'member',
  'Jean Kouamé',
  'Électricité Moderne CI',
  '+225 07 12 34 56 78',
  2,
  1,
  'white'
),
(
  '00000000-0000-0000-0000-000000000003',
  '<EMAIL>',
  'vip',
  'Fatou Diallo',
  'SolarTech Sénégal',
  '+221 77 123 45 67',
  8,
  5,
  'white'
),
(
  '00000000-0000-0000-0000-000000000004',
  '<EMAIL>',
  'member',
  'Ibrahim Traoré',
  'ElectroBF',
  '+226 70 12 34 56',
  5,
  0,
  'grey'
);

-- 6. Insérer quelques validations d'exemple
INSERT INTO public.validations (user_id, title, description, status, admin_notes) VALUES
(
  '00000000-0000-0000-0000-000000000002',
  'Validation schéma électrique villa',
  'Schéma électrique pour une villa de 200m² avec piscine. Besoin de validation pour conformité NF C 15-100.',
  'Validé',
  'Schéma conforme. Attention à la liaison équipotentielle de la piscine selon section 702.'
),
(
  '00000000-0000-0000-0000-000000000003',
  'Dimensionnement installation solaire',
  'Installation photovoltaïque 50kWc pour bâtiment industriel. Vérification des calculs de dimensionnement.',
  'En cours',
  null
),
(
  '00000000-0000-0000-0000-000000000004',
  'Tableau électrique immeuble',
  'Conception tableau général pour immeuble R+3. Répartition des charges et protection.',
  'Refusé',
  'Section des conducteurs insuffisante pour les charges prévues. Revoir le dimensionnement.'
);

-- 7. Insérer quelques abonnements aux alertes
INSERT INTO public.user_alerts (user_id, alert_id) VALUES
('00000000-0000-0000-0000-000000000002', 1),
('00000000-0000-0000-0000-000000000002', 2),
('00000000-0000-0000-0000-000000000003', 1),
('00000000-0000-0000-0000-000000000003', 3),
('00000000-0000-0000-0000-000000000003', 4),
('00000000-0000-0000-0000-000000000004', 1),
('00000000-0000-0000-0000-000000000004', 5);

-- 8. Insérer quelques téléchargements de kits
INSERT INTO public.kit_downloads (user_id, kit_id) VALUES
('00000000-0000-0000-0000-000000000002', 1),
('00000000-0000-0000-0000-000000000002', 4),
('00000000-0000-0000-0000-000000000003', 1),
('00000000-0000-0000-0000-000000000003', 2),
('00000000-0000-0000-0000-000000000003', 3),
('00000000-0000-0000-0000-000000000004', 1);

-- 9. Insérer quelques inscriptions aux événements
INSERT INTO public.event_registrations (user_id, event_id) VALUES
('00000000-0000-0000-0000-000000000002', 1),
('00000000-0000-0000-0000-000000000002', 4),
('00000000-0000-0000-0000-000000000003', 1),
('00000000-0000-0000-0000-000000000003', 2),
('00000000-0000-0000-0000-000000000003', 3),
('00000000-0000-0000-0000-000000000004', 1),
('00000000-0000-0000-0000-000000000004', 4);

-- 10. Mettre à jour les compteurs
UPDATE public.club_events SET current_participants = (
  SELECT COUNT(*) FROM public.event_registrations WHERE event_id = club_events.id
);

UPDATE public.kits SET downloads = (
  SELECT COUNT(*) FROM public.kit_downloads WHERE kit_id = kits.id
);
