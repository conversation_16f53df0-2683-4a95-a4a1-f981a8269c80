import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { FileService } from '@/lib/services/fileService'

export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })

    // Vérifier l'authentification
    const { data: { session }, error: authError } = await supabase.auth.getSession()

    if (authError || !session) {
      return NextResponse.json(
        { error: 'Non authentifié' },
        { status: 401 }
      )
    }

    const { kitId } = await request.json()

    if (!kitId) {
      return NextResponse.json(
        { error: 'Kit ID requis' },
        { status: 400 }
      )
    }

    // Vérifier que le kit existe
    const { data: kit, error: kitError } = await supabase
      .from('kits')
      .select('*')
      .eq('id', kitId)
      .single()

    if (kitError || !kit) {
      return NextResponse.json(
        { error: 'Kit non trouvé' },
        { status: 404 }
      )
    }

    // Vérifier les permissions utilisateur
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('role, statut')
      .eq('id', session.user.id)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Utilisateur non trouvé' },
        { status: 404 }
      )
    }

    // Vérifier l'accès aux kits (membres et plus)
    if (user.role === 'guest') {
      return NextResponse.json(
        { error: 'Accès membre requis pour télécharger les kits' },
        { status: 403 }
      )
    }

    // Vérifier l'accès aux kits premium
    if (kit.is_premium && !['vip', 'admin'].includes(user.role)) {
      return NextResponse.json(
        { error: 'Accès VIP requis pour ce kit premium' },
        { status: 403 }
      )
    }

    // Vérifier si l'utilisateur est blacklisté
    if (user.statut === 'black') {
      return NextResponse.json(
        { error: 'Accès suspendu' },
        { status: 403 }
      )
    }

    // Vérifier si déjà téléchargé
    const { data: existingDownload } = await supabase
      .from('user_downloads')
      .select('id')
      .eq('user_id', session.user.id)
      .eq('kit_id', kitId)
      .single()

    const isFirstDownload = !existingDownload

    // Incrémenter le compteur seulement si premier téléchargement
    if (isFirstDownload) {
      const { error: updateError } = await supabase
        .from('kits')
        .update({ downloads: kit.downloads + 1 })
        .eq('id', kitId)

      if (updateError) {
        console.error('Erreur mise à jour compteur:', updateError)
      }

      // Enregistrer le téléchargement
      const { error: downloadError } = await supabase
        .from('user_downloads')
        .insert({
          user_id: session.user.id,
          kit_id: kitId
        })

      if (downloadError) {
        console.error('Erreur enregistrement téléchargement:', downloadError)
      }
    }

    // Obtenir l'URL de téléchargement depuis Supabase Storage
    let downloadUrl = kit.file_url

    // Si le fichier est dans Supabase Storage
    if (kit.file_url.startsWith('kits/')) {
      const { data: urlData } = supabase.storage
        .from(FileService.BUCKETS.KITS)
        .getPublicUrl(kit.file_url)
      downloadUrl = urlData.publicUrl
    }

    return NextResponse.json({
      success: true,
      downloadUrl,
      fileName: kit.file_name,
      fileSize: kit.file_size,
      isFirstDownload,
      totalDownloads: isFirstDownload ? kit.downloads + 1 : kit.downloads
    })

  } catch (error) {
    console.error('Erreur API download:', error)
    return NextResponse.json(
      { error: 'Erreur serveur' },
      { status: 500 }
    )
  }
}
