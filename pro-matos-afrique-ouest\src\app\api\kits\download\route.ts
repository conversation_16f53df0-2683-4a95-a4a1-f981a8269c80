import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

export async function POST(request: NextRequest) {
  try {
    const { kitId, userId } = await request.json()

    if (!kitId || !userId) {
      return NextResponse.json(
        { error: 'Kit ID et User ID requis' },
        { status: 400 }
      )
    }

    // Vérifier que le kit existe
    const { data: kit, error: kitError } = await supabase
      .from('kits')
      .select('*')
      .eq('id', kitId)
      .single()

    if (kitError || !kit) {
      return NextResponse.json(
        { error: 'Kit non trouvé' },
        { status: 404 }
      )
    }

    // Vérifier les permissions utilisateur
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('role')
      .eq('id', userId)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Utilisateur non trouvé' },
        { status: 404 }
      )
    }

    // Vérifier l'accès aux kits premium
    if (kit.is_premium && user.role !== 'vip' && user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Accès premium requis' },
        { status: 403 }
      )
    }

    // Incrémenter le compteur de téléchargements
    const { error: updateError } = await supabase
      .from('kits')
      .update({ downloads: kit.downloads + 1 })
      .eq('id', kitId)

    if (updateError) {
      console.error('Erreur mise à jour compteur:', updateError)
    }

    // Enregistrer le téléchargement
    const { error: downloadError } = await supabase
      .from('user_downloads')
      .upsert({
        user_id: userId,
        kit_id: kitId,
        downloaded_at: new Date().toISOString()
      })

    if (downloadError) {
      console.error('Erreur enregistrement téléchargement:', downloadError)
    }

    // Obtenir l'URL de téléchargement
    const { data: urlData } = supabase.storage
      .from('kits')
      .getPublicUrl(kit.file_url)

    return NextResponse.json({
      success: true,
      downloadUrl: urlData.publicUrl,
      fileName: kit.file_name
    })

  } catch (error) {
    console.error('Erreur API download:', error)
    return NextResponse.json(
      { error: 'Erreur serveur' },
      { status: 500 }
    )
  }
}
