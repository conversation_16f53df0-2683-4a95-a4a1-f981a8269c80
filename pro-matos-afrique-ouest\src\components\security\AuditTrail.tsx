'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Shield, 
  Clock, 
  User,
  MapPin,
  Monitor,
  Smartphone,
  AlertTriangle,
  CheckCircle,
  X,
  Eye,
  Download,
  Filter,
  Search,
  Calendar,
  Activity,
  Lock,
  Unlock,
  FileText,
  Database,
  Settings
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { DatePickerWithRange } from '@/components/ui/date-range-picker'
import { toast } from 'sonner'

interface AuditEvent {
  id: string
  timestamp: string
  userId: string
  userName: string
  action: string
  resource: string
  resourceId?: string
  outcome: 'success' | 'failure' | 'warning'
  ipAddress: string
  userAgent: string
  location?: {
    country: string
    city: string
    coordinates?: [number, number]
  }
  details: Record<string, any>
  riskLevel: 'low' | 'medium' | 'high' | 'critical'
  sessionId: string
}

interface AuditFilters {
  dateRange?: { from: Date; to: Date }
  userId?: string
  action?: string
  outcome?: string
  riskLevel?: string
  searchTerm?: string
}

interface AuditTrailProps {
  className?: string
}

export default function AuditTrail({ className = '' }: AuditTrailProps) {
  const [events, setEvents] = useState<AuditEvent[]>([
    {
      id: '1',
      timestamp: new Date().toISOString(),
      userId: 'user-123',
      userName: 'Jean Kouassi',
      action: 'LOGIN',
      resource: 'AUTH_SYSTEM',
      outcome: 'success',
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      location: {
        country: 'Côte d\'Ivoire',
        city: 'Abidjan',
        coordinates: [5.3364, -4.0267]
      },
      details: {
        method: 'biometric',
        deviceType: 'desktop'
      },
      riskLevel: 'low',
      sessionId: 'sess-abc123'
    },
    {
      id: '2',
      timestamp: new Date(Date.now() - 300000).toISOString(),
      userId: 'user-123',
      userName: 'Jean Kouassi',
      action: 'DATA_EXPORT',
      resource: 'CUSTOMER_DATA',
      resourceId: 'customer-456',
      outcome: 'success',
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      location: {
        country: 'Côte d\'Ivoire',
        city: 'Abidjan'
      },
      details: {
        exportFormat: 'PDF',
        recordCount: 1,
        fileSize: '2.3MB'
      },
      riskLevel: 'medium',
      sessionId: 'sess-abc123'
    },
    {
      id: '3',
      timestamp: new Date(Date.now() - 600000).toISOString(),
      userId: 'user-456',
      userName: 'Marie Diallo',
      action: 'LOGIN_FAILED',
      resource: 'AUTH_SYSTEM',
      outcome: 'failure',
      ipAddress: '************',
      userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X)',
      location: {
        country: 'Sénégal',
        city: 'Dakar'
      },
      details: {
        reason: 'invalid_credentials',
        attemptCount: 3
      },
      riskLevel: 'high',
      sessionId: 'sess-def456'
    },
    {
      id: '4',
      timestamp: new Date(Date.now() - 900000).toISOString(),
      userId: 'admin-001',
      userName: 'Admin Système',
      action: 'PERMISSION_CHANGE',
      resource: 'USER_ACCOUNT',
      resourceId: 'user-789',
      outcome: 'success',
      ipAddress: '********',
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
      location: {
        country: 'Côte d\'Ivoire',
        city: 'Abidjan'
      },
      details: {
        previousRole: 'user',
        newRole: 'premium',
        changedBy: 'admin-001'
      },
      riskLevel: 'medium',
      sessionId: 'sess-ghi789'
    },
    {
      id: '5',
      timestamp: new Date(Date.now() - 1200000).toISOString(),
      userId: 'user-123',
      userName: 'Jean Kouassi',
      action: 'GDPR_REQUEST',
      resource: 'PERSONAL_DATA',
      outcome: 'success',
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      location: {
        country: 'Côte d\'Ivoire',
        city: 'Abidjan'
      },
      details: {
        requestType: 'data_access',
        processingTime: '2 days'
      },
      riskLevel: 'low',
      sessionId: 'sess-abc123'
    }
  ])

  const [filteredEvents, setFilteredEvents] = useState<AuditEvent[]>(events)
  const [filters, setFilters] = useState<AuditFilters>({})
  const [showFilters, setShowFilters] = useState(false)

  // Appliquer les filtres
  useEffect(() => {
    let filtered = [...events]

    if (filters.searchTerm) {
      const term = filters.searchTerm.toLowerCase()
      filtered = filtered.filter(event => 
        event.userName.toLowerCase().includes(term) ||
        event.action.toLowerCase().includes(term) ||
        event.resource.toLowerCase().includes(term) ||
        event.ipAddress.includes(term)
      )
    }

    if (filters.userId) {
      filtered = filtered.filter(event => event.userId === filters.userId)
    }

    if (filters.action) {
      filtered = filtered.filter(event => event.action === filters.action)
    }

    if (filters.outcome) {
      filtered = filtered.filter(event => event.outcome === filters.outcome)
    }

    if (filters.riskLevel) {
      filtered = filtered.filter(event => event.riskLevel === filters.riskLevel)
    }

    if (filters.dateRange) {
      filtered = filtered.filter(event => {
        const eventDate = new Date(event.timestamp)
        return eventDate >= filters.dateRange!.from && eventDate <= filters.dateRange!.to
      })
    }

    setFilteredEvents(filtered)
  }, [events, filters])

  // Exporter les logs d'audit
  const exportAuditLogs = () => {
    const data = {
      events: filteredEvents,
      exportDate: new Date().toISOString(),
      filters: filters,
      totalEvents: filteredEvents.length
    }

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `audit-logs-${new Date().toISOString().split('T')[0]}.json`
    a.click()
    URL.revokeObjectURL(url)

    toast.success('Logs d\'audit exportés avec succès')
  }

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'LOGIN': return <Unlock className="h-4 w-4" />
      case 'LOGIN_FAILED': return <Lock className="h-4 w-4" />
      case 'LOGOUT': return <Lock className="h-4 w-4" />
      case 'DATA_EXPORT': return <Download className="h-4 w-4" />
      case 'DATA_VIEW': return <Eye className="h-4 w-4" />
      case 'PERMISSION_CHANGE': return <Settings className="h-4 w-4" />
      case 'GDPR_REQUEST': return <FileText className="h-4 w-4" />
      default: return <Activity className="h-4 w-4" />
    }
  }

  const getOutcomeIcon = (outcome: string) => {
    switch (outcome) {
      case 'success': return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'failure': return <X className="h-4 w-4 text-red-500" />
      case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      default: return <Activity className="h-4 w-4 text-gray-500" />
    }
  }

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200'
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low': return 'bg-green-100 text-green-800 border-green-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getDeviceIcon = (userAgent: string) => {
    if (userAgent.includes('iPhone') || userAgent.includes('Android')) {
      return <Smartphone className="h-4 w-4" />
    }
    return <Monitor className="h-4 w-4" />
  }

  const uniqueActions = [...new Set(events.map(e => e.action))]
  const uniqueUsers = [...new Set(events.map(e => ({ id: e.userId, name: e.userName })))]

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-purple-400 to-purple-500 rounded-lg flex items-center justify-center">
                <Shield className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle>Journal d'Audit</CardTitle>
                <CardDescription>
                  Traçabilité complète des actions utilisateurs et système
                </CardDescription>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Badge className="bg-blue-100 text-blue-800 border-blue-200">
                {filteredEvents.length} événements
              </Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
              >
                <Filter className="h-4 w-4 mr-2" />
                Filtres
              </Button>
              <Button onClick={exportAuditLogs} size="sm">
                <Download className="h-4 w-4 mr-2" />
                Exporter
              </Button>
            </div>
          </div>
        </CardHeader>

        {/* Filtres */}
        {showFilters && (
          <CardContent className="border-t border-gray-200 bg-gray-50">
            <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-700 mb-1 block">Recherche</label>
                <Input
                  placeholder="Rechercher..."
                  value={filters.searchTerm || ''}
                  onChange={(e) => setFilters(prev => ({ ...prev, searchTerm: e.target.value }))}
                />
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-700 mb-1 block">Utilisateur</label>
                <Select
                  value={filters.userId || ''}
                  onValueChange={(value) => setFilters(prev => ({ ...prev, userId: value || undefined }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Tous" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Tous</SelectItem>
                    {uniqueUsers.map(user => (
                      <SelectItem key={user.id} value={user.id}>{user.name}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-700 mb-1 block">Action</label>
                <Select
                  value={filters.action || ''}
                  onValueChange={(value) => setFilters(prev => ({ ...prev, action: value || undefined }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Toutes" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Toutes</SelectItem>
                    {uniqueActions.map(action => (
                      <SelectItem key={action} value={action}>{action}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-700 mb-1 block">Résultat</label>
                <Select
                  value={filters.outcome || ''}
                  onValueChange={(value) => setFilters(prev => ({ ...prev, outcome: value || undefined }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Tous" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Tous</SelectItem>
                    <SelectItem value="success">Succès</SelectItem>
                    <SelectItem value="failure">Échec</SelectItem>
                    <SelectItem value="warning">Avertissement</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-700 mb-1 block">Risque</label>
                <Select
                  value={filters.riskLevel || ''}
                  onValueChange={(value) => setFilters(prev => ({ ...prev, riskLevel: value || undefined }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Tous" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Tous</SelectItem>
                    <SelectItem value="low">Faible</SelectItem>
                    <SelectItem value="medium">Moyen</SelectItem>
                    <SelectItem value="high">Élevé</SelectItem>
                    <SelectItem value="critical">Critique</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-700 mb-1 block">Actions</label>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setFilters({})}
                  className="w-full"
                >
                  Réinitialiser
                </Button>
              </div>
            </div>
          </CardContent>
        )}
      </Card>

      {/* Liste des événements */}
      <Card>
        <CardHeader>
          <CardTitle>Événements d'Audit</CardTitle>
          <CardDescription>
            Chronologie détaillée des actions et événements système
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {filteredEvents.map((event) => (
              <motion.div
                key={event.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-start space-x-4">
                  <div className="flex items-center space-x-2 mt-1">
                    {getOutcomeIcon(event.outcome)}
                    <div className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
                      {getActionIcon(event.action)}
                    </div>
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between mb-2">
                      <div>
                        <h4 className="font-medium text-gray-900">
                          {event.action.replace(/_/g, ' ')}
                        </h4>
                        <p className="text-sm text-gray-600">
                          {event.userName} • {event.resource}
                          {event.resourceId && ` (${event.resourceId})`}
                        </p>
                      </div>

                      <div className="flex items-center space-x-2 ml-4">
                        <Badge className={getRiskLevelColor(event.riskLevel)}>
                          {event.riskLevel === 'low' && 'Faible'}
                          {event.riskLevel === 'medium' && 'Moyen'}
                          {event.riskLevel === 'high' && 'Élevé'}
                          {event.riskLevel === 'critical' && 'Critique'}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {event.outcome === 'success' && 'Succès'}
                          {event.outcome === 'failure' && 'Échec'}
                          {event.outcome === 'warning' && 'Avertissement'}
                        </Badge>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600 mb-3">
                      <div className="flex items-center space-x-1">
                        <Clock className="h-3 w-3" />
                        <span>{new Date(event.timestamp).toLocaleString('fr-FR')}</span>
                      </div>

                      <div className="flex items-center space-x-1">
                        <User className="h-3 w-3" />
                        <span>{event.userName}</span>
                      </div>

                      <div className="flex items-center space-x-1">
                        {getDeviceIcon(event.userAgent)}
                        <span>{event.ipAddress}</span>
                      </div>

                      {event.location && (
                        <div className="flex items-center space-x-1">
                          <MapPin className="h-3 w-3" />
                          <span>{event.location.city}, {event.location.country}</span>
                        </div>
                      )}
                    </div>

                    {/* Détails supplémentaires */}
                    {Object.keys(event.details).length > 0 && (
                      <div className="bg-gray-50 rounded-lg p-3 mt-3">
                        <h5 className="text-sm font-medium text-gray-900 mb-2">Détails</h5>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-2 text-sm">
                          {Object.entries(event.details).map(([key, value]) => (
                            <div key={key} className="flex justify-between">
                              <span className="text-gray-600 capitalize">
                                {key.replace(/([A-Z])/g, ' $1').toLowerCase()}:
                              </span>
                              <span className="font-medium ml-2">
                                {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}

            {filteredEvents.length === 0 && (
              <div className="text-center py-12 text-gray-500">
                <Activity className="h-16 w-16 mx-auto mb-4 text-gray-300" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun événement trouvé</h3>
                <p className="text-gray-600">
                  Aucun événement ne correspond aux critères de filtrage sélectionnés.
                </p>
                <Button
                  variant="outline"
                  onClick={() => setFilters({})}
                  className="mt-4"
                >
                  Réinitialiser les filtres
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Statistiques d'audit */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Événements réussis</p>
                <p className="text-2xl font-bold text-green-600">
                  {events.filter(e => e.outcome === 'success').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                <X className="h-6 w-6 text-red-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Événements échoués</p>
                <p className="text-2xl font-bold text-red-600">
                  {events.filter(e => e.outcome === 'failure').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                <AlertTriangle className="h-6 w-6 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Risque élevé</p>
                <p className="text-2xl font-bold text-orange-600">
                  {events.filter(e => e.riskLevel === 'high' || e.riskLevel === 'critical').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <Database className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Total événements</p>
                <p className="text-2xl font-bold text-blue-600">{events.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
