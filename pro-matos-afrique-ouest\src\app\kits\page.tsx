'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Package, 
  Download, 
  FileText, 
  Star,
  ArrowLeft,
  Search,
  Filter,
  Crown,
  Lock,
  Eye,
  Users
} from 'lucide-react'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { toast } from 'sonner'
import { useAuthStore } from '@/lib/stores/authStore'
import { supabase, getFileUrl } from '@/lib/supabase/client'
import GlobalNavigation from '@/components/navigation/GlobalNavigation'

interface Kit {
  id: number
  title: string
  description: string
  category: string
  file_url: string
  file_name: string
  file_size: number
  downloads: number
  is_premium: boolean
  created_at: string
  updated_at: string
}

export default function KitsPage() {
  const { user } = useAuthStore()
  const [kits, setKits] = useState<Kit[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [categoryFilter, setCategoryFilter] = useState<string>('all')
  const [downloadingKits, setDownloadingKits] = useState<Set<number>>(new Set())

  useEffect(() => {
    fetchKits()
  }, [])

  const fetchKits = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('kits')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error
      setKits(data || [])
    } catch (error) {
      console.error('Erreur lors du chargement des kits:', error)
      toast.error('Erreur lors du chargement des kits')
    } finally {
      setLoading(false)
    }
  }

  const categories = ['all', ...Array.from(new Set(kits.map(kit => kit.category)))]

  const filteredKits = kits.filter(kit => {
    const matchesSearch = kit.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         kit.description.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesCategory = categoryFilter === 'all' || kit.category === categoryFilter
    
    // Filtrer les kits premium si l'utilisateur n'est pas VIP
    const canAccess = !kit.is_premium || (user?.role === 'vip' || user?.role === 'admin')
    
    return matchesSearch && matchesCategory && canAccess
  })

  const handleDownload = async (kit: Kit) => {
    if (!user) {
      toast.error('Vous devez être connecté pour télécharger')
      return
    }

    // Vérifier les permissions pour les kits premium
    if (kit.is_premium && user.role !== 'vip' && user.role !== 'admin') {
      toast.error('Ce kit est réservé aux membres VIP', {
        description: 'Passez au statut VIP pour accéder aux kits premium'
      })
      return
    }

    setDownloadingKits(prev => new Set(prev).add(kit.id))
    toast.loading('Préparation du téléchargement...', { id: `download-${kit.id}` })

    try {
      // Obtenir l'URL de téléchargement
      const { data } = getFileUrl('kits', kit.file_url)
      
      // Créer le lien de téléchargement
      const link = document.createElement('a')
      link.href = data.publicUrl
      link.download = kit.file_name
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      // Incrémenter le compteur de téléchargements
      await supabase
        .from('kits')
        .update({ downloads: kit.downloads + 1 })
        .eq('id', kit.id)

      // Enregistrer le téléchargement pour l'utilisateur
      await supabase
        .from('user_downloads')
        .insert({
          user_id: user.id,
          kit_id: kit.id,
          downloaded_at: new Date().toISOString()
        })

      // Mettre à jour l'état local
      setKits(prev => prev.map(k => 
        k.id === kit.id ? { ...k, downloads: k.downloads + 1 } : k
      ))

      toast.success('Téléchargement démarré !', { 
        id: `download-${kit.id}`,
        description: `${kit.title} - ${kit.file_name}`
      })

    } catch (error: any) {
      console.error('Erreur lors du téléchargement:', error)
      toast.error('Erreur lors du téléchargement', { 
        id: `download-${kit.id}`,
        description: 'Veuillez réessayer'
      })
    } finally {
      setDownloadingKits(prev => {
        const newSet = new Set(prev)
        newSet.delete(kit.id)
        return newSet
      })
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getCategoryColor = (category: string) => {
    const colors: { [key: string]: string } = {
      'Résidentiel': 'bg-blue-100 text-blue-800',
      'Industriel': 'bg-red-100 text-red-800',
      'Tertiaire': 'bg-green-100 text-green-800',
      'Sécurité': 'bg-yellow-100 text-yellow-800',
      'Éclairage': 'bg-purple-100 text-purple-800',
      'Automatisme': 'bg-indigo-100 text-indigo-800'
    }
    return colors[category] || 'bg-gray-100 text-gray-800'
  }

  const stats = {
    total: kits.length,
    premium: kits.filter(k => k.is_premium).length,
    totalDownloads: kits.reduce((sum, k) => sum + k.downloads, 0)
  }

  return (
    <>
      <GlobalNavigation />
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
        {/* Header */}
        <div className="bg-white border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Link
                  href="/hub"
                  className="flex items-center space-x-2 text-slate-700 hover:text-blue-600 transition-colors"
                >
                  <ArrowLeft className="h-5 w-5" />
                  <span>Retour au Hub</span>
                </Link>
                <div className="h-6 w-px bg-gray-300" />
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 flex items-center space-x-2">
                    <Package className="h-6 w-6" />
                    <span>Kits de Prescription</span>
                  </h1>
                  <p className="text-gray-600">Documents techniques et templates professionnels</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Badge className="bg-blue-100 text-blue-800">
                  {user?.role === 'vip' || user?.role === 'admin' ? 'Accès VIP' : 'Membre'}
                </Badge>
                {(user?.role !== 'vip' && user?.role !== 'admin') && (
                  <Link href="/club">
                    <Button size="sm" className="bg-amber-600 hover:bg-amber-700">
                      <Crown className="h-4 w-4 mr-2" />
                      Devenir VIP
                    </Button>
                  </Link>
                )}
              </div>
            </div>
          </div>
        </div>

        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Statistiques */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <Package className="h-8 w-8 text-blue-500" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Kits Disponibles</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <Crown className="h-8 w-8 text-amber-500" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Kits Premium</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.premium}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <Download className="h-8 w-8 text-green-500" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Total Téléchargements</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.totalDownloads.toLocaleString()}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Filtres */}
          <Card className="mb-8">
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Rechercher un kit..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Filter className="h-4 w-4 text-gray-500" />
                  <select
                    value={categoryFilter}
                    onChange={(e) => setCategoryFilter(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">Toutes les catégories</option>
                    {categories.filter(cat => cat !== 'all').map(category => (
                      <option key={category} value={category}>{category}</option>
                    ))}
                  </select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Grille des kits */}
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500" />
              <span className="ml-2 text-gray-600">Chargement des kits...</span>
            </div>
          ) : filteredKits.length === 0 ? (
            <Card>
              <CardContent className="p-12 text-center">
                <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 mb-4">
                  {searchQuery || categoryFilter !== 'all' 
                    ? 'Aucun kit ne correspond à vos critères'
                    : 'Aucun kit disponible pour le moment'
                  }
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredKits.map((kit) => (
                <motion.div
                  key={kit.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 }}
                >
                  <Card className="h-full hover:shadow-lg transition-shadow">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <CardTitle className="text-lg flex items-center space-x-2">
                            <FileText className="h-5 w-5" />
                            <span>{kit.title}</span>
                            {kit.is_premium && (
                              <Crown className="h-4 w-4 text-amber-500" />
                            )}
                          </CardTitle>
                          <div className="flex items-center space-x-2 mt-2">
                            <Badge className={getCategoryColor(kit.category)}>
                              {kit.category}
                            </Badge>
                            {kit.is_premium && (
                              <Badge className="bg-amber-100 text-amber-800">
                                Premium
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <CardDescription className="mb-4">
                        {kit.description}
                      </CardDescription>
                      
                      <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                        <span className="flex items-center space-x-1">
                          <Download className="h-4 w-4" />
                          <span>{kit.downloads} téléchargements</span>
                        </span>
                        <span>{formatFileSize(kit.file_size)}</span>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Button
                          onClick={() => handleDownload(kit)}
                          disabled={downloadingKits.has(kit.id)}
                          className="flex-1"
                        >
                          {downloadingKits.has(kit.id) ? (
                            <>
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                              Téléchargement...
                            </>
                          ) : (
                            <>
                              <Download className="h-4 w-4 mr-2" />
                              Télécharger
                            </>
                          )}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          )}

          {/* Message pour les non-VIP */}
          {(user?.role !== 'vip' && user?.role !== 'admin') && kits.some(k => k.is_premium) && (
            <Card className="mt-8 border-amber-200 bg-amber-50">
              <CardContent className="p-6">
                <div className="flex items-center space-x-3">
                  <Crown className="h-8 w-8 text-amber-600" />
                  <div>
                    <h3 className="font-semibold text-amber-900">Accédez aux Kits Premium</h3>
                    <p className="text-amber-800 text-sm">
                      Devenez membre VIP pour accéder à {stats.premium} kits premium exclusifs avec des templates avancés et des outils professionnels.
                    </p>
                    <Link href="/club">
                      <Button className="mt-3 bg-amber-600 hover:bg-amber-700">
                        <Crown className="h-4 w-4 mr-2" />
                        Découvrir les avantages VIP
                      </Button>
                    </Link>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </main>
      </div>
    </>
  )
}
