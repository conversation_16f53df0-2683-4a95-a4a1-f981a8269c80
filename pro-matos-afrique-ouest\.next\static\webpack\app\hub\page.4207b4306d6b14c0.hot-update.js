"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/hub/page",{

/***/ "(app-pages-browser)/./src/components/hub/InformationHub.tsx":
/*!***********************************************!*\
  !*** ./src/components/hub/InformationHub.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ InformationHub; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Calendar,Clock,Newspaper,Package,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Calendar,Clock,Newspaper,Package,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Calendar,Clock,Newspaper,Package,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Calendar,Clock,Newspaper,Package,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Calendar,Clock,Newspaper,Package,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Calendar,Clock,Newspaper,Package,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/newspaper.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Calendar,Clock,Newspaper,Package,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Calendar,Clock,Newspaper,Package,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Calendar,Clock,Newspaper,Package,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _store_useStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/useStore */ \"(app-pages-browser)/./src/store/useStore.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction InformationHub(param) {\n    let { className = \"\" } = param;\n    _s();\n    const { user } = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_2__.useStore)();\n    const [alerts, setAlerts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [realTimeConnected, setRealTimeConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [lastUpdateTime, setLastUpdateTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"alerts\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filterType, setFilterType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [filterCategory, setFilterCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialisation des données\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Marquer que nous sommes côté client\n        setIsClient(true);\n        const loadInitialData = ()=>{\n            // Données statiques d'exemple pour démonstration\n            setMarketAlerts([\n                {\n                    id: \"1\",\n                    title: \"Nouvelle norme NF C 15-100 - Amendement A6\",\n                    message: \"Mise \\xe0 jour importante des r\\xe8gles d'installation \\xe9lectrique\",\n                    severity: \"medium\",\n                    type: \"regulation\",\n                    is_active: true,\n                    created_at: \"2024-01-15T10:00:00Z\",\n                    source: \"Veille r\\xe9glementaire\",\n                    affected_regions: [\n                        \"C\\xf4te d'Ivoire\",\n                        \"S\\xe9n\\xe9gal\",\n                        \"Mali\"\n                    ],\n                    category: \"R\\xe9glementation\"\n                },\n                {\n                    id: \"2\",\n                    title: \"Formation Schneider Electric - Nouveaux produits\",\n                    message: \"Session de formation sur la gamme Acti9 nouvelle g\\xe9n\\xe9ration\",\n                    severity: \"low\",\n                    type: \"training\",\n                    is_active: true,\n                    created_at: \"2024-01-14T14:30:00Z\",\n                    source: \"Partenaire\",\n                    affected_regions: [\n                        \"Abidjan\",\n                        \"Dakar\"\n                    ],\n                    category: \"Formation\"\n                }\n            ]);\n            // Pas de données temps réel - interface statique\n            setStockUpdates([]);\n            setTrainingEvents([]);\n            setNewsUpdates([]);\n        };\n        loadInitialData();\n        // Pas de connexion temps réel - données statiques\n        setRealTimeConnected(false);\n    // Pas d'abonnement temps réel\n    // const unsubscribe = HubService.subscribeToRealTimeUpdates(...)\n    // return () => {\n    //   unsubscribe()\n    //   setRealTimeConnected(false)\n    // }\n    }, []);\n    // Filtrage des alertes\n    const filteredAlerts = marketAlerts.filter((alert)=>{\n        const matchesSearch = searchQuery === \"\" || alert.title.toLowerCase().includes(searchQuery.toLowerCase()) || alert.message.toLowerCase().includes(searchQuery.toLowerCase());\n        const matchesSeverity = filterSeverity === \"all\" || alert.severity === filterSeverity;\n        return matchesSearch && matchesSeverity && alert.is_active;\n    });\n    const getSeverityColor = (severity)=>{\n        switch(severity){\n            case \"critical\":\n                return \"bg-red-100 text-red-800 border-red-200\";\n            case \"high\":\n                return \"bg-orange-100 text-orange-800 border-orange-200\";\n            case \"medium\":\n                return \"bg-yellow-100 text-yellow-800 border-yellow-200\";\n            case \"low\":\n                return \"bg-blue-100 text-blue-800 border-blue-200\";\n            default:\n                return \"bg-gray-100 text-gray-800 border-gray-200\";\n        }\n    };\n    const getUpdateTypeIcon = (type)=>{\n        switch(type){\n            case \"restock\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 30\n                }, this);\n            case \"decrease\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4 text-orange-600 rotate-180\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 31\n                }, this);\n            case \"out_of_stock\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 35\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 text-blue-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const tabs = [\n        {\n            id: \"alerts\",\n            label: \"Alertes March\\xe9\",\n            icon: _barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            count: filteredAlerts.length\n        },\n        {\n            id: \"stocks\",\n            label: \"Stocks\",\n            icon: _barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            count: stockUpdates.length\n        },\n        {\n            id: \"training\",\n            label: \"Formations\",\n            icon: _barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            count: trainingEvents.length\n        },\n        {\n            id: \"news\",\n            label: \"Actualit\\xe9s\",\n            icon: _barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            count: newsUpdates.length\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"Hub d'Information Pro Matos\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-1\",\n                                        children: \"Centre de veille technologique et r\\xe9glementaire\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 rounded-full bg-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-blue-600 font-medium\",\n                                                children: \"Mode D\\xe9monstration\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-sm text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"Mis \\xe0 jour \",\n                                                    isClient ? (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatDate)(lastUpdateTime) : \"--\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: updateLastUpdateTime,\n                                        className: \"p-2 rounded-lg hover:bg-gray-100 transition-colors\",\n                                        title: \"Actualiser les donn\\xe9es\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Rechercher dans le hub...\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                        className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400 focus:border-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, this),\n                            activeTab === \"alerts\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: filterSeverity,\n                                onChange: (e)=>setFilterSeverity(e.target.value),\n                                className: \"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400 focus:border-transparent\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"all\",\n                                        children: \"Toutes les priorit\\xe9s\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"critical\",\n                                        children: \"Critique\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"high\",\n                                        children: \"\\xc9lev\\xe9e\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"medium\",\n                                        children: \"Moyenne\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"low\",\n                                        children: \"Faible\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"flex space-x-8 px-6\",\n                    children: tabs.map((tab)=>{\n                        const Icon = tab.icon;\n                        const isActive = activeTab === tab.id;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: ()=>setActiveTab(tab.id),\n                            className: \"flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors \".concat(isActive ? \"border-amber-500 text-amber-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: tab.label\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 17\n                                }, this),\n                                tab.count > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(isActive ? \"bg-amber-100 text-amber-800\" : \"bg-gray-100 text-gray-600\"),\n                                    children: tab.count\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, tab.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                lineNumber: 203,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.AnimatePresence, {\n                    mode: \"wait\",\n                    children: [\n                        activeTab === \"alerts\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            className: \"space-y-4\",\n                            children: filteredAlerts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8 text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Aucune alerte correspondant aux crit\\xe8res\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 17\n                            }, this) : filteredAlerts.map((alert)=>{\n                                var _alert_affected_regions;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: -20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    className: \"p-4 rounded-lg border \".concat(getSeverityColor(alert.severity)),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-medium uppercase tracking-wide\",\n                                                            children: alert.type.replace(\"_\", \" \")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatDate)(alert.created_at)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold mb-1\",\n                                                    children: alert.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm mb-2\",\n                                                    children: alert.message\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4 text-xs text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"Cat\\xe9gorie: \",\n                                                                alert.category || alert.type\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                            lineNumber: 272,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"R\\xe9gions: \",\n                                                                ((_alert_affected_regions = alert.affected_regions) === null || _alert_affected_regions === void 0 ? void 0 : _alert_affected_regions.join(\", \")) || \"Non sp\\xe9cifi\\xe9\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 21\n                                    }, this)\n                                }, alert.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, \"alerts\", false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 13\n                        }, this),\n                        activeTab === \"stocks\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            className: \"space-y-4\",\n                            children: stockUpdates.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8 text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Aucune mise \\xe0 jour de stock r\\xe9cente\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 17\n                            }, this) : stockUpdates.map((update)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: -20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    className: \"p-4 rounded-lg border border-gray-200 bg-gray-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    getUpdateTypeIcon(update.update_type),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-gray-900\",\n                                                                children: [\n                                                                    \"Produit #\",\n                                                                    update.product_id\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                lineNumber: 308,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: update.location\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                lineNumber: 311,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-lg font-bold text-gray-900\",\n                                                        children: [\n                                                            update.previous_quantity,\n                                                            \" → \",\n                                                            update.current_quantity\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatDate)(update.created_at)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 21\n                                    }, this)\n                                }, update.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 19\n                                }, this))\n                        }, \"stocks\", false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 13\n                        }, this),\n                        activeTab === \"training\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            className: \"space-y-4\",\n                            children: trainingEvents.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8 text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Aucun \\xe9v\\xe9nement de formation programm\\xe9\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 17\n                            }, this) : trainingEvents.map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: -20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    className: \"p-6 rounded-lg border border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2 mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(event.type === \"certification\" ? \"bg-purple-100 text-purple-800\" : event.type === \"webinar\" ? \"bg-green-100 text-green-800\" : event.type === \"workshop\" ? \"bg-blue-100 text-blue-800\" : \"bg-gray-100 text-gray-800\"),\n                                                                children: event.type\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                lineNumber: 353,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            event.membership_required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800\",\n                                                                children: [\n                                                                    event.membership_required,\n                                                                    \"+\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                lineNumber: 362,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                        lineNumber: 352,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                                        children: event.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 mb-3\",\n                                                        children: event.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-4 text-sm text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"Instructeur:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                        lineNumber: 373,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    \" \",\n                                                                    event.instructor\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                lineNumber: 372,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"Lieu:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                        lineNumber: 376,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    \" \",\n                                                                    event.location\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"Date:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                        lineNumber: 379,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    \" \",\n                                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatDate)(event.start_date)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                lineNumber: 378,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"Participants:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                        lineNumber: 382,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    \" \",\n                                                                    event.current_participants,\n                                                                    \"/\",\n                                                                    event.max_participants\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                lineNumber: 381,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right ml-4\",\n                                                children: [\n                                                    event.registration_fee > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-lg font-bold text-gray-900\",\n                                                        children: [\n                                                            event.registration_fee.toLocaleString(),\n                                                            \" FCFA\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 27\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-lg font-bold text-green-600\",\n                                                        children: \"Gratuit\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        className: \"mt-2 px-4 py-2 bg-amber-500 text-white rounded-lg hover:bg-amber-600 transition-colors\",\n                                                        children: \"S'inscrire\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 21\n                                    }, this)\n                                }, event.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 19\n                                }, this))\n                        }, \"training\", false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 13\n                        }, this),\n                        activeTab === \"news\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            className: \"space-y-6\",\n                            children: newsUpdates.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8 text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_Clock_Newspaper_Package_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Aucune actualit\\xe9 r\\xe9cente\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 17\n                            }, this) : newsUpdates.map((news)=>{\n                                var _news_tags;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: -20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    className: \"p-6 rounded-lg border border-gray-200 \".concat(news.is_featured ? \"bg-gradient-to-r from-amber-50 to-yellow-50 border-amber-200\" : \"bg-white\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start space-x-4\",\n                                        children: [\n                                            news.image_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-24 h-24 bg-gray-200 rounded-lg flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2 mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(news.category === \"regulation\" ? \"bg-red-100 text-red-800\" : news.category === \"technology\" ? \"bg-blue-100 text-blue-800\" : news.category === \"company\" ? \"bg-green-100 text-green-800\" : news.category === \"market\" ? \"bg-purple-100 text-purple-800\" : \"bg-gray-100 text-gray-800\"),\n                                                                children: news.category\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                lineNumber: 437,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            news.is_featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800\",\n                                                                children: \"\\xc0 la une\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                lineNumber: 447,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatDate)(news.published_at)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                lineNumber: 451,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                                        children: news.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                        lineNumber: 455,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 mb-3\",\n                                                        children: news.summary\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                        lineNumber: 458,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-4 text-sm text-gray-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"Par \",\n                                                                            news.author\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                        lineNumber: 461,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex space-x-1\",\n                                                                        children: ((_news_tags = news.tags) === null || _news_tags === void 0 ? void 0 : _news_tags.slice(0, 3).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"px-2 py-1 bg-gray-100 rounded text-xs\",\n                                                                                children: [\n                                                                                    \"#\",\n                                                                                    tag\n                                                                                ]\n                                                                            }, tag, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                                lineNumber: 464,\n                                                                                columnNumber: 33\n                                                                            }, this))) || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-400\",\n                                                                            children: \"Aucun tag\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                            lineNumber: 467,\n                                                                            columnNumber: 37\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                        lineNumber: 462,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                lineNumber: 460,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            news.source_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: news.source_url,\n                                                                target: \"_blank\",\n                                                                rel: \"noopener noreferrer\",\n                                                                className: \"text-amber-600 hover:text-amber-700 text-sm font-medium\",\n                                                                children: \"Lire la suite →\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                                lineNumber: 471,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 21\n                                    }, this)\n                                }, news.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, \"news\", false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                            lineNumber: 409,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n                lineNumber: 236,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\hub\\\\InformationHub.tsx\",\n        lineNumber: 139,\n        columnNumber: 5\n    }, this);\n}\n_s(InformationHub, \"6PIsVuPkAL78OqUWgxtyje1KRM4=\", false, function() {\n    return [\n        _store_useStore__WEBPACK_IMPORTED_MODULE_2__.useStore\n    ];\n});\n_c = InformationHub;\nvar _c;\n$RefreshReg$(_c, \"InformationHub\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2h1Yi9JbmZvcm1hdGlvbkh1Yi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUUyQztBQUNZO0FBZ0JsQztBQUNzQjtBQUVIO0FBVXpCLFNBQVNlLGVBQWUsS0FBdUM7UUFBdkMsRUFBRUMsWUFBWSxFQUFFLEVBQXVCLEdBQXZDOztJQUNyQyxNQUFNLEVBQUVDLElBQUksRUFBRSxHQUFHSix5REFBUUE7SUFFekIsTUFBTSxDQUFDSyxRQUFRQyxVQUFVLEdBQUduQiwrQ0FBUUEsQ0FBVSxFQUFFO0lBQ2hELE1BQU0sQ0FBQ29CLFNBQVNDLFdBQVcsR0FBR3JCLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ3NCLG1CQUFtQkMscUJBQXFCLEdBQUd2QiwrQ0FBUUEsQ0FBQztJQUMzRCxNQUFNLENBQUN3QixnQkFBZ0JDLGtCQUFrQixHQUFHekIsK0NBQVFBLENBQU8sSUFBSTBCO0lBRS9ELE1BQU0sQ0FBQ0MsV0FBV0MsYUFBYSxHQUFHNUIsK0NBQVFBLENBQTRDO0lBQ3RGLE1BQU0sQ0FBQzZCLGFBQWFDLGVBQWUsR0FBRzlCLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQytCLFlBQVlDLGNBQWMsR0FBR2hDLCtDQUFRQSxDQUFvRDtJQUNoRyxNQUFNLENBQUNpQyxnQkFBZ0JDLGtCQUFrQixHQUFHbEMsK0NBQVFBLENBQVM7SUFDN0QsTUFBTSxDQUFDbUMsVUFBVUMsWUFBWSxHQUFHcEMsK0NBQVFBLENBQUM7SUFFekMsNkJBQTZCO0lBQzdCQyxnREFBU0EsQ0FBQztRQUNSLHNDQUFzQztRQUN0Q21DLFlBQVk7UUFFWixNQUFNQyxrQkFBa0I7WUFDdEIsaURBQWlEO1lBQ2pEQyxnQkFBZ0I7Z0JBQ2Q7b0JBQ0VDLElBQUk7b0JBQ0pDLE9BQU87b0JBQ1BDLFNBQVM7b0JBQ1RDLFVBQVU7b0JBQ1ZDLE1BQU07b0JBQ05DLFdBQVc7b0JBQ1hDLFlBQVk7b0JBQ1pDLFFBQVE7b0JBQ1JDLGtCQUFrQjt3QkFBQzt3QkFBa0I7d0JBQVc7cUJBQU87b0JBQ3ZEQyxVQUFVO2dCQUNaO2dCQUNBO29CQUNFVCxJQUFJO29CQUNKQyxPQUFPO29CQUNQQyxTQUFTO29CQUNUQyxVQUFVO29CQUNWQyxNQUFNO29CQUNOQyxXQUFXO29CQUNYQyxZQUFZO29CQUNaQyxRQUFRO29CQUNSQyxrQkFBa0I7d0JBQUM7d0JBQVc7cUJBQVE7b0JBQ3RDQyxVQUFVO2dCQUNaO2FBQ0Q7WUFFRCxpREFBaUQ7WUFDakRDLGdCQUFnQixFQUFFO1lBQ2xCQyxrQkFBa0IsRUFBRTtZQUNwQkMsZUFBZSxFQUFFO1FBQ25CO1FBRUFkO1FBRUEsa0RBQWtEO1FBQ2xEZCxxQkFBcUI7SUFFckIsOEJBQThCO0lBQzlCLGlFQUFpRTtJQUVqRSxpQkFBaUI7SUFDakIsa0JBQWtCO0lBQ2xCLGdDQUFnQztJQUNoQyxJQUFJO0lBQ04sR0FBRyxFQUFFO0lBRUwsdUJBQXVCO0lBQ3ZCLE1BQU02QixpQkFBaUJDLGFBQWFDLE1BQU0sQ0FBQ0MsQ0FBQUE7UUFDekMsTUFBTUMsZ0JBQWdCM0IsZ0JBQWdCLE1BQ3BDMEIsTUFBTWYsS0FBSyxDQUFDaUIsV0FBVyxHQUFHQyxRQUFRLENBQUM3QixZQUFZNEIsV0FBVyxPQUMxREYsTUFBTWQsT0FBTyxDQUFDZ0IsV0FBVyxHQUFHQyxRQUFRLENBQUM3QixZQUFZNEIsV0FBVztRQUU5RCxNQUFNRSxrQkFBa0JDLG1CQUFtQixTQUFTTCxNQUFNYixRQUFRLEtBQUtrQjtRQUV2RSxPQUFPSixpQkFBaUJHLG1CQUFtQkosTUFBTVgsU0FBUztJQUM1RDtJQUVBLE1BQU1pQixtQkFBbUIsQ0FBQ25CO1FBQ3hCLE9BQVFBO1lBQ04sS0FBSztnQkFBWSxPQUFPO1lBQ3hCLEtBQUs7Z0JBQVEsT0FBTztZQUNwQixLQUFLO2dCQUFVLE9BQU87WUFDdEIsS0FBSztnQkFBTyxPQUFPO1lBQ25CO2dCQUFTLE9BQU87UUFDbEI7SUFDRjtJQUVBLE1BQU1vQixvQkFBb0IsQ0FBQ25CO1FBQ3pCLE9BQVFBO1lBQ04sS0FBSztnQkFBVyxxQkFBTyw4REFBQ3RDLDJKQUFVQTtvQkFBQ1csV0FBVTs7Ozs7O1lBQzdDLEtBQUs7Z0JBQVkscUJBQU8sOERBQUNYLDJKQUFVQTtvQkFBQ1csV0FBVTs7Ozs7O1lBQzlDLEtBQUs7Z0JBQWdCLHFCQUFPLDhEQUFDVCwySkFBYUE7b0JBQUNTLFdBQVU7Ozs7OztZQUNyRDtnQkFBUyxxQkFBTyw4REFBQ1YsMkpBQU9BO29CQUFDVSxXQUFVOzs7Ozs7UUFDckM7SUFDRjtJQUVBLE1BQU0rQyxPQUFPO1FBQ1g7WUFBRXhCLElBQUk7WUFBVXlCLE9BQU87WUFBa0JDLE1BQU03RCwySkFBSUE7WUFBRThELE9BQU9kLGVBQWVlLE1BQU07UUFBQztRQUNsRjtZQUFFNUIsSUFBSTtZQUFVeUIsT0FBTztZQUFVQyxNQUFNM0QsMkpBQU9BO1lBQUU0RCxPQUFPRSxhQUFhRCxNQUFNO1FBQUM7UUFDM0U7WUFBRTVCLElBQUk7WUFBWXlCLE9BQU87WUFBY0MsTUFBTXpELDJKQUFRQTtZQUFFMEQsT0FBT0csZUFBZUYsTUFBTTtRQUFDO1FBQ3BGO1lBQUU1QixJQUFJO1lBQVF5QixPQUFPO1lBQWNDLE1BQU14RCwySkFBU0E7WUFBRXlELE9BQU9JLFlBQVlILE1BQU07UUFBQztLQUMvRTtJQUVELHFCQUNFLDhEQUFDSTtRQUFJdkQsV0FBVyxpQ0FBMkMsT0FBVkE7OzBCQUUvQyw4REFBQ3VEO2dCQUFJdkQsV0FBVTs7a0NBQ2IsOERBQUN1RDt3QkFBSXZELFdBQVU7OzBDQUNiLDhEQUFDdUQ7O2tEQUNDLDhEQUFDQzt3Q0FBR3hELFdBQVU7a0RBQW1DOzs7Ozs7a0RBQ2pELDhEQUFDeUQ7d0NBQUV6RCxXQUFVO2tEQUFxQjs7Ozs7Ozs7Ozs7OzBDQUdwQyw4REFBQ3VEO2dDQUFJdkQsV0FBVTs7a0RBRWIsOERBQUN1RDt3Q0FBSXZELFdBQVU7OzBEQUNiLDhEQUFDdUQ7Z0RBQUl2RCxXQUFVOzs7Ozs7MERBQ2YsOERBQUMwRDtnREFBSzFELFdBQVU7MERBQW9DOzs7Ozs7Ozs7Ozs7a0RBSXRELDhEQUFDdUQ7d0NBQUl2RCxXQUFVOzswREFDYiw4REFBQ04sNEpBQUtBO2dEQUFDTSxXQUFVOzs7Ozs7MERBQ2pCLDhEQUFDMEQ7O29EQUFLO29EQUFZdkMsV0FBV3JCLHNEQUFVQSxDQUFDVSxrQkFBa0I7Ozs7Ozs7Ozs7Ozs7a0RBSTVELDhEQUFDbUQ7d0NBQ0NoQyxNQUFLO3dDQUNMaUMsU0FBU0M7d0NBQ1Q3RCxXQUFVO3dDQUNWd0IsT0FBTTtrREFFTiw0RUFBQzVCLDRKQUFTQTs0Q0FBQ0ksV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBTTNCLDhEQUFDdUQ7d0JBQUl2RCxXQUFVOzswQ0FDYiw4REFBQ3VEO2dDQUFJdkQsV0FBVTs7a0RBQ2IsOERBQUNMLDRKQUFNQTt3Q0FBQ0ssV0FBVTs7Ozs7O2tEQUNsQiw4REFBQzhEO3dDQUNDbkMsTUFBSzt3Q0FDTG9DLGFBQVk7d0NBQ1pDLE9BQU9uRDt3Q0FDUG9ELFVBQVUsQ0FBQ0MsSUFBTXBELGVBQWVvRCxFQUFFQyxNQUFNLENBQUNILEtBQUs7d0NBQzlDaEUsV0FBVTs7Ozs7Ozs7Ozs7OzRCQUliVyxjQUFjLDBCQUNiLDhEQUFDeUQ7Z0NBQ0NKLE9BQU9wQjtnQ0FDUHFCLFVBQVUsQ0FBQ0MsSUFBTUcsa0JBQWtCSCxFQUFFQyxNQUFNLENBQUNILEtBQUs7Z0NBQ2pEaEUsV0FBVTs7a0RBRVYsOERBQUNzRTt3Q0FBT04sT0FBTTtrREFBTTs7Ozs7O2tEQUNwQiw4REFBQ007d0NBQU9OLE9BQU07a0RBQVc7Ozs7OztrREFDekIsOERBQUNNO3dDQUFPTixPQUFNO2tEQUFPOzs7Ozs7a0RBQ3JCLDhEQUFDTTt3Q0FBT04sT0FBTTtrREFBUzs7Ozs7O2tEQUN2Qiw4REFBQ007d0NBQU9OLE9BQU07a0RBQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFPNUIsOERBQUNUO2dCQUFJdkQsV0FBVTswQkFDYiw0RUFBQ3VFO29CQUFJdkUsV0FBVTs4QkFDWitDLEtBQUt5QixHQUFHLENBQUMsQ0FBQ0M7d0JBQ1QsTUFBTUMsT0FBT0QsSUFBSXhCLElBQUk7d0JBQ3JCLE1BQU0wQixXQUFXaEUsY0FBYzhELElBQUlsRCxFQUFFO3dCQUVyQyxxQkFDRSw4REFBQ29DOzRCQUVDaEMsTUFBSzs0QkFDTGlDLFNBQVMsSUFBTWhELGFBQWE2RCxJQUFJbEQsRUFBRTs0QkFDbEN2QixXQUFXLDBGQUlWLE9BSEMyRSxXQUNJLG9DQUNBOzs4Q0FHTiw4REFBQ0Q7b0NBQUsxRSxXQUFVOzs7Ozs7OENBQ2hCLDhEQUFDMEQ7OENBQU1lLElBQUl6QixLQUFLOzs7Ozs7Z0NBQ2Z5QixJQUFJdkIsS0FBSyxHQUFHLG1CQUNYLDhEQUFDUTtvQ0FBSzFELFdBQVcsOENBRWhCLE9BREMyRSxXQUFXLGdDQUFnQzs4Q0FFMUNGLElBQUl2QixLQUFLOzs7Ozs7OzJCQWZUdUIsSUFBSWxELEVBQUU7Ozs7O29CQW9CakI7Ozs7Ozs7Ozs7OzBCQUtKLDhEQUFDZ0M7Z0JBQUl2RCxXQUFVOzBCQUNiLDRFQUFDYiwyREFBZUE7b0JBQUN5RixNQUFLOzt3QkFDbkJqRSxjQUFjLDBCQUNiLDhEQUFDekIsa0RBQU1BLENBQUNxRSxHQUFHOzRCQUVUc0IsU0FBUztnQ0FBRUMsU0FBUztnQ0FBR0MsR0FBRzs0QkFBRzs0QkFDN0JDLFNBQVM7Z0NBQUVGLFNBQVM7Z0NBQUdDLEdBQUc7NEJBQUU7NEJBQzVCRSxNQUFNO2dDQUFFSCxTQUFTO2dDQUFHQyxHQUFHLENBQUM7NEJBQUc7NEJBQzNCL0UsV0FBVTtzQ0FFVG9DLGVBQWVlLE1BQU0sS0FBSyxrQkFDekIsOERBQUNJO2dDQUFJdkQsV0FBVTs7a0RBQ2IsOERBQUNaLDJKQUFJQTt3Q0FBQ1ksV0FBVTs7Ozs7O2tEQUNoQiw4REFBQ3lEO2tEQUFFOzs7Ozs7Ozs7Ozt1Q0FHTHJCLGVBQWVvQyxHQUFHLENBQUMsQ0FBQ2pDO29DQXFCTUE7cURBcEJ4Qiw4REFBQ3JELGtEQUFNQSxDQUFDcUUsR0FBRztvQ0FFVHNCLFNBQVM7d0NBQUVDLFNBQVM7d0NBQUdJLEdBQUcsQ0FBQztvQ0FBRztvQ0FDOUJGLFNBQVM7d0NBQUVGLFNBQVM7d0NBQUdJLEdBQUc7b0NBQUU7b0NBQzVCbEYsV0FBVyx5QkFBMEQsT0FBakM2QyxpQkFBaUJOLE1BQU1iLFFBQVE7OENBRW5FLDRFQUFDNkI7d0NBQUl2RCxXQUFVO2tEQUNiLDRFQUFDdUQ7NENBQUl2RCxXQUFVOzs4REFDYiw4REFBQ3VEO29EQUFJdkQsV0FBVTs7c0VBQ2IsOERBQUMwRDs0REFBSzFELFdBQVU7c0VBQ2J1QyxNQUFNWixJQUFJLENBQUN3RCxPQUFPLENBQUMsS0FBSzs7Ozs7O3NFQUUzQiw4REFBQ3pCOzREQUFLMUQsV0FBVTtzRUFDYkYsc0RBQVVBLENBQUN5QyxNQUFNVixVQUFVOzs7Ozs7Ozs7Ozs7OERBR2hDLDhEQUFDdUQ7b0RBQUdwRixXQUFVOzhEQUFzQnVDLE1BQU1mLEtBQUs7Ozs7Ozs4REFDL0MsOERBQUNpQztvREFBRXpELFdBQVU7OERBQWdCdUMsTUFBTWQsT0FBTzs7Ozs7OzhEQUMxQyw4REFBQzhCO29EQUFJdkQsV0FBVTs7c0VBQ2IsOERBQUMwRDs7Z0VBQUs7Z0VBQVluQixNQUFNUCxRQUFRLElBQUlPLE1BQU1aLElBQUk7Ozs7Ozs7c0VBQzlDLDhEQUFDK0I7O2dFQUFLO2dFQUFVbkIsRUFBQUEsMEJBQUFBLE1BQU1SLGdCQUFnQixjQUF0QlEsOENBQUFBLHdCQUF3QjhDLElBQUksQ0FBQyxVQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7bUNBbkJ2RDlDLE1BQU1oQixFQUFFOzs7Ozs7MkJBZGY7Ozs7O3dCQTJDUFosY0FBYywwQkFDYiw4REFBQ3pCLGtEQUFNQSxDQUFDcUUsR0FBRzs0QkFFVHNCLFNBQVM7Z0NBQUVDLFNBQVM7Z0NBQUdDLEdBQUc7NEJBQUc7NEJBQzdCQyxTQUFTO2dDQUFFRixTQUFTO2dDQUFHQyxHQUFHOzRCQUFFOzRCQUM1QkUsTUFBTTtnQ0FBRUgsU0FBUztnQ0FBR0MsR0FBRyxDQUFDOzRCQUFHOzRCQUMzQi9FLFdBQVU7c0NBRVRvRCxhQUFhRCxNQUFNLEtBQUssa0JBQ3ZCLDhEQUFDSTtnQ0FBSXZELFdBQVU7O2tEQUNiLDhEQUFDViwySkFBT0E7d0NBQUNVLFdBQVU7Ozs7OztrREFDbkIsOERBQUN5RDtrREFBRTs7Ozs7Ozs7Ozs7dUNBR0xMLGFBQWFvQixHQUFHLENBQUMsQ0FBQ2MsdUJBQ2hCLDhEQUFDcEcsa0RBQU1BLENBQUNxRSxHQUFHO29DQUVUc0IsU0FBUzt3Q0FBRUMsU0FBUzt3Q0FBR0ksR0FBRyxDQUFDO29DQUFHO29DQUM5QkYsU0FBUzt3Q0FBRUYsU0FBUzt3Q0FBR0ksR0FBRztvQ0FBRTtvQ0FDNUJsRixXQUFVOzhDQUVWLDRFQUFDdUQ7d0NBQUl2RCxXQUFVOzswREFDYiw4REFBQ3VEO2dEQUFJdkQsV0FBVTs7b0RBQ1o4QyxrQkFBa0J3QyxPQUFPQyxXQUFXO2tFQUNyQyw4REFBQ2hDOzswRUFDQyw4REFBQzZCO2dFQUFHcEYsV0FBVTs7b0VBQThCO29FQUNoQ3NGLE9BQU9FLFVBQVU7Ozs7Ozs7MEVBRTdCLDhEQUFDL0I7Z0VBQUV6RCxXQUFVOzBFQUF5QnNGLE9BQU9HLFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFHekQsOERBQUNsQztnREFBSXZELFdBQVU7O2tFQUNiLDhEQUFDdUQ7d0RBQUl2RCxXQUFVOzs0REFDWnNGLE9BQU9JLGlCQUFpQjs0REFBQzs0REFBSUosT0FBT0ssZ0JBQWdCOzs7Ozs7O2tFQUV2RCw4REFBQ3BDO3dEQUFJdkQsV0FBVTtrRUFDWkYsc0RBQVVBLENBQUN3RixPQUFPekQsVUFBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7O21DQXBCOUJ5RCxPQUFPL0QsRUFBRTs7Ozs7MkJBZGhCOzs7Ozt3QkE0Q1BaLGNBQWMsNEJBQ2IsOERBQUN6QixrREFBTUEsQ0FBQ3FFLEdBQUc7NEJBRVRzQixTQUFTO2dDQUFFQyxTQUFTO2dDQUFHQyxHQUFHOzRCQUFHOzRCQUM3QkMsU0FBUztnQ0FBRUYsU0FBUztnQ0FBR0MsR0FBRzs0QkFBRTs0QkFDNUJFLE1BQU07Z0NBQUVILFNBQVM7Z0NBQUdDLEdBQUcsQ0FBQzs0QkFBRzs0QkFDM0IvRSxXQUFVO3NDQUVUcUQsZUFBZUYsTUFBTSxLQUFLLGtCQUN6Qiw4REFBQ0k7Z0NBQUl2RCxXQUFVOztrREFDYiw4REFBQ1IsMkpBQVFBO3dDQUFDUSxXQUFVOzs7Ozs7a0RBQ3BCLDhEQUFDeUQ7a0RBQUU7Ozs7Ozs7Ozs7O3VDQUdMSixlQUFlbUIsR0FBRyxDQUFDLENBQUNvQixzQkFDbEIsOERBQUMxRyxrREFBTUEsQ0FBQ3FFLEdBQUc7b0NBRVRzQixTQUFTO3dDQUFFQyxTQUFTO3dDQUFHSSxHQUFHLENBQUM7b0NBQUc7b0NBQzlCRixTQUFTO3dDQUFFRixTQUFTO3dDQUFHSSxHQUFHO29DQUFFO29DQUM1QmxGLFdBQVU7OENBRVYsNEVBQUN1RDt3Q0FBSXZELFdBQVU7OzBEQUNiLDhEQUFDdUQ7Z0RBQUl2RCxXQUFVOztrRUFDYiw4REFBQ3VEO3dEQUFJdkQsV0FBVTs7MEVBQ2IsOERBQUMwRDtnRUFBSzFELFdBQVcsOENBS2hCLE9BSkM0RixNQUFNakUsSUFBSSxLQUFLLGtCQUFrQixrQ0FDakNpRSxNQUFNakUsSUFBSSxLQUFLLFlBQVksZ0NBQzNCaUUsTUFBTWpFLElBQUksS0FBSyxhQUFhLDhCQUM1QjswRUFFQ2lFLE1BQU1qRSxJQUFJOzs7Ozs7NERBRVppRSxNQUFNQyxtQkFBbUIsa0JBQ3hCLDhEQUFDbkM7Z0VBQUsxRCxXQUFVOztvRUFDYjRGLE1BQU1DLG1CQUFtQjtvRUFBQzs7Ozs7Ozs7Ozs7OztrRUFJakMsOERBQUNUO3dEQUFHcEYsV0FBVTtrRUFDWDRGLE1BQU1wRSxLQUFLOzs7Ozs7a0VBRWQsOERBQUNpQzt3REFBRXpELFdBQVU7a0VBQXNCNEYsTUFBTUUsV0FBVzs7Ozs7O2tFQUNwRCw4REFBQ3ZDO3dEQUFJdkQsV0FBVTs7MEVBQ2IsOERBQUN1RDs7a0ZBQ0MsOERBQUNHO3dFQUFLMUQsV0FBVTtrRkFBYzs7Ozs7O29FQUFtQjtvRUFBRTRGLE1BQU1HLFVBQVU7Ozs7Ozs7MEVBRXJFLDhEQUFDeEM7O2tGQUNDLDhEQUFDRzt3RUFBSzFELFdBQVU7a0ZBQWM7Ozs7OztvRUFBWTtvRUFBRTRGLE1BQU1ILFFBQVE7Ozs7Ozs7MEVBRTVELDhEQUFDbEM7O2tGQUNDLDhEQUFDRzt3RUFBSzFELFdBQVU7a0ZBQWM7Ozs7OztvRUFBWTtvRUFBRUYsc0RBQVVBLENBQUM4RixNQUFNSSxVQUFVOzs7Ozs7OzBFQUV6RSw4REFBQ3pDOztrRkFDQyw4REFBQ0c7d0VBQUsxRCxXQUFVO2tGQUFjOzs7Ozs7b0VBQW9CO29FQUFFNEYsTUFBTUssb0JBQW9CO29FQUFDO29FQUFFTCxNQUFNTSxnQkFBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBSTdHLDhEQUFDM0M7Z0RBQUl2RCxXQUFVOztvREFDWjRGLE1BQU1PLGdCQUFnQixHQUFHLGtCQUN4Qiw4REFBQzVDO3dEQUFJdkQsV0FBVTs7NERBQ1o0RixNQUFNTyxnQkFBZ0IsQ0FBQ0MsY0FBYzs0REFBRzs7Ozs7OzZFQUczQyw4REFBQzdDO3dEQUFJdkQsV0FBVTtrRUFBbUM7Ozs7OztrRUFFcEQsOERBQUMyRDt3REFDQ2hDLE1BQUs7d0RBQ0wzQixXQUFVO2tFQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7bUNBcERBNEYsTUFBTXJFLEVBQUU7Ozs7OzJCQWRmOzs7Ozt3QkE2RVBaLGNBQWMsd0JBQ2IsOERBQUN6QixrREFBTUEsQ0FBQ3FFLEdBQUc7NEJBRVRzQixTQUFTO2dDQUFFQyxTQUFTO2dDQUFHQyxHQUFHOzRCQUFHOzRCQUM3QkMsU0FBUztnQ0FBRUYsU0FBUztnQ0FBR0MsR0FBRzs0QkFBRTs0QkFDNUJFLE1BQU07Z0NBQUVILFNBQVM7Z0NBQUdDLEdBQUcsQ0FBQzs0QkFBRzs0QkFDM0IvRSxXQUFVO3NDQUVUc0QsWUFBWUgsTUFBTSxLQUFLLGtCQUN0Qiw4REFBQ0k7Z0NBQUl2RCxXQUFVOztrREFDYiw4REFBQ1AsMkpBQVNBO3dDQUFDTyxXQUFVOzs7Ozs7a0RBQ3JCLDhEQUFDeUQ7a0RBQUU7Ozs7Ozs7Ozs7O3VDQUdMSCxZQUFZa0IsR0FBRyxDQUFDLENBQUM2QjtvQ0F5Q0ZBO3FEQXhDYiw4REFBQ25ILGtEQUFNQSxDQUFDcUUsR0FBRztvQ0FFVHNCLFNBQVM7d0NBQUVDLFNBQVM7d0NBQUdJLEdBQUcsQ0FBQztvQ0FBRztvQ0FDOUJGLFNBQVM7d0NBQUVGLFNBQVM7d0NBQUdJLEdBQUc7b0NBQUU7b0NBQzVCbEYsV0FBVyx5Q0FFVixPQURDcUcsS0FBS0MsV0FBVyxHQUFHLGlFQUFpRTs4Q0FHdEYsNEVBQUMvQzt3Q0FBSXZELFdBQVU7OzRDQUNacUcsS0FBS0UsU0FBUyxrQkFDYiw4REFBQ2hEO2dEQUFJdkQsV0FBVTs7Ozs7OzBEQUVqQiw4REFBQ3VEO2dEQUFJdkQsV0FBVTs7a0VBQ2IsOERBQUN1RDt3REFBSXZELFdBQVU7OzBFQUNiLDhEQUFDMEQ7Z0VBQUsxRCxXQUFXLDhDQU1oQixPQUxDcUcsS0FBS3JFLFFBQVEsS0FBSyxlQUFlLDRCQUNqQ3FFLEtBQUtyRSxRQUFRLEtBQUssZUFBZSw4QkFDakNxRSxLQUFLckUsUUFBUSxLQUFLLFlBQVksZ0NBQzlCcUUsS0FBS3JFLFFBQVEsS0FBSyxXQUFXLGtDQUM3QjswRUFFQ3FFLEtBQUtyRSxRQUFROzs7Ozs7NERBRWZxRSxLQUFLQyxXQUFXLGtCQUNmLDhEQUFDNUM7Z0VBQUsxRCxXQUFVOzBFQUF5RTs7Ozs7OzBFQUkzRiw4REFBQzBEO2dFQUFLMUQsV0FBVTswRUFDYkYsc0RBQVVBLENBQUN1RyxLQUFLRyxZQUFZOzs7Ozs7Ozs7Ozs7a0VBR2pDLDhEQUFDcEI7d0RBQUdwRixXQUFVO2tFQUNYcUcsS0FBSzdFLEtBQUs7Ozs7OztrRUFFYiw4REFBQ2lDO3dEQUFFekQsV0FBVTtrRUFBc0JxRyxLQUFLSSxPQUFPOzs7Ozs7a0VBQy9DLDhEQUFDbEQ7d0RBQUl2RCxXQUFVOzswRUFDYiw4REFBQ3VEO2dFQUFJdkQsV0FBVTs7a0ZBQ2IsOERBQUMwRDs7NEVBQUs7NEVBQUsyQyxLQUFLSyxNQUFNOzs7Ozs7O2tGQUN0Qiw4REFBQ25EO3dFQUFJdkQsV0FBVTtrRkFDWnFHLEVBQUFBLGFBQUFBLEtBQUtNLElBQUksY0FBVE4saUNBQUFBLFdBQVdPLEtBQUssQ0FBQyxHQUFHLEdBQUdwQyxHQUFHLENBQUMsQ0FBQ3FDLG9CQUMzQiw4REFBQ25EO2dGQUFlMUQsV0FBVTs7b0ZBQXdDO29GQUM5RDZHOzsrRUFET0E7Ozs7d0dBR1AsOERBQUNuRDs0RUFBSzFELFdBQVU7c0ZBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs0REFHakRxRyxLQUFLUyxVQUFVLGtCQUNkLDhEQUFDQztnRUFDQ0MsTUFBTVgsS0FBS1MsVUFBVTtnRUFDckIzQyxRQUFPO2dFQUNQOEMsS0FBSTtnRUFDSmpILFdBQVU7MEVBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzttQ0FwREpxRyxLQUFLOUUsRUFBRTs7Ozs7OzJCQWRkOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBa0ZsQjtHQTNjd0J4Qjs7UUFDTEYscURBQVFBOzs7S0FESEUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvaHViL0luZm9ybWF0aW9uSHViLnRzeD9iODhiIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBtb3Rpb24sIEFuaW1hdGVQcmVzZW5jZSB9IGZyb20gJ2ZyYW1lci1tb3Rpb24nXG5pbXBvcnQge1xuICBCZWxsLFxuICBUcmVuZGluZ1VwLFxuICBQYWNrYWdlLFxuICBBbGVydFRyaWFuZ2xlLFxuICBDYWxlbmRhcixcbiAgTmV3c3BhcGVyLFxuICBXaWZpLFxuICBXaWZpT2ZmLFxuICBDbG9jayxcbiAgRmlsdGVyLFxuICBTZWFyY2gsXG4gIFJlZnJlc2hDdyxcbiAgUGx1cyxcbiAgTWludXNcbn0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuaW1wb3J0IHsgdXNlU3RvcmUgfSBmcm9tICdAL3N0b3JlL3VzZVN0b3JlJ1xuaW1wb3J0IHsgQWxlcnRTZXJ2aWNlLCBBbGVydCB9IGZyb20gJ0AvbGliL3NlcnZpY2VzL2FsZXJ0U2VydmljZSdcbmltcG9ydCB7IGZvcm1hdERhdGUgfSBmcm9tICdAL2xpYi91dGlscydcbmltcG9ydCB7IHRvYXN0IH0gZnJvbSAnc29ubmVyJ1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2J1dHRvbidcbmltcG9ydCB7IElucHV0IH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2lucHV0J1xuaW1wb3J0IHsgQmFkZ2UgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYmFkZ2UnXG5cbmludGVyZmFjZSBJbmZvcm1hdGlvbkh1YlByb3BzIHtcbiAgY2xhc3NOYW1lPzogc3RyaW5nXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEluZm9ybWF0aW9uSHViKHsgY2xhc3NOYW1lID0gJycgfTogSW5mb3JtYXRpb25IdWJQcm9wcykge1xuICBjb25zdCB7IHVzZXIgfSA9IHVzZVN0b3JlKClcblxuICBjb25zdCBbYWxlcnRzLCBzZXRBbGVydHNdID0gdXNlU3RhdGU8QWxlcnRbXT4oW10pXG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpXG4gIGNvbnN0IFtyZWFsVGltZUNvbm5lY3RlZCwgc2V0UmVhbFRpbWVDb25uZWN0ZWRdID0gdXNlU3RhdGUodHJ1ZSlcbiAgY29uc3QgW2xhc3RVcGRhdGVUaW1lLCBzZXRMYXN0VXBkYXRlVGltZV0gPSB1c2VTdGF0ZTxEYXRlPihuZXcgRGF0ZSgpKVxuXG4gIGNvbnN0IFthY3RpdmVUYWIsIHNldEFjdGl2ZVRhYl0gPSB1c2VTdGF0ZTwnYWxlcnRzJyB8ICdzdG9ja3MnIHwgJ3RyYWluaW5nJyB8ICduZXdzJz4oJ2FsZXJ0cycpXG4gIGNvbnN0IFtzZWFyY2hRdWVyeSwgc2V0U2VhcmNoUXVlcnldID0gdXNlU3RhdGUoJycpXG4gIGNvbnN0IFtmaWx0ZXJUeXBlLCBzZXRGaWx0ZXJUeXBlXSA9IHVzZVN0YXRlPCdhbGwnIHwgJ2luZm8nIHwgJ3dhcm5pbmcnIHwgJ2NyaXRpY2FsJyB8ICdwcm9tbyc+KCdhbGwnKVxuICBjb25zdCBbZmlsdGVyQ2F0ZWdvcnksIHNldEZpbHRlckNhdGVnb3J5XSA9IHVzZVN0YXRlPHN0cmluZz4oJ2FsbCcpXG4gIGNvbnN0IFtpc0NsaWVudCwgc2V0SXNDbGllbnRdID0gdXNlU3RhdGUoZmFsc2UpXG5cbiAgLy8gSW5pdGlhbGlzYXRpb24gZGVzIGRvbm7DqWVzXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8gTWFycXVlciBxdWUgbm91cyBzb21tZXMgY8O0dMOpIGNsaWVudFxuICAgIHNldElzQ2xpZW50KHRydWUpXG5cbiAgICBjb25zdCBsb2FkSW5pdGlhbERhdGEgPSAoKSA9PiB7XG4gICAgICAvLyBEb25uw6llcyBzdGF0aXF1ZXMgZCdleGVtcGxlIHBvdXIgZMOpbW9uc3RyYXRpb25cbiAgICAgIHNldE1hcmtldEFsZXJ0cyhbXG4gICAgICAgIHtcbiAgICAgICAgICBpZDogJzEnLFxuICAgICAgICAgIHRpdGxlOiAnTm91dmVsbGUgbm9ybWUgTkYgQyAxNS0xMDAgLSBBbWVuZGVtZW50IEE2JyxcbiAgICAgICAgICBtZXNzYWdlOiAnTWlzZSDDoCBqb3VyIGltcG9ydGFudGUgZGVzIHLDqGdsZXMgZFxcJ2luc3RhbGxhdGlvbiDDqWxlY3RyaXF1ZScsXG4gICAgICAgICAgc2V2ZXJpdHk6ICdtZWRpdW0nLFxuICAgICAgICAgIHR5cGU6ICdyZWd1bGF0aW9uJyxcbiAgICAgICAgICBpc19hY3RpdmU6IHRydWUsXG4gICAgICAgICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMTVUMTA6MDA6MDBaJyxcbiAgICAgICAgICBzb3VyY2U6ICdWZWlsbGUgcsOpZ2xlbWVudGFpcmUnLFxuICAgICAgICAgIGFmZmVjdGVkX3JlZ2lvbnM6IFsnQ8O0dGUgZFxcJ0l2b2lyZScsICdTw6luw6lnYWwnLCAnTWFsaSddLFxuICAgICAgICAgIGNhdGVnb3J5OiAnUsOpZ2xlbWVudGF0aW9uJ1xuICAgICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICAgaWQ6ICcyJyxcbiAgICAgICAgICB0aXRsZTogJ0Zvcm1hdGlvbiBTY2huZWlkZXIgRWxlY3RyaWMgLSBOb3V2ZWF1eCBwcm9kdWl0cycsXG4gICAgICAgICAgbWVzc2FnZTogJ1Nlc3Npb24gZGUgZm9ybWF0aW9uIHN1ciBsYSBnYW1tZSBBY3RpOSBub3V2ZWxsZSBnw6luw6lyYXRpb24nLFxuICAgICAgICAgIHNldmVyaXR5OiAnbG93JyxcbiAgICAgICAgICB0eXBlOiAndHJhaW5pbmcnLFxuICAgICAgICAgIGlzX2FjdGl2ZTogdHJ1ZSxcbiAgICAgICAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0xNFQxNDozMDowMFonLFxuICAgICAgICAgIHNvdXJjZTogJ1BhcnRlbmFpcmUnLFxuICAgICAgICAgIGFmZmVjdGVkX3JlZ2lvbnM6IFsnQWJpZGphbicsICdEYWthciddLFxuICAgICAgICAgIGNhdGVnb3J5OiAnRm9ybWF0aW9uJ1xuICAgICAgICB9XG4gICAgICBdKVxuXG4gICAgICAvLyBQYXMgZGUgZG9ubsOpZXMgdGVtcHMgcsOpZWwgLSBpbnRlcmZhY2Ugc3RhdGlxdWVcbiAgICAgIHNldFN0b2NrVXBkYXRlcyhbXSlcbiAgICAgIHNldFRyYWluaW5nRXZlbnRzKFtdKVxuICAgICAgc2V0TmV3c1VwZGF0ZXMoW10pXG4gICAgfVxuXG4gICAgbG9hZEluaXRpYWxEYXRhKClcblxuICAgIC8vIFBhcyBkZSBjb25uZXhpb24gdGVtcHMgcsOpZWwgLSBkb25uw6llcyBzdGF0aXF1ZXNcbiAgICBzZXRSZWFsVGltZUNvbm5lY3RlZChmYWxzZSlcblxuICAgIC8vIFBhcyBkJ2Fib25uZW1lbnQgdGVtcHMgcsOpZWxcbiAgICAvLyBjb25zdCB1bnN1YnNjcmliZSA9IEh1YlNlcnZpY2Uuc3Vic2NyaWJlVG9SZWFsVGltZVVwZGF0ZXMoLi4uKVxuXG4gICAgLy8gcmV0dXJuICgpID0+IHtcbiAgICAvLyAgIHVuc3Vic2NyaWJlKClcbiAgICAvLyAgIHNldFJlYWxUaW1lQ29ubmVjdGVkKGZhbHNlKVxuICAgIC8vIH1cbiAgfSwgW10pXG5cbiAgLy8gRmlsdHJhZ2UgZGVzIGFsZXJ0ZXNcbiAgY29uc3QgZmlsdGVyZWRBbGVydHMgPSBtYXJrZXRBbGVydHMuZmlsdGVyKGFsZXJ0ID0+IHtcbiAgICBjb25zdCBtYXRjaGVzU2VhcmNoID0gc2VhcmNoUXVlcnkgPT09ICcnIHx8IFxuICAgICAgYWxlcnQudGl0bGUudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hRdWVyeS50b0xvd2VyQ2FzZSgpKSB8fFxuICAgICAgYWxlcnQubWVzc2FnZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFF1ZXJ5LnRvTG93ZXJDYXNlKCkpXG4gICAgXG4gICAgY29uc3QgbWF0Y2hlc1NldmVyaXR5ID0gZmlsdGVyU2V2ZXJpdHkgPT09ICdhbGwnIHx8IGFsZXJ0LnNldmVyaXR5ID09PSBmaWx0ZXJTZXZlcml0eVxuICAgIFxuICAgIHJldHVybiBtYXRjaGVzU2VhcmNoICYmIG1hdGNoZXNTZXZlcml0eSAmJiBhbGVydC5pc19hY3RpdmVcbiAgfSlcblxuICBjb25zdCBnZXRTZXZlcml0eUNvbG9yID0gKHNldmVyaXR5OiBzdHJpbmcpID0+IHtcbiAgICBzd2l0Y2ggKHNldmVyaXR5KSB7XG4gICAgICBjYXNlICdjcml0aWNhbCc6IHJldHVybiAnYmctcmVkLTEwMCB0ZXh0LXJlZC04MDAgYm9yZGVyLXJlZC0yMDAnXG4gICAgICBjYXNlICdoaWdoJzogcmV0dXJuICdiZy1vcmFuZ2UtMTAwIHRleHQtb3JhbmdlLTgwMCBib3JkZXItb3JhbmdlLTIwMCdcbiAgICAgIGNhc2UgJ21lZGl1bSc6IHJldHVybiAnYmcteWVsbG93LTEwMCB0ZXh0LXllbGxvdy04MDAgYm9yZGVyLXllbGxvdy0yMDAnXG4gICAgICBjYXNlICdsb3cnOiByZXR1cm4gJ2JnLWJsdWUtMTAwIHRleHQtYmx1ZS04MDAgYm9yZGVyLWJsdWUtMjAwJ1xuICAgICAgZGVmYXVsdDogcmV0dXJuICdiZy1ncmF5LTEwMCB0ZXh0LWdyYXktODAwIGJvcmRlci1ncmF5LTIwMCdcbiAgICB9XG4gIH1cblxuICBjb25zdCBnZXRVcGRhdGVUeXBlSWNvbiA9ICh0eXBlOiBzdHJpbmcpID0+IHtcbiAgICBzd2l0Y2ggKHR5cGUpIHtcbiAgICAgIGNhc2UgJ3Jlc3RvY2snOiByZXR1cm4gPFRyZW5kaW5nVXAgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LWdyZWVuLTYwMFwiIC8+XG4gICAgICBjYXNlICdkZWNyZWFzZSc6IHJldHVybiA8VHJlbmRpbmdVcCBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtb3JhbmdlLTYwMCByb3RhdGUtMTgwXCIgLz5cbiAgICAgIGNhc2UgJ291dF9vZl9zdG9jayc6IHJldHVybiA8QWxlcnRUcmlhbmdsZSBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtcmVkLTYwMFwiIC8+XG4gICAgICBkZWZhdWx0OiByZXR1cm4gPFBhY2thZ2UgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LWJsdWUtNjAwXCIgLz5cbiAgICB9XG4gIH1cblxuICBjb25zdCB0YWJzID0gW1xuICAgIHsgaWQ6ICdhbGVydHMnLCBsYWJlbDogJ0FsZXJ0ZXMgTWFyY2jDqScsIGljb246IEJlbGwsIGNvdW50OiBmaWx0ZXJlZEFsZXJ0cy5sZW5ndGggfSxcbiAgICB7IGlkOiAnc3RvY2tzJywgbGFiZWw6ICdTdG9ja3MnLCBpY29uOiBQYWNrYWdlLCBjb3VudDogc3RvY2tVcGRhdGVzLmxlbmd0aCB9LFxuICAgIHsgaWQ6ICd0cmFpbmluZycsIGxhYmVsOiAnRm9ybWF0aW9ucycsIGljb246IENhbGVuZGFyLCBjb3VudDogdHJhaW5pbmdFdmVudHMubGVuZ3RoIH0sXG4gICAgeyBpZDogJ25ld3MnLCBsYWJlbDogJ0FjdHVhbGl0w6lzJywgaWNvbjogTmV3c3BhcGVyLCBjb3VudDogbmV3c1VwZGF0ZXMubGVuZ3RoIH1cbiAgXVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2BiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1sZyAke2NsYXNzTmFtZX1gfT5cbiAgICAgIHsvKiBIZWFkZXIgYXZlYyBzdGF0dXQgdGVtcHMgcsOpZWwgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNiBib3JkZXItYiBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+SHViIGQnSW5mb3JtYXRpb24gUHJvIE1hdG9zPC9oMj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbXQtMVwiPkNlbnRyZSBkZSB2ZWlsbGUgdGVjaG5vbG9naXF1ZSBldCByw6lnbGVtZW50YWlyZTwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICBcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgey8qIFN0YXR1dCBwbGF0ZWZvcm1lICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTMgaC0zIHJvdW5kZWQtZnVsbCBiZy1ibHVlLTUwMFwiPjwvZGl2PlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtYmx1ZS02MDAgZm9udC1tZWRpdW1cIj5Nb2RlIETDqW1vbnN0cmF0aW9uPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIHsvKiBEZXJuacOocmUgbWlzZSDDoCBqb3VyICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgdGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgIDxDbG9jayBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgPHNwYW4+TWlzIMOgIGpvdXIge2lzQ2xpZW50ID8gZm9ybWF0RGF0ZShsYXN0VXBkYXRlVGltZSkgOiAnLS0nfTwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgXG4gICAgICAgICAgICB7LyogQm91dG9uIGFjdHVhbGlzZXIgKi99XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICBvbkNsaWNrPXt1cGRhdGVMYXN0VXBkYXRlVGltZX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0yIHJvdW5kZWQtbGcgaG92ZXI6YmctZ3JheS0xMDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICB0aXRsZT1cIkFjdHVhbGlzZXIgbGVzIGRvbm7DqWVzXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPFJlZnJlc2hDdyBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtZ3JheS02MDBcIiAvPlxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBCYXJyZSBkZSByZWNoZXJjaGUgZXQgZmlsdHJlcyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC00IGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHJlbGF0aXZlXCI+XG4gICAgICAgICAgICA8U2VhcmNoIGNsYXNzTmFtZT1cImFic29sdXRlIGxlZnQtMyB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIGgtNCB3LTQgdGV4dC1ncmF5LTQwMFwiIC8+XG4gICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlJlY2hlcmNoZXIgZGFucyBsZSBodWIuLi5cIlxuICAgICAgICAgICAgICB2YWx1ZT17c2VhcmNoUXVlcnl9XG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0U2VhcmNoUXVlcnkoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcGwtMTAgcHItNCBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1sZyBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1hbWJlci00MDAgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50XCJcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgXG4gICAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ2FsZXJ0cycgJiYgKFxuICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICB2YWx1ZT17ZmlsdGVyU2V2ZXJpdHl9XG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0RmlsdGVyU2V2ZXJpdHkoZS50YXJnZXQudmFsdWUgYXMgYW55KX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNCBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1sZyBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1hbWJlci00MDAgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50XCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImFsbFwiPlRvdXRlcyBsZXMgcHJpb3JpdMOpczwvb3B0aW9uPlxuICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiY3JpdGljYWxcIj5Dcml0aXF1ZTwvb3B0aW9uPlxuICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiaGlnaFwiPsOJbGV2w6llPC9vcHRpb24+XG4gICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJtZWRpdW1cIj5Nb3llbm5lPC9vcHRpb24+XG4gICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJsb3dcIj5GYWlibGU8L29wdGlvbj5cbiAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBPbmdsZXRzICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJib3JkZXItYiBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgPG5hdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtOCBweC02XCI+XG4gICAgICAgICAge3RhYnMubWFwKCh0YWIpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IEljb24gPSB0YWIuaWNvblxuICAgICAgICAgICAgY29uc3QgaXNBY3RpdmUgPSBhY3RpdmVUYWIgPT09IHRhYi5pZFxuICAgICAgICAgICAgXG4gICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAga2V5PXt0YWIuaWR9XG4gICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0QWN0aXZlVGFiKHRhYi5pZCBhcyBhbnkpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBweS00IHB4LTEgYm9yZGVyLWItMiBmb250LW1lZGl1bSB0ZXh0LXNtIHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICAgICAgICBpc0FjdGl2ZVxuICAgICAgICAgICAgICAgICAgICA/ICdib3JkZXItYW1iZXItNTAwIHRleHQtYW1iZXItNjAwJ1xuICAgICAgICAgICAgICAgICAgICA6ICdib3JkZXItdHJhbnNwYXJlbnQgdGV4dC1ncmF5LTUwMCBob3Zlcjp0ZXh0LWdyYXktNzAwIGhvdmVyOmJvcmRlci1ncmF5LTMwMCdcbiAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxJY29uIGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPlxuICAgICAgICAgICAgICAgIDxzcGFuPnt0YWIubGFiZWx9PC9zcGFuPlxuICAgICAgICAgICAgICAgIHt0YWIuY291bnQgPiAwICYmIChcbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YHB4LTIgcHktMSByb3VuZGVkLWZ1bGwgdGV4dC14cyBmb250LW1lZGl1bSAke1xuICAgICAgICAgICAgICAgICAgICBpc0FjdGl2ZSA/ICdiZy1hbWJlci0xMDAgdGV4dC1hbWJlci04MDAnIDogJ2JnLWdyYXktMTAwIHRleHQtZ3JheS02MDAnXG4gICAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICAgIHt0YWIuY291bnR9XG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICApXG4gICAgICAgICAgfSl9XG4gICAgICAgIDwvbmF2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBDb250ZW51IGRlcyBvbmdsZXRzICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgICAgPEFuaW1hdGVQcmVzZW5jZSBtb2RlPVwid2FpdFwiPlxuICAgICAgICAgIHthY3RpdmVUYWIgPT09ICdhbGVydHMnICYmIChcbiAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgIGtleT1cImFsZXJ0c1wiXG4gICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMjAgfX1cbiAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgICAgIGV4aXQ9e3sgb3BhY2l0eTogMCwgeTogLTIwIH19XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInNwYWNlLXktNFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHtmaWx0ZXJlZEFsZXJ0cy5sZW5ndGggPT09IDAgPyAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS04IHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICAgIDxCZWxsIGNsYXNzTmFtZT1cImgtMTIgdy0xMiBteC1hdXRvIG1iLTQgdGV4dC1ncmF5LTMwMFwiIC8+XG4gICAgICAgICAgICAgICAgICA8cD5BdWN1bmUgYWxlcnRlIGNvcnJlc3BvbmRhbnQgYXV4IGNyaXTDqHJlczwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICBmaWx0ZXJlZEFsZXJ0cy5tYXAoKGFsZXJ0KSA9PiAoXG4gICAgICAgICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICAgICAgICBrZXk9e2FsZXJ0LmlkfVxuICAgICAgICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHg6IC0yMCB9fVxuICAgICAgICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHg6IDAgfX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcC00IHJvdW5kZWQtbGcgYm9yZGVyICR7Z2V0U2V2ZXJpdHlDb2xvcihhbGVydC5zZXZlcml0eSl9YH1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgZm9udC1tZWRpdW0gdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7YWxlcnQudHlwZS5yZXBsYWNlKCdfJywgJyAnKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0RGF0ZShhbGVydC5jcmVhdGVkX2F0KX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCBtYi0xXCI+e2FsZXJ0LnRpdGxlfTwvaDM+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIG1iLTJcIj57YWxlcnQubWVzc2FnZX08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNCB0ZXh0LXhzIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+Q2F0w6lnb3JpZToge2FsZXJ0LmNhdGVnb3J5IHx8IGFsZXJ0LnR5cGV9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5Sw6lnaW9uczoge2FsZXJ0LmFmZmVjdGVkX3JlZ2lvbnM/LmpvaW4oJywgJykgfHwgJ05vbiBzcMOpY2lmacOpJ308L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICAgICAgICAgKSlcbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICApfVxuXG4gICAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ3N0b2NrcycgJiYgKFxuICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAga2V5PVwic3RvY2tzXCJcbiAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxuICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgICAgZXhpdD17eyBvcGFjaXR5OiAwLCB5OiAtMjAgfX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwic3BhY2UteS00XCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge3N0b2NrVXBkYXRlcy5sZW5ndGggPT09IDAgPyAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS04IHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICAgIDxQYWNrYWdlIGNsYXNzTmFtZT1cImgtMTIgdy0xMiBteC1hdXRvIG1iLTQgdGV4dC1ncmF5LTMwMFwiIC8+XG4gICAgICAgICAgICAgICAgICA8cD5BdWN1bmUgbWlzZSDDoCBqb3VyIGRlIHN0b2NrIHLDqWNlbnRlPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIHN0b2NrVXBkYXRlcy5tYXAoKHVwZGF0ZSkgPT4gKFxuICAgICAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgICAgICAga2V5PXt1cGRhdGUuaWR9XG4gICAgICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeDogLTIwIH19XG4gICAgICAgICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeDogMCB9fVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTQgcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLWdyYXktMjAwIGJnLWdyYXktNTBcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7Z2V0VXBkYXRlVHlwZUljb24odXBkYXRlLnVwZGF0ZV90eXBlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBQcm9kdWl0ICN7dXBkYXRlLnByb2R1Y3RfaWR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPnt1cGRhdGUubG9jYXRpb259PC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge3VwZGF0ZS5wcmV2aW91c19xdWFudGl0eX0g4oaSIHt1cGRhdGUuY3VycmVudF9xdWFudGl0eX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2Zvcm1hdERhdGUodXBkYXRlLmNyZWF0ZWRfYXQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICAgICAgICkpXG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICAgKX1cblxuICAgICAgICAgIHthY3RpdmVUYWIgPT09ICd0cmFpbmluZycgJiYgKFxuICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAga2V5PVwidHJhaW5pbmdcIlxuICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDIwIH19XG4gICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgICAgICBleGl0PXt7IG9wYWNpdHk6IDAsIHk6IC0yMCB9fVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJzcGFjZS15LTRcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7dHJhaW5pbmdFdmVudHMubGVuZ3RoID09PSAwID8gKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktOCB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICA8Q2FsZW5kYXIgY2xhc3NOYW1lPVwiaC0xMiB3LTEyIG14LWF1dG8gbWItNCB0ZXh0LWdyYXktMzAwXCIgLz5cbiAgICAgICAgICAgICAgICAgIDxwPkF1Y3VuIMOpdsOpbmVtZW50IGRlIGZvcm1hdGlvbiBwcm9ncmFtbcOpPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIHRyYWluaW5nRXZlbnRzLm1hcCgoZXZlbnQpID0+IChcbiAgICAgICAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgICAgICAgIGtleT17ZXZlbnQuaWR9XG4gICAgICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeDogLTIwIH19XG4gICAgICAgICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeDogMCB9fVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTYgcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLWdyYXktMjAwIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTUwIHRvLWluZGlnby01MFwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2BweC0yIHB5LTEgcm91bmRlZC1mdWxsIHRleHQteHMgZm9udC1tZWRpdW0gJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBldmVudC50eXBlID09PSAnY2VydGlmaWNhdGlvbicgPyAnYmctcHVycGxlLTEwMCB0ZXh0LXB1cnBsZS04MDAnIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBldmVudC50eXBlID09PSAnd2ViaW5hcicgPyAnYmctZ3JlZW4tMTAwIHRleHQtZ3JlZW4tODAwJyA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZXZlbnQudHlwZSA9PT0gJ3dvcmtzaG9wJyA/ICdiZy1ibHVlLTEwMCB0ZXh0LWJsdWUtODAwJyA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgJ2JnLWdyYXktMTAwIHRleHQtZ3JheS04MDAnXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7ZXZlbnQudHlwZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7ZXZlbnQubWVtYmVyc2hpcF9yZXF1aXJlZCAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwicHgtMiBweS0xIHJvdW5kZWQtZnVsbCB0ZXh0LXhzIGZvbnQtbWVkaXVtIGJnLWFtYmVyLTEwMCB0ZXh0LWFtYmVyLTgwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2V2ZW50Lm1lbWJlcnNoaXBfcmVxdWlyZWR9K1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2V2ZW50LnRpdGxlfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbWItM1wiPntldmVudC5kZXNjcmlwdGlvbn08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTIgZ2FwLTQgdGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj5JbnN0cnVjdGV1cjo8L3NwYW4+IHtldmVudC5pbnN0cnVjdG9yfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPkxpZXU6PC9zcGFuPiB7ZXZlbnQubG9jYXRpb259XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+RGF0ZTo8L3NwYW4+IHtmb3JtYXREYXRlKGV2ZW50LnN0YXJ0X2RhdGUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPlBhcnRpY2lwYW50czo8L3NwYW4+IHtldmVudC5jdXJyZW50X3BhcnRpY2lwYW50c30ve2V2ZW50Lm1heF9wYXJ0aWNpcGFudHN9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0IG1sLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtldmVudC5yZWdpc3RyYXRpb25fZmVlID4gMCA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2V2ZW50LnJlZ2lzdHJhdGlvbl9mZWUudG9Mb2NhbGVTdHJpbmcoKX0gRkNGQVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LWJvbGQgdGV4dC1ncmVlbi02MDBcIj5HcmF0dWl0PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibXQtMiBweC00IHB5LTIgYmctYW1iZXItNTAwIHRleHQtd2hpdGUgcm91bmRlZC1sZyBob3ZlcjpiZy1hbWJlci02MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICBTJ2luc2NyaXJlXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICAgICAgICAgKSlcbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICApfVxuXG4gICAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ25ld3MnICYmIChcbiAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgIGtleT1cIm5ld3NcIlxuICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDIwIH19XG4gICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgICAgICBleGl0PXt7IG9wYWNpdHk6IDAsIHk6IC0yMCB9fVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJzcGFjZS15LTZcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7bmV3c1VwZGF0ZXMubGVuZ3RoID09PSAwID8gKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktOCB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICA8TmV3c3BhcGVyIGNsYXNzTmFtZT1cImgtMTIgdy0xMiBteC1hdXRvIG1iLTQgdGV4dC1ncmF5LTMwMFwiIC8+XG4gICAgICAgICAgICAgICAgICA8cD5BdWN1bmUgYWN0dWFsaXTDqSByw6ljZW50ZTwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICBuZXdzVXBkYXRlcy5tYXAoKG5ld3MpID0+IChcbiAgICAgICAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgICAgICAgIGtleT17bmV3cy5pZH1cbiAgICAgICAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB4OiAtMjAgfX1cbiAgICAgICAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB4OiAwIH19XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHAtNiByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItZ3JheS0yMDAgJHtcbiAgICAgICAgICAgICAgICAgICAgICBuZXdzLmlzX2ZlYXR1cmVkID8gJ2JnLWdyYWRpZW50LXRvLXIgZnJvbS1hbWJlci01MCB0by15ZWxsb3ctNTAgYm9yZGVyLWFtYmVyLTIwMCcgOiAnYmctd2hpdGUnXG4gICAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQgc3BhY2UteC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAge25ld3MuaW1hZ2VfdXJsICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yNCBoLTI0IGJnLWdyYXktMjAwIHJvdW5kZWQtbGcgZmxleC1zaHJpbmstMFwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgcHgtMiBweS0xIHJvdW5kZWQtZnVsbCB0ZXh0LXhzIGZvbnQtbWVkaXVtICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbmV3cy5jYXRlZ29yeSA9PT0gJ3JlZ3VsYXRpb24nID8gJ2JnLXJlZC0xMDAgdGV4dC1yZWQtODAwJyA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbmV3cy5jYXRlZ29yeSA9PT0gJ3RlY2hub2xvZ3knID8gJ2JnLWJsdWUtMTAwIHRleHQtYmx1ZS04MDAnIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBuZXdzLmNhdGVnb3J5ID09PSAnY29tcGFueScgPyAnYmctZ3JlZW4tMTAwIHRleHQtZ3JlZW4tODAwJyA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbmV3cy5jYXRlZ29yeSA9PT0gJ21hcmtldCcgPyAnYmctcHVycGxlLTEwMCB0ZXh0LXB1cnBsZS04MDAnIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAnYmctZ3JheS0xMDAgdGV4dC1ncmF5LTgwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtuZXdzLmNhdGVnb3J5fVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtuZXdzLmlzX2ZlYXR1cmVkICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJweC0yIHB5LTEgcm91bmRlZC1mdWxsIHRleHQteHMgZm9udC1tZWRpdW0gYmctYW1iZXItMTAwIHRleHQtYW1iZXItODAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICDDgCBsYSB1bmVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXREYXRlKG5ld3MucHVibGlzaGVkX2F0KX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7bmV3cy50aXRsZX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIG1iLTNcIj57bmV3cy5zdW1tYXJ5fTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00IHRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPlBhciB7bmV3cy5hdXRob3J9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtuZXdzLnRhZ3M/LnNsaWNlKDAsIDMpLm1hcCgodGFnKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGtleT17dGFnfSBjbGFzc05hbWU9XCJweC0yIHB5LTEgYmctZ3JheS0xMDAgcm91bmRlZCB0ZXh0LXhzXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgI3t0YWd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkpIHx8IDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTQwMFwiPkF1Y3VuIHRhZzwvc3Bhbj59XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7bmV3cy5zb3VyY2VfdXJsICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaHJlZj17bmV3cy5zb3VyY2VfdXJsfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGFyZ2V0PVwiX2JsYW5rXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlbD1cIm5vb3BlbmVyIG5vcmVmZXJyZXJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1hbWJlci02MDAgaG92ZXI6dGV4dC1hbWJlci03MDAgdGV4dC1zbSBmb250LW1lZGl1bVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgTGlyZSBsYSBzdWl0ZSDihpJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICAgICAgICAgKSlcbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICApfVxuICAgICAgICA8L0FuaW1hdGVQcmVzZW5jZT5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJtb3Rpb24iLCJBbmltYXRlUHJlc2VuY2UiLCJCZWxsIiwiVHJlbmRpbmdVcCIsIlBhY2thZ2UiLCJBbGVydFRyaWFuZ2xlIiwiQ2FsZW5kYXIiLCJOZXdzcGFwZXIiLCJDbG9jayIsIlNlYXJjaCIsIlJlZnJlc2hDdyIsInVzZVN0b3JlIiwiZm9ybWF0RGF0ZSIsIkluZm9ybWF0aW9uSHViIiwiY2xhc3NOYW1lIiwidXNlciIsImFsZXJ0cyIsInNldEFsZXJ0cyIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwicmVhbFRpbWVDb25uZWN0ZWQiLCJzZXRSZWFsVGltZUNvbm5lY3RlZCIsImxhc3RVcGRhdGVUaW1lIiwic2V0TGFzdFVwZGF0ZVRpbWUiLCJEYXRlIiwiYWN0aXZlVGFiIiwic2V0QWN0aXZlVGFiIiwic2VhcmNoUXVlcnkiLCJzZXRTZWFyY2hRdWVyeSIsImZpbHRlclR5cGUiLCJzZXRGaWx0ZXJUeXBlIiwiZmlsdGVyQ2F0ZWdvcnkiLCJzZXRGaWx0ZXJDYXRlZ29yeSIsImlzQ2xpZW50Iiwic2V0SXNDbGllbnQiLCJsb2FkSW5pdGlhbERhdGEiLCJzZXRNYXJrZXRBbGVydHMiLCJpZCIsInRpdGxlIiwibWVzc2FnZSIsInNldmVyaXR5IiwidHlwZSIsImlzX2FjdGl2ZSIsImNyZWF0ZWRfYXQiLCJzb3VyY2UiLCJhZmZlY3RlZF9yZWdpb25zIiwiY2F0ZWdvcnkiLCJzZXRTdG9ja1VwZGF0ZXMiLCJzZXRUcmFpbmluZ0V2ZW50cyIsInNldE5ld3NVcGRhdGVzIiwiZmlsdGVyZWRBbGVydHMiLCJtYXJrZXRBbGVydHMiLCJmaWx0ZXIiLCJhbGVydCIsIm1hdGNoZXNTZWFyY2giLCJ0b0xvd2VyQ2FzZSIsImluY2x1ZGVzIiwibWF0Y2hlc1NldmVyaXR5IiwiZmlsdGVyU2V2ZXJpdHkiLCJnZXRTZXZlcml0eUNvbG9yIiwiZ2V0VXBkYXRlVHlwZUljb24iLCJ0YWJzIiwibGFiZWwiLCJpY29uIiwiY291bnQiLCJsZW5ndGgiLCJzdG9ja1VwZGF0ZXMiLCJ0cmFpbmluZ0V2ZW50cyIsIm5ld3NVcGRhdGVzIiwiZGl2IiwiaDIiLCJwIiwic3BhbiIsImJ1dHRvbiIsIm9uQ2xpY2siLCJ1cGRhdGVMYXN0VXBkYXRlVGltZSIsImlucHV0IiwicGxhY2Vob2xkZXIiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwiZSIsInRhcmdldCIsInNlbGVjdCIsInNldEZpbHRlclNldmVyaXR5Iiwib3B0aW9uIiwibmF2IiwibWFwIiwidGFiIiwiSWNvbiIsImlzQWN0aXZlIiwibW9kZSIsImluaXRpYWwiLCJvcGFjaXR5IiwieSIsImFuaW1hdGUiLCJleGl0IiwieCIsInJlcGxhY2UiLCJoMyIsImpvaW4iLCJ1cGRhdGUiLCJ1cGRhdGVfdHlwZSIsInByb2R1Y3RfaWQiLCJsb2NhdGlvbiIsInByZXZpb3VzX3F1YW50aXR5IiwiY3VycmVudF9xdWFudGl0eSIsImV2ZW50IiwibWVtYmVyc2hpcF9yZXF1aXJlZCIsImRlc2NyaXB0aW9uIiwiaW5zdHJ1Y3RvciIsInN0YXJ0X2RhdGUiLCJjdXJyZW50X3BhcnRpY2lwYW50cyIsIm1heF9wYXJ0aWNpcGFudHMiLCJyZWdpc3RyYXRpb25fZmVlIiwidG9Mb2NhbGVTdHJpbmciLCJuZXdzIiwiaXNfZmVhdHVyZWQiLCJpbWFnZV91cmwiLCJwdWJsaXNoZWRfYXQiLCJzdW1tYXJ5IiwiYXV0aG9yIiwidGFncyIsInNsaWNlIiwidGFnIiwic291cmNlX3VybCIsImEiLCJocmVmIiwicmVsIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/hub/InformationHub.tsx\n"));

/***/ })

});