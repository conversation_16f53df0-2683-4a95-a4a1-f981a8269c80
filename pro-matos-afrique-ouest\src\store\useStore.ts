import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import {
  User,
  Product,
  Alert,
  MarketAlert,
  StockUpdate,
  TrainingEvent,
  NewsUpdate,
  TechnicalDocument,
  CompatibilityCheck,
  ExpertConsultation,
  ExpertProfile,
  TechnicalResource,
  TechnicalReport
} from '@/lib/supabase'

interface AppState {
  // User state
  user: User | null
  isAuthenticated: boolean
  
  // Products state
  products: Product[]
  filteredProducts: Product[]
  searchQuery: string
  selectedCategory: string
  
  // Alerts state
  alerts: Alert[]
  unreadAlertsCount: number

  // Hub d'Information state
  marketAlerts: MarketAlert[]
  stockUpdates: StockUpdate[]
  trainingEvents: TrainingEvent[]
  newsUpdates: NewsUpdate[]
  realTimeConnected: boolean
  lastUpdateTime: string

  // Espace Conseil Technique state
  technicalDocuments: TechnicalDocument[]
  compatibilityChecks: CompatibilityCheck[]
  expertConsultations: ExpertConsultation[]
  expertProfiles: ExpertProfile[]
  technicalResources: TechnicalResource[]
  technicalReports: TechnicalReport[]
  selectedExpert: ExpertProfile | null
  activeConsultation: ExpertConsultation | null

  // UI state
  isLoading: boolean
  isDarkMode: boolean
  sidebarOpen: boolean
  
  // Actions
  setUser: (user: User | null) => void
  setProducts: (products: Product[]) => void
  setSearchQuery: (query: string) => void
  setSelectedCategory: (category: string) => void
  setAlerts: (alerts: Alert[]) => void
  markAlertAsRead: (alertId: string) => void

  // Hub d'Information actions
  setMarketAlerts: (alerts: MarketAlert[]) => void
  addMarketAlert: (alert: MarketAlert) => void
  setStockUpdates: (updates: StockUpdate[]) => void
  addStockUpdate: (update: StockUpdate) => void
  setTrainingEvents: (events: TrainingEvent[]) => void
  setNewsUpdates: (news: NewsUpdate[]) => void
  addNewsUpdate: (news: NewsUpdate) => void
  setRealTimeConnected: (connected: boolean) => void
  updateLastUpdateTime: () => void

  // Espace Conseil Technique actions
  setTechnicalDocuments: (documents: TechnicalDocument[]) => void
  addTechnicalDocument: (document: TechnicalDocument) => void
  updateTechnicalDocument: (id: string, updates: Partial<TechnicalDocument>) => void
  setCompatibilityChecks: (checks: CompatibilityCheck[]) => void
  addCompatibilityCheck: (check: CompatibilityCheck) => void
  setExpertConsultations: (consultations: ExpertConsultation[]) => void
  addExpertConsultation: (consultation: ExpertConsultation) => void
  updateExpertConsultation: (id: string, updates: Partial<ExpertConsultation>) => void
  setExpertProfiles: (profiles: ExpertProfile[]) => void
  setTechnicalResources: (resources: TechnicalResource[]) => void
  setTechnicalReports: (reports: TechnicalReport[]) => void
  addTechnicalReport: (report: TechnicalReport) => void
  setSelectedExpert: (expert: ExpertProfile | null) => void
  setActiveConsultation: (consultation: ExpertConsultation | null) => void

  setLoading: (loading: boolean) => void
  toggleDarkMode: () => void
  toggleSidebar: () => void
  filterProducts: () => void
}

export const useStore = create<AppState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      isAuthenticated: false,
      products: [],
      filteredProducts: [],
      searchQuery: '',
      selectedCategory: '',
      alerts: [],
      unreadAlertsCount: 0,

      // Hub d'Information initial state
      marketAlerts: [],
      stockUpdates: [],
      trainingEvents: [],
      newsUpdates: [],
      realTimeConnected: false,
      lastUpdateTime: new Date().toISOString(),

      // Espace Conseil Technique initial state
      technicalDocuments: [],
      compatibilityChecks: [],
      expertConsultations: [],
      expertProfiles: [],
      technicalResources: [],
      technicalReports: [],
      selectedExpert: null,
      activeConsultation: null,

      isLoading: false,
      isDarkMode: false,
      sidebarOpen: false,

      // Actions
      setUser: (user) => set({ 
        user, 
        isAuthenticated: !!user 
      }),

      setProducts: (products) => {
        set({ products })
        get().filterProducts()
      },

      setSearchQuery: (searchQuery) => {
        set({ searchQuery })
        get().filterProducts()
      },

      setSelectedCategory: (selectedCategory) => {
        set({ selectedCategory })
        get().filterProducts()
      },

      setAlerts: (alerts) => {
        const unreadAlertsCount = alerts.filter(alert => !alert.is_read).length
        set({ alerts, unreadAlertsCount })
      },

      markAlertAsRead: (alertId) => {
        const alerts = get().alerts.map(alert =>
          alert.id === alertId ? { ...alert, is_read: true } : alert
        )
        const unreadAlertsCount = alerts.filter(alert => !alert.is_read).length
        set({ alerts, unreadAlertsCount })
      },

      // Hub d'Information actions
      setMarketAlerts: (marketAlerts) => {
        set({ marketAlerts })
        get().updateLastUpdateTime()
      },

      addMarketAlert: (alert) => {
        const marketAlerts = [alert, ...get().marketAlerts].slice(0, 50) // Garder les 50 plus récentes
        set({ marketAlerts })
        get().updateLastUpdateTime()
      },

      setStockUpdates: (stockUpdates) => {
        set({ stockUpdates })
        get().updateLastUpdateTime()
      },

      addStockUpdate: (update) => {
        const stockUpdates = [update, ...get().stockUpdates].slice(0, 100) // Garder les 100 plus récentes
        set({ stockUpdates })
        get().updateLastUpdateTime()
      },

      setTrainingEvents: (trainingEvents) => set({ trainingEvents }),

      setNewsUpdates: (newsUpdates) => set({ newsUpdates }),

      addNewsUpdate: (news) => {
        const newsUpdates = [news, ...get().newsUpdates].slice(0, 20) // Garder les 20 plus récentes
        set({ newsUpdates })
        get().updateLastUpdateTime()
      },

      setRealTimeConnected: (realTimeConnected) => set({ realTimeConnected }),

      updateLastUpdateTime: () => set({ lastUpdateTime: new Date().toISOString() }),

      // Espace Conseil Technique actions
      setTechnicalDocuments: (technicalDocuments) => set({ technicalDocuments }),

      addTechnicalDocument: (document) => {
        const technicalDocuments = [document, ...get().technicalDocuments]
        set({ technicalDocuments })
      },

      updateTechnicalDocument: (id, updates) => {
        const technicalDocuments = get().technicalDocuments.map(doc =>
          doc.id === id ? { ...doc, ...updates } : doc
        )
        set({ technicalDocuments })
      },

      setCompatibilityChecks: (compatibilityChecks) => set({ compatibilityChecks }),

      addCompatibilityCheck: (check) => {
        const compatibilityChecks = [check, ...get().compatibilityChecks]
        set({ compatibilityChecks })
      },

      setExpertConsultations: (expertConsultations) => set({ expertConsultations }),

      addExpertConsultation: (consultation) => {
        const expertConsultations = [consultation, ...get().expertConsultations]
        set({ expertConsultations })
      },

      updateExpertConsultation: (id, updates) => {
        const expertConsultations = get().expertConsultations.map(consult =>
          consult.id === id ? { ...consult, ...updates } : consult
        )
        set({ expertConsultations })
      },

      setExpertProfiles: (expertProfiles) => set({ expertProfiles }),

      setTechnicalResources: (technicalResources) => set({ technicalResources }),

      setTechnicalReports: (technicalReports) => set({ technicalReports }),

      addTechnicalReport: (report) => {
        const technicalReports = [report, ...get().technicalReports]
        set({ technicalReports })
      },

      setSelectedExpert: (selectedExpert) => set({ selectedExpert }),

      setActiveConsultation: (activeConsultation) => set({ activeConsultation }),

      setLoading: (isLoading) => set({ isLoading }),

      toggleDarkMode: () => set((state) => ({ 
        isDarkMode: !state.isDarkMode 
      })),

      toggleSidebar: () => set((state) => ({ 
        sidebarOpen: !state.sidebarOpen 
      })),

      filterProducts: () => {
        const { products, searchQuery, selectedCategory } = get()
        
        let filtered = products

        if (selectedCategory) {
          filtered = filtered.filter(product => 
            product.category === selectedCategory
          )
        }

        if (searchQuery) {
          const query = searchQuery.toLowerCase()
          filtered = filtered.filter(product =>
            product.name.toLowerCase().includes(query) ||
            product.description.toLowerCase().includes(query) ||
            product.brand.toLowerCase().includes(query) ||
            product.model.toLowerCase().includes(query)
          )
        }

        set({ filteredProducts: filtered })
      },
    }),
    {
      name: 'pro-matos-storage',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
        isDarkMode: state.isDarkMode,
      }),
    }
  )
)
