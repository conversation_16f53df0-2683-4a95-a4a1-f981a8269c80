import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { User, Product, Alert, MarketAlert, StockUpdate, TrainingEvent, NewsUpdate } from '@/lib/supabase'

interface AppState {
  // User state
  user: User | null
  isAuthenticated: boolean
  
  // Products state
  products: Product[]
  filteredProducts: Product[]
  searchQuery: string
  selectedCategory: string
  
  // Alerts state
  alerts: Alert[]
  unreadAlertsCount: number

  // Hub d'Information state
  marketAlerts: MarketAlert[]
  stockUpdates: StockUpdate[]
  trainingEvents: TrainingEvent[]
  newsUpdates: NewsUpdate[]
  realTimeConnected: boolean
  lastUpdateTime: string

  // UI state
  isLoading: boolean
  isDarkMode: boolean
  sidebarOpen: boolean
  
  // Actions
  setUser: (user: User | null) => void
  setProducts: (products: Product[]) => void
  setSearchQuery: (query: string) => void
  setSelectedCategory: (category: string) => void
  setAlerts: (alerts: <PERSON><PERSON>[]) => void
  markAlertAsRead: (alertId: string) => void

  // Hub d'Information actions
  setMarketAlerts: (alerts: MarketAlert[]) => void
  addMarketAlert: (alert: MarketAlert) => void
  setStockUpdates: (updates: StockUpdate[]) => void
  addStockUpdate: (update: StockUpdate) => void
  setTrainingEvents: (events: TrainingEvent[]) => void
  setNewsUpdates: (news: NewsUpdate[]) => void
  addNewsUpdate: (news: NewsUpdate) => void
  setRealTimeConnected: (connected: boolean) => void
  updateLastUpdateTime: () => void

  setLoading: (loading: boolean) => void
  toggleDarkMode: () => void
  toggleSidebar: () => void
  filterProducts: () => void
}

export const useStore = create<AppState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      isAuthenticated: false,
      products: [],
      filteredProducts: [],
      searchQuery: '',
      selectedCategory: '',
      alerts: [],
      unreadAlertsCount: 0,

      // Hub d'Information initial state
      marketAlerts: [],
      stockUpdates: [],
      trainingEvents: [],
      newsUpdates: [],
      realTimeConnected: false,
      lastUpdateTime: new Date().toISOString(),

      isLoading: false,
      isDarkMode: false,
      sidebarOpen: false,

      // Actions
      setUser: (user) => set({ 
        user, 
        isAuthenticated: !!user 
      }),

      setProducts: (products) => {
        set({ products })
        get().filterProducts()
      },

      setSearchQuery: (searchQuery) => {
        set({ searchQuery })
        get().filterProducts()
      },

      setSelectedCategory: (selectedCategory) => {
        set({ selectedCategory })
        get().filterProducts()
      },

      setAlerts: (alerts) => {
        const unreadAlertsCount = alerts.filter(alert => !alert.is_read).length
        set({ alerts, unreadAlertsCount })
      },

      markAlertAsRead: (alertId) => {
        const alerts = get().alerts.map(alert =>
          alert.id === alertId ? { ...alert, is_read: true } : alert
        )
        const unreadAlertsCount = alerts.filter(alert => !alert.is_read).length
        set({ alerts, unreadAlertsCount })
      },

      // Hub d'Information actions
      setMarketAlerts: (marketAlerts) => {
        set({ marketAlerts })
        get().updateLastUpdateTime()
      },

      addMarketAlert: (alert) => {
        const marketAlerts = [alert, ...get().marketAlerts].slice(0, 50) // Garder les 50 plus récentes
        set({ marketAlerts })
        get().updateLastUpdateTime()
      },

      setStockUpdates: (stockUpdates) => {
        set({ stockUpdates })
        get().updateLastUpdateTime()
      },

      addStockUpdate: (update) => {
        const stockUpdates = [update, ...get().stockUpdates].slice(0, 100) // Garder les 100 plus récentes
        set({ stockUpdates })
        get().updateLastUpdateTime()
      },

      setTrainingEvents: (trainingEvents) => set({ trainingEvents }),

      setNewsUpdates: (newsUpdates) => set({ newsUpdates }),

      addNewsUpdate: (news) => {
        const newsUpdates = [news, ...get().newsUpdates].slice(0, 20) // Garder les 20 plus récentes
        set({ newsUpdates })
        get().updateLastUpdateTime()
      },

      setRealTimeConnected: (realTimeConnected) => set({ realTimeConnected }),

      updateLastUpdateTime: () => set({ lastUpdateTime: new Date().toISOString() }),

      setLoading: (isLoading) => set({ isLoading }),

      toggleDarkMode: () => set((state) => ({ 
        isDarkMode: !state.isDarkMode 
      })),

      toggleSidebar: () => set((state) => ({ 
        sidebarOpen: !state.sidebarOpen 
      })),

      filterProducts: () => {
        const { products, searchQuery, selectedCategory } = get()
        
        let filtered = products

        if (selectedCategory) {
          filtered = filtered.filter(product => 
            product.category === selectedCategory
          )
        }

        if (searchQuery) {
          const query = searchQuery.toLowerCase()
          filtered = filtered.filter(product =>
            product.name.toLowerCase().includes(query) ||
            product.description.toLowerCase().includes(query) ||
            product.brand.toLowerCase().includes(query) ||
            product.model.toLowerCase().includes(query)
          )
        }

        set({ filteredProducts: filtered })
      },
    }),
    {
      name: 'pro-matos-storage',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
        isDarkMode: state.isDarkMode,
      }),
    }
  )
)
