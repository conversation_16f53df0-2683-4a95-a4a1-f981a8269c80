import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { User, Product, Alert } from '@/lib/supabase'

interface AppState {
  // User state
  user: User | null
  isAuthenticated: boolean
  
  // Products state
  products: Product[]
  filteredProducts: Product[]
  searchQuery: string
  selectedCategory: string
  
  // Alerts state
  alerts: Alert[]
  unreadAlertsCount: number
  
  // UI state
  isLoading: boolean
  isDarkMode: boolean
  sidebarOpen: boolean
  
  // Actions
  setUser: (user: User | null) => void
  setProducts: (products: Product[]) => void
  setSearchQuery: (query: string) => void
  setSelectedCategory: (category: string) => void
  setAlerts: (alerts: Alert[]) => void
  markAlertAsRead: (alertId: string) => void
  setLoading: (loading: boolean) => void
  toggleDarkMode: () => void
  toggleSidebar: () => void
  filterProducts: () => void
}

export const useStore = create<AppState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      isAuthenticated: false,
      products: [],
      filteredProducts: [],
      searchQuery: '',
      selectedCategory: '',
      alerts: [],
      unreadAlertsCount: 0,
      isLoading: false,
      isDarkMode: false,
      sidebarOpen: false,

      // Actions
      setUser: (user) => set({ 
        user, 
        isAuthenticated: !!user 
      }),

      setProducts: (products) => {
        set({ products })
        get().filterProducts()
      },

      setSearchQuery: (searchQuery) => {
        set({ searchQuery })
        get().filterProducts()
      },

      setSelectedCategory: (selectedCategory) => {
        set({ selectedCategory })
        get().filterProducts()
      },

      setAlerts: (alerts) => {
        const unreadAlertsCount = alerts.filter(alert => !alert.is_read).length
        set({ alerts, unreadAlertsCount })
      },

      markAlertAsRead: (alertId) => {
        const alerts = get().alerts.map(alert =>
          alert.id === alertId ? { ...alert, is_read: true } : alert
        )
        const unreadAlertsCount = alerts.filter(alert => !alert.is_read).length
        set({ alerts, unreadAlertsCount })
      },

      setLoading: (isLoading) => set({ isLoading }),

      toggleDarkMode: () => set((state) => ({ 
        isDarkMode: !state.isDarkMode 
      })),

      toggleSidebar: () => set((state) => ({ 
        sidebarOpen: !state.sidebarOpen 
      })),

      filterProducts: () => {
        const { products, searchQuery, selectedCategory } = get()
        
        let filtered = products

        if (selectedCategory) {
          filtered = filtered.filter(product => 
            product.category === selectedCategory
          )
        }

        if (searchQuery) {
          const query = searchQuery.toLowerCase()
          filtered = filtered.filter(product =>
            product.name.toLowerCase().includes(query) ||
            product.description.toLowerCase().includes(query) ||
            product.brand.toLowerCase().includes(query) ||
            product.model.toLowerCase().includes(query)
          )
        }

        set({ filteredProducts: filtered })
      },
    }),
    {
      name: 'pro-matos-storage',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
        isDarkMode: state.isDarkMode,
      }),
    }
  )
)
