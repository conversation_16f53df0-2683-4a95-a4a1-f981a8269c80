-- Pro Matos Afrique Ouest - Schema Supabase
-- Exécuter dans l'ordre dans le SQL Editor de Supabase

-- 1. Extension pour UUID
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 2. Table users (étend auth.users)
CREATE TABLE public.users (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  email TEXT NOT NULL,
  role TEXT DEFAULT 'guest' CHECK (role IN ('guest', 'member', 'vip', 'admin')),
  full_name TEXT,
  company TEXT,
  phone TEXT,
  devis_demandes INTEGER DEFAULT 0,
  achats INTEGER DEFAULT 0,
  statut TEXT DEFAULT 'white' CHECK (statut IN ('white', 'grey', 'black')),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 3. Table alerts
CREATE TABLE public.alerts (
  id SERIAL PRIMARY KEY,
  title TEXT NOT NULL,
  body TEXT NOT NULL,
  type TEXT DEFAULT 'info' CHECK (type IN ('info', 'warning', 'critical', 'promo')),
  category TEXT DEFAULT 'general',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 4. Table user_alerts (abonnements)
CREATE TABLE public.user_alerts (
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  alert_id INTEGER REFERENCES public.alerts(id) ON DELETE CASCADE,
  subscribed_at TIMESTAMPTZ DEFAULT NOW(),
  PRIMARY KEY (user_id, alert_id)
);

-- 5. Table validations
CREATE TABLE public.validations (
  id SERIAL PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  file_url TEXT,
  file_name TEXT,
  status TEXT DEFAULT 'En cours' CHECK (status IN ('En cours', 'Validé', 'Refusé')),
  admin_notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 6. Table kits
CREATE TABLE public.kits (
  id SERIAL PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  file_url TEXT NOT NULL,
  file_name TEXT,
  category TEXT DEFAULT 'general',
  downloads INTEGER DEFAULT 0,
  is_premium BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 7. Table kit_downloads (tracking)
CREATE TABLE public.kit_downloads (
  id SERIAL PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  kit_id INTEGER REFERENCES public.kits(id) ON DELETE CASCADE,
  downloaded_at TIMESTAMPTZ DEFAULT NOW()
);

-- 8. Table club_events
CREATE TABLE public.club_events (
  id SERIAL PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  event_date TIMESTAMPTZ NOT NULL,
  location TEXT,
  max_participants INTEGER DEFAULT 50,
  current_participants INTEGER DEFAULT 0,
  is_vip_only BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 9. Table event_registrations
CREATE TABLE public.event_registrations (
  id SERIAL PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  event_id INTEGER REFERENCES public.club_events(id) ON DELETE CASCADE,
  registered_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, event_id)
);

-- 10. RLS Policies
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.validations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.kits ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.kit_downloads ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.club_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.event_registrations ENABLE ROW LEVEL SECURITY;

-- Policies pour users
CREATE POLICY "Users can view own profile" ON public.users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.users
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Admins can view all users" ON public.users
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Policies pour alerts
CREATE POLICY "Everyone can view active alerts" ON public.alerts
  FOR SELECT USING (is_active = true);

CREATE POLICY "Admins can manage alerts" ON public.alerts
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Policies pour user_alerts
CREATE POLICY "Users can manage own subscriptions" ON public.user_alerts
  FOR ALL USING (auth.uid() = user_id);

-- Policies pour validations
CREATE POLICY "Users can view own validations" ON public.validations
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create validations" ON public.validations
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Admins can view all validations" ON public.validations
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Policies pour kits
CREATE POLICY "Members can view kits" ON public.kits
  FOR SELECT USING (
    NOT is_premium OR 
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE id = auth.uid() AND role IN ('member', 'vip', 'admin')
    )
  );

-- Policies pour kit_downloads
CREATE POLICY "Users can view own downloads" ON public.kit_downloads
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create downloads" ON public.kit_downloads
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Policies pour club_events
CREATE POLICY "VIP can view club events" ON public.club_events
  FOR SELECT USING (
    NOT is_vip_only OR 
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE id = auth.uid() AND role IN ('vip', 'admin')
    )
  );

-- Policies pour event_registrations
CREATE POLICY "Users can manage own registrations" ON public.event_registrations
  FOR ALL USING (auth.uid() = user_id);

-- 11. Functions et Triggers
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.users (id, email, full_name)
  VALUES (NEW.id, NEW.email, NEW.raw_user_meta_data->>'full_name');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 12. Storage buckets
INSERT INTO storage.buckets (id, name, public) VALUES 
  ('validations', 'validations', false),
  ('kits', 'kits', true);

-- Storage policies
CREATE POLICY "Users can upload validation files" ON storage.objects
  FOR INSERT WITH CHECK (bucket_id = 'validations' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can view own validation files" ON storage.objects
  FOR SELECT USING (bucket_id = 'validations' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Everyone can view kit files" ON storage.objects
  FOR SELECT USING (bucket_id = 'kits');
