"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./middleware.ts":
/*!***********************!*\
  !*** ./middleware.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(middleware)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\n\nasync function middleware(req) {\n    const res = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next();\n    // Désactiver l'authentification en mode test\n    if ( false || process.env.PLAYWRIGHT_TEST === \"true\") {\n        return res;\n    }\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createMiddlewareClient)({\n        req,\n        res\n    });\n    // Récupérer la session\n    const { data: { session } } = await supabase.auth.getSession();\n    // Routes publiques qui ne nécessitent pas d'authentification\n    const publicRoutes = [\n        \"/auth/signin\",\n        \"/auth/callback\",\n        \"/\"\n    ];\n    const isPublicRoute = publicRoutes.some((route)=>req.nextUrl.pathname.startsWith(route));\n    // Si l'utilisateur n'est pas connecté et essaie d'accéder à une route protégée\n    if (!session && !isPublicRoute) {\n        const redirectUrl = req.nextUrl.clone();\n        redirectUrl.pathname = \"/auth/signin\";\n        redirectUrl.searchParams.set(\"redirectedFrom\", req.nextUrl.pathname);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n    }\n    // Si l'utilisateur est connecté et essaie d'accéder aux pages d'auth\n    if (session && req.nextUrl.pathname.startsWith(\"/auth/signin\")) {\n        const redirectUrl = req.nextUrl.clone();\n        redirectUrl.pathname = \"/hub\";\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n    }\n    return res;\n}\nconst config = {\n    matcher: [\n        /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder\n     */ \"/((?!_next/static|_next/image|favicon.ico|.*\\\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./middleware.ts\n");

/***/ })

});