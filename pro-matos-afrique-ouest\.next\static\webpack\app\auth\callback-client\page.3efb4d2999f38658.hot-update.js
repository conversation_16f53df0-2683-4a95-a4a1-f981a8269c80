"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/callback-client/page",{

/***/ "(app-pages-browser)/./src/app/auth/callback-client/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/auth/callback-client/page.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AuthCallbackClient; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction AuthCallbackClient() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Traitement de la connexion...\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleAuthCallback = async ()=>{\n            try {\n                console.log(\"=== CLIENT CALLBACK DEBUG ===\");\n                console.log(\"URL compl\\xe8te:\", window.location.href);\n                console.log(\"Hash:\", window.location.hash);\n                console.log(\"Search:\", window.location.search);\n                // Lire les paramètres du fragment (#)\n                const hashParams = new URLSearchParams(window.location.hash.substring(1));\n                const accessToken = hashParams.get(\"access_token\");\n                const refreshToken = hashParams.get(\"refresh_token\");\n                const tokenType = hashParams.get(\"token_type\");\n                const type = hashParams.get(\"type\");\n                const error = hashParams.get(\"error\");\n                console.log(\"Param\\xe8tres extraits:\", {\n                    accessToken: accessToken ? \"pr\\xe9sent\" : \"absent\",\n                    refreshToken: refreshToken ? \"pr\\xe9sent\" : \"absent\",\n                    tokenType,\n                    type,\n                    error\n                });\n                console.log(\"AccessToken (d\\xe9but):\", accessToken === null || accessToken === void 0 ? void 0 : accessToken.substring(0, 50));\n                console.log(\"RefreshToken:\", refreshToken);\n                if (error) {\n                    console.error(\"Erreur dans l'URL:\", error);\n                    setStatus(\"Erreur: \".concat(error));\n                    setTimeout(()=>{\n                        router.push(\"/auth/signin?error=\" + error);\n                    }, 2000);\n                    return;\n                }\n                if (accessToken && refreshToken) {\n                    setStatus(\"Cr\\xe9ation de la session...\");\n                    // Créer la session avec les tokens\n                    const { data, error: sessionError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.setSession({\n                        access_token: accessToken,\n                        refresh_token: refreshToken\n                    });\n                    if (sessionError) {\n                        console.error(\"Erreur cr\\xe9ation session:\", sessionError);\n                        setStatus(\"Erreur de session: \".concat(sessionError.message));\n                        setTimeout(()=>{\n                            router.push(\"/auth/signin?error=session_error\");\n                        }, 2000);\n                        return;\n                    }\n                    if (data.session) {\n                        console.log(\"✅ Session cr\\xe9\\xe9e avec succ\\xe8s pour:\", data.session.user.email);\n                        setStatus(\"Connexion r\\xe9ussie ! Redirection vers le hub...\");\n                        // Nettoyer l'URL\n                        window.history.replaceState({}, document.title, \"/auth/callback-client\");\n                        // Rediriger vers le hub\n                        setTimeout(()=>{\n                            router.push(\"/hub\");\n                        }, 1000);\n                    } else {\n                        console.error(\"Pas de session cr\\xe9\\xe9e\");\n                        setStatus(\"Erreur: Impossible de cr\\xe9er la session\");\n                        setTimeout(()=>{\n                            router.push(\"/auth/signin?error=no_session\");\n                        }, 2000);\n                    }\n                } else {\n                    console.error(\"Tokens manquants\");\n                    setStatus(\"Erreur: Tokens d'authentification manquants\");\n                    setTimeout(()=>{\n                        router.push(\"/auth/signin?error=missing_tokens\");\n                    }, 2000);\n                }\n            } catch (error) {\n                console.error(\"Exception dans le callback:\", error);\n                setStatus(\"Exception: \".concat(error));\n                setTimeout(()=>{\n                    router.push(\"/auth/signin?error=callback_exception\");\n                }, 2000);\n            }\n        };\n        handleAuthCallback();\n    }, [\n        router\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full bg-white rounded-lg shadow-md p-6 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\app\\\\auth\\\\callback-client\\\\page.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-xl font-semibold text-gray-900 mb-2\",\n                    children: \"Connexion en cours\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\app\\\\auth\\\\callback-client\\\\page.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600\",\n                    children: status\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\app\\\\auth\\\\callback-client\\\\page.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\app\\\\auth\\\\callback-client\\\\page.tsx\",\n            lineNumber: 104,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\app\\\\auth\\\\callback-client\\\\page.tsx\",\n        lineNumber: 103,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthCallbackClient, \"FweN7jrWa9mpZOYAHNyYrO0QKqg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AuthCallbackClient;\nvar _c;\n$RefreshReg$(_c, \"AuthCallbackClient\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/auth/callback-client/page.tsx\n"));

/***/ })

});