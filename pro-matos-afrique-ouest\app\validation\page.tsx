'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Upload, FileText, Send, AlertCircle } from 'lucide-react'
import { useAuthStore, usePermissions } from '@/lib/stores/authStore'
import { supabase, uploadFile } from '@/lib/supabase/client'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { toast } from 'sonner'
import DashboardLayout from '@/components/layout/DashboardLayout'

export default function ValidationPage() {
  const { user, profile } = useAuthStore()
  const permissions = usePermissions()
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    file: null as File | null
  })

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      // Vérifier la taille (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        toast.error('Le fichier est trop volumineux (max 10MB)')
        return
      }
      
      // Vérifier le type
      const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png', 'image/jpg']
      if (!allowedTypes.includes(file.type)) {
        toast.error('Type de fichier non supporté (PDF, JPG, PNG uniquement)')
        return
      }
      
      setFormData(prev => ({ ...prev, file }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!user) {
      toast.error('Vous devez être connecté')
      return
    }

    if (!permissions.canAccessValidation) {
      toast.error('Accès restreint', {
        description: 'Votre compte a été limité. Contactez le support.'
      })
      return
    }

    if (!formData.title || !formData.description) {
      toast.error('Veuillez remplir tous les champs obligatoires')
      return
    }

    setLoading(true)
    toast.loading('Envoi en cours...', { id: 'validation-submit' })

    try {
      let fileUrl = null
      let fileName = null

      // Upload du fichier si présent
      if (formData.file) {
        const filePath = `${user.id}/${Date.now()}-${formData.file.name}`
        const { data, error } = await uploadFile('validations', filePath, formData.file)
        
        if (error) throw error
        
        fileUrl = `validations/${filePath}`
        fileName = formData.file.name
      }

      // Créer la validation
      const { error: insertError } = await supabase
        .from('validations')
        .insert({
          user_id: user.id,
          title: formData.title,
          description: formData.description,
          file_url: fileUrl,
          file_name: fileName
        })

      if (insertError) throw insertError

      // Envoyer notification email (simulation)
      await fetch('/api/notifications/validation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userEmail: user.email,
          title: formData.title,
          description: formData.description,
          hasFile: !!formData.file
        })
      })

      toast.success('Demande envoyée avec succès', { id: 'validation-submit' })
      
      // Reset form
      setFormData({ title: '', description: '', file: null })
      
      // Reset file input
      const fileInput = document.getElementById('file-upload') as HTMLInputElement
      if (fileInput) fileInput.value = ''

    } catch (error) {
      console.error('Erreur lors de l\'envoi:', error)
      toast.error('Erreur lors de l\'envoi', { id: 'validation-submit' })
    } finally {
      setLoading(false)
    }
  }

  if (!permissions.canAccessValidation) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[60vh]">
          <Card className="w-full max-w-md">
            <CardContent className="p-6 text-center">
              <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Accès Restreint
              </h3>
              <p className="text-gray-600 mb-4">
                Votre compte a été temporairement limité. Vous avez dépassé le nombre 
                de demandes de devis autorisées sans achat.
              </p>
              <Button variant="outline" onClick={() => window.history.back()}>
                Retour
              </Button>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="max-w-2xl mx-auto space-y-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <h1 className="text-3xl font-bold text-gray-900">Validation Technique</h1>
          <p className="text-gray-600 mt-2">
            Soumettez vos documents pour validation par nos experts techniques
          </p>
        </motion.div>

        {/* Info Alert */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Alert>
            <FileText className="h-4 w-4" />
            <AlertDescription>
              Nos experts examineront votre demande sous 24-48h. Vous recevrez une notification 
              par email dès que la validation sera terminée.
            </AlertDescription>
          </Alert>
        </motion.div>

        {/* Form */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card>
            <CardHeader>
              <CardTitle>Nouvelle Demande de Validation</CardTitle>
              <CardDescription>
                Remplissez le formulaire ci-dessous pour soumettre votre demande
              </CardDescription>
            </CardHeader>
            
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Titre */}
                <div className="space-y-2">
                  <Label htmlFor="title">Titre de la demande *</Label>
                  <Input
                    id="title"
                    placeholder="Ex: Validation schéma électrique résidentiel"
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    required
                  />
                </div>

                {/* Description */}
                <div className="space-y-2">
                  <Label htmlFor="description">Description détaillée *</Label>
                  <Textarea
                    id="description"
                    placeholder="Décrivez votre projet, les normes à respecter, les contraintes spécifiques..."
                    rows={4}
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    required
                  />
                </div>

                {/* Upload fichier */}
                <div className="space-y-2">
                  <Label htmlFor="file-upload">Document à valider (optionnel)</Label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                    <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                    <div className="space-y-2">
                      <p className="text-sm text-gray-600">
                        Glissez votre fichier ici ou cliquez pour sélectionner
                      </p>
                      <p className="text-xs text-gray-500">
                        PDF, JPG, PNG - Max 10MB
                      </p>
                      <Input
                        id="file-upload"
                        type="file"
                        accept=".pdf,.jpg,.jpeg,.png"
                        onChange={handleFileChange}
                        className="hidden"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => document.getElementById('file-upload')?.click()}
                      >
                        Sélectionner un fichier
                      </Button>
                    </div>
                  </div>
                  
                  {formData.file && (
                    <div className="flex items-center space-x-2 p-3 bg-green-50 border border-green-200 rounded-lg">
                      <FileText className="h-4 w-4 text-green-600" />
                      <span className="text-sm text-green-800">{formData.file.name}</span>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => setFormData(prev => ({ ...prev, file: null }))}
                        className="text-red-600 hover:text-red-700"
                      >
                        Supprimer
                      </Button>
                    </div>
                  )}
                </div>

                {/* Submit */}
                <div className="flex justify-end space-x-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => window.history.back()}
                  >
                    Annuler
                  </Button>
                  
                  <Button
                    type="submit"
                    disabled={loading || !formData.title || !formData.description}
                    className="bg-amber-600 hover:bg-amber-700"
                  >
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                        Envoi...
                      </>
                    ) : (
                      <>
                        <Send className="h-4 w-4 mr-2" />
                        Envoyer la demande
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </motion.div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="flex justify-center"
        >
          <Button
            variant="outline"
            onClick={() => window.location.href = '/validation/history'}
            className="text-amber-600 border-amber-200 hover:bg-amber-50"
          >
            Voir l'historique de mes demandes
          </Button>
        </motion.div>
      </div>
    </DashboardLayout>
  )
}
