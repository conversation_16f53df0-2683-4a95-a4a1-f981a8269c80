'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Bell,
  TrendingUp,
  Package,
  AlertTriangle,
  Calendar,
  Newspaper,
  Wifi,
  WifiOff,
  Clock,
  Filter,
  Search,
  RefreshCw,
  Plus,
  Minus
} from 'lucide-react'
import { useStore } from '@/store/useStore'
import { AlertService, Alert } from '@/lib/services/alertService'
import { formatDate } from '@/lib/utils'
import { toast } from 'sonner'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'

interface InformationHubProps {
  className?: string
}

export default function InformationHub({ className = '' }: InformationHubProps) {
  const { user } = useStore()

  const [alerts, setAlerts] = useState<Alert[]>([])
  const [loading, setLoading] = useState(true)
  const [realTimeConnected, setRealTimeConnected] = useState(true)
  const [lastUpdateTime, setLastUpdateTime] = useState<Date>(new Date())

  const [activeTab, setActiveTab] = useState<'alerts' | 'stocks' | 'training' | 'news'>('alerts')
  const [searchQuery, setSearchQuery] = useState('')
  const [filterType, setFilterType] = useState<'all' | 'info' | 'warning' | 'critical' | 'promo'>('all')
  const [filterCategory, setFilterCategory] = useState<string>('all')
  const [isClient, setIsClient] = useState(false)

  // Initialisation des données
  useEffect(() => {
    // Marquer que nous sommes côté client
    setIsClient(true)

    const loadInitialData = () => {
      // Données statiques d'exemple pour démonstration
      setMarketAlerts([
        {
          id: '1',
          title: 'Nouvelle norme NF C 15-100 - Amendement A6',
          message: 'Mise à jour importante des règles d\'installation électrique',
          severity: 'medium',
          type: 'regulation',
          is_active: true,
          created_at: '2024-01-15T10:00:00Z',
          source: 'Veille réglementaire',
          affected_regions: ['Côte d\'Ivoire', 'Sénégal', 'Mali'],
          category: 'Réglementation'
        },
        {
          id: '2',
          title: 'Formation Schneider Electric - Nouveaux produits',
          message: 'Session de formation sur la gamme Acti9 nouvelle génération',
          severity: 'low',
          type: 'training',
          is_active: true,
          created_at: '2024-01-14T14:30:00Z',
          source: 'Partenaire',
          affected_regions: ['Abidjan', 'Dakar'],
          category: 'Formation'
        }
      ])

      // Pas de données temps réel - interface statique
      setStockUpdates([])
      setTrainingEvents([])
      setNewsUpdates([])
    }

    loadInitialData()

    // Pas de connexion temps réel - données statiques
    setRealTimeConnected(false)

    // Pas d'abonnement temps réel
    // const unsubscribe = HubService.subscribeToRealTimeUpdates(...)

    // return () => {
    //   unsubscribe()
    //   setRealTimeConnected(false)
    // }
  }, [])

  // Filtrage des alertes
  const filteredAlerts = marketAlerts.filter(alert => {
    const matchesSearch = searchQuery === '' || 
      alert.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      alert.message.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesSeverity = filterSeverity === 'all' || alert.severity === filterSeverity
    
    return matchesSearch && matchesSeverity && alert.is_active
  })

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200'
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low': return 'bg-blue-100 text-blue-800 border-blue-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getUpdateTypeIcon = (type: string) => {
    switch (type) {
      case 'restock': return <TrendingUp className="h-4 w-4 text-green-600" />
      case 'decrease': return <TrendingUp className="h-4 w-4 text-orange-600 rotate-180" />
      case 'out_of_stock': return <AlertTriangle className="h-4 w-4 text-red-600" />
      default: return <Package className="h-4 w-4 text-blue-600" />
    }
  }

  const tabs = [
    { id: 'alerts', label: 'Alertes Marché', icon: Bell, count: filteredAlerts.length },
    { id: 'stocks', label: 'Stocks', icon: Package, count: stockUpdates.length },
    { id: 'training', label: 'Formations', icon: Calendar, count: trainingEvents.length },
    { id: 'news', label: 'Actualités', icon: Newspaper, count: newsUpdates.length }
  ]

  return (
    <div className={`bg-white rounded-lg shadow-lg ${className}`}>
      {/* Header avec statut temps réel */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Hub d'Information Pro Matos</h2>
            <p className="text-gray-600 mt-1">Centre de veille technologique et réglementaire</p>
          </div>
          
          <div className="flex items-center space-x-4">
            {/* Statut plateforme */}
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 rounded-full bg-blue-500"></div>
              <span className="text-sm text-blue-600 font-medium">Mode Démonstration</span>
            </div>
            
            {/* Dernière mise à jour */}
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <Clock className="h-4 w-4" />
              <span>Mis à jour {isClient ? formatDate(lastUpdateTime) : '--'}</span>
            </div>
            
            {/* Bouton actualiser */}
            <button
              type="button"
              onClick={updateLastUpdateTime}
              className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
              title="Actualiser les données"
            >
              <RefreshCw className="h-5 w-5 text-gray-600" />
            </button>
          </div>
        </div>

        {/* Barre de recherche et filtres */}
        <div className="mt-4 flex items-center space-x-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Rechercher dans le hub..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400 focus:border-transparent"
            />
          </div>
          
          {activeTab === 'alerts' && (
            <select
              value={filterSeverity}
              onChange={(e) => setFilterSeverity(e.target.value as any)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400 focus:border-transparent"
            >
              <option value="all">Toutes les priorités</option>
              <option value="critical">Critique</option>
              <option value="high">Élevée</option>
              <option value="medium">Moyenne</option>
              <option value="low">Faible</option>
            </select>
          )}
        </div>
      </div>

      {/* Onglets */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6">
          {tabs.map((tab) => {
            const Icon = tab.icon
            const isActive = activeTab === tab.id
            
            return (
              <button
                key={tab.id}
                type="button"
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  isActive
                    ? 'border-amber-500 text-amber-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="h-5 w-5" />
                <span>{tab.label}</span>
                {tab.count > 0 && (
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    isActive ? 'bg-amber-100 text-amber-800' : 'bg-gray-100 text-gray-600'
                  }`}>
                    {tab.count}
                  </span>
                )}
              </button>
            )
          })}
        </nav>
      </div>

      {/* Contenu des onglets */}
      <div className="p-6">
        <AnimatePresence mode="wait">
          {activeTab === 'alerts' && (
            <motion.div
              key="alerts"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-4"
            >
              {filteredAlerts.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Bell className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>Aucune alerte correspondant aux critères</p>
                </div>
              ) : (
                filteredAlerts.map((alert) => (
                  <motion.div
                    key={alert.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    className={`p-4 rounded-lg border ${getSeverityColor(alert.severity)}`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <span className="text-xs font-medium uppercase tracking-wide">
                            {alert.type.replace('_', ' ')}
                          </span>
                          <span className="text-xs text-gray-500">
                            {formatDate(alert.created_at)}
                          </span>
                        </div>
                        <h3 className="font-semibold mb-1">{alert.title}</h3>
                        <p className="text-sm mb-2">{alert.message}</p>
                        <div className="flex items-center space-x-4 text-xs text-gray-600">
                          <span>Catégorie: {alert.category || alert.type}</span>
                          <span>Régions: {alert.affected_regions?.join(', ') || 'Non spécifié'}</span>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))
              )}
            </motion.div>
          )}

          {activeTab === 'stocks' && (
            <motion.div
              key="stocks"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-4"
            >
              {stockUpdates.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Package className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>Aucune mise à jour de stock récente</p>
                </div>
              ) : (
                stockUpdates.map((update) => (
                  <motion.div
                    key={update.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    className="p-4 rounded-lg border border-gray-200 bg-gray-50"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        {getUpdateTypeIcon(update.update_type)}
                        <div>
                          <h3 className="font-semibold text-gray-900">
                            Produit #{update.product_id}
                          </h3>
                          <p className="text-sm text-gray-600">{update.location}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold text-gray-900">
                          {update.previous_quantity} → {update.current_quantity}
                        </div>
                        <div className="text-xs text-gray-500">
                          {formatDate(update.created_at)}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))
              )}
            </motion.div>
          )}

          {activeTab === 'training' && (
            <motion.div
              key="training"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-4"
            >
              {trainingEvents.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Calendar className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>Aucun événement de formation programmé</p>
                </div>
              ) : (
                trainingEvents.map((event) => (
                  <motion.div
                    key={event.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    className="p-6 rounded-lg border border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            event.type === 'certification' ? 'bg-purple-100 text-purple-800' :
                            event.type === 'webinar' ? 'bg-green-100 text-green-800' :
                            event.type === 'workshop' ? 'bg-blue-100 text-blue-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {event.type}
                          </span>
                          {event.membership_required && (
                            <span className="px-2 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                              {event.membership_required}+
                            </span>
                          )}
                        </div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">
                          {event.title}
                        </h3>
                        <p className="text-gray-600 mb-3">{event.description}</p>
                        <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
                          <div>
                            <span className="font-medium">Instructeur:</span> {event.instructor}
                          </div>
                          <div>
                            <span className="font-medium">Lieu:</span> {event.location}
                          </div>
                          <div>
                            <span className="font-medium">Date:</span> {formatDate(event.start_date)}
                          </div>
                          <div>
                            <span className="font-medium">Participants:</span> {event.current_participants}/{event.max_participants}
                          </div>
                        </div>
                      </div>
                      <div className="text-right ml-4">
                        {event.registration_fee > 0 ? (
                          <div className="text-lg font-bold text-gray-900">
                            {event.registration_fee.toLocaleString()} FCFA
                          </div>
                        ) : (
                          <div className="text-lg font-bold text-green-600">Gratuit</div>
                        )}
                        <button
                          type="button"
                          className="mt-2 px-4 py-2 bg-amber-500 text-white rounded-lg hover:bg-amber-600 transition-colors"
                        >
                          S'inscrire
                        </button>
                      </div>
                    </div>
                  </motion.div>
                ))
              )}
            </motion.div>
          )}

          {activeTab === 'news' && (
            <motion.div
              key="news"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-6"
            >
              {newsUpdates.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Newspaper className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>Aucune actualité récente</p>
                </div>
              ) : (
                newsUpdates.map((news) => (
                  <motion.div
                    key={news.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    className={`p-6 rounded-lg border border-gray-200 ${
                      news.is_featured ? 'bg-gradient-to-r from-amber-50 to-yellow-50 border-amber-200' : 'bg-white'
                    }`}
                  >
                    <div className="flex items-start space-x-4">
                      {news.image_url && (
                        <div className="w-24 h-24 bg-gray-200 rounded-lg flex-shrink-0"></div>
                      )}
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            news.category === 'regulation' ? 'bg-red-100 text-red-800' :
                            news.category === 'technology' ? 'bg-blue-100 text-blue-800' :
                            news.category === 'company' ? 'bg-green-100 text-green-800' :
                            news.category === 'market' ? 'bg-purple-100 text-purple-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {news.category}
                          </span>
                          {news.is_featured && (
                            <span className="px-2 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                              À la une
                            </span>
                          )}
                          <span className="text-xs text-gray-500">
                            {formatDate(news.published_at)}
                          </span>
                        </div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">
                          {news.title}
                        </h3>
                        <p className="text-gray-600 mb-3">{news.summary}</p>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4 text-sm text-gray-500">
                            <span>Par {news.author}</span>
                            <div className="flex space-x-1">
                              {news.tags?.slice(0, 3).map((tag) => (
                                <span key={tag} className="px-2 py-1 bg-gray-100 rounded text-xs">
                                  #{tag}
                                </span>
                              )) || <span className="text-xs text-gray-400">Aucun tag</span>}
                            </div>
                          </div>
                          {news.source_url && (
                            <a
                              href={news.source_url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-amber-600 hover:text-amber-700 text-sm font-medium"
                            >
                              Lire la suite →
                            </a>
                          )}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}
