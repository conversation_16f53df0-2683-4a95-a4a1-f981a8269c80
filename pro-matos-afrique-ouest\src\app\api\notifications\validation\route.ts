import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { userEmail, title, description, hasFile } = body

    // Simulation d'envoi d'email
    // En production, utiliser un service comme Resend, SendGrid, etc.
    console.log('📧 Notification email envoyée:', {
      to: '<EMAIL>',
      subject: `Nouvelle demande de validation: ${title}`,
      content: {
        userEmail,
        title,
        description,
        hasFile,
        timestamp: new Date().toISOString()
      }
    })

    // Simuler un délai d'envoi
    await new Promise(resolve => setTimeout(resolve, 1000))

    return NextResponse.json({ 
      success: true, 
      message: 'Notification envoyée avec succès' 
    })

  } catch (error) {
    console.error('Erreur lors de l\'envoi de la notification:', error)
    
    return NextResponse.json(
      { success: false, error: 'Erreur lors de l\'envoi de la notification' },
      { status: 500 }
    )
  }
}
