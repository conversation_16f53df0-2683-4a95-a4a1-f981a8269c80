'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Crown, 
  Download, 
  Star,
  Shield,
  Award
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { toast } from 'sonner'
import { useAuthStore } from '@/lib/stores/authStore'

interface VIPBadgeProps {
  className?: string
}

export default function VIPBadge({ className = '' }: VIPBadgeProps) {
  const { user } = useAuthStore()
  const [isGenerating, setIsGenerating] = useState(false)

  const generateBadgeSVG = (userName: string, userEmail: string) => {
    const currentDate = new Date().toLocaleDateString('fr-FR')
    
    return `
      <svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
            <stop offset="50%" style="stop-color:#FFA500;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#FF8C00;stop-opacity:1" />
          </linearGradient>
          <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#1e40af;stop-opacity:1" />
          </linearGradient>
          <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
            <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
          </filter>
        </defs>
        
        <!-- Background -->
        <rect width="400" height="300" rx="20" fill="url(#bgGradient)" filter="url(#shadow)"/>
        
        <!-- Border -->
        <rect x="10" y="10" width="380" height="280" rx="15" fill="none" stroke="url(#goldGradient)" stroke-width="3"/>
        
        <!-- Header -->
        <text x="200" y="40" text-anchor="middle" fill="url(#goldGradient)" font-family="Arial, sans-serif" font-size="24" font-weight="bold">
          PRO MATOS AFRIQUE OUEST
        </text>
        
        <!-- Crown Icon -->
        <g transform="translate(180, 60)">
          <path d="M8 2L3 7L8 12L13 7L8 2Z M8 4L11 7L8 10L5 7L8 4Z" fill="url(#goldGradient)" stroke="#FFD700" stroke-width="1"/>
          <circle cx="8" cy="7" r="2" fill="#FFD700"/>
          <circle cx="4" cy="5" r="1" fill="#FFD700"/>
          <circle cx="12" cy="5" r="1" fill="#FFD700"/>
          <circle cx="8" cy="3" r="1" fill="#FFD700"/>
        </g>
        
        <!-- VIP Title -->
        <text x="200" y="110" text-anchor="middle" fill="#FFFFFF" font-family="Arial, sans-serif" font-size="32" font-weight="bold">
          MEMBRE VIP
        </text>
        
        <!-- User Info -->
        <text x="200" y="140" text-anchor="middle" fill="#E5E7EB" font-family="Arial, sans-serif" font-size="16">
          ${userName || 'Membre VIP'}
        </text>
        <text x="200" y="160" text-anchor="middle" fill="#9CA3AF" font-family="Arial, sans-serif" font-size="12">
          ${userEmail}
        </text>
        
        <!-- Benefits -->
        <text x="50" y="190" fill="#FFFFFF" font-family="Arial, sans-serif" font-size="12" font-weight="bold">
          ✓ Accès aux kits premium
        </text>
        <text x="50" y="210" fill="#FFFFFF" font-family="Arial, sans-serif" font-size="12" font-weight="bold">
          ✓ Support prioritaire 24/7
        </text>
        <text x="50" y="230" fill="#FFFFFF" font-family="Arial, sans-serif" font-size="12" font-weight="bold">
          ✓ Réseau exclusif d'experts
        </text>
        
        <!-- Date -->
        <text x="200" y="270" text-anchor="middle" fill="#9CA3AF" font-family="Arial, sans-serif" font-size="10">
          Membre depuis le ${currentDate}
        </text>
        
        <!-- Stars decoration -->
        <g fill="url(#goldGradient)" opacity="0.3">
          <circle cx="50" cy="50" r="2"/>
          <circle cx="350" cy="50" r="2"/>
          <circle cx="50" cy="250" r="2"/>
          <circle cx="350" cy="250" r="2"/>
        </g>
      </svg>
    `
  }

  const downloadBadge = async () => {
    if (!user) {
      toast.error('Vous devez être connecté')
      return
    }

    if (user.role !== 'vip' && user.role !== 'admin') {
      toast.error('Ce badge est réservé aux membres VIP')
      return
    }

    setIsGenerating(true)
    toast.loading('Génération de votre badge...', { id: 'badge-generation' })

    try {
      // Générer le SVG
      const svgContent = generateBadgeSVG(
        user.user_metadata?.full_name || user.email?.split('@')[0] || 'Membre VIP',
        user.email || ''
      )

      // Créer un blob et télécharger
      const blob = new Blob([svgContent], { type: 'image/svg+xml' })
      const url = URL.createObjectURL(blob)
      
      const link = document.createElement('a')
      link.href = url
      link.download = `badge-vip-pro-matos-${user.email?.split('@')[0] || 'membre'}.svg`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      URL.revokeObjectURL(url)

      toast.success('Badge téléchargé avec succès !', { 
        id: 'badge-generation',
        description: 'Vous pouvez maintenant l\'utiliser sur vos documents professionnels'
      })

    } catch (error) {
      console.error('Erreur génération badge:', error)
      toast.error('Erreur lors de la génération', { id: 'badge-generation' })
    } finally {
      setIsGenerating(false)
    }
  }

  const downloadPNG = async () => {
    if (!user) return

    setIsGenerating(true)
    toast.loading('Génération du badge PNG...', { id: 'badge-png' })

    try {
      const svgContent = generateBadgeSVG(
        user.user_metadata?.full_name || user.email?.split('@')[0] || 'Membre VIP',
        user.email || ''
      )

      // Créer un canvas pour convertir en PNG
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()

      canvas.width = 800  // 2x pour meilleure qualité
      canvas.height = 600

      const svgBlob = new Blob([svgContent], { type: 'image/svg+xml;charset=utf-8' })
      const url = URL.createObjectURL(svgBlob)

      img.onload = () => {
        if (ctx) {
          ctx.drawImage(img, 0, 0, 800, 600)
          
          canvas.toBlob((blob) => {
            if (blob) {
              const pngUrl = URL.createObjectURL(blob)
              const link = document.createElement('a')
              link.href = pngUrl
              link.download = `badge-vip-pro-matos-${user.email?.split('@')[0] || 'membre'}.png`
              document.body.appendChild(link)
              link.click()
              document.body.removeChild(link)
              URL.revokeObjectURL(pngUrl)
              
              toast.success('Badge PNG téléchargé !', { id: 'badge-png' })
            }
          }, 'image/png', 0.95)
        }
        URL.revokeObjectURL(url)
      }

      img.src = url

    } catch (error) {
      console.error('Erreur génération PNG:', error)
      toast.error('Erreur lors de la génération PNG', { id: 'badge-png' })
    } finally {
      setIsGenerating(false)
    }
  }

  // Aperçu du badge
  const previewSVG = user ? generateBadgeSVG(
    user.user_metadata?.full_name || user.email?.split('@')[0] || 'Membre VIP',
    user.email || ''
  ) : ''

  return (
    <div className={`space-y-6 ${className}`}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Award className="h-5 w-5 text-amber-600" />
            <span>Badge Membre VIP</span>
          </CardTitle>
          <CardDescription>
            Téléchargez votre badge professionnel personnalisé pour vos documents et signatures
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Aperçu */}
          {user && (user.role === 'vip' || user.role === 'admin') && (
            <div className="text-center">
              <h3 className="font-medium text-gray-900 mb-4">Aperçu de votre badge :</h3>
              <div 
                className="inline-block border border-gray-200 rounded-lg p-4 bg-white shadow-sm"
                dangerouslySetInnerHTML={{ __html: previewSVG }}
              />
            </div>
          )}

          {/* Boutons de téléchargement */}
          {user && (user.role === 'vip' || user.role === 'admin') ? (
            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                onClick={downloadBadge}
                disabled={isGenerating}
                className="flex-1 bg-amber-600 hover:bg-amber-700"
              >
                {isGenerating ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Génération...
                  </>
                ) : (
                  <>
                    <Download className="h-4 w-4 mr-2" />
                    Télécharger SVG
                  </>
                )}
              </Button>
              <Button
                onClick={downloadPNG}
                disabled={isGenerating}
                variant="outline"
                className="flex-1"
              >
                <Download className="h-4 w-4 mr-2" />
                Télécharger PNG
              </Button>
            </div>
          ) : (
            <div className="text-center p-6 bg-gray-50 rounded-lg">
              <Crown className="h-12 w-12 text-gray-400 mx-auto mb-3" />
              <h3 className="font-medium text-gray-900 mb-2">Badge VIP Requis</h3>
              <p className="text-gray-600 text-sm mb-4">
                Ce badge est exclusivement réservé aux membres VIP
              </p>
              <Button className="bg-amber-600 hover:bg-amber-700">
                <Crown className="h-4 w-4 mr-2" />
                Devenir VIP
              </Button>
            </div>
          )}

          {/* Informations d'utilisation */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-medium text-blue-900 mb-2">Comment utiliser votre badge :</h4>
            <ul className="text-blue-800 text-sm space-y-1">
              <li>• Ajoutez-le à vos devis et factures professionnels</li>
              <li>• Utilisez-le dans votre signature email</li>
              <li>• Affichez-le sur votre site web ou vos réseaux sociaux</li>
              <li>• Imprimez-le pour vos cartes de visite</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
