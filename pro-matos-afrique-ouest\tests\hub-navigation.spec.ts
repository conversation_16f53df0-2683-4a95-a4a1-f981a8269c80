import { test, expect } from '@playwright/test'

// Mock d'authentification pour les tests
test.beforeEach(async ({ page }) => {
  // Simuler un utilisateur connecté en mockant les appels Supabase
  await page.addInitScript(() => {
    // Mock localStorage pour simuler une session
    localStorage.setItem('supabase.auth.token', JSON.stringify({
      access_token: 'mock-token',
      user: {
        id: 'test-user-id',
        email: '<EMAIL>'
      }
    }))
  })
})

test.describe('Hub Navigation', () => {
  test('should display hub page with alerts', async ({ page }) => {
    await page.goto('/hub')

    // Attendre que la page se charge complètement
    await page.waitForLoadState('networkidle')

    // Debug: Afficher le contenu de la page
    const title = await page.title()
    console.log('Page title:', title)

    const h1Elements = await page.locator('h1').all()
    console.log('H1 elements found:', h1Elements.length)

    for (let i = 0; i < h1Elements.length; i++) {
      const text = await h1Elements[i].textContent()
      console.log(`H1 ${i + 1}:`, text)
    }

    // Debug: Afficher tout le contenu de la page
    const bodyText = await page.locator('body').textContent()
    console.log('Page contains "Hub d\'Information":', bodyText?.includes('Hub d\'Information'))
    console.log('Page contains "Restez informé":', bodyText?.includes('Restez informé'))

    // Debug: Chercher tous les éléments contenant "Hub d'Information"
    const hubElements = await page.locator('text=Hub d\'Information').all()
    console.log('Elements with "Hub d\'Information":', hubElements.length)

    // Vérifier les éléments principaux - chercher spécifiquement le h1 avec "Hub d'Information"
    await expect(page.locator('h1:has-text("Hub d\'Information")')).toBeVisible()
    await expect(page.locator('text=Restez informé')).toBeVisible()
    
    // Vérifier la présence des filtres
    await expect(page.locator('input[placeholder*="Rechercher"]')).toBeVisible()
    await expect(page.locator('text=Type d\'alerte')).toBeVisible()
  })

  test('should filter alerts by search', async ({ page }) => {
    await page.goto('/hub')
    
    // Attendre que les alertes se chargent
    await page.waitForSelector('[data-testid="alert-card"]', { timeout: 10000 })
    
    // Compter les alertes initiales
    const initialAlerts = await page.locator('[data-testid="alert-card"]').count()
    
    // Rechercher un terme spécifique
    await page.fill('input[placeholder*="Rechercher"]', 'norme')
    
    // Attendre que le filtrage se fasse
    await page.waitForTimeout(500)
    
    // Vérifier que le nombre d'alertes a changé ou qu'il y a des résultats
    const filteredAlerts = await page.locator('[data-testid="alert-card"]').count()
    expect(filteredAlerts).toBeGreaterThanOrEqual(0)
  })

  test('should show alert subscription functionality', async ({ page }) => {
    await page.goto('/hub')
    
    // Attendre qu'une alerte soit visible
    await page.waitForSelector('[data-testid="alert-card"]', { timeout: 10000 })
    
    // Cliquer sur le premier bouton d'abonnement
    const subscribeButton = page.locator('button').filter({ hasText: /S'abonner|Se désabonner/ }).first()
    await subscribeButton.click()
    
    // Vérifier qu'un toast apparaît (succès ou erreur)
    await expect(page.locator('[data-sonner-toaster]')).toBeVisible({ timeout: 5000 })
  })

  test('should display stats correctly', async ({ page }) => {
    await page.goto('/hub')
    
    // Vérifier les statistiques
    await expect(page.locator('text=Alertes actives')).toBeVisible()
    await expect(page.locator('text=Abonnements')).toBeVisible()
    await expect(page.locator('text=Alertes critiques')).toBeVisible()
    
    // Vérifier que les nombres sont affichés
    const statsNumbers = page.locator('[data-testid="stat-number"]')
    const count = await statsNumbers.count()
    expect(count).toBeGreaterThan(0)
  })
})

test.describe('Responsive Design', () => {
  test('should work on mobile viewport', async ({ page }) => {
    // Définir une taille mobile
    await page.setViewportSize({ width: 375, height: 667 })
    await page.goto('/hub')
    
    // Vérifier que la page s'affiche correctement
    await expect(page.locator('h1')).toContainText('Hub d\'Information')
    
    // Vérifier que les éléments sont empilés verticalement
    const searchInput = page.locator('input[placeholder*="Rechercher"]')
    await expect(searchInput).toBeVisible()
  })

  test('should show mobile menu', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 })
    await page.goto('/hub')
    
    // Chercher le bouton menu mobile
    const menuButton = page.locator('button').filter({ hasText: /Menu|☰/ }).first()
    if (await menuButton.isVisible()) {
      await menuButton.click()
      
      // Vérifier que le menu s'ouvre
      await expect(page.locator('nav')).toBeVisible()
    }
  })
})
