'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { supabase } from '@/lib/supabase/client'

export default function AuthCallbackClient() {
  const router = useRouter()
  const [status, setStatus] = useState('Traitement de la connexion...')

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        console.log('=== CLIENT CALLBACK DEBUG ===')
        console.log('URL complète:', window.location.href)
        console.log('Hash:', window.location.hash)
        console.log('Search:', window.location.search)

        // Lire les paramètres du fragment (#)
        const hashParams = new URLSearchParams(window.location.hash.substring(1))
        const accessToken = hashParams.get('access_token')
        const refreshToken = hashParams.get('refresh_token')
        const tokenType = hashParams.get('token_type')
        const type = hashParams.get('type')
        const error = hashParams.get('error')

        console.log('Paramètres extraits:', {
          accessToken: accessToken ? 'présent' : 'absent',
          refreshToken: refreshToken ? 'présent' : 'absent',
          tokenType,
          type,
          error
        })

        console.log('AccessToken (début):', accessToken?.substring(0, 50))
        console.log('RefreshToken:', refreshToken)

        if (error) {
          console.error('Erreur dans l\'URL:', error)
          setStatus(`Erreur: ${error}`)
          setTimeout(() => {
            router.push('/auth/signin?error=' + error)
          }, 2000)
          return
        }

        if (accessToken && refreshToken) {
          setStatus('Création de la session...')
          
          // Créer la session avec les tokens
          const { data, error: sessionError } = await supabase.auth.setSession({
            access_token: accessToken,
            refresh_token: refreshToken
          })

          if (sessionError) {
            console.error('Erreur création session:', sessionError)
            setStatus(`Erreur de session: ${sessionError.message}`)
            setTimeout(() => {
              router.push('/auth/signin?error=session_error')
            }, 2000)
            return
          }

          if (data.session) {
            console.log('✅ Session créée avec succès pour:', data.session.user.email)
            setStatus(`Connexion réussie ! Finalisation...`)

            // Nettoyer l'URL
            window.history.replaceState({}, document.title, '/auth/callback-client')

            // Vérifier que la session est bien établie avant de rediriger
            const verifyAndRedirect = async () => {
              try {
                const { data: { session: currentSession } } = await supabase.auth.getSession()
                if (currentSession) {
                  console.log('✅ Session vérifiée, redirection vers le hub')
                  setStatus(`Session vérifiée, redirection vers le hub...`)
                  // Forcer un rechargement de page pour que le middleware reconnaisse la session
                  window.location.href = '/hub'
                } else {
                  console.log('❌ Session non trouvée après création')
                  setStatus('Erreur: Session non établie')
                  setTimeout(() => {
                    router.push('/auth/signin?error=session_not_established')
                  }, 2000)
                }
              } catch (error) {
                console.error('Erreur vérification session:', error)
                setStatus('Erreur lors de la vérification')
                setTimeout(() => {
                  router.push('/auth/signin?error=verification_failed')
                }, 2000)
              }
            }

            setTimeout(verifyAndRedirect, 1500)
          } else {
            console.error('Pas de session créée')
            setStatus('Erreur: Impossible de créer la session')
            setTimeout(() => {
              router.push('/auth/signin?error=no_session')
            }, 2000)
          }
        } else {
          console.error('Tokens manquants')
          setStatus('Erreur: Tokens d\'authentification manquants')
          setTimeout(() => {
            router.push('/auth/signin?error=missing_tokens')
          }, 2000)
        }
      } catch (error) {
        console.error('Exception dans le callback:', error)
        setStatus(`Exception: ${error}`)
        setTimeout(() => {
          router.push('/auth/signin?error=callback_exception')
        }, 2000)
      }
    }

    handleAuthCallback()
  }, [router])

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <h1 className="text-xl font-semibold text-gray-900 mb-2">
          Connexion en cours
        </h1>
        <p className="text-gray-600">
          {status}
        </p>
      </div>
    </div>
  )
}
