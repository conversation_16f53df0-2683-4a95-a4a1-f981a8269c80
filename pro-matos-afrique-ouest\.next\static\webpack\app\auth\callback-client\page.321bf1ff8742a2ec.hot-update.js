"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/callback-client/page",{

/***/ "(app-pages-browser)/./src/app/auth/callback-client/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/auth/callback-client/page.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AuthCallbackClient; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction AuthCallbackClient() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Traitement de la connexion...\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleAuthCallback = async ()=>{\n            try {\n                console.log(\"=== CLIENT CALLBACK DEBUG ===\");\n                console.log(\"URL compl\\xe8te:\", window.location.href);\n                console.log(\"Hash:\", window.location.hash);\n                console.log(\"Search:\", window.location.search);\n                // Lire les paramètres du fragment (#)\n                const hashParams = new URLSearchParams(window.location.hash.substring(1));\n                const accessToken = hashParams.get(\"access_token\");\n                const refreshToken = hashParams.get(\"refresh_token\");\n                const tokenType = hashParams.get(\"token_type\");\n                const type = hashParams.get(\"type\");\n                const error = hashParams.get(\"error\");\n                console.log(\"Param\\xe8tres extraits:\", {\n                    accessToken: accessToken ? \"pr\\xe9sent\" : \"absent\",\n                    refreshToken: refreshToken ? \"pr\\xe9sent\" : \"absent\",\n                    tokenType,\n                    type,\n                    error\n                });\n                console.log(\"AccessToken (d\\xe9but):\", accessToken === null || accessToken === void 0 ? void 0 : accessToken.substring(0, 50));\n                console.log(\"RefreshToken:\", refreshToken);\n                if (error) {\n                    console.error(\"Erreur dans l'URL:\", error);\n                    setStatus(\"Erreur: \".concat(error));\n                    setTimeout(()=>{\n                        router.push(\"/auth/signin?error=\" + error);\n                    }, 2000);\n                    return;\n                }\n                if (accessToken && refreshToken) {\n                    setStatus(\"Cr\\xe9ation de la session...\");\n                    // Créer la session avec les tokens\n                    const { data, error: sessionError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.setSession({\n                        access_token: accessToken,\n                        refresh_token: refreshToken\n                    });\n                    if (sessionError) {\n                        console.error(\"Erreur cr\\xe9ation session:\", sessionError);\n                        setStatus(\"Erreur de session: \".concat(sessionError.message));\n                        setTimeout(()=>{\n                            router.push(\"/auth/signin?error=session_error\");\n                        }, 2000);\n                        return;\n                    }\n                    if (data.session) {\n                        console.log(\"✅ Session cr\\xe9\\xe9e avec succ\\xe8s pour:\", data.session.user.email);\n                        setStatus(\"Connexion r\\xe9ussie ! Finalisation...\");\n                        // Nettoyer l'URL\n                        window.history.replaceState({}, document.title, \"/auth/callback-client\");\n                        // Vérifier que la session est bien établie avant de rediriger\n                        const verifyAndRedirect = async ()=>{\n                            try {\n                                const { data: { session: currentSession } } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getSession();\n                                if (currentSession) {\n                                    console.log(\"✅ Session v\\xe9rifi\\xe9e, redirection vers le hub\");\n                                    setStatus(\"Session v\\xe9rifi\\xe9e, redirection vers le hub...\");\n                                    // Forcer un rechargement de page pour que le middleware reconnaisse la session\n                                    window.location.href = \"/hub\";\n                                } else {\n                                    console.log(\"❌ Session non trouv\\xe9e apr\\xe8s cr\\xe9ation\");\n                                    setStatus(\"Erreur: Session non \\xe9tablie\");\n                                    setTimeout(()=>{\n                                        router.push(\"/auth/signin?error=session_not_established\");\n                                    }, 2000);\n                                }\n                            } catch (error) {\n                                console.error(\"Erreur v\\xe9rification session:\", error);\n                                setStatus(\"Erreur lors de la v\\xe9rification\");\n                                setTimeout(()=>{\n                                    router.push(\"/auth/signin?error=verification_failed\");\n                                }, 2000);\n                            }\n                        };\n                        setTimeout(verifyAndRedirect, 1500);\n                    } else {\n                        console.error(\"Pas de session cr\\xe9\\xe9e\");\n                        setStatus(\"Erreur: Impossible de cr\\xe9er la session\");\n                        setTimeout(()=>{\n                            router.push(\"/auth/signin?error=no_session\");\n                        }, 2000);\n                    }\n                } else {\n                    console.error(\"Tokens manquants\");\n                    setStatus(\"Erreur: Tokens d'authentification manquants\");\n                    setTimeout(()=>{\n                        router.push(\"/auth/signin?error=missing_tokens\");\n                    }, 2000);\n                }\n            } catch (error) {\n                console.error(\"Exception dans le callback:\", error);\n                setStatus(\"Exception: \".concat(error));\n                setTimeout(()=>{\n                    router.push(\"/auth/signin?error=callback_exception\");\n                }, 2000);\n            }\n        };\n        handleAuthCallback();\n    }, [\n        router\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full bg-white rounded-lg shadow-md p-6 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\app\\\\auth\\\\callback-client\\\\page.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-xl font-semibold text-gray-900 mb-2\",\n                    children: \"Connexion en cours\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\app\\\\auth\\\\callback-client\\\\page.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600\",\n                    children: status\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\app\\\\auth\\\\callback-client\\\\page.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\app\\\\auth\\\\callback-client\\\\page.tsx\",\n            lineNumber: 126,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\app\\\\auth\\\\callback-client\\\\page.tsx\",\n        lineNumber: 125,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthCallbackClient, \"FweN7jrWa9mpZOYAHNyYrO0QKqg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AuthCallbackClient;\nvar _c;\n$RefreshReg$(_c, \"AuthCallbackClient\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/auth/callback-client/page.tsx\n"));

/***/ })

});