'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Building2, 
  Handshake, 
  TrendingUp,
  DollarSign,
  Users,
  Package,
  Star,
  MapPin,
  Phone,
  Mail,
  Globe,
  Calendar,
  FileText,
  Download,
  Upload,
  Plus,
  Edit,
  Eye,
  BarChart3,
  Target,
  Award
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { toast } from 'sonner'

interface Manufacturer {
  id: string
  name: string
  logo: string
  country: string
  city: string
  website: string
  email: string
  phone: string
  description: string
  categories: string[]
  partnershipLevel: 'bronze' | 'silver' | 'gold' | 'platinum'
  joinDate: string
  status: 'active' | 'pending' | 'suspended'
  metrics: {
    totalProducts: number
    monthlyOrders: number
    revenue: number
    rating: number
    responseTime: number // en heures
  }
  terms: {
    commissionRate: number
    paymentTerms: string
    minimumOrder: number
    exclusivity: boolean
  }
}

interface PartnershipProposal {
  id: string
  manufacturerId: string
  manufacturerName: string
  proposalType: 'new_partnership' | 'upgrade' | 'special_terms'
  title: string
  description: string
  proposedTerms: {
    commissionRate: number
    minimumOrder: number
    exclusivity: boolean
    duration: string
  }
  status: 'pending' | 'approved' | 'rejected' | 'negotiating'
  submittedDate: string
  expectedRevenue: number
}

interface ManufacturerPartnershipsProps {
  className?: string
}

export default function ManufacturerPartnerships({ className = '' }: ManufacturerPartnershipsProps) {
  const [manufacturers, setManufacturers] = useState<Manufacturer[]>([
    {
      id: '1',
      name: 'Schneider Electric',
      logo: '/logos/schneider.png',
      country: 'France',
      city: 'Rueil-Malmaison',
      website: 'https://schneider-electric.com',
      email: '<EMAIL>',
      phone: '+33 1 41 29 70 00',
      description: 'Leader mondial de la transformation numérique de la gestion de l\'énergie et des automatismes',
      categories: ['Disjoncteurs', 'Tableaux électriques', 'Automatismes', 'Onduleurs'],
      partnershipLevel: 'platinum',
      joinDate: '2023-01-15',
      status: 'active',
      metrics: {
        totalProducts: 1250,
        monthlyOrders: 450,
        revenue: 2850000,
        rating: 4.8,
        responseTime: 2
      },
      terms: {
        commissionRate: 12,
        paymentTerms: '30 jours',
        minimumOrder: 50000,
        exclusivity: true
      }
    },
    {
      id: '2',
      name: 'Legrand',
      logo: '/logos/legrand.png',
      country: 'France',
      city: 'Limoges',
      website: 'https://legrand.com',
      email: '<EMAIL>',
      phone: '+33 5 55 06 87 87',
      description: 'Spécialiste mondial des infrastructures électriques et numériques du bâtiment',
      categories: ['Appareillage', 'Prises', 'Interrupteurs', 'Goulottes'],
      partnershipLevel: 'gold',
      joinDate: '2023-03-20',
      status: 'active',
      metrics: {
        totalProducts: 890,
        monthlyOrders: 320,
        revenue: 1950000,
        rating: 4.6,
        responseTime: 3
      },
      terms: {
        commissionRate: 10,
        paymentTerms: '45 jours',
        minimumOrder: 30000,
        exclusivity: false
      }
    },
    {
      id: '3',
      name: 'ABB',
      logo: '/logos/abb.png',
      country: 'Suisse',
      city: 'Zurich',
      website: 'https://abb.com',
      email: '<EMAIL>',
      phone: '+41 43 317 71 11',
      description: 'Technologies pionnières qui écrivent l\'avenir de l\'industrialisation numérique',
      categories: ['Moteurs', 'Variateurs', 'Robotique', 'Transformateurs'],
      partnershipLevel: 'silver',
      joinDate: '2023-06-10',
      status: 'active',
      metrics: {
        totalProducts: 650,
        monthlyOrders: 180,
        revenue: 1200000,
        rating: 4.4,
        responseTime: 4
      },
      terms: {
        commissionRate: 8,
        paymentTerms: '60 jours',
        minimumOrder: 75000,
        exclusivity: false
      }
    },
    {
      id: '4',
      name: 'Siemens',
      logo: '/logos/siemens.png',
      country: 'Allemagne',
      city: 'Munich',
      website: 'https://siemens.com',
      email: '<EMAIL>',
      phone: '+49 89 636 00',
      description: 'Technologie focalisée sur l\'industrie, les infrastructures, la mobilité et la santé',
      categories: ['Automatismes', 'Capteurs', 'Logiciels industriels', 'Sécurité'],
      partnershipLevel: 'gold',
      joinDate: '2023-02-28',
      status: 'pending',
      metrics: {
        totalProducts: 420,
        monthlyOrders: 95,
        revenue: 680000,
        rating: 4.5,
        responseTime: 6
      },
      terms: {
        commissionRate: 9,
        paymentTerms: '30 jours',
        minimumOrder: 100000,
        exclusivity: true
      }
    }
  ])

  const [proposals, setProposals] = useState<PartnershipProposal[]>([
    {
      id: '1',
      manufacturerId: '4',
      manufacturerName: 'Siemens',
      proposalType: 'new_partnership',
      title: 'Partenariat Stratégique Afrique de l\'Ouest',
      description: 'Proposition de partenariat exclusif pour la distribution des solutions d\'automatisme industriel en Afrique de l\'Ouest',
      proposedTerms: {
        commissionRate: 15,
        minimumOrder: 150000,
        exclusivity: true,
        duration: '3 ans'
      },
      status: 'negotiating',
      submittedDate: '2024-01-15',
      expectedRevenue: 5000000
    },
    {
      id: '2',
      manufacturerId: '2',
      manufacturerName: 'Legrand',
      proposalType: 'upgrade',
      title: 'Upgrade vers Partenariat Platinum',
      description: 'Demande d\'upgrade du niveau de partenariat avec conditions préférentielles',
      proposedTerms: {
        commissionRate: 12,
        minimumOrder: 25000,
        exclusivity: true,
        duration: '2 ans'
      },
      status: 'pending',
      submittedDate: '2024-01-20',
      expectedRevenue: 3200000
    }
  ])

  const [showNewPartnerForm, setShowNewPartnerForm] = useState(false)
  const [selectedManufacturer, setSelectedManufacturer] = useState<Manufacturer | null>(null)

  // Calculer les métriques globales
  const totalRevenue = manufacturers.reduce((sum, m) => sum + m.metrics.revenue, 0)
  const totalProducts = manufacturers.reduce((sum, m) => sum + m.metrics.totalProducts, 0)
  const totalOrders = manufacturers.reduce((sum, m) => sum + m.metrics.monthlyOrders, 0)
  const averageRating = manufacturers.reduce((sum, m) => sum + m.metrics.rating, 0) / manufacturers.length

  const getPartnershipLevelColor = (level: string) => {
    switch (level) {
      case 'platinum': return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'gold': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'silver': return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'bronze': return 'bg-orange-100 text-orange-800 border-orange-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 border-green-200'
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'suspended': return 'bg-red-100 text-red-800 border-red-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getProposalStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800 border-green-200'
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'rejected': return 'bg-red-100 text-red-800 border-red-200'
      case 'negotiating': return 'bg-blue-100 text-blue-800 border-blue-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const approveProposal = (proposalId: string) => {
    setProposals(prev => prev.map(p => 
      p.id === proposalId ? { ...p, status: 'approved' as const } : p
    ))
    toast.success('Proposition approuvée')
  }

  const rejectProposal = (proposalId: string) => {
    setProposals(prev => prev.map(p => 
      p.id === proposalId ? { ...p, status: 'rejected' as const } : p
    ))
    toast.success('Proposition rejetée')
  }

  const exportPartnershipData = () => {
    const data = {
      manufacturers,
      proposals,
      metrics: {
        totalRevenue,
        totalProducts,
        totalOrders,
        averageRating
      },
      exportDate: new Date().toISOString()
    }

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `partenariats-fabricants-${new Date().toISOString().split('T')[0]}.json`
    a.click()
    URL.revokeObjectURL(url)

    toast.success('Données exportées avec succès')
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-400 to-blue-500 rounded-lg flex items-center justify-center">
                <Handshake className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle>Partenariats Fabricants</CardTitle>
                <CardDescription>
                  Gestion des relations avec les fabricants et fournisseurs
                </CardDescription>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button onClick={exportPartnershipData} variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Exporter
              </Button>
              <Button onClick={() => setShowNewPartnerForm(true)} size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Nouveau Partenaire
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Métriques globales */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Chiffre d'affaires</p>
                <p className="text-2xl font-bold text-green-600">
                  {(totalRevenue / 1000000).toFixed(1)}M FCFA
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <Package className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Produits</p>
                <p className="text-2xl font-bold text-blue-600">{totalProducts.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                <TrendingUp className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Commandes/mois</p>
                <p className="text-2xl font-bold text-purple-600">{totalOrders.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
                <Star className="h-6 w-6 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Note moyenne</p>
                <p className="text-2xl font-bold text-yellow-600">{averageRating.toFixed(1)}/5</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Contenu principal avec onglets */}
      <Tabs defaultValue="partners" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="partners">Partenaires</TabsTrigger>
          <TabsTrigger value="proposals">Propositions</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        {/* Onglet Partenaires */}
        <TabsContent value="partners" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {manufacturers.map((manufacturer) => (
              <motion.div
                key={manufacturer.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="group"
              >
                <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                          <Building2 className="h-6 w-6 text-gray-600" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                            {manufacturer.name}
                          </h3>
                          <p className="text-sm text-gray-600">
                            {manufacturer.city}, {manufacturer.country}
                          </p>
                        </div>
                      </div>

                      <div className="flex flex-col items-end space-y-1">
                        <Badge className={getPartnershipLevelColor(manufacturer.partnershipLevel)}>
                          {manufacturer.partnershipLevel.toUpperCase()}
                        </Badge>
                        <Badge className={getStatusColor(manufacturer.status)}>
                          {manufacturer.status === 'active' && 'Actif'}
                          {manufacturer.status === 'pending' && 'En attente'}
                          {manufacturer.status === 'suspended' && 'Suspendu'}
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    <p className="text-sm text-gray-600 line-clamp-2">
                      {manufacturer.description}
                    </p>

                    {/* Catégories */}
                    <div>
                      <h5 className="text-sm font-medium text-gray-900 mb-2">Catégories</h5>
                      <div className="flex flex-wrap gap-1">
                        {manufacturer.categories.slice(0, 3).map((category, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {category}
                          </Badge>
                        ))}
                        {manufacturer.categories.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{manufacturer.categories.length - 3}
                          </Badge>
                        )}
                      </div>
                    </div>

                    {/* Métriques */}
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Produits :</span>
                        <span className="ml-2 font-medium">{manufacturer.metrics.totalProducts}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">CA mensuel :</span>
                        <span className="ml-2 font-medium">
                          {(manufacturer.metrics.revenue / 1000000).toFixed(1)}M
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-600">Note :</span>
                        <span className="ml-2 font-medium flex items-center">
                          <Star className="h-3 w-3 text-yellow-500 mr-1" />
                          {manufacturer.metrics.rating}
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-600">Commission :</span>
                        <span className="ml-2 font-medium">{manufacturer.terms.commissionRate}%</span>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex space-x-2 pt-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setSelectedManufacturer(manufacturer)}
                      >
                        <Eye className="h-3 w-3 mr-1" />
                        Détails
                      </Button>
                      <Button variant="outline" size="sm">
                        <Edit className="h-3 w-3 mr-1" />
                        Modifier
                      </Button>
                      <Button variant="outline" size="sm">
                        <BarChart3 className="h-3 w-3 mr-1" />
                        Stats
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>

        {/* Onglet Propositions */}
        <TabsContent value="proposals" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Propositions de Partenariat</CardTitle>
              <CardDescription>
                Gérez les demandes et négociations de partenariat
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {proposals.map((proposal) => (
                  <div key={proposal.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h4 className="font-semibold text-gray-900">{proposal.title}</h4>
                        <p className="text-sm text-gray-600">{proposal.manufacturerName}</p>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Badge className={getProposalStatusColor(proposal.status)}>
                          {proposal.status === 'pending' && 'En attente'}
                          {proposal.status === 'approved' && 'Approuvé'}
                          {proposal.status === 'rejected' && 'Rejeté'}
                          {proposal.status === 'negotiating' && 'Négociation'}
                        </Badge>
                        <Badge variant="outline">
                          {(proposal.expectedRevenue / 1000000).toFixed(1)}M FCFA
                        </Badge>
                      </div>
                    </div>

                    <p className="text-sm text-gray-600 mb-4">{proposal.description}</p>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4 text-sm">
                      <div>
                        <span className="text-gray-600">Commission :</span>
                        <span className="ml-2 font-medium">{proposal.proposedTerms.commissionRate}%</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Commande min :</span>
                        <span className="ml-2 font-medium">
                          {(proposal.proposedTerms.minimumOrder / 1000).toFixed(0)}k FCFA
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-600">Exclusivité :</span>
                        <span className="ml-2 font-medium">
                          {proposal.proposedTerms.exclusivity ? 'Oui' : 'Non'}
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-600">Durée :</span>
                        <span className="ml-2 font-medium">{proposal.proposedTerms.duration}</span>
                      </div>
                    </div>

                    {proposal.status === 'pending' || proposal.status === 'negotiating' ? (
                      <div className="flex space-x-2">
                        <Button
                          size="sm"
                          onClick={() => approveProposal(proposal.id)}
                          className="bg-green-500 hover:bg-green-600"
                        >
                          Approuver
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => rejectProposal(proposal.id)}
                        >
                          Rejeter
                        </Button>
                        <Button variant="outline" size="sm">
                          Négocier
                        </Button>
                      </div>
                    ) : (
                      <div className="text-sm text-gray-500">
                        Traité le {new Date(proposal.submittedDate).toLocaleDateString('fr-FR')}
                      </div>
                    )}
                  </div>
                ))}

                {proposals.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <FileText className="h-12 w-12 mx-auto mb-3 text-gray-300" />
                    <p>Aucune proposition en cours</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Onglet Analytics */}
        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Performance par partenaire */}
            <Card>
              <CardHeader>
                <CardTitle>Performance par Partenaire</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {manufacturers.map((manufacturer) => (
                    <div key={manufacturer.id} className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="font-medium">{manufacturer.name}</span>
                        <span className="text-gray-600">
                          {(manufacturer.metrics.revenue / 1000000).toFixed(1)}M FCFA
                        </span>
                      </div>
                      <Progress
                        value={(manufacturer.metrics.revenue / totalRevenue) * 100}
                        className="h-2"
                      />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Répartition par niveau */}
            <Card>
              <CardHeader>
                <CardTitle>Répartition par Niveau</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {['platinum', 'gold', 'silver', 'bronze'].map((level) => {
                    const count = manufacturers.filter(m => m.partnershipLevel === level).length
                    const percentage = (count / manufacturers.length) * 100

                    return (
                      <div key={level} className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <span className="font-medium capitalize">{level}</span>
                          <span className="text-gray-600">{count} partenaires</span>
                        </div>
                        <Progress value={percentage} className="h-2" />
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Modal détails fabricant */}
      <AnimatePresence>
        {selectedManufacturer && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
            >
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-bold text-gray-900">
                    Détails du Partenaire
                  </h2>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedManufacturer(null)}
                  >
                    ×
                  </Button>
                </div>

                <div className="space-y-6">
                  {/* Informations générales */}
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-3">Informations Générales</h3>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Nom :</span>
                        <span className="ml-2 font-medium">{selectedManufacturer.name}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Pays :</span>
                        <span className="ml-2 font-medium">{selectedManufacturer.country}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Ville :</span>
                        <span className="ml-2 font-medium">{selectedManufacturer.city}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Membre depuis :</span>
                        <span className="ml-2 font-medium">
                          {new Date(selectedManufacturer.joinDate).toLocaleDateString('fr-FR')}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Contact */}
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-3">Contact</h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center space-x-2">
                        <Mail className="h-4 w-4 text-gray-500" />
                        <span>{selectedManufacturer.email}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Phone className="h-4 w-4 text-gray-500" />
                        <span>{selectedManufacturer.phone}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Globe className="h-4 w-4 text-gray-500" />
                        <a
                          href={selectedManufacturer.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:underline"
                        >
                          {selectedManufacturer.website}
                        </a>
                      </div>
                    </div>
                  </div>

                  {/* Métriques détaillées */}
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-3">Métriques</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="bg-gray-50 p-3 rounded-lg">
                        <div className="text-2xl font-bold text-blue-600">
                          {selectedManufacturer.metrics.totalProducts}
                        </div>
                        <div className="text-sm text-gray-600">Produits</div>
                      </div>
                      <div className="bg-gray-50 p-3 rounded-lg">
                        <div className="text-2xl font-bold text-green-600">
                          {selectedManufacturer.metrics.monthlyOrders}
                        </div>
                        <div className="text-sm text-gray-600">Commandes/mois</div>
                      </div>
                      <div className="bg-gray-50 p-3 rounded-lg">
                        <div className="text-2xl font-bold text-purple-600">
                          {(selectedManufacturer.metrics.revenue / 1000000).toFixed(1)}M
                        </div>
                        <div className="text-sm text-gray-600">CA (FCFA)</div>
                      </div>
                      <div className="bg-gray-50 p-3 rounded-lg">
                        <div className="text-2xl font-bold text-yellow-600">
                          {selectedManufacturer.metrics.rating}/5
                        </div>
                        <div className="text-sm text-gray-600">Note</div>
                      </div>
                    </div>
                  </div>

                  {/* Conditions commerciales */}
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-3">Conditions Commerciales</h3>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Commission :</span>
                        <span className="ml-2 font-medium">{selectedManufacturer.terms.commissionRate}%</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Paiement :</span>
                        <span className="ml-2 font-medium">{selectedManufacturer.terms.paymentTerms}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Commande minimum :</span>
                        <span className="ml-2 font-medium">
                          {(selectedManufacturer.terms.minimumOrder / 1000).toFixed(0)}k FCFA
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-600">Exclusivité :</span>
                        <span className="ml-2 font-medium">
                          {selectedManufacturer.terms.exclusivity ? 'Oui' : 'Non'}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
