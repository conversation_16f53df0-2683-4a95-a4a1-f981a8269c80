'use client'

import { motion } from 'framer-motion'
import { 
  ArrowLeft,
  Shield,
  Users,
  Award,
  Zap,
  FileCheck,
  MessageSquare,
  BookOpen,
  TrendingUp
} from 'lucide-react'
import Link from 'next/link'
import ExpertSpace from '@/components/expert/ExpertSpace'
import GlobalNavigation from '@/components/navigation/GlobalNavigation'
import ResponsiveLayout from '@/components/layout/ResponsiveLayout'
import EcosystemHub from '@/components/integration/EcosystemHub'
import ExpertChat from '@/components/expert/ExpertChat'


export default function ExpertPage() {
  const stats = [
    {
      label: 'Documents Validés',
      value: '1,247',
      change: '+23',
      changeType: 'increase',
      icon: <FileCheck className="h-6 w-6" />
    },
    {
      label: 'Vérifications Compatibilité',
      value: '892',
      change: '+15',
      changeType: 'increase',
      icon: <Zap className="h-6 w-6" />
    },
    {
      label: 'Consultations Résolues',
      value: '456',
      change: '+8',
      changeType: 'increase',
      icon: <MessageSquare className="h-6 w-6" />
    },
    {
      label: 'Ressources Disponibles',
      value: '234',
      change: '+12',
      changeType: 'increase',
      icon: <BookOpen className="h-6 w-6" />
    }
  ]

  const expertiseAreas = [
    {
      title: 'Validation Technique',
      description: 'Upload et validation de vos documents techniques par nos experts certifiés',
      icon: <Shield className="h-8 w-8" />,
      features: ['Analyse de conformité', 'Recommandations d\'amélioration', 'Certification officielle'],
      color: 'from-blue-500 to-blue-600'
    },
    {
      title: 'Compatibilité Produits',
      description: 'Vérification automatique et experte de la compatibilité entre composants',
      icon: <Zap className="h-8 w-8" />,
      features: ['Simulation intelligente', 'Alertes de sécurité', 'Recommandations alternatives'],
      color: 'from-green-500 to-green-600'
    },
    {
      title: 'Consultation Expert',
      description: 'Accès direct à nos experts pour conseils personnalisés en temps réel',
      icon: <Users className="h-8 w-8" />,
      features: ['Chat en direct', 'Visioconférence', 'Rapports détaillés'],
      color: 'from-purple-500 to-purple-600'
    },
    {
      title: 'Bibliothèque Technique',
      description: 'Ressources exclusives, tutoriels et formations selon votre niveau',
      icon: <BookOpen className="h-8 w-8" />,
      features: ['Guides détaillés', 'Vidéos tutoriels', 'Templates professionnels'],
      color: 'from-amber-500 to-amber-600'
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-md border-b border-slate-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link 
                href="/"
                className="flex items-center space-x-2 text-slate-700 hover:text-amber-600 transition-colors"
              >
                <ArrowLeft className="h-5 w-5" />
                <span>Retour à l'accueil</span>
              </Link>
            </div>
            
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-amber-400 to-amber-500 rounded-lg flex items-center justify-center">
                <Shield className="h-5 w-5 text-slate-900" />
              </div>
              <div>
                <h1 className="text-lg font-bold text-slate-900">Pro Matos</h1>
                <p className="text-xs text-slate-600">Conseil Technique Expert</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Contenu principal */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Titre et description */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-3xl font-bold text-slate-900 mb-2">
            Espace Conseil Technique Expert
          </h1>
          <p className="text-lg text-slate-600">
            Votre expertise technique validée par les meilleurs spécialistes d'Afrique de l'Ouest
          </p>
        </motion.div>

        {/* Statistiques rapides */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
        >
          {stats.map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 + index * 0.05 }}
              className="industrial-card p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">{stat.label}</p>
                  <div className="flex items-baseline space-x-2">
                    <p className="text-2xl font-bold text-slate-900">{stat.value}</p>
                    <span className={`text-sm font-medium ${
                      stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {stat.change}
                    </span>
                  </div>
                </div>
                <div className="p-3 bg-amber-100 rounded-lg text-amber-600">
                  {stat.icon}
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Domaines d'expertise */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mb-8"
        >
          <h2 className="text-2xl font-bold text-slate-900 mb-6">Nos Domaines d'Expertise</h2>
          <div className="grid md:grid-cols-2 gap-6">
            {expertiseAreas.map((area, index) => (
              <motion.div
                key={area.title}
                initial={{ opacity: 0, x: index % 2 === 0 ? -20 : 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3 + index * 0.1 }}
                className="industrial-card p-6"
              >
                <div className={`w-16 h-16 bg-gradient-to-r ${area.color} rounded-lg flex items-center justify-center text-white mb-4`}>
                  {area.icon}
                </div>
                <h3 className="text-xl font-bold text-slate-900 mb-2">{area.title}</h3>
                <p className="text-slate-600 mb-4">{area.description}</p>
                <ul className="space-y-2">
                  {area.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center space-x-2 text-sm text-slate-700">
                      <div className="w-1.5 h-1.5 bg-amber-500 rounded-full"></div>
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Intégration écosystème */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <EcosystemHub currentModule="expert" />
        </motion.div>

        {/* Espace Expert principal */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <ExpertSpace />
        </motion.div>

        {/* Générateur de rapports techniques */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="bg-white rounded-lg shadow-lg p-6"
        >
          <h3 className="text-xl font-semibold text-gray-900 mb-4">
            Générateur de Rapports Techniques
          </h3>
          <p className="text-gray-600">
            Fonctionnalité en cours de développement. Bientôt disponible pour générer des rapports techniques professionnels.
          </p>
        </motion.div>

        {/* Section avantages membres */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="mt-8 bg-gradient-to-r from-amber-50 to-yellow-50 rounded-lg p-6 border border-amber-200"
        >
          <div className="flex items-center space-x-4 mb-4">
            <Award className="h-8 w-8 text-amber-600" />
            <h3 className="text-xl font-bold text-amber-900">Avantages Membres Exclusifs</h3>
          </div>
          <div className="grid md:grid-cols-3 gap-6">
            <div>
              <h4 className="font-semibold text-amber-800 mb-2">🥉 Bronze</h4>
              <ul className="text-sm text-amber-700 space-y-1">
                <li>• Validation documents basique</li>
                <li>• Ressources publiques</li>
                <li>• Support par email</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-amber-800 mb-2">🥈 Silver</h4>
              <ul className="text-sm text-amber-700 space-y-1">
                <li>• Validation prioritaire</li>
                <li>• Simulateur compatibilité</li>
                <li>• Chat expert (limité)</li>
                <li>• Guides techniques avancés</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-amber-800 mb-2">🥇 Gold & Platinum</h4>
              <ul className="text-sm text-amber-700 space-y-1">
                <li>• Validation express 24h</li>
                <li>• Consultation illimitée</li>
                <li>• Rapports certifiés</li>
                <li>• Formations exclusives</li>
                <li>• Support téléphonique</li>
              </ul>
            </div>
          </div>
        </motion.div>
      </main>

      {/* Chat Expert */}
      <ExpertChat />
    </div>
  )
}
