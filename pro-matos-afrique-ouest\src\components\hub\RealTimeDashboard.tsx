'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Activity,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  Package,
  Users,
  Clock,
  Zap,
  BarChart3,
  PieChart
} from 'lucide-react'
import { useStore } from '@/store/useStore'

interface MetricData {
  id: string
  label: string
  value: number
  previousValue: number
  unit: string
  icon: React.ReactNode
  color: string
  trend: 'up' | 'down' | 'stable'
  changePercentage: number
}

export default function RealTimeDashboard() {
  const { marketAlerts, stockUpdates, realTimeConnected } = useStore()
  const [metrics, setMetrics] = useState<MetricData[]>([])
  const [lastUpdate, setLastUpdate] = useState(new Date())

  // Simulation des métriques temps réel
  useEffect(() => {
    const generateMetrics = (): MetricData[] => {
      const baseMetrics = [
        {
          id: 'active_alerts',
          label: 'Alertes Actives',
          value: marketAlerts.filter(a => a.is_active).length,
          previousValue: Math.max(0, marketAlerts.filter(a => a.is_active).length - Math.floor(Math.random() * 3)),
          unit: '',
          icon: <AlertTriangle className="h-5 w-5" />,
          color: 'text-red-600',
          trend: 'up' as const,
          changePercentage: 0
        },
        {
          id: 'stock_movements',
          label: 'Mouvements Stock',
          value: stockUpdates.length,
          previousValue: Math.max(0, stockUpdates.length - Math.floor(Math.random() * 5)),
          unit: '',
          icon: <Package className="h-5 w-5" />,
          color: 'text-blue-600',
          trend: 'up' as const,
          changePercentage: 0
        },
        {
          id: 'market_activity',
          label: 'Activité Marché',
          value: Math.floor(Math.random() * 100) + 50,
          previousValue: Math.floor(Math.random() * 100) + 40,
          unit: '%',
          icon: <Activity className="h-5 w-5" />,
          color: 'text-green-600',
          trend: 'up' as const,
          changePercentage: 0
        },
        {
          id: 'connected_suppliers',
          label: 'Fournisseurs Connectés',
          value: Math.floor(Math.random() * 10) + 45,
          previousValue: Math.floor(Math.random() * 10) + 40,
          unit: '',
          icon: <Users className="h-5 w-5" />,
          color: 'text-purple-600',
          trend: 'stable' as const,
          changePercentage: 0
        },
        {
          id: 'response_time',
          label: 'Temps de Réponse',
          value: Math.floor(Math.random() * 50) + 150,
          previousValue: Math.floor(Math.random() * 50) + 160,
          unit: 'ms',
          icon: <Clock className="h-5 w-5" />,
          color: 'text-amber-600',
          trend: 'down' as const,
          changePercentage: 0
        },
        {
          id: 'data_throughput',
          label: 'Débit Données',
          value: Math.floor(Math.random() * 500) + 1200,
          previousValue: Math.floor(Math.random() * 500) + 1100,
          unit: 'KB/s',
          icon: <Zap className="h-5 w-5" />,
          color: 'text-indigo-600',
          trend: 'up' as const,
          changePercentage: 0
        }
      ]

      // Calculer les tendances et pourcentages de changement
      return baseMetrics.map(metric => {
        const change = metric.value - metric.previousValue
        const changePercentage = metric.previousValue > 0 
          ? Math.round((change / metric.previousValue) * 100) 
          : 0

        let trend: 'up' | 'down' | 'stable' = 'stable'
        if (Math.abs(changePercentage) > 2) {
          trend = changePercentage > 0 ? 'up' : 'down'
        }

        return {
          ...metric,
          trend,
          changePercentage: Math.abs(changePercentage)
        }
      })
    }

    const updateMetrics = () => {
      setMetrics(generateMetrics())
      setLastUpdate(new Date())
    }

    // Mise à jour initiale
    updateMetrics()

    // Mise à jour toutes les 10 secondes si connecté
    let interval: NodeJS.Timeout
    if (realTimeConnected) {
      interval = setInterval(updateMetrics, 10000)
    }

    return () => {
      if (interval) clearInterval(interval)
    }
  }, [marketAlerts, stockUpdates, realTimeConnected])

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-600" />
      case 'down':
        return <TrendingDown className="h-4 w-4 text-red-600" />
      default:
        return <BarChart3 className="h-4 w-4 text-gray-600" />
    }
  }

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'up':
        return 'text-green-600'
      case 'down':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Tableau de Bord Temps Réel</h3>
          <p className="text-sm text-gray-600">Métriques système et marché</p>
        </div>
        
        <div className="flex items-center space-x-2">
          <div className={`w-2 h-2 rounded-full ${realTimeConnected ? 'bg-green-500 animate-pulse' : 'bg-red-500'}`}></div>
          <span className="text-sm text-gray-600">
            Mis à jour: {lastUpdate.toLocaleTimeString('fr-FR')}
          </span>
        </div>
      </div>

      {/* Métriques Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {metrics.map((metric, index) => (
          <motion.div
            key={metric.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg p-4 border border-gray-200"
          >
            <div className="flex items-center justify-between mb-3">
              <div className={`p-2 rounded-lg bg-white ${metric.color}`}>
                {metric.icon}
              </div>
              <div className="flex items-center space-x-1">
                {getTrendIcon(metric.trend)}
                <span className={`text-sm font-medium ${getTrendColor(metric.trend)}`}>
                  {metric.changePercentage > 0 && `${metric.changePercentage}%`}
                </span>
              </div>
            </div>
            
            <div className="mb-2">
              <div className="text-2xl font-bold text-gray-900">
                {metric.value.toLocaleString()}{metric.unit}
              </div>
              <div className="text-sm text-gray-600">{metric.label}</div>
            </div>
            
            <div className="text-xs text-gray-500">
              Précédent: {metric.previousValue.toLocaleString()}{metric.unit}
            </div>
          </motion.div>
        ))}
      </div>

      {/* Graphique d'activité simplifié */}
      <div className="mt-8 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
        <div className="flex items-center justify-between mb-4">
          <h4 className="font-semibold text-blue-900">Activité des 24 dernières heures</h4>
          <PieChart className="h-5 w-5 text-blue-600" />
        </div>
        
        <div className="grid grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-lg font-bold text-blue-900">
              {marketAlerts.filter(a => a.severity === 'critical').length}
            </div>
            <div className="text-xs text-blue-700">Alertes Critiques</div>
          </div>
          <div>
            <div className="text-lg font-bold text-blue-900">
              {stockUpdates.filter(u => u.update_type === 'out_of_stock').length}
            </div>
            <div className="text-xs text-blue-700">Ruptures Stock</div>
          </div>
          <div>
            <div className="text-lg font-bold text-blue-900">
              {stockUpdates.filter(u => u.update_type === 'restock').length}
            </div>
            <div className="text-xs text-blue-700">Réappros</div>
          </div>
          <div>
            <div className="text-lg font-bold text-blue-900">
              {Math.floor(Math.random() * 20) + 80}%
            </div>
            <div className="text-xs text-blue-700">Disponibilité</div>
          </div>
        </div>
      </div>
    </div>
  )
}
