'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  DollarSign, 
  Handshake, 
  Database, 
  BarChart3,
  ArrowLeft,
  TrendingUp,
  Users,
  Globe,
  Target
} from 'lucide-react'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import ManufacturerPartnerships from '@/components/monetization/ManufacturerPartnerships'
import MarketDataExport from '@/components/monetization/MarketDataExport'
import AdvancedMetrics from '@/components/monetization/AdvancedMetrics'
import GlobalNavigation from '@/components/navigation/GlobalNavigation'
import ResponsiveLayout from '@/components/layout/ResponsiveLayout'

export default function MonetizationPage() {
  const monetizationFeatures = [
    {
      id: 'partnerships',
      title: 'Partenariats Fabricants',
      description: 'Gestion des relations avec les fabricants et négociation des conditions commerciales',
      icon: <Handshake className="h-8 w-8" />,
      color: 'from-blue-400 to-blue-500',
      metrics: {
        partners: 12,
        revenue: '6.8M FCFA',
        growth: '+18.5%'
      }
    },
    {
      id: 'data-export',
      title: 'Export de Données Market',
      description: 'Monétisation des données de marché et insights business pour les partenaires',
      icon: <Database className="h-8 w-8" />,
      color: 'from-green-400 to-green-500',
      metrics: {
        datasets: 15,
        downloads: '1.2k',
        revenue: '2.1M FCFA'
      }
    },
    {
      id: 'analytics',
      title: 'Métriques Avancées',
      description: 'Analytics détaillées pour optimiser les performances et la rentabilité',
      icon: <BarChart3 className="h-8 w-8" />,
      color: 'from-purple-400 to-purple-500',
      metrics: {
        kpis: 25,
        regions: 5,
        accuracy: '98.5%'
      }
    }
  ]

  const overallMetrics = {
    totalRevenue: 12500000,
    monthlyGrowth: 15.7,
    activePartners: 12,
    dataRevenue: 2100000,
    conversionRate: 3.2,
    userGrowth: 7.1
  }

  return (
    <ResponsiveLayout>
      <GlobalNavigation />
      
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
        {/* Header */}
        <div className="bg-white border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Link href="/hub">
                  <Button variant="ghost" size="sm">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Retour au Hub
                  </Button>
                </Link>
                
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-gradient-to-r from-green-400 to-green-500 rounded-lg flex items-center justify-center">
                    <DollarSign className="h-7 w-7 text-white" />
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold text-gray-900">Monétisation & Analytics</h1>
                    <p className="text-gray-600">Optimisation des revenus et insights business</p>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <Badge className="bg-green-100 text-green-800 border-green-200">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +{overallMetrics.monthlyGrowth}% ce mois
                </Badge>
                <Badge className="bg-blue-100 text-blue-800 border-blue-200">
                  <Target className="h-3 w-3 mr-1" />
                  {overallMetrics.conversionRate}% conversion
                </Badge>
              </div>
            </div>
          </div>
        </div>

        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Métriques globales */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
          >
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <DollarSign className="h-6 w-6 text-green-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Revenus Totaux</p>
                    <p className="text-2xl font-bold text-green-600">
                      {(overallMetrics.totalRevenue / 1000000).toFixed(1)}M FCFA
                    </p>
                    <div className="flex items-center text-xs text-green-600">
                      <TrendingUp className="h-3 w-3 mr-1" />
                      +{overallMetrics.monthlyGrowth}%
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Handshake className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Partenaires Actifs</p>
                    <p className="text-2xl font-bold text-blue-600">{overallMetrics.activePartners}</p>
                    <div className="flex items-center text-xs text-blue-600">
                      <TrendingUp className="h-3 w-3 mr-1" />
                      +2 ce mois
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <Database className="h-6 w-6 text-purple-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Revenus Data</p>
                    <p className="text-2xl font-bold text-purple-600">
                      {(overallMetrics.dataRevenue / 1000000).toFixed(1)}M FCFA
                    </p>
                    <div className="flex items-center text-xs text-purple-600">
                      <TrendingUp className="h-3 w-3 mr-1" />
                      +22.3%
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                    <Users className="h-6 w-6 text-orange-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Croissance Utilisateurs</p>
                    <p className="text-2xl font-bold text-orange-600">+{overallMetrics.userGrowth}%</p>
                    <div className="flex items-center text-xs text-orange-600">
                      <TrendingUp className="h-3 w-3 mr-1" />
                      8.4k nouveaux
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Vue d'ensemble des fonctionnalités */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8"
          >
            {monetizationFeatures.map((feature, index) => (
              <motion.div
                key={feature.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 * index }}
              >
                <Card className="hover:shadow-lg transition-shadow cursor-pointer group">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className={`w-12 h-12 bg-gradient-to-r ${feature.color} rounded-lg flex items-center justify-center text-white group-hover:scale-110 transition-transform`}>
                        {feature.icon}
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium text-gray-900">
                          {Object.values(feature.metrics)[0]}
                        </div>
                        <div className="text-xs text-gray-600">
                          {Object.keys(feature.metrics)[0]}
                        </div>
                      </div>
                    </div>
                    <CardTitle className="group-hover:text-green-600 transition-colors">
                      {feature.title}
                    </CardTitle>
                    <CardDescription>{feature.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-3 gap-2 text-sm">
                      {Object.entries(feature.metrics).map(([key, value], idx) => (
                        <div key={key} className="text-center">
                          <div className="font-bold text-gray-900">{value}</div>
                          <div className="text-xs text-gray-600 capitalize">{key}</div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>

          {/* Contenu principal avec onglets */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <Tabs defaultValue="partnerships" className="space-y-6">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="partnerships" className="flex items-center space-x-2">
                  <Handshake className="h-4 w-4" />
                  <span>Partenariats</span>
                </TabsTrigger>
                <TabsTrigger value="data-export" className="flex items-center space-x-2">
                  <Database className="h-4 w-4" />
                  <span>Export Data</span>
                </TabsTrigger>
                <TabsTrigger value="analytics" className="flex items-center space-x-2">
                  <BarChart3 className="h-4 w-4" />
                  <span>Analytics</span>
                </TabsTrigger>
              </TabsList>

              {/* Partenariats Fabricants */}
              <TabsContent value="partnerships" className="space-y-6">
                <ManufacturerPartnerships />
              </TabsContent>

              {/* Export de Données Market */}
              <TabsContent value="data-export" className="space-y-6">
                <MarketDataExport />
              </TabsContent>

              {/* Métriques Avancées */}
              <TabsContent value="analytics" className="space-y-6">
                <AdvancedMetrics />
              </TabsContent>
            </Tabs>
          </motion.div>

          {/* Informations stratégiques */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="mt-8"
          >
            <Card className="bg-gradient-to-r from-green-50 to-blue-50 border-green-200">
              <CardContent className="p-6">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Target className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-green-900 mb-2">
                      Stratégie de Monétisation 2024
                    </h3>
                    <p className="text-green-700 mb-4">
                      Pro Matos Afrique Ouest développe un écosystème de revenus diversifiés : 
                      partenariats fabricants, monétisation des données de marché, services premium 
                      et solutions B2B. Notre approche data-driven optimise chaque source de revenus.
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-green-800">Commissions partenaires : 8-15%</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span className="text-green-800">API Data : 25k-100k FCFA/mois</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                        <span className="text-green-800">Abonnements Premium : 50k FCFA/an</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                        <span className="text-green-800">Services consulting : 200k FCFA/jour</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </main>
      </div>
    </ResponsiveLayout>
  )
}
