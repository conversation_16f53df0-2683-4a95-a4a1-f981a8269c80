import { 
  TechnicalDocument, 
  CompatibilityCheck, 
  ExpertConsultation, 
  ExpertProfile, 
  TechnicalResource, 
  TechnicalReport,
  Product 
} from '@/lib/supabase'

// Service pour l'Espace Conseil Technique Expert
export class ExpertService {
  
  // Simulation des experts disponibles
  static generateExpertProfiles(): ExpertProfile[] {
    return [
      {
        id: 'expert_001',
        user_id: 'user_expert_001',
        display_name: 'Ing. <PERSON><PERSON><PERSON>',
        title: 'Ingénieur Électricien Senior',
        company: 'Schneider Electric Afrique',
        specialties: ['Installations BT', 'Protection électrique', 'Normes NF C 15-100'],
        certifications: ['Schneider Electric Certified', 'IEC 61439 Expert', 'NF C 15-100'],
        experience_years: 15,
        rating: 4.9,
        total_consultations: 247,
        hourly_rate: 75000, // 75,000 FCFA/heure
        availability_status: 'available',
        languages: ['Français', 'Anglais'],
        bio: 'Expert en installations électriques basse tension avec 15 ans d\'expérience chez Schneider Electric. Spécialisé dans la mise en conformité selon les normes internationales.',
        profile_image: '/images/experts/kouassi-yao.jpg',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        id: 'expert_002',
        user_id: 'user_expert_002',
        display_name: 'Dr. Aminata Traoré',
        title: 'Experte Énergie Renouvelable',
        company: 'SolarTech Solutions',
        specialties: ['Photovoltaïque', 'Stockage énergie', 'Micro-réseaux'],
        certifications: ['PV System Design', 'Battery Storage Expert', 'Grid Integration'],
        experience_years: 12,
        rating: 4.8,
        total_consultations: 189,
        hourly_rate: 85000, // 85,000 FCFA/heure
        availability_status: 'available',
        languages: ['Français', 'Anglais', 'Bambara'],
        bio: 'Docteure en énergies renouvelables, spécialisée dans les systèmes photovoltaïques et le stockage d\'énergie pour l\'Afrique de l\'Ouest.',
        profile_image: '/images/experts/aminata-traore.jpg',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        id: 'expert_003',
        user_id: 'user_expert_003',
        display_name: 'Ing. Jean-Baptiste Kone',
        title: 'Spécialiste Automatismes Industriels',
        company: 'ABB Côte d\'Ivoire',
        specialties: ['Automatismes', 'Variateurs de vitesse', 'Supervision SCADA'],
        certifications: ['ABB Certified', 'Siemens TIA Portal', 'Schneider Unity Pro'],
        experience_years: 18,
        rating: 4.7,
        total_consultations: 312,
        hourly_rate: 90000, // 90,000 FCFA/heure
        availability_status: 'busy',
        languages: ['Français', 'Anglais'],
        bio: 'Ingénieur spécialisé en automatismes industriels avec une expertise approfondie des systèmes de contrôle et supervision.',
        profile_image: '/images/experts/jean-baptiste-kone.jpg',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }
    ]
  }

  // Simulation des ressources techniques
  static generateTechnicalResources(): TechnicalResource[] {
    return [
      {
        id: 'resource_001',
        title: 'Guide d\'Installation NF C 15-100',
        description: 'Guide complet pour les installations électriques selon la norme NF C 15-100',
        type: 'guide',
        category: 'Installation',
        difficulty_level: 'intermediate',
        duration_minutes: 45,
        file_url: '/resources/guide-nfc15100.pdf',
        thumbnail_url: '/images/resources/nfc15100-thumb.jpg',
        tags: ['norme', 'installation', 'basse tension'],
        author: 'Ing. Kouassi Yao',
        views_count: 1247,
        downloads_count: 892,
        rating: 4.8,
        membership_required: 'silver',
        is_featured: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        id: 'resource_002',
        title: 'Tutoriel Dimensionnement Photovoltaïque',
        description: 'Méthode de calcul pour dimensionner une installation photovoltaïque',
        type: 'tutorial',
        category: 'Énergie Renouvelable',
        difficulty_level: 'advanced',
        duration_minutes: 60,
        video_url: '/videos/dimensionnement-pv.mp4',
        thumbnail_url: '/images/resources/pv-sizing-thumb.jpg',
        tags: ['photovoltaïque', 'dimensionnement', 'calcul'],
        author: 'Dr. Aminata Traoré',
        views_count: 2156,
        downloads_count: 0,
        rating: 4.9,
        membership_required: 'gold',
        is_featured: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        id: 'resource_003',
        title: 'Template Rapport de Conformité',
        description: 'Modèle de rapport de conformité pour installations électriques',
        type: 'template',
        category: 'Documentation',
        difficulty_level: 'beginner',
        file_url: '/templates/rapport-conformite.docx',
        thumbnail_url: '/images/resources/template-thumb.jpg',
        tags: ['template', 'conformité', 'rapport'],
        author: 'Pro Matos Team',
        views_count: 3421,
        downloads_count: 2156,
        rating: 4.6,
        membership_required: 'bronze',
        is_featured: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }
    ]
  }

  // Simulation des vérifications de compatibilité
  static generateCompatibilityChecks(): CompatibilityCheck[] {
    return [
      {
        id: 'compat_001',
        user_id: 'user_001',
        primary_product_id: 'prod_001',
        secondary_product_id: 'prod_002',
        compatibility_status: 'compatible',
        compatibility_score: 95,
        notes: 'Parfaitement compatible. Section de câble adaptée au courant nominal du disjoncteur.',
        conditions: ['Respecter la longueur maximale de 50m', 'Utiliser des bornes adaptées'],
        warnings: [],
        recommendations: ['Prévoir une protection différentielle en amont'],
        verified_by: 'expert_001',
        created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // Il y a 2 heures
        updated_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      },
      {
        id: 'compat_002',
        user_id: 'user_002',
        primary_product_id: 'prod_003',
        secondary_product_id: 'prod_004',
        compatibility_status: 'conditional',
        compatibility_score: 75,
        notes: 'Compatible sous certaines conditions. Vérifier la tension nominale.',
        conditions: ['Tension d\'alimentation 400V obligatoire', 'Température ambiante < 40°C'],
        warnings: ['Risque de surchauffe si mal ventilé'],
        recommendations: ['Installer un système de ventilation forcée', 'Prévoir un contrôle de température'],
        verified_by: 'expert_002',
        created_at: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // Il y a 4 heures
        updated_at: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
      }
    ]
  }

  // Simulation des consultations d'experts
  static generateExpertConsultations(): ExpertConsultation[] {
    return [
      {
        id: 'consult_001',
        user_id: 'user_001',
        expert_id: 'expert_001',
        title: 'Validation schéma électrique industriel',
        description: 'Besoin de validation d\'un schéma électrique pour une installation industrielle de 400kVA',
        category: 'design',
        priority: 'high',
        status: 'in_progress',
        attachments: ['/uploads/schema-industriel.pdf', '/uploads/liste-materiel.xlsx'],
        estimated_duration: 120, // 2 heures
        actual_duration: 90,
        cost: 150000, // 150,000 FCFA
        created_at: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(), // Il y a 3 heures
        updated_at: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(), // Il y a 1 heure
      },
      {
        id: 'consult_002',
        user_id: 'user_003',
        expert_id: 'expert_002',
        title: 'Dimensionnement installation solaire',
        description: 'Aide pour dimensionner une installation photovoltaïque de 50kWc avec stockage',
        category: 'technical',
        priority: 'medium',
        status: 'resolved',
        attachments: ['/uploads/consommation-electrique.pdf'],
        estimated_duration: 90,
        actual_duration: 85,
        cost: 127500, // 127,500 FCFA
        created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // Il y a 24 heures
        updated_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // Il y a 2 heures
        resolved_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      }
    ]
  }

  // Simulation des documents techniques
  static generateTechnicalDocuments(): TechnicalDocument[] {
    return [
      {
        id: 'doc_001',
        user_id: 'user_001',
        title: 'Schéma unifilaire installation industrielle',
        description: 'Schéma électrique unifilaire pour usine textile 2MW',
        file_url: '/uploads/schema-unifilaire-textile.pdf',
        file_type: 'pdf',
        file_size: 2456789, // ~2.5MB
        validation_status: 'approved',
        validation_notes: 'Schéma conforme aux normes. Quelques recommandations d\'amélioration ajoutées.',
        validated_by: 'expert_001',
        validation_date: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
        category: 'design',
        tags: ['schéma', 'industriel', 'textile', '2MW'],
        is_public: false,
        created_at: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
      },
      {
        id: 'doc_002',
        user_id: 'user_002',
        title: 'Plan d\'installation photovoltaïque',
        description: 'Plans détaillés pour installation PV 100kWc sur toiture industrielle',
        file_url: '/uploads/plan-pv-100kwc.dwg',
        file_type: 'cad',
        file_size: 5234567, // ~5.2MB
        validation_status: 'in_review',
        validation_notes: 'En cours de révision par l\'expert énergie renouvelable',
        category: 'installation',
        tags: ['photovoltaïque', 'toiture', '100kWc', 'industriel'],
        is_public: false,
        created_at: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
      }
    ]
  }

  // Méthode pour vérifier la compatibilité entre deux produits
  static async checkCompatibility(productA: Product, productB: Product): Promise<CompatibilityCheck> {
    // Simulation d'une vérification de compatibilité intelligente
    const compatibilityRules = [
      {
        condition: (a: Product, b: Product) => 
          a.category === 'Protection électrique' && b.category === 'Câblage',
        score: 90,
        status: 'compatible' as const,
        notes: 'Disjoncteur et câble sont compatibles'
      },
      {
        condition: (a: Product, b: Product) => 
          a.category === 'Énergie renouvelable' && b.category === 'Stockage',
        score: 85,
        status: 'compatible' as const,
        notes: 'Système solaire et batterie compatibles'
      }
    ]

    const rule = compatibilityRules.find(r => r.condition(productA, productB))
    
    return {
      id: `compat_${Date.now()}`,
      user_id: 'current_user',
      primary_product_id: productA.id,
      secondary_product_id: productB.id,
      compatibility_status: rule?.status || 'unknown',
      compatibility_score: rule?.score || 50,
      notes: rule?.notes || 'Compatibilité à vérifier manuellement',
      conditions: rule ? [] : ['Vérification manuelle requise'],
      warnings: [],
      recommendations: ['Consulter un expert pour validation'],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }
  }

  // Méthode pour générer un rapport technique
  static generateTechnicalReport(
    type: 'validation' | 'compatibility' | 'installation',
    data: any
  ): TechnicalReport {
    return {
      id: `report_${Date.now()}`,
      user_id: 'current_user',
      title: `Rapport ${type} - ${new Date().toLocaleDateString('fr-FR')}`,
      report_type: type,
      content: data,
      products: data.products || [],
      recommendations: data.recommendations || [],
      warnings: data.warnings || [],
      compliance_status: data.compliance_status || 'unknown',
      generated_by: 'system',
      is_certified: false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }
  }
}
