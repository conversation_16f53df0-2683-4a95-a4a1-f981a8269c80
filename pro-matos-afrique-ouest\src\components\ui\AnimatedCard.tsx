'use client'

import { useState } from 'react'
import { motion, useMotionValue, useTransform } from 'framer-motion'
import { 
  Heart,
  Share2,
  Bookmark,
  MoreHorizontal,
  TrendingUp,
  Eye,
  Clock
} from 'lucide-react'

interface AnimatedCardProps {
  children: React.ReactNode
  className?: string
  hover?: boolean
  tilt?: boolean
  glow?: boolean
  interactive?: boolean
  delay?: number
  onClick?: () => void
}

export default function AnimatedCard({ 
  children, 
  className = '', 
  hover = true,
  tilt = false,
  glow = false,
  interactive = false,
  delay = 0,
  onClick 
}: AnimatedCardProps) {
  const [isHovered, setIsHovered] = useState(false)
  const [isPressed, setIsPressed] = useState(false)

  // Motion values pour l'effet de tilt
  const x = useMotionValue(0)
  const y = useMotionValue(0)
  const rotateX = useTransform(y, [-100, 100], [30, -30])
  const rotateY = useTransform(x, [-100, 100], [-30, 30])

  const handleMouseMove = (event: React.MouseEvent<HTMLDivElement>) => {
    if (!tilt) return
    
    const rect = event.currentTarget.getBoundingClientRect()
    const centerX = rect.left + rect.width / 2
    const centerY = rect.top + rect.height / 2
    
    x.set(event.clientX - centerX)
    y.set(event.clientY - centerY)
  }

  const handleMouseLeave = () => {
    setIsHovered(false)
    if (tilt) {
      x.set(0)
      y.set(0)
    }
  }

  const cardVariants = {
    initial: { 
      opacity: 0, 
      y: 20,
      scale: 0.95
    },
    animate: { 
      opacity: 1, 
      y: 0,
      scale: 1,
      transition: {
        duration: 0.5,
        delay,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    },
    hover: hover ? {
      y: -8,
      scale: 1.02,
      transition: {
        duration: 0.3,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    } : {},
    tap: {
      scale: 0.98,
      transition: {
        duration: 0.1
      }
    }
  }

  const glowVariants = {
    initial: { opacity: 0 },
    hover: { 
      opacity: glow ? 0.6 : 0,
      transition: { duration: 0.3 }
    }
  }

  return (
    <motion.div
      className={`relative ${className}`}
      variants={cardVariants}
      initial="initial"
      animate="animate"
      whileHover="hover"
      whileTap="tap"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={handleMouseLeave}
      onMouseMove={handleMouseMove}
      onMouseDown={() => setIsPressed(true)}
      onMouseUp={() => setIsPressed(false)}
      onClick={onClick}
      style={tilt ? { rotateX, rotateY, transformStyle: "preserve-3d" } : {}}
    >
      {/* Glow effect */}
      {glow && (
        <motion.div
          className="absolute -inset-1 bg-gradient-to-r from-amber-400 to-amber-600 rounded-lg blur opacity-0"
          variants={glowVariants}
          animate={isHovered ? "hover" : "initial"}
        />
      )}

      {/* Card content */}
      <div className={`relative bg-white rounded-lg border border-gray-200 overflow-hidden ${
        interactive ? 'cursor-pointer' : ''
      }`}>
        {children}

        {/* Interactive overlay */}
        {interactive && (
          <motion.div
            className="absolute inset-0 bg-gradient-to-t from-black/0 via-black/0 to-black/0 opacity-0"
            animate={{
              opacity: isHovered ? 1 : 0,
              background: isHovered 
                ? 'linear-gradient(to top, rgba(0,0,0,0.1), rgba(0,0,0,0), rgba(0,0,0,0))'
                : 'linear-gradient(to top, rgba(0,0,0,0), rgba(0,0,0,0), rgba(0,0,0,0))'
            }}
            transition={{ duration: 0.3 }}
          >
            {/* Action buttons */}
            <div className="absolute top-4 right-4 flex space-x-2">
              <motion.button
                className="p-2 bg-white/90 backdrop-blur-sm rounded-full shadow-lg"
                initial={{ scale: 0, opacity: 0 }}
                animate={{ 
                  scale: isHovered ? 1 : 0, 
                  opacity: isHovered ? 1 : 0 
                }}
                transition={{ delay: 0.1 }}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <Heart className="h-4 w-4 text-gray-600" />
              </motion.button>
              
              <motion.button
                className="p-2 bg-white/90 backdrop-blur-sm rounded-full shadow-lg"
                initial={{ scale: 0, opacity: 0 }}
                animate={{ 
                  scale: isHovered ? 1 : 0, 
                  opacity: isHovered ? 1 : 0 
                }}
                transition={{ delay: 0.2 }}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <Share2 className="h-4 w-4 text-gray-600" />
              </motion.button>
              
              <motion.button
                className="p-2 bg-white/90 backdrop-blur-sm rounded-full shadow-lg"
                initial={{ scale: 0, opacity: 0 }}
                animate={{ 
                  scale: isHovered ? 1 : 0, 
                  opacity: isHovered ? 1 : 0 
                }}
                transition={{ delay: 0.3 }}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <Bookmark className="h-4 w-4 text-gray-600" />
              </motion.button>
            </div>

            {/* Stats overlay */}
            <motion.div
              className="absolute bottom-4 left-4 right-4"
              initial={{ y: 20, opacity: 0 }}
              animate={{ 
                y: isHovered ? 0 : 20, 
                opacity: isHovered ? 1 : 0 
              }}
              transition={{ delay: 0.2 }}
            >
              <div className="bg-white/90 backdrop-blur-sm rounded-lg p-3">
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-1 text-gray-600">
                      <Eye className="h-4 w-4" />
                      <span>1.2k</span>
                    </div>
                    <div className="flex items-center space-x-1 text-gray-600">
                      <TrendingUp className="h-4 w-4" />
                      <span>+12%</span>
                    </div>
                    <div className="flex items-center space-x-1 text-gray-600">
                      <Clock className="h-4 w-4" />
                      <span>2h</span>
                    </div>
                  </div>
                  <button className="p-1 text-gray-600 hover:text-gray-900">
                    <MoreHorizontal className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}

        {/* Ripple effect */}
        {isPressed && (
          <motion.div
            className="absolute inset-0 pointer-events-none"
            initial={{ scale: 0, opacity: 0.5 }}
            animate={{ scale: 2, opacity: 0 }}
            transition={{ duration: 0.6 }}
          >
            <div className="w-full h-full bg-amber-400 rounded-full" />
          </motion.div>
        )}
      </div>
    </motion.div>
  )
}

// Composant de grille animée
export function AnimatedGrid({ 
  children, 
  className = '',
  stagger = 0.1 
}: { 
  children: React.ReactNode
  className?: string
  stagger?: number 
}) {
  const containerVariants = {
    initial: {},
    animate: {
      transition: {
        staggerChildren: stagger
      }
    }
  }

  return (
    <motion.div
      className={`grid gap-6 ${className}`}
      variants={containerVariants}
      initial="initial"
      animate="animate"
    >
      {children}
    </motion.div>
  )
}

// Hook pour les animations de scroll
export function useScrollAnimation() {
  const scrollY = useMotionValue(0)
  const opacity = useTransform(scrollY, [0, 300], [1, 0])
  const scale = useTransform(scrollY, [0, 300], [1, 0.8])

  return { scrollY, opacity, scale }
}
