'use client'

import { useState, useRef } from 'react'
import { motion } from 'framer-motion'
import { 
  FileText, 
  Download, 
  Printer, 
  Share2, 
  Settings,
  CheckCircle,
  AlertTriangle,
  Info,
  Zap,
  Calculator,
  BarChart3,
  FileImage,
  Calendar,
  User,
  Building,
  MapPin,
  Phone,
  Mail,
  Globe
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { toast } from 'sonner'

interface ReportSection {
  id: string
  title: string
  content: string
  type: 'text' | 'calculation' | 'table' | 'image' | 'chart'
  required: boolean
  data?: any
}

interface ProjectInfo {
  name: string
  client: string
  location: string
  date: string
  engineer: string
  company: string
  contact: {
    phone: string
    email: string
    website: string
  }
}

interface TechnicalReportGeneratorProps {
  className?: string
}

export default function TechnicalReportGenerator({ className = '' }: TechnicalReportGeneratorProps) {
  const [projectInfo, setProjectInfo] = useState<ProjectInfo>({
    name: '',
    client: '',
    location: '',
    date: new Date().toISOString().split('T')[0],
    engineer: '',
    company: 'Pro Matos Afrique Ouest',
    contact: {
      phone: '+225 XX XX XX XX',
      email: '<EMAIL>',
      website: 'www.promatos.com'
    }
  })

  const [reportSections, setReportSections] = useState<ReportSection[]>([
    {
      id: 'executive-summary',
      title: 'Résumé Exécutif',
      content: '',
      type: 'text',
      required: true
    },
    {
      id: 'technical-analysis',
      title: 'Analyse Technique',
      content: '',
      type: 'text',
      required: true
    },
    {
      id: 'calculations',
      title: 'Calculs et Dimensionnement',
      content: '',
      type: 'calculation',
      required: false,
      data: {
        power: 0,
        voltage: 230,
        current: 0,
        cableSection: 0,
        protectionType: ''
      }
    },
    {
      id: 'compliance',
      title: 'Conformité Réglementaire',
      content: '',
      type: 'text',
      required: true
    },
    {
      id: 'recommendations',
      title: 'Recommandations',
      content: '',
      type: 'text',
      required: true
    },
    {
      id: 'conclusion',
      title: 'Conclusion',
      content: '',
      type: 'text',
      required: true
    }
  ])

  const [reportTemplate, setReportTemplate] = useState('standard')
  const [isGenerating, setIsGenerating] = useState(false)
  const [showPreview, setShowPreview] = useState(false)
  const printRef = useRef<HTMLDivElement>(null)

  const templates = [
    { id: 'standard', name: 'Rapport Standard', description: 'Rapport technique complet' },
    { id: 'compliance', name: 'Conformité Réglementaire', description: 'Focus sur la conformité' },
    { id: 'installation', name: 'Installation Électrique', description: 'Spécialisé installations' },
    { id: 'maintenance', name: 'Maintenance Préventive', description: 'Rapport de maintenance' },
    { id: 'audit', name: 'Audit Sécurité', description: 'Audit de sécurité électrique' }
  ]

  const updateProjectInfo = (field: keyof ProjectInfo, value: string) => {
    setProjectInfo(prev => ({ ...prev, [field]: value }))
  }

  const updateContactInfo = (field: keyof ProjectInfo['contact'], value: string) => {
    setProjectInfo(prev => ({
      ...prev,
      contact: { ...prev.contact, [field]: value }
    }))
  }

  const updateSectionContent = (sectionId: string, content: string) => {
    setReportSections(prev => prev.map(section =>
      section.id === sectionId ? { ...section, content } : section
    ))
  }

  const updateCalculationData = (sectionId: string, field: string, value: any) => {
    setReportSections(prev => prev.map(section =>
      section.id === sectionId 
        ? { ...section, data: { ...section.data, [field]: value } }
        : section
    ))
  }

  const calculateElectricalValues = (sectionId: string) => {
    const section = reportSections.find(s => s.id === sectionId)
    if (!section || !section.data) return

    const { power, voltage } = section.data
    if (power && voltage) {
      const current = power / voltage
      const cableSection = current <= 16 ? 2.5 : current <= 25 ? 4 : current <= 32 ? 6 : 10
      
      updateCalculationData(sectionId, 'current', current.toFixed(2))
      updateCalculationData(sectionId, 'cableSection', cableSection)
      
      // Déterminer le type de protection
      let protectionType = ''
      if (current <= 16) protectionType = 'Disjoncteur C16'
      else if (current <= 20) protectionType = 'Disjoncteur C20'
      else if (current <= 25) protectionType = 'Disjoncteur C25'
      else if (current <= 32) protectionType = 'Disjoncteur C32'
      else protectionType = 'Protection spécialisée requise'
      
      updateCalculationData(sectionId, 'protectionType', protectionType)
    }
  }

  const generateReport = async () => {
    setIsGenerating(true)
    try {
      // Vérifier que les sections requises sont remplies
      const missingRequired = reportSections
        .filter(section => section.required && !section.content.trim())
        .map(section => section.title)

      if (missingRequired.length > 0) {
        toast.error(`Sections requises manquantes: ${missingRequired.join(', ')}`)
        return
      }

      if (!projectInfo.name || !projectInfo.client) {
        toast.error('Informations projet incomplètes')
        return
      }

      // Simulation de génération
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      setShowPreview(true)
      toast.success('Rapport généré avec succès !')
      
    } catch (error) {
      toast.error('Erreur lors de la génération du rapport')
    } finally {
      setIsGenerating(false)
    }
  }

  const exportToPDF = () => {
    if (printRef.current) {
      window.print()
    }
  }

  const shareReport = async () => {
    try {
      if (navigator.share) {
        await navigator.share({
          title: `Rapport Technique - ${projectInfo.name}`,
          text: `Rapport technique pour le projet ${projectInfo.name}`,
          url: window.location.href
        })
      } else {
        // Fallback: copier le lien
        await navigator.clipboard.writeText(window.location.href)
        toast.success('Lien copié dans le presse-papiers')
      }
    } catch (error) {
      toast.error('Erreur lors du partage')
    }
  }

  const getSectionIcon = (type: string) => {
    switch (type) {
      case 'calculation': return <Calculator className="h-5 w-5" />
      case 'table': return <BarChart3 className="h-5 w-5" />
      case 'image': return <FileImage className="h-5 w-5" />
      case 'chart': return <BarChart3 className="h-5 w-5" />
      default: return <FileText className="h-5 w-5" />
    }
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Générateur de Rapports Techniques</h2>
          <p className="text-gray-600">Créez des rapports techniques professionnels personnalisés</p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Select value={reportTemplate} onValueChange={setReportTemplate}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Choisir un modèle" />
            </SelectTrigger>
            <SelectContent>
              {templates.map(template => (
                <SelectItem key={template.id} value={template.id}>
                  <div>
                    <div className="font-medium">{template.name}</div>
                    <div className="text-xs text-gray-500">{template.description}</div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Button
            onClick={generateReport}
            disabled={isGenerating}
            className="bg-amber-500 hover:bg-amber-600"
          >
            {isGenerating ? (
              <>
                <Settings className="h-4 w-4 mr-2 animate-spin" />
                Génération...
              </>
            ) : (
              <>
                <FileText className="h-4 w-4 mr-2" />
                Générer Rapport
              </>
            )}
          </Button>
        </div>

        {/* Sections du rapport */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <FileText className="h-5 w-5" />
                <span>Contenu du Rapport</span>
              </CardTitle>
              <CardDescription>
                Remplissez les sections du rapport technique
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {reportSections.map((section) => (
                <motion.div
                  key={section.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="border border-gray-200 rounded-lg p-4"
                >
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      {getSectionIcon(section.type)}
                      <h4 className="font-medium text-gray-900">{section.title}</h4>
                      {section.required && (
                        <Badge variant="destructive" className="text-xs">Requis</Badge>
                      )}
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {section.type}
                    </Badge>
                  </div>

                  {section.type === 'calculation' ? (
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label>Puissance (W)</Label>
                          <Input
                            type="number"
                            value={section.data?.power || ''}
                            onChange={(e) => updateCalculationData(section.id, 'power', parseFloat(e.target.value) || 0)}
                            placeholder="3000"
                          />
                        </div>
                        <div>
                          <Label>Tension (V)</Label>
                          <Input
                            type="number"
                            value={section.data?.voltage || 230}
                            onChange={(e) => updateCalculationData(section.id, 'voltage', parseFloat(e.target.value) || 230)}
                            placeholder="230"
                          />
                        </div>
                      </div>

                      <Button
                        onClick={() => calculateElectricalValues(section.id)}
                        variant="outline"
                        size="sm"
                      >
                        <Calculator className="h-4 w-4 mr-2" />
                        Calculer
                      </Button>

                      {section.data?.current && (
                        <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                          <h5 className="font-medium text-gray-900">Résultats de calcul :</h5>
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <span className="text-gray-600">Courant :</span>
                              <span className="ml-2 font-medium">{section.data.current} A</span>
                            </div>
                            <div>
                              <span className="text-gray-600">Section câble :</span>
                              <span className="ml-2 font-medium">{section.data.cableSection} mm²</span>
                            </div>
                            <div className="col-span-2">
                              <span className="text-gray-600">Protection :</span>
                              <span className="ml-2 font-medium">{section.data.protectionType}</span>
                            </div>
                          </div>
                        </div>
                      )}

                      <Textarea
                        value={section.content}
                        onChange={(e) => updateSectionContent(section.id, e.target.value)}
                        placeholder="Commentaires et explications techniques..."
                        rows={3}
                      />
                    </div>
                  ) : (
                    <Textarea
                      value={section.content}
                      onChange={(e) => updateSectionContent(section.id, e.target.value)}
                      placeholder={`Contenu de la section ${section.title.toLowerCase()}...`}
                      rows={4}
                    />
                  )}
                </motion.div>
              ))}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Aperçu du rapport */}
      {showPreview && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-8"
        >
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center space-x-2">
                  <FileText className="h-5 w-5" />
                  <span>Aperçu du Rapport</span>
                </CardTitle>

                <div className="flex items-center space-x-2">
                  <Button variant="outline" onClick={exportToPDF}>
                    <Download className="h-4 w-4 mr-2" />
                    Exporter PDF
                  </Button>
                  <Button variant="outline" onClick={() => window.print()}>
                    <Printer className="h-4 w-4 mr-2" />
                    Imprimer
                  </Button>
                  <Button variant="outline" onClick={shareReport}>
                    <Share2 className="h-4 w-4 mr-2" />
                    Partager
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div ref={printRef} className="bg-white p-8 border border-gray-200 rounded-lg print:border-0 print:shadow-none">
                {/* En-tête du rapport */}
                <div className="border-b border-gray-200 pb-6 mb-8">
                  <div className="flex items-start justify-between">
                    <div>
                      <h1 className="text-3xl font-bold text-gray-900 mb-2">
                        Rapport Technique
                      </h1>
                      <h2 className="text-xl text-gray-700 mb-4">{projectInfo.name}</h2>

                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <div className="flex items-center space-x-2 mb-1">
                            <User className="h-4 w-4 text-gray-500" />
                            <span className="text-gray-600">Client :</span>
                            <span className="font-medium">{projectInfo.client}</span>
                          </div>
                          <div className="flex items-center space-x-2 mb-1">
                            <MapPin className="h-4 w-4 text-gray-500" />
                            <span className="text-gray-600">Localisation :</span>
                            <span className="font-medium">{projectInfo.location}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Calendar className="h-4 w-4 text-gray-500" />
                            <span className="text-gray-600">Date :</span>
                            <span className="font-medium">{new Date(projectInfo.date).toLocaleDateString('fr-FR')}</span>
                          </div>
                        </div>

                        <div>
                          <div className="flex items-center space-x-2 mb-1">
                            <Building className="h-4 w-4 text-gray-500" />
                            <span className="text-gray-600">Ingénieur :</span>
                            <span className="font-medium">{projectInfo.engineer}</span>
                          </div>
                          <div className="flex items-center space-x-2 mb-1">
                            <Phone className="h-4 w-4 text-gray-500" />
                            <span className="text-gray-600">Tél :</span>
                            <span className="font-medium">{projectInfo.contact.phone}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Mail className="h-4 w-4 text-gray-500" />
                            <span className="text-gray-600">Email :</span>
                            <span className="font-medium">{projectInfo.contact.email}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="text-right">
                      <div className="w-24 h-24 bg-gradient-to-r from-amber-400 to-amber-500 rounded-lg flex items-center justify-center mb-2">
                        <Zap className="h-12 w-12 text-white" />
                      </div>
                      <div className="text-sm font-medium text-gray-900">{projectInfo.company}</div>
                      <div className="text-xs text-gray-600">{projectInfo.contact.website}</div>
                    </div>
                  </div>
                </div>

                {/* Contenu du rapport */}
                <div className="space-y-8">
                  {reportSections.map((section) => (
                    <div key={section.id} className="break-inside-avoid">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center space-x-2">
                        {getSectionIcon(section.type)}
                        <span>{section.title}</span>
                      </h3>

                      {section.type === 'calculation' && section.data?.current ? (
                        <div className="mb-4">
                          <div className="bg-gray-50 p-4 rounded-lg">
                            <h4 className="font-medium text-gray-900 mb-3">Calculs Électriques :</h4>
                            <div className="grid grid-cols-3 gap-4 text-sm">
                              <div className="text-center">
                                <div className="text-2xl font-bold text-amber-600">{section.data.power}W</div>
                                <div className="text-gray-600">Puissance</div>
                              </div>
                              <div className="text-center">
                                <div className="text-2xl font-bold text-blue-600">{section.data.current}A</div>
                                <div className="text-gray-600">Courant</div>
                              </div>
                              <div className="text-center">
                                <div className="text-2xl font-bold text-green-600">{section.data.cableSection}mm²</div>
                                <div className="text-gray-600">Section</div>
                              </div>
                            </div>
                            <div className="mt-3 p-3 bg-white rounded border">
                              <div className="text-sm">
                                <span className="font-medium">Protection recommandée :</span>
                                <span className="ml-2">{section.data.protectionType}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      ) : null}

                      <div className="text-gray-700 leading-relaxed whitespace-pre-wrap">
                        {section.content || (
                          <div className="text-gray-400 italic">
                            Contenu de la section {section.title.toLowerCase()} à compléter...
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>

                {/* Pied de page */}
                <div className="mt-12 pt-6 border-t border-gray-200 text-center text-sm text-gray-600">
                  <p>Ce rapport a été généré par {projectInfo.company}</p>
                  <p>Contact : {projectInfo.contact.email} | {projectInfo.contact.phone}</p>
                  <p className="mt-2 text-xs">
                    Rapport généré le {new Date().toLocaleDateString('fr-FR')} à {new Date().toLocaleTimeString('fr-FR')}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </div>
  )
}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Configuration du projet */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Building className="h-5 w-5" />
                <span>Informations Projet</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="project-name">Nom du Projet *</Label>
                <Input
                  id="project-name"
                  value={projectInfo.name}
                  onChange={(e) => updateProjectInfo('name', e.target.value)}
                  placeholder="Installation électrique..."
                />
              </div>
              
              <div>
                <Label htmlFor="client">Client *</Label>
                <Input
                  id="client"
                  value={projectInfo.client}
                  onChange={(e) => updateProjectInfo('client', e.target.value)}
                  placeholder="Nom du client"
                />
              </div>
              
              <div>
                <Label htmlFor="location">Localisation</Label>
                <Input
                  id="location"
                  value={projectInfo.location}
                  onChange={(e) => updateProjectInfo('location', e.target.value)}
                  placeholder="Abidjan, Côte d'Ivoire"
                />
              </div>
              
              <div>
                <Label htmlFor="date">Date</Label>
                <Input
                  id="date"
                  type="date"
                  value={projectInfo.date}
                  onChange={(e) => updateProjectInfo('date', e.target.value)}
                />
              </div>
              
              <div>
                <Label htmlFor="engineer">Ingénieur Responsable</Label>
                <Input
                  id="engineer"
                  value={projectInfo.engineer}
                  onChange={(e) => updateProjectInfo('engineer', e.target.value)}
                  placeholder="Nom de l'ingénieur"
                />
              </div>

              <Separator />

              <div className="space-y-3">
                <h4 className="font-medium text-gray-900">Contact Entreprise</h4>
                
                <div>
                  <Label htmlFor="phone">Téléphone</Label>
                  <Input
                    id="phone"
                    value={projectInfo.contact.phone}
                    onChange={(e) => updateContactInfo('phone', e.target.value)}
                  />
                </div>
                
                <div>
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    value={projectInfo.contact.email}
                    onChange={(e) => updateContactInfo('email', e.target.value)}
                  />
                </div>
                
                <div>
                  <Label htmlFor="website">Site Web</Label>
                  <Input
                    id="website"
                    value={projectInfo.contact.website}
                    onChange={(e) => updateContactInfo('website', e.target.value)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
