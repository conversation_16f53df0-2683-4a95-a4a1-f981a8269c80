'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { toast } from 'sonner'
import { StripeService } from '@/lib/services/stripeService'
import { 
  CreditCard, 
  CheckCircle, 
  XCircle, 
  Clock, 
  ArrowLeft,
  Shield,
  Zap
} from 'lucide-react'

export default function CheckoutSimulationPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  
  const sessionId = searchParams.get('session_id')
  const productId = searchParams.get('product_id')
  const userId = searchParams.get('user_id')
  
  const [processing, setProcessing] = useState(false)
  const [paymentStatus, setPaymentStatus] = useState<'pending' | 'success' | 'failed'>('pending')
  const [product, setProduct] = useState<any>(null)

  useEffect(() => {
    if (productId) {
      const productInfo = StripeService.getProduct(productId)
      setProduct(productInfo)
    }
  }, [productId])

  const simulatePayment = async (success: boolean) => {
    setProcessing(true)
    
    // Simuler un délai de traitement
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    if (success) {
      setPaymentStatus('success')
      toast.success('Paiement simulé avec succès !', {
        description: 'Vous allez être redirigé vers la page de confirmation'
      })
      
      // Simuler l'upgrade du rôle utilisateur
      try {
        const response = await fetch('/api/club/upgrade-complete', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            sessionId,
            productId,
            userId,
            simulation: true
          })
        })
        
        if (response.ok) {
          setTimeout(() => {
            router.push(`/club/upgrade-success?session_id=${sessionId}&simulation=true`)
          }, 2000)
        }
      } catch (error) {
        console.error('Erreur simulation upgrade:', error)
      }
    } else {
      setPaymentStatus('failed')
      toast.error('Paiement simulé échoué', {
        description: 'Ceci est une simulation - aucun vrai paiement n\'a été tenté'
      })
    }
    
    setProcessing(false)
  }

  if (!sessionId || !productId) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Session invalide</h2>
            <p className="text-gray-600 mb-4">
              Les paramètres de session sont manquants ou invalides.
            </p>
            <Button onClick={() => router.push('/club')}>
              Retour au Club
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4 max-w-2xl">
        {/* Header */}
        <div className="mb-6">
          <Button
            variant="ghost"
            onClick={() => router.push('/club')}
            className="mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Retour au Club
          </Button>
          
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Simulation de Paiement Stripe
            </h1>
            <p className="text-gray-600">
              Mode développement - Aucun vrai paiement ne sera effectué
            </p>
          </div>
        </div>

        {/* Alerte de simulation */}
        <Card className="mb-6 border-orange-200 bg-orange-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Zap className="h-5 w-5 text-orange-500" />
              <div>
                <h3 className="font-semibold text-orange-800">Mode Simulation</h3>
                <p className="text-sm text-orange-700">
                  Ceci est une simulation pour tester l'interface. Aucun paiement réel ne sera traité.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Détails du produit */}
        {product && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Récapitulatif de commande
                <Badge variant="secondary">Simulation</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-semibold">{product.name}</h3>
                    <p className="text-sm text-gray-600">{product.description}</p>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold">
                      {StripeService.formatPrice(product.price, product.currency)}
                    </div>
                    <div className="text-sm text-gray-500">
                      par {product.interval === 'month' ? 'mois' : 'an'}
                    </div>
                  </div>
                </div>
                
                <div className="border-t pt-4">
                  <h4 className="font-medium mb-2">Fonctionnalités incluses :</h4>
                  <ul className="space-y-1">
                    {product.features.map((feature: string, index: number) => (
                      <li key={index} className="flex items-center gap-2 text-sm">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Interface de paiement simulé */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Simulation de Paiement
            </CardTitle>
          </CardHeader>
          <CardContent>
            {paymentStatus === 'pending' && (
              <div className="space-y-4">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center gap-3">
                    <Shield className="h-5 w-5 text-blue-500" />
                    <div>
                      <h4 className="font-medium text-blue-800">Paiement sécurisé</h4>
                      <p className="text-sm text-blue-700">
                        En production, cette page serait gérée par Stripe de manière sécurisée.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Button
                    onClick={() => simulatePayment(true)}
                    disabled={processing}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    {processing ? (
                      <>
                        <Clock className="h-4 w-4 mr-2 animate-spin" />
                        Traitement...
                      </>
                    ) : (
                      <>
                        <CheckCircle className="h-4 w-4 mr-2" />
                        Simuler Paiement Réussi
                      </>
                    )}
                  </Button>
                  
                  <Button
                    variant="destructive"
                    onClick={() => simulatePayment(false)}
                    disabled={processing}
                  >
                    <XCircle className="h-4 w-4 mr-2" />
                    Simuler Paiement Échoué
                  </Button>
                </div>

                <div className="text-xs text-gray-500 text-center">
                  Session ID: {sessionId}
                </div>
              </div>
            )}

            {paymentStatus === 'success' && (
              <div className="text-center py-8">
                <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-green-800 mb-2">
                  Paiement Simulé Réussi !
                </h3>
                <p className="text-green-700 mb-4">
                  Redirection vers la page de confirmation...
                </p>
                <div className="animate-pulse">
                  <div className="h-2 bg-green-200 rounded-full">
                    <div className="h-2 bg-green-500 rounded-full animate-pulse"></div>
                  </div>
                </div>
              </div>
            )}

            {paymentStatus === 'failed' && (
              <div className="text-center py-8">
                <XCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-red-800 mb-2">
                  Paiement Simulé Échoué
                </h3>
                <p className="text-red-700 mb-4">
                  Ceci est une simulation d'échec de paiement.
                </p>
                <div className="space-y-2">
                  <Button
                    onClick={() => setPaymentStatus('pending')}
                    variant="outline"
                  >
                    Réessayer
                  </Button>
                  <Button
                    onClick={() => router.push('/club')}
                    variant="ghost"
                  >
                    Retour au Club
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Informations de développement */}
        <Card className="mt-6 border-gray-200 bg-gray-50">
          <CardContent className="p-4">
            <h4 className="font-medium mb-2">Informations de développement :</h4>
            <div className="text-xs text-gray-600 space-y-1">
              <div>Session ID: {sessionId}</div>
              <div>Product ID: {productId}</div>
              <div>User ID: {userId}</div>
              <div>Mode: Simulation (développement)</div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
