'use client'

import { motion } from 'framer-motion'
import { 
  ArrowLeft,
  BarChart3,
  TrendingUp,
  Users,
  Zap
} from 'lucide-react'
import Link from 'next/link'
import InformationHub from '@/components/hub/InformationHub'
import RealTimeNotifications from '@/components/hub/RealTimeNotifications'
import RealTimeDashboard from '@/components/hub/RealTimeDashboard'

export default function HubPage() {
  const stats = [
    {
      label: 'Alertes Actives',
      value: '12',
      change: '+3',
      changeType: 'increase',
      icon: <Zap className="h-6 w-6" />
    },
    {
      label: 'Mises à Jour Stock',
      value: '47',
      change: '+8',
      changeType: 'increase',
      icon: <BarChart3 className="h-6 w-6" />
    },
    {
      label: 'Formations Disponibles',
      value: '6',
      change: '+2',
      changeType: 'increase',
      icon: <Users className="h-6 w-6" />
    },
    {
      label: 'Actualités',
      value: '23',
      change: '+5',
      changeType: 'increase',
      icon: <TrendingUp className="h-6 w-6" />
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-md border-b border-slate-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link 
                href="/"
                className="flex items-center space-x-2 text-slate-700 hover:text-amber-600 transition-colors"
              >
                <ArrowLeft className="h-5 w-5" />
                <span>Retour à l'accueil</span>
              </Link>
            </div>
            
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-amber-400 to-amber-500 rounded-lg flex items-center justify-center">
                <Zap className="h-5 w-5 text-slate-900" />
              </div>
              <div>
                <h1 className="text-lg font-bold text-slate-900">Pro Matos</h1>
                <p className="text-xs text-slate-600">Hub d'Information</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Contenu principal */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Titre et description */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-3xl font-bold text-slate-900 mb-2">
            Hub d'Information et Veille
          </h1>
          <p className="text-lg text-slate-600">
            Votre centre de contrôle pour maîtriser l'écosystème électrique en temps réel
          </p>
        </motion.div>

        {/* Statistiques rapides */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
        >
          {stats.map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 + index * 0.05 }}
              className="industrial-card p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">{stat.label}</p>
                  <div className="flex items-baseline space-x-2">
                    <p className="text-2xl font-bold text-slate-900">{stat.value}</p>
                    <span className={`text-sm font-medium ${
                      stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {stat.change}
                    </span>
                  </div>
                </div>
                <div className="p-3 bg-amber-100 rounded-lg text-amber-600">
                  {stat.icon}
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Tableau de bord temps réel */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mb-8"
        >
          <RealTimeDashboard />
        </motion.div>

        {/* Hub d'Information principal */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <InformationHub />
        </motion.div>

        {/* Section d'aide */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="mt-8 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200"
        >
          <h3 className="text-lg font-semibold text-blue-900 mb-2">
            💡 Comment utiliser le Hub d'Information
          </h3>
          <div className="grid md:grid-cols-2 gap-4 text-sm text-blue-800">
            <div>
              <h4 className="font-medium mb-1">Alertes Marché</h4>
              <p>Surveillez les ruptures de stock, variations de prix et nouveaux produits en temps réel</p>
            </div>
            <div>
              <h4 className="font-medium mb-1">Mises à Jour Stock</h4>
              <p>Suivez les mouvements de stock chez tous vos fournisseurs partenaires</p>
            </div>
            <div>
              <h4 className="font-medium mb-1">Formations</h4>
              <p>Accédez aux formations exclusives et certifications selon votre niveau d'adhésion</p>
            </div>
            <div>
              <h4 className="font-medium mb-1">Actualités</h4>
              <p>Restez informé des dernières tendances et réglementations du secteur</p>
            </div>
          </div>
        </motion.div>
      </main>

      {/* Notifications temps réel */}
      <RealTimeNotifications />
    </div>
  )
}
