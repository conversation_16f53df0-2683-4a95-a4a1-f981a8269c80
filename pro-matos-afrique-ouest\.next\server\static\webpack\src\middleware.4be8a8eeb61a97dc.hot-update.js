"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("src/middleware",{

/***/ "(middleware)/./src/middleware.ts":
/*!***************************!*\
  !*** ./src/middleware.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(middleware)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\n\nasync function middleware(req) {\n    const res = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next();\n    // Exclure les routes d'authentification du middleware\n    const authRoutes = [\n        \"/auth/signin\",\n        \"/auth/callback\",\n        \"/auth/callback-client\",\n        \"/test-auth\"\n    ];\n    const isAuthRoute = authRoutes.some((route)=>req.nextUrl.pathname.startsWith(route));\n    if (isAuthRoute) {\n        console.log(\"Route d'authentification d\\xe9tect\\xe9e, passage sans v\\xe9rification:\", req.nextUrl.pathname);\n        return res;\n    }\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createMiddlewareClient)({\n        req,\n        res\n    });\n    // Vérifier la session utilisateur\n    const { data: { session } } = await supabase.auth.getSession();\n    // Routes protégées qui nécessitent une authentification\n    const protectedRoutes = [\n        \"/hub\",\n        \"/validation\",\n        \"/kits\",\n        \"/club\",\n        \"/crm\",\n        \"/expert\",\n        \"/prescriptor\"\n    ];\n    const isProtectedRoute = protectedRoutes.some((route)=>req.nextUrl.pathname.startsWith(route));\n    // Rediriger vers la connexion si pas authentifié\n    if (isProtectedRoute && !session) {\n        console.log(\"Redirection vers signin pour route prot\\xe9g\\xe9e:\", req.nextUrl.pathname);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(new URL(\"/auth/signin\", req.url));\n    }\n    // Si authentifié, ajouter les informations de base aux headers\n    if (session && isProtectedRoute) {\n        // Ajouter les informations utilisateur de base aux headers\n        const requestHeaders = new Headers(req.headers);\n        requestHeaders.set(\"x-user-id\", session.user.id);\n        requestHeaders.set(\"x-user-email\", session.user.email || \"\");\n        requestHeaders.set(\"x-user-role\", \"member\") // Rôle par défaut\n        ;\n        requestHeaders.set(\"x-user-statut\", \"white\") // Statut par défaut\n        ;\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n            request: {\n                headers: requestHeaders\n            }\n        });\n    }\n    return res;\n}\nconst config = {\n    matcher: [\n        /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder\n     */ \"/((?!api|_next/static|_next/image|favicon.ico|public).*)\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.ts\n");

/***/ })

});