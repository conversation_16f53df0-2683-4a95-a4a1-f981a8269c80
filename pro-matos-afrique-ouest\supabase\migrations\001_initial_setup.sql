-- Migration initiale pour Pro Matos Afrique Ouest
-- Supprime les tables existantes si elles existent pour éviter les conflits

-- Supprimer les tables dans l'ordre inverse des dépendances
DROP TABLE IF EXISTS public.user_alerts CASCADE;
DROP TABLE IF EXISTS public.alerts CASCADE;
DROP TABLE IF EXISTS public.users CASCADE;

-- Activer <PERSON> (Row Level Security)
ALTER DATABASE postgres SET "app.jwt_secret" TO 'your-jwt-secret';

-- 1. Table users (profils utilisateurs)
CREATE TABLE public.users (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  role TEXT DEFAULT 'guest' CHECK (role IN ('guest', 'member', 'vip', 'admin')),
  full_name TEXT,
  company TEXT,
  phone TEXT,
  devis_demandes INTEGER DEFAULT 0,
  achats INTEGER DEFAULT 0,
  statut TEXT DEFAULT 'white' CHECK (statut IN ('white', 'grey', 'black')),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 2. Table alerts
CREATE TABLE public.alerts (
  id SERIAL PRIMARY KEY,
  title TEXT NOT NULL,
  body TEXT NOT NULL,
  type TEXT DEFAULT 'info' CHECK (type IN ('info', 'warning', 'critical', 'promo')),
  category TEXT DEFAULT 'general',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 3. Table user_alerts (abonnements)
CREATE TABLE public.user_alerts (
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  alert_id INTEGER REFERENCES public.alerts(id) ON DELETE CASCADE,
  subscribed_at TIMESTAMPTZ DEFAULT NOW(),
  PRIMARY KEY (user_id, alert_id)
);

-- Activer RLS sur toutes les tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_alerts ENABLE ROW LEVEL SECURITY;

-- Politiques RLS pour users
CREATE POLICY "Users can view own profile" ON public.users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.users
  FOR UPDATE USING (auth.uid() = id);

-- Politiques RLS pour alerts (lecture publique)
CREATE POLICY "Anyone can view active alerts" ON public.alerts
  FOR SELECT USING (is_active = true);

-- Politiques RLS pour user_alerts
CREATE POLICY "Users can view own subscriptions" ON public.user_alerts
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own subscriptions" ON public.user_alerts
  FOR ALL USING (auth.uid() = user_id);

-- Fonction pour créer automatiquement un profil utilisateur
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.users (id, email, full_name)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email)
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger pour créer automatiquement un profil
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Insérer des données d'exemple pour les alertes
INSERT INTO public.alerts (title, body, type, category, is_active) VALUES
('Nouvelle norme NF C 15-100 - Amendement A6', 'Mise à jour importante des règles d''installation électrique pour les bâtiments résidentiels et tertiaires.', 'info', 'Réglementation', true),
('Rupture de stock - Disjoncteurs Schneider', 'Stock épuisé sur les disjoncteurs C60N 32A chez plusieurs fournisseurs d''Abidjan.', 'warning', 'Stock', true),
('Formation technique Legrand', 'Session de formation sur les nouveaux produits de la gamme Mosaic disponible.', 'info', 'Formation', true),
('Alerte sécurité - Rappel produit', 'Rappel de sécurité sur certains modèles de prises électriques défectueuses.', 'critical', 'Sécurité', true),
('Promotion spéciale - Câbles électriques', 'Remise de 20% sur tous les câbles électriques ce mois-ci.', 'promo', 'Promotion', true);

-- Créer un utilisateur admin par défaut (optionnel)
-- Note: Ceci sera créé automatiquement lors de la première connexion via le trigger
