import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export async function middleware(req: NextRequest) {
  const res = NextResponse.next()

  // Exclure les routes d'authentification du middleware
  const authRoutes = ['/auth/signin', '/auth/callback', '/auth/callback-client', '/test-auth']
  const isAuthRoute = authRoutes.some(route => req.nextUrl.pathname.startsWith(route))

  if (isAuthRoute) {
    console.log('Route d\'authentification détectée, passage sans vérification:', req.nextUrl.pathname)
    return res
  }

  const supabase = createMiddlewareClient({ req, res })

  // Vérifier la session utilisateur
  const {
    data: { session },
  } = await supabase.auth.getSession()

  // Routes protégées qui nécessitent une authentification
  const protectedRoutes = ['/hub', '/validation', '/kits', '/club', '/crm', '/expert', '/prescriptor']
  const isProtectedRoute = protectedRoutes.some(route => req.nextUrl.pathname.startsWith(route))

  // Rediriger vers la connexion si pas authentifié
  if (isProtectedRoute && !session) {
    console.log('Redirection vers signin pour route protégée:', req.nextUrl.pathname)
    return NextResponse.redirect(new URL('/auth/signin', req.url))
  }

  // Si authentifié, récupérer et vérifier les informations utilisateur
  if (session && isProtectedRoute) {
    try {
      const { data: user, error: userError } = await supabase
        .from('users')
        .select('role, statut, devis_demandes, achats')
        .eq('id', session.user.id)
        .single()

      if (userError) {
        console.error('Erreur récupération utilisateur:', userError)
        // Si l'utilisateur n'existe pas en base, le créer
        if (userError.code === 'PGRST116') {
          await supabase
            .from('users')
            .insert({
              id: session.user.id,
              email: session.user.email || '',
              role: 'guest'
            })

          // Utiliser les valeurs par défaut
          user = { role: 'guest', statut: 'white', devis_demandes: 0, achats: 0 }
        } else {
          return NextResponse.redirect(new URL('/auth/signin?error=user_error', req.url))
        }
      }

      // Vérifications spécifiques par route et rôle
      const pathname = req.nextUrl.pathname

      // Page CRM - Réservée aux admins
      if (pathname.startsWith('/crm') && user?.role !== 'admin') {
        console.log('Accès CRM refusé pour rôle:', user?.role)
        return NextResponse.redirect(new URL('/hub?error=access_denied', req.url))
      }

      // Page Kits - Accessible aux membres et plus
      if (pathname.startsWith('/kits') && user?.role === 'guest') {
        console.log('Accès Kits refusé pour guest')
        return NextResponse.redirect(new URL('/hub?error=member_required', req.url))
      }

      // Page Club - Accessible aux VIP et admins
      if (pathname.startsWith('/club') && !['vip', 'admin'].includes(user?.role || '')) {
        console.log('Accès Club refusé pour rôle:', user?.role)
        return NextResponse.redirect(new URL('/hub?error=vip_required', req.url))
      }

      // Limitation pour utilisateurs grey avec trop de devis
      if (user?.statut === 'grey' && (user?.devis_demandes || 0) > 3 && (user?.achats || 0) === 0) {
        if (pathname.startsWith('/validation')) {
          console.log('Accès Validation limité pour utilisateur grey')
          return NextResponse.redirect(new URL('/hub?error=validation_limited', req.url))
        }
      }

      // Blocage pour utilisateurs blacklistés
      if (user?.statut === 'black') {
        console.log('Accès bloqué pour utilisateur blacklisté')
        return NextResponse.redirect(new URL('/hub?error=access_suspended', req.url))
      }

      // Ajouter les informations utilisateur aux headers
      const requestHeaders = new Headers(req.headers)
      requestHeaders.set('x-user-id', session.user.id)
      requestHeaders.set('x-user-email', session.user.email || '')
      requestHeaders.set('x-user-role', user?.role || 'guest')
      requestHeaders.set('x-user-statut', user?.statut || 'white')
      requestHeaders.set('x-user-devis', (user?.devis_demandes || 0).toString())
      requestHeaders.set('x-user-achats', (user?.achats || 0).toString())

      return NextResponse.next({
        request: {
          headers: requestHeaders,
        },
      })
    } catch (error) {
      console.error('Erreur middleware:', error)
      // En cas d'erreur, rediriger vers la connexion
      return NextResponse.redirect(new URL('/auth/signin?error=middleware_error', req.url))
    }
  }

  return res
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!api|_next/static|_next/image|favicon.ico|public).*)',
  ],
}
