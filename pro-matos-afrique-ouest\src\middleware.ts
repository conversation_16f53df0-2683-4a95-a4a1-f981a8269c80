import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export async function middleware(req: NextRequest) {
  const res = NextResponse.next()
  const supabase = createMiddlewareClient({ req, res })

  // Vérifier la session utilisateur
  const {
    data: { session },
  } = await supabase.auth.getSession()

  // Routes protégées qui nécessitent une authentification
  const protectedRoutes = ['/hub', '/validation', '/kits', '/club', '/crm', '/expert', '/prescriptor']
  const isProtectedRoute = protectedRoutes.some(route => req.nextUrl.pathname.startsWith(route))

  // Rediriger vers la connexion si pas authentifié
  if (isProtectedRoute && !session) {
    return NextResponse.redirect(new URL('/auth/signin', req.url))
  }

  // Si authentifié, vérifier les permissions spécifiques
  if (session && isProtectedRoute) {
    try {
      // Récupérer le profil utilisateur
      const { data: user, error } = await supabase
        .from('users')
        .select('role, statut, devis_demandes, achats')
        .eq('id', session.user.id)
        .single()

      if (error || !user) {
        console.error('Erreur récupération profil utilisateur:', error)
        return NextResponse.redirect(new URL('/auth/signin', req.url))
      }

      // Vérifications spécifiques par route
      const pathname = req.nextUrl.pathname

      // Page CRM - Réservée aux admins
      if (pathname.startsWith('/crm') && user.role !== 'admin') {
        return NextResponse.redirect(new URL('/hub?error=access_denied', req.url))
      }

      // Page Club - Réservée aux VIP et admins
      if (pathname.startsWith('/club') && !['vip', 'admin'].includes(user.role)) {
        // Permettre l'accès pour voir les offres d'upgrade
        // mais limiter certaines fonctionnalités côté client
      }

      // Page Kits - Accessible aux membres et plus
      if (pathname.startsWith('/kits') && user.role === 'guest') {
        return NextResponse.redirect(new URL('/hub?error=member_required', req.url))
      }

      // Logique de limitation pour utilisateurs "gris"
      if (user.statut === 'grey') {
        // Si l'utilisateur a plus de 3 devis sans achat, limiter l'accès à /validation
        if (pathname.startsWith('/validation') && user.devis_demandes > 3 && user.achats === 0) {
          return NextResponse.redirect(new URL('/hub?error=validation_limited', req.url))
        }
      }

      // Utilisateurs "noirs" - Accès très limité
      if (user.statut === 'black') {
        const allowedPaths = ['/hub', '/auth']
        const isAllowed = allowedPaths.some(path => pathname.startsWith(path))
        
        if (!isAllowed) {
          return NextResponse.redirect(new URL('/hub?error=account_suspended', req.url))
        }
      }

      // Ajouter les informations utilisateur aux headers pour les pages
      const requestHeaders = new Headers(req.headers)
      requestHeaders.set('x-user-role', user.role)
      requestHeaders.set('x-user-statut', user.statut)
      requestHeaders.set('x-user-id', session.user.id)

      return NextResponse.next({
        request: {
          headers: requestHeaders,
        },
      })

    } catch (error) {
      console.error('Erreur middleware:', error)
      return NextResponse.redirect(new URL('/auth/signin', req.url))
    }
  }

  return res
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!api|_next/static|_next/image|favicon.ico|public).*)',
  ],
}
