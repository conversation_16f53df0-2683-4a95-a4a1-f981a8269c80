import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export async function middleware(req: NextRequest) {
  const res = NextResponse.next()
  const supabase = createMiddlewareClient({ req, res })

  // Vérifier la session utilisateur
  const {
    data: { session },
  } = await supabase.auth.getSession()

  // Routes protégées qui nécessitent une authentification
  const protectedRoutes = ['/hub', '/validation', '/kits', '/club', '/crm', '/expert', '/prescriptor']
  const isProtectedRoute = protectedRoutes.some(route => req.nextUrl.pathname.startsWith(route))

  // Rediriger vers la connexion si pas authentifié
  if (isProtectedRoute && !session) {
    return NextResponse.redirect(new URL('/auth/signin', req.url))
  }

  // Si authentifié, récupérer le profil utilisateur et ajouter aux headers
  if (session && isProtectedRoute) {
    try {
      const { data: user } = await supabase
        .from('users')
        .select('role, statut, devis_demandes, achats')
        .eq('id', session.user.id)
        .single()

      // Ajouter les informations utilisateur aux headers
      const requestHeaders = new Headers(req.headers)
      requestHeaders.set('x-user-id', session.user.id)
      requestHeaders.set('x-user-email', session.user.email || '')
      requestHeaders.set('x-user-role', user?.role || 'guest')
      requestHeaders.set('x-user-statut', user?.statut || 'white')

      // Vérifications spécifiques par route
      const pathname = req.nextUrl.pathname

      // Page CRM - Réservée aux admins
      if (pathname.startsWith('/crm') && user?.role !== 'admin') {
        return NextResponse.redirect(new URL('/hub?error=access_denied', req.url))
      }

      // Page Kits - Accessible aux membres et plus
      if (pathname.startsWith('/kits') && user?.role === 'guest') {
        return NextResponse.redirect(new URL('/hub?error=member_required', req.url))
      }

      return NextResponse.next({
        request: {
          headers: requestHeaders,
        },
      })
    } catch (error) {
      console.error('Erreur middleware:', error)
      // En cas d'erreur, laisser passer mais sans privilèges
      return res
    }
  }

  return res
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!api|_next/static|_next/image|favicon.ico|public).*)',
  ],
}
