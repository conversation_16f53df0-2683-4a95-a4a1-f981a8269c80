'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Bell, 
  TrendingUp, 
  Package, 
  AlertTriangle, 
  Calendar,
  Newspaper,
  Wifi,
  WifiOff,
  Clock,
  Filter,
  Search,
  RefreshCw,
  Plus,
  Minus,
  CheckCircle,
  XCircle
} from 'lucide-react'
import { useStore } from '@/store/useStore'
import { AlertService, Alert } from '@/lib/services/alertService'
import { formatDate } from '@/lib/utils'
import { toast } from 'sonner'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

interface InformationHubProps {
  className?: string
}

export default function ModernInformationHub({ className = '' }: InformationHubProps) {
  const { user } = useStore()
  
  const [alerts, setAlerts] = useState<Alert[]>([])
  const [loading, setLoading] = useState(true)
  const [realTimeConnected, setRealTimeConnected] = useState(true)
  const [lastUpdateTime, setLastUpdateTime] = useState<Date>(new Date())
  
  const [activeTab, setActiveTab] = useState<'alerts' | 'stocks' | 'training' | 'news'>('alerts')
  const [searchQuery, setSearchQuery] = useState('')
  const [filterType, setFilterType] = useState<'all' | 'info' | 'warning' | 'critical' | 'promo'>('all')
  const [filterCategory, setFilterCategory] = useState<string>('all')

  // Charger les alertes
  const loadAlerts = async () => {
    setLoading(true)
    try {
      const result = await AlertService.getAlertsWithSubscriptions(user?.id)
      
      if (result.success && result.data) {
        setAlerts(result.data)
        setLastUpdateTime(new Date())
        setRealTimeConnected(true)
      } else {
        toast.error(result.error || 'Erreur lors du chargement des alertes')
        setRealTimeConnected(false)
      }
    } catch (error) {
      console.error('Erreur chargement alertes:', error)
      toast.error('Erreur lors du chargement des alertes')
      setRealTimeConnected(false)
    } finally {
      setLoading(false)
    }
  }

  // S'abonner à une alerte
  const handleSubscribe = async (alertId: number) => {
    if (!user) {
      toast.error('Vous devez être connecté pour vous abonner')
      return
    }

    try {
      const response = await fetch('/api/alerts/subscribe', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ alertId })
      })

      const result = await response.json()

      if (result.success) {
        toast.success('Abonnement créé avec succès')
        setAlerts(prev => prev.map(alert => 
          alert.id === alertId ? { ...alert, isSubscribed: true } : alert
        ))
      } else {
        toast.error(result.error || 'Erreur lors de l\'abonnement')
      }
    } catch (error) {
      console.error('Erreur abonnement:', error)
      toast.error('Erreur lors de l\'abonnement')
    }
  }

  // Se désabonner d'une alerte
  const handleUnsubscribe = async (alertId: number) => {
    if (!user) return

    try {
      const response = await fetch('/api/alerts/subscribe', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ alertId })
      })

      const result = await response.json()

      if (result.success) {
        toast.success('Désabonnement effectué')
        setAlerts(prev => prev.map(alert => 
          alert.id === alertId ? { ...alert, isSubscribed: false } : alert
        ))
      } else {
        toast.error(result.error || 'Erreur lors du désabonnement')
      }
    } catch (error) {
      console.error('Erreur désabonnement:', error)
      toast.error('Erreur lors du désabonnement')
    }
  }

  useEffect(() => {
    loadAlerts()
  }, [user])

  // Actualiser les données
  const refreshData = () => {
    loadAlerts()
    toast.success('Données actualisées')
  }

  // Filtrer les alertes
  const filteredAlerts = alerts.filter(alert => {
    const matchesSearch = alert.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         alert.body.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesType = filterType === 'all' || alert.type === filterType
    const matchesCategory = filterCategory === 'all' || alert.category === filterCategory
    
    return matchesSearch && matchesType && matchesCategory
  })

  // Obtenir les catégories uniques
  const categories = Array.from(new Set(alerts.map(alert => alert.category)))

  // Icônes par type d'alerte
  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'critical': return <AlertTriangle className="h-5 w-5 text-red-500" />
      case 'warning': return <AlertTriangle className="h-5 w-5 text-orange-500" />
      case 'promo': return <TrendingUp className="h-5 w-5 text-green-500" />
      default: return <Bell className="h-5 w-5 text-blue-500" />
    }
  }

  // Couleurs par type
  const getAlertColor = (type: string) => {
    switch (type) {
      case 'critical': return 'destructive'
      case 'warning': return 'secondary'
      case 'promo': return 'default'
      default: return 'outline'
    }
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header avec statut temps réel */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h1 className="text-2xl font-bold text-gray-900">Hub d'Information</h1>
          <div className="flex items-center space-x-2">
            {realTimeConnected ? (
              <Wifi className="h-4 w-4 text-green-500" />
            ) : (
              <WifiOff className="h-4 w-4 text-red-500" />
            )}
            <span className="text-sm text-gray-500">
              {realTimeConnected ? 'Connecté' : 'Hors ligne'}
            </span>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-500">
            Dernière mise à jour: {formatDate(lastUpdateTime)}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={refreshData}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </div>

      {/* Filtres et recherche */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Rechercher dans les alertes..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <div className="flex gap-2">
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value as any)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                title="Filtrer par type"
              >
                <option value="all">Tous les types</option>
                <option value="info">Information</option>
                <option value="warning">Avertissement</option>
                <option value="critical">Critique</option>
                <option value="promo">Promotion</option>
              </select>
              
              <select
                value={filterCategory}
                onChange={(e) => setFilterCategory(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                title="Filtrer par catégorie"
              >
                <option value="all">Toutes les catégories</option>
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Contenu principal */}
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="alerts" className="flex items-center gap-2">
            <Bell className="h-4 w-4" />
            Alertes ({filteredAlerts.length})
          </TabsTrigger>
          <TabsTrigger value="stocks" className="flex items-center gap-2">
            <Package className="h-4 w-4" />
            Stocks
          </TabsTrigger>
          <TabsTrigger value="training" className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Formations
          </TabsTrigger>
          <TabsTrigger value="news" className="flex items-center gap-2">
            <Newspaper className="h-4 w-4" />
            Actualités
          </TabsTrigger>
        </TabsList>

        <TabsContent value="alerts" className="space-y-4">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="h-6 w-6 animate-spin" />
              <span className="ml-2">Chargement des alertes...</span>
            </div>
          ) : filteredAlerts.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <Bell className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Aucune alerte</h3>
                <p className="text-gray-500">Aucune alerte ne correspond à vos critères de recherche.</p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {filteredAlerts.map((alert) => (
                <motion.div
                  key={alert.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                >
                  <Card>
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            {getAlertIcon(alert.type)}
                            <h3 className="text-lg font-semibold text-gray-900">
                              {alert.title}
                            </h3>
                            <Badge variant={getAlertColor(alert.type) as any}>
                              {alert.type}
                            </Badge>
                            <Badge variant="outline">
                              {alert.category}
                            </Badge>
                          </div>
                          
                          <p className="text-gray-600 mb-3">
                            {alert.body}
                          </p>
                          
                          <div className="flex items-center gap-4 text-sm text-gray-500">
                            <span className="flex items-center gap-1">
                              <Clock className="h-4 w-4" />
                              {formatDate(new Date(alert.created_at))}
                            </span>
                          </div>
                        </div>
                        
                        <div className="ml-4">
                          {user && (
                            <Button
                              variant={alert.isSubscribed ? "destructive" : "default"}
                              size="sm"
                              onClick={() => alert.isSubscribed 
                                ? handleUnsubscribe(alert.id) 
                                : handleSubscribe(alert.id)
                              }
                              className="flex items-center gap-2"
                            >
                              {alert.isSubscribed ? (
                                <>
                                  <Minus className="h-4 w-4" />
                                  Se désabonner
                                </>
                              ) : (
                                <>
                                  <Plus className="h-4 w-4" />
                                  S'abonner
                                </>
                              )}
                            </Button>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="stocks">
          <Card>
            <CardContent className="p-8 text-center">
              <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Stocks</h3>
              <p className="text-gray-500">Fonctionnalité en cours de développement</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="training">
          <Card>
            <CardContent className="p-8 text-center">
              <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Formations</h3>
              <p className="text-gray-500">Fonctionnalité en cours de développement</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="news">
          <Card>
            <CardContent className="p-8 text-center">
              <Newspaper className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Actualités</h3>
              <p className="text-gray-500">Fonctionnalité en cours de développement</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
