'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { usePathname, useRouter } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Home,
  Bell,
  CheckCircle,
  Package,
  Crown,
  User,
  Settings,
  LogOut,
  Menu,
  X,
  Shield,
  Users
} from 'lucide-react'
import { useAuthStore, usePermissions } from '@/lib/stores/authStore'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { toast } from 'sonner'

interface DashboardLayoutProps {
  children: React.ReactNode
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const pathname = usePathname()
  const router = useRouter()
  const { user, profile, signOut, loading } = useAuthStore()
  const permissions = usePermissions()

  const navigation = [
    {
      name: 'Hub',
      href: '/hub',
      icon: Bell,
      badge: 'LIVE',
      badgeColor: 'bg-red-500',
      description: 'Alertes et informations'
    },
    {
      name: 'Validation',
      href: '/validation',
      icon: CheckCircle,
      badge: 'PRO',
      badgeColor: 'bg-blue-500',
      description: 'Validation technique',
      disabled: !permissions.canAccessValidation
    },
    {
      name: 'Kits',
      href: '/kits',
      icon: Package,
      badge: 'PREMIUM',
      badgeColor: 'bg-purple-500',
      description: 'Kits de prescription',
      disabled: !permissions.canAccessKits
    },
    {
      name: 'Club',
      href: '/club',
      icon: Crown,
      badge: 'VIP',
      badgeColor: 'bg-amber-500',
      description: 'Club exclusif',
      disabled: !permissions.canAccessClub
    }
  ]

  // Navigation admin
  if (permissions.isAdmin) {
    navigation.push({
      name: 'CRM',
      href: '/crm',
      icon: Users,
      badge: 'ADMIN',
      badgeColor: 'bg-red-600',
      description: 'Gestion utilisateurs'
    })
  }

  const handleNavigation = async (href: string, disabled?: boolean) => {
    if (disabled) {
      toast.error('Accès restreint', {
        description: 'Vous devez être membre pour accéder à cette section'
      })
      return
    }

    toast.loading('Chargement...', { id: 'navigation' })
    
    try {
      router.push(href)
      setSidebarOpen(false)
      
      // Simuler un délai de chargement
      await new Promise(resolve => setTimeout(resolve, 500))
      
      toast.success('Page chargée', { id: 'navigation' })
    } catch (error) {
      toast.error('Erreur de navigation', { id: 'navigation' })
    }
  }

  const handleSignOut = async () => {
    toast.loading('Déconnexion...', { id: 'signout' })
    
    try {
      await signOut()
      router.push('/auth/signin')
      toast.success('Déconnecté avec succès', { id: 'signout' })
    } catch (error) {
      toast.error('Erreur lors de la déconnexion', { id: 'signout' })
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-amber-500"></div>
      </div>
    )
  }

  if (!user) {
    router.push('/auth/signin')
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Sidebar Mobile Overlay */}
      <AnimatePresence>
        {sidebarOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 lg:hidden"
          >
            <div 
              className="fixed inset-0 bg-black bg-opacity-50"
              onClick={() => setSidebarOpen(false)}
            />
            <motion.div
              initial={{ x: -300 }}
              animate={{ x: 0 }}
              exit={{ x: -300 }}
              className="fixed left-0 top-0 h-full w-80 bg-white shadow-xl"
            >
              <SidebarContent 
                navigation={navigation}
                pathname={pathname}
                profile={profile}
                onNavigate={handleNavigation}
                onSignOut={handleSignOut}
                onClose={() => setSidebarOpen(false)}
                mobile
              />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Sidebar Desktop */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-80 lg:flex-col">
        <SidebarContent 
          navigation={navigation}
          pathname={pathname}
          profile={profile}
          onNavigate={handleNavigation}
          onSignOut={handleSignOut}
        />
      </div>

      {/* Main Content */}
      <div className="lg:pl-80">
        {/* Top Bar Mobile */}
        <div className="sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm lg:hidden">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setSidebarOpen(true)}
          >
            <Menu className="h-6 w-6" />
          </Button>
          
          <div className="flex-1 text-sm font-semibold leading-6 text-gray-900">
            Pro Matos Afrique Ouest
          </div>

          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-xs">
              {profile?.role?.toUpperCase()}
            </Badge>
            <Avatar className="h-8 w-8">
              <AvatarFallback>
                {profile?.full_name?.charAt(0) || user.email?.charAt(0)}
              </AvatarFallback>
            </Avatar>
          </div>
        </div>

        {/* Page Content */}
        <main className="py-6">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            {children}
          </div>
        </main>
      </div>
    </div>
  )
}

// Composant Sidebar séparé
interface SidebarContentProps {
  navigation: any[]
  pathname: string
  profile: any
  onNavigate: (href: string, disabled?: boolean) => void
  onSignOut: () => void
  onClose?: () => void
  mobile?: boolean
}

function SidebarContent({ 
  navigation, 
  pathname, 
  profile, 
  onNavigate, 
  onSignOut, 
  onClose,
  mobile = false 
}: SidebarContentProps) {
  return (
    <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-white px-6 pb-4 border-r border-gray-200">
      {/* Header */}
      <div className="flex h-16 shrink-0 items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gradient-to-r from-amber-500 to-amber-600 rounded-lg flex items-center justify-center">
            <Shield className="h-5 w-5 text-white" />
          </div>
          <span className="text-lg font-bold text-gray-900">Pro Matos</span>
        </div>
        
        {mobile && (
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-5 w-5" />
          </Button>
        )}
      </div>

      {/* Profile Section */}
      <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
        <Avatar>
          <AvatarFallback>
            {profile?.full_name?.charAt(0) || profile?.email?.charAt(0)}
          </AvatarFallback>
        </Avatar>
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium text-gray-900 truncate">
            {profile?.full_name || 'Utilisateur'}
          </p>
          <p className="text-xs text-gray-500 truncate">
            {profile?.email}
          </p>
          <div className="flex items-center space-x-2 mt-1">
            <Badge variant="outline" className="text-xs">
              {profile?.role?.toUpperCase()}
            </Badge>
            <Badge 
              variant="outline" 
              className={`text-xs ${
                profile?.statut === 'white' ? 'border-green-500 text-green-700' :
                profile?.statut === 'grey' ? 'border-yellow-500 text-yellow-700' :
                'border-red-500 text-red-700'
              }`}
            >
              {profile?.statut?.toUpperCase()}
            </Badge>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex flex-1 flex-col">
        <ul role="list" className="flex flex-1 flex-col gap-y-7">
          <li>
            <ul role="list" className="-mx-2 space-y-1">
              {navigation.map((item) => {
                const isActive = pathname === item.href
                const Icon = item.icon
                
                return (
                  <li key={item.name}>
                    <button
                      onClick={() => onNavigate(item.href, item.disabled)}
                      disabled={item.disabled}
                      className={`
                        group flex w-full gap-x-3 rounded-md p-3 text-left text-sm leading-6 font-semibold transition-colors
                        ${isActive 
                          ? 'bg-amber-50 text-amber-700 border border-amber-200' 
                          : item.disabled
                            ? 'text-gray-400 cursor-not-allowed'
                            : 'text-gray-700 hover:text-amber-700 hover:bg-amber-50'
                        }
                      `}
                    >
                      <Icon className={`h-6 w-6 shrink-0 ${isActive ? 'text-amber-700' : 'text-gray-400'}`} />
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <span>{item.name}</span>
                          <Badge 
                            className={`text-xs ${item.badgeColor} text-white`}
                          >
                            {item.badge}
                          </Badge>
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          {item.description}
                        </p>
                      </div>
                    </button>
                  </li>
                )
              })}
            </ul>
          </li>
          
          {/* Bottom Actions */}
          <li className="mt-auto">
            <div className="space-y-1">
              <Link
                href="/profile"
                className="group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold text-gray-700 hover:text-amber-700 hover:bg-amber-50"
              >
                <User className="h-6 w-6 shrink-0 text-gray-400" />
                Profil
              </Link>
              
              <button
                onClick={onSignOut}
                className="group flex w-full gap-x-3 rounded-md p-2 text-left text-sm leading-6 font-semibold text-gray-700 hover:text-red-700 hover:bg-red-50"
              >
                <LogOut className="h-6 w-6 shrink-0 text-gray-400" />
                Déconnexion
              </button>
            </div>
          </li>
        </ul>
      </nav>
    </div>
  )
}
