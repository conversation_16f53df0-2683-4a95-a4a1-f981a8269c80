import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Types pour la base de données
export interface User {
  id: string
  email: string
  full_name: string
  company: string
  phone: string
  membership_level: 'bronze' | 'silver' | 'gold' | 'platinum'
  points: number
  created_at: string
  updated_at: string
}

export interface Product {
  id: string
  name: string
  description: string
  category: string
  brand: string
  model: string
  price: number
  stock_quantity: number
  technical_specs: Record<string, any>
  images: string[]
  qr_code: string
  created_at: string
  updated_at: string
}

export interface Alert {
  id: string
  user_id: string
  type: 'stock' | 'price' | 'new_product' | 'training'
  title: string
  message: string
  is_read: boolean
  created_at: string
}

export interface TechnicalValidation {
  id: string
  user_id: string
  product_id: string
  validation_status: 'pending' | 'approved' | 'rejected'
  technical_notes: string
  validated_by: string
  created_at: string
  updated_at: string
}

// Types pour le Hub d'Information et Veille
export interface MarketAlert {
  id: string
  type: 'stock_low' | 'stock_out' | 'price_change' | 'new_product' | 'market_trend'
  title: string
  message: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  category: string
  product_id?: string
  affected_regions: string[]
  is_active: boolean
  created_at: string
  expires_at?: string
}

export interface StockUpdate {
  id: string
  product_id: string
  supplier_id: string
  previous_quantity: number
  current_quantity: number
  location: string
  update_type: 'increase' | 'decrease' | 'restock' | 'out_of_stock'
  created_at: string
}

export interface PriceHistory {
  id: string
  product_id: string
  supplier_id: string
  price: number
  currency: string
  change_percentage: number
  effective_date: string
  created_at: string
}

export interface TrainingEvent {
  id: string
  title: string
  description: string
  type: 'webinar' | 'workshop' | 'certification' | 'conference'
  instructor: string
  start_date: string
  end_date: string
  location: string
  is_virtual: boolean
  max_participants: number
  current_participants: number
  membership_required: 'bronze' | 'silver' | 'gold' | 'platinum' | null
  registration_fee: number
  status: 'upcoming' | 'ongoing' | 'completed' | 'cancelled'
  created_at: string
}

export interface NewsUpdate {
  id: string
  title: string
  content: string
  summary: string
  category: 'industry' | 'regulation' | 'technology' | 'market' | 'company'
  author: string
  source_url?: string
  image_url?: string
  tags: string[]
  is_featured: boolean
  published_at: string
  created_at: string
}

export interface Supplier {
  id: string
  name: string
  company: string
  email: string
  phone: string
  address: string
  country: string
  specialties: string[]
  reliability_score: number
  response_time_hours: number
  is_verified: boolean
  created_at: string
  updated_at: string
}
