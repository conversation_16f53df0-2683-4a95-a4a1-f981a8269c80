import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Types pour la base de données
export interface User {
  id: string
  email: string
  full_name: string
  company: string
  phone: string
  membership_level: 'bronze' | 'silver' | 'gold' | 'platinum'
  points: number
  created_at: string
  updated_at: string
}

export interface Product {
  id: string
  name: string
  description: string
  category: string
  brand: string
  model: string
  price: number
  stock_quantity: number
  technical_specs: Record<string, any>
  images: string[]
  qr_code: string
  created_at: string
  updated_at: string
}

export interface Alert {
  id: string
  user_id: string
  type: 'stock' | 'price' | 'new_product' | 'training'
  title: string
  message: string
  is_read: boolean
  created_at: string
}

export interface TechnicalValidation {
  id: string
  user_id: string
  product_id: string
  validation_status: 'pending' | 'approved' | 'rejected'
  technical_notes: string
  validated_by: string
  created_at: string
  updated_at: string
}
