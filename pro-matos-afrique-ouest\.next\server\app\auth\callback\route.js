/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/auth/callback/route";
exports.ids = ["app/auth/callback/route"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fcallback%2Froute&page=%2Fauth%2Fcallback%2Froute&appPaths=&pagePath=private-next-app-dir%2Fauth%2Fcallback%2Froute.ts&appDir=C%3A%5CUsers%5CMartial%5CDocuments%5CPro%20Matos%20Afrique%20Ouest%5Cpro-matos-afrique-ouest%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CMartial%5CDocuments%5CPro%20Matos%20Afrique%20Ouest%5Cpro-matos-afrique-ouest&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fcallback%2Froute&page=%2Fauth%2Fcallback%2Froute&appPaths=&pagePath=private-next-app-dir%2Fauth%2Fcallback%2Froute.ts&appDir=C%3A%5CUsers%5CMartial%5CDocuments%5CPro%20Matos%20Afrique%20Ouest%5Cpro-matos-afrique-ouest%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CMartial%5CDocuments%5CPro%20Matos%20Afrique%20Ouest%5Cpro-matos-afrique-ouest&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Martial_Documents_Pro_Matos_Afrique_Ouest_pro_matos_afrique_ouest_src_app_auth_callback_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/auth/callback/route.ts */ \"(rsc)/./src/app/auth/callback/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/auth/callback/route\",\n        pathname: \"/auth/callback\",\n        filename: \"route\",\n        bundlePath: \"app/auth/callback/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\app\\\\auth\\\\callback\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Martial_Documents_Pro_Matos_Afrique_Ouest_pro_matos_afrique_ouest_src_app_auth_callback_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/auth/callback/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fcallback%2Froute&page=%2Fauth%2Fcallback%2Froute&appPaths=&pagePath=private-next-app-dir%2Fauth%2Fcallback%2Froute.ts&appDir=C%3A%5CUsers%5CMartial%5CDocuments%5CPro%20Matos%20Afrique%20Ouest%5Cpro-matos-afrique-ouest%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CMartial%5CDocuments%5CPro%20Matos%20Afrique%20Ouest%5Cpro-matos-afrique-ouest&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/auth/callback/route.ts":
/*!****************************************!*\
  !*** ./src/app/auth/callback/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(rsc)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n\n\nasync function GET(request) {\n    const requestUrl = new URL(request.url);\n    const code = requestUrl.searchParams.get(\"code\");\n    const error = requestUrl.searchParams.get(\"error\");\n    console.log(\"=== AUTH CALLBACK DEBUG ===\");\n    console.log(\"URL compl\\xe8te:\", requestUrl.toString());\n    console.log(\"Code re\\xe7u:\", code);\n    console.log(\"Erreur re\\xe7ue:\", error);\n    console.log(\"Tous les param\\xe8tres:\", Object.fromEntries(requestUrl.searchParams));\n    if (error) {\n        console.error(\"Erreur dans l'URL:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.redirect(`${requestUrl.origin}/auth/signin?error=${error}`);\n    }\n    if (code) {\n        const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n        const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createRouteHandlerClient)({\n            cookies: ()=>cookieStore\n        });\n        try {\n            console.log(\"Tentative d'\\xe9change du code...\");\n            const { data, error } = await supabase.auth.exchangeCodeForSession(code);\n            if (error) {\n                console.error(\"Erreur lors de l'\\xe9change du code:\", error);\n                return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.redirect(`${requestUrl.origin}/auth/signin?error=auth_error`);\n            }\n            if (data.session) {\n                console.log(\"✅ Session cr\\xe9\\xe9e avec succ\\xe8s pour:\", data.session.user.email);\n                console.log(\"User ID:\", data.session.user.id);\n                // Rediriger vers le hub après connexion réussie\n                return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.redirect(`${requestUrl.origin}/hub`);\n            } else {\n                console.log(\"❌ Pas de session cr\\xe9\\xe9e\");\n                return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.redirect(`${requestUrl.origin}/auth/signin?error=no_session`);\n            }\n        } catch (error) {\n            console.error(\"❌ Exception lors de l'\\xe9change du code:\", error);\n            return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.redirect(`${requestUrl.origin}/auth/signin?error=auth_error`);\n        }\n    }\n    // Si pas de code, rediriger vers la connexion\n    console.log(\"❌ Aucun code re\\xe7u dans l'URL\");\n    return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.redirect(`${requestUrl.origin}/auth/signin?error=no_code`);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/auth/callback/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows","vendor-chunks/jose","vendor-chunks/set-cookie-parser"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fcallback%2Froute&page=%2Fauth%2Fcallback%2Froute&appPaths=&pagePath=private-next-app-dir%2Fauth%2Fcallback%2Froute.ts&appDir=C%3A%5CUsers%5CMartial%5CDocuments%5CPro%20Matos%20Afrique%20Ouest%5Cpro-matos-afrique-ouest%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CMartial%5CDocuments%5CPro%20Matos%20Afrique%20Ouest%5Cpro-matos-afrique-ouest&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();