/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/auth/callback/route";
exports.ids = ["app/auth/callback/route"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fcallback%2Froute&page=%2Fauth%2Fcallback%2Froute&appPaths=&pagePath=private-next-app-dir%2Fauth%2Fcallback%2Froute.ts&appDir=C%3A%5CUsers%5CMartial%5CDocuments%5CPro%20Matos%20Afrique%20Ouest%5Cpro-matos-afrique-ouest%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CMartial%5CDocuments%5CPro%20Matos%20Afrique%20Ouest%5Cpro-matos-afrique-ouest&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fcallback%2Froute&page=%2Fauth%2Fcallback%2Froute&appPaths=&pagePath=private-next-app-dir%2Fauth%2Fcallback%2Froute.ts&appDir=C%3A%5CUsers%5CMartial%5CDocuments%5CPro%20Matos%20Afrique%20Ouest%5Cpro-matos-afrique-ouest%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CMartial%5CDocuments%5CPro%20Matos%20Afrique%20Ouest%5Cpro-matos-afrique-ouest&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Martial_Documents_Pro_Matos_Afrique_Ouest_pro_matos_afrique_ouest_src_app_auth_callback_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/auth/callback/route.ts */ \"(rsc)/./src/app/auth/callback/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/auth/callback/route\",\n        pathname: \"/auth/callback\",\n        filename: \"route\",\n        bundlePath: \"app/auth/callback/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\app\\\\auth\\\\callback\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Martial_Documents_Pro_Matos_Afrique_Ouest_pro_matos_afrique_ouest_src_app_auth_callback_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/auth/callback/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fcallback%2Froute&page=%2Fauth%2Fcallback%2Froute&appPaths=&pagePath=private-next-app-dir%2Fauth%2Fcallback%2Froute.ts&appDir=C%3A%5CUsers%5CMartial%5CDocuments%5CPro%20Matos%20Afrique%20Ouest%5Cpro-matos-afrique-ouest%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CMartial%5CDocuments%5CPro%20Matos%20Afrique%20Ouest%5Cpro-matos-afrique-ouest&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/auth/callback/route.ts":
/*!****************************************!*\
  !*** ./src/app/auth/callback/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(rsc)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n\n\nasync function GET(request) {\n    const requestUrl = new URL(request.url);\n    const token_hash = requestUrl.searchParams.get(\"token_hash\");\n    const type = requestUrl.searchParams.get(\"type\");\n    const code = requestUrl.searchParams.get(\"code\");\n    const error = requestUrl.searchParams.get(\"error\");\n    console.log(\"=== AUTH CALLBACK DEBUG ===\");\n    console.log(\"URL compl\\xe8te:\", requestUrl.toString());\n    console.log(\"Code re\\xe7u:\", code);\n    console.log(\"Token hash re\\xe7u:\", token_hash);\n    console.log(\"Type re\\xe7u:\", type);\n    console.log(\"Erreur re\\xe7ue:\", error);\n    console.log(\"Tous les param\\xe8tres:\", Object.fromEntries(requestUrl.searchParams));\n    if (error) {\n        console.error(\"Erreur dans l'URL:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.redirect(`${requestUrl.origin}/auth/signin?error=${error}`);\n    }\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createRouteHandlerClient)({\n        cookies: ()=>cookieStore\n    });\n    // Gestion du magic link avec token_hash\n    if (token_hash && type) {\n        try {\n            console.log(\"Tentative de v\\xe9rification du token hash...\");\n            const { data, error } = await supabase.auth.verifyOtp({\n                token_hash,\n                type: type\n            });\n            if (error) {\n                console.error(\"Erreur lors de la v\\xe9rification du token:\", error);\n                return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.redirect(`${requestUrl.origin}/auth/signin?error=token_error`);\n            }\n            if (data.session) {\n                console.log(\"✅ Session cr\\xe9\\xe9e avec succ\\xe8s pour:\", data.session.user.email);\n                console.log(\"User ID:\", data.session.user.id);\n                return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.redirect(`${requestUrl.origin}/hub`);\n            }\n        } catch (error) {\n            console.error(\"❌ Exception lors de la v\\xe9rification du token:\", error);\n            return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.redirect(`${requestUrl.origin}/auth/signin?error=token_error`);\n        }\n    }\n    // Gestion du code d'autorisation (OAuth)\n    if (code) {\n        try {\n            console.log(\"Tentative d'\\xe9change du code...\");\n            const { data, error } = await supabase.auth.exchangeCodeForSession(code);\n            if (error) {\n                console.error(\"Erreur lors de l'\\xe9change du code:\", error);\n                return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.redirect(`${requestUrl.origin}/auth/signin?error=auth_error`);\n            }\n            if (data.session) {\n                console.log(\"✅ Session cr\\xe9\\xe9e avec succ\\xe8s pour:\", data.session.user.email);\n                console.log(\"User ID:\", data.session.user.id);\n                return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.redirect(`${requestUrl.origin}/hub`);\n            }\n        } catch (error) {\n            console.error(\"❌ Exception lors de l'\\xe9change du code:\", error);\n            return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.redirect(`${requestUrl.origin}/auth/signin?error=auth_error`);\n        }\n    }\n    // Si aucun paramètre valide, rediriger vers la connexion\n    console.log(\"❌ Aucun param\\xe8tre d'authentification valide re\\xe7u\");\n    return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.redirect(`${requestUrl.origin}/auth/signin?error=no_auth_params`);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/auth/callback/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows","vendor-chunks/jose","vendor-chunks/set-cookie-parser"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fcallback%2Froute&page=%2Fauth%2Fcallback%2Froute&appPaths=&pagePath=private-next-app-dir%2Fauth%2Fcallback%2Froute.ts&appDir=C%3A%5CUsers%5CMartial%5CDocuments%5CPro%20Matos%20Afrique%20Ouest%5Cpro-matos-afrique-ouest%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CMartial%5CDocuments%5CPro%20Matos%20Afrique%20Ouest%5Cpro-matos-afrique-ouest&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();