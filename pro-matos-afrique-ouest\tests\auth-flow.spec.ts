import { test, expect } from '@playwright/test'

test.describe('Authentication Flow', () => {
  test('should display signin page', async ({ page }) => {
    await page.goto('/auth/signin')
    
    // Vérifier les éléments de la page
    await expect(page.locator('h1')).toContainText('Pro Matos')
    await expect(page.locator('h2')).toContainText('L\'écosystème professionnel de l\'électrique')
    await expect(page.locator('input[type="email"]')).toBeVisible()
    await expect(page.locator('button[type="submit"]')).toContainText('Envoyer le lien de connexion')
  })

  test('should validate email input', async ({ page }) => {
    await page.goto('/auth/signin')
    
    // Tenter de soumettre sans email
    await page.click('button[type="submit"]')
    
    // Vérifier que le formulaire ne se soumet pas
    await expect(page.locator('input[type="email"]')).toBeVisible()
  })

  test('should show email sent confirmation', async ({ page }) => {
    await page.goto('/auth/signin')
    
    // Remplir l'email
    await page.fill('input[type="email"]', '<EMAIL>')
    
    // Soumettre le formulaire
    await page.click('button[type="submit"]')
    
    // Vérifier le message de confirmation
    await expect(page.locator('text=Email envoyé !')).toBeVisible()
    await expect(page.locator('text=<EMAIL>')).toBeVisible()
  })

  test('should redirect unauthenticated users', async ({ page }) => {
    await page.goto('/hub')
    
    // Vérifier la redirection vers signin
    await expect(page).toHaveURL('/auth/signin')
  })
})

test.describe('Navigation', () => {
  test('should redirect from home to signin when not authenticated', async ({ page }) => {
    await page.goto('/')
    
    // Attendre la redirection
    await page.waitForURL('/auth/signin')
    await expect(page).toHaveURL('/auth/signin')
  })
})
