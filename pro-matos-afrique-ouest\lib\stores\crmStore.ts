import { create } from 'zustand'
import { User, Validation } from '@/lib/types/database'
import { supabase } from '@/lib/supabase/client'

interface CRMState {
  users: User[]
  validations: Validation[]
  loading: boolean
  fetchUsers: () => Promise<void>
  fetchValidations: () => Promise<void>
  updateUserRole: (userId: string, role: User['role']) => Promise<{ error: Error | null }>
  updateUserStatus: (userId: string, statut: User['statut']) => Promise<{ error: Error | null }>
  updateValidationStatus: (validationId: number, status: Validation['status'], adminNotes?: string) => Promise<{ error: Error | null }>
  incrementDevis: (userId: string) => Promise<{ error: Error | null }>
  incrementAchats: (userId: string) => Promise<{ error: Error | null }>
}

export const useCRMStore = create<CRMState>((set, get) => ({
  users: [],
  validations: [],
  loading: false,

  fetchUsers: async () => {
    try {
      set({ loading: true })

      const { data, error } = await supabase
        .from('users')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error

      set({ users: data || [] })
    } catch (error) {
      console.error('Erreur lors du chargement des utilisateurs:', error)
    } finally {
      set({ loading: false })
    }
  },

  fetchValidations: async () => {
    try {
      const { data, error } = await supabase
        .from('validations')
        .select(`
          *,
          users (
            full_name,
            email,
            company
          )
        `)
        .order('created_at', { ascending: false })

      if (error) throw error

      set({ validations: data || [] })
    } catch (error) {
      console.error('Erreur lors du chargement des validations:', error)
    }
  },

  updateUserRole: async (userId: string, role: User['role']) => {
    try {
      const { error } = await supabase
        .from('users')
        .update({ 
          role,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)

      if (error) throw error

      // Mettre à jour l'état local
      const { users } = get()
      set({
        users: users.map(user => 
          user.id === userId 
            ? { ...user, role, updated_at: new Date().toISOString() }
            : user
        )
      })

      return { error: null }
    } catch (error) {
      return { error: error as Error }
    }
  },

  updateUserStatus: async (userId: string, statut: User['statut']) => {
    try {
      const { error } = await supabase
        .from('users')
        .update({ 
          statut,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)

      if (error) throw error

      // Mettre à jour l'état local
      const { users } = get()
      set({
        users: users.map(user => 
          user.id === userId 
            ? { ...user, statut, updated_at: new Date().toISOString() }
            : user
        )
      })

      return { error: null }
    } catch (error) {
      return { error: error as Error }
    }
  },

  updateValidationStatus: async (validationId: number, status: Validation['status'], adminNotes?: string) => {
    try {
      const { error } = await supabase
        .from('validations')
        .update({ 
          status,
          admin_notes: adminNotes,
          updated_at: new Date().toISOString()
        })
        .eq('id', validationId)

      if (error) throw error

      // Mettre à jour l'état local
      const { validations } = get()
      set({
        validations: validations.map(validation => 
          validation.id === validationId 
            ? { 
                ...validation, 
                status, 
                admin_notes: adminNotes || validation.admin_notes,
                updated_at: new Date().toISOString() 
              }
            : validation
        )
      })

      return { error: null }
    } catch (error) {
      return { error: error as Error }
    }
  },

  incrementDevis: async (userId: string) => {
    try {
      const { users } = get()
      const user = users.find(u => u.id === userId)
      if (!user) throw new Error('Utilisateur non trouvé')

      const newDevisCount = user.devis_demandes + 1
      let newStatut = user.statut

      // Logique de changement de statut
      if (newDevisCount > 3 && user.achats === 0) {
        newStatut = 'grey'
      }

      const { error } = await supabase
        .from('users')
        .update({ 
          devis_demandes: newDevisCount,
          statut: newStatut,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)

      if (error) throw error

      // Mettre à jour l'état local
      set({
        users: users.map(user => 
          user.id === userId 
            ? { 
                ...user, 
                devis_demandes: newDevisCount,
                statut: newStatut,
                updated_at: new Date().toISOString() 
              }
            : user
        )
      })

      return { error: null }
    } catch (error) {
      return { error: error as Error }
    }
  },

  incrementAchats: async (userId: string) => {
    try {
      const { users } = get()
      const user = users.find(u => u.id === userId)
      if (!user) throw new Error('Utilisateur non trouvé')

      const newAchatsCount = user.achats + 1
      let newStatut = user.statut

      // Logique de changement de statut
      if (newAchatsCount > 0 && user.statut === 'grey') {
        newStatut = 'white'
      }

      const { error } = await supabase
        .from('users')
        .update({ 
          achats: newAchatsCount,
          statut: newStatut,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)

      if (error) throw error

      // Mettre à jour l'état local
      set({
        users: users.map(user => 
          user.id === userId 
            ? { 
                ...user, 
                achats: newAchatsCount,
                statut: newStatut,
                updated_at: new Date().toISOString() 
              }
            : user
        )
      })

      return { error: null }
    } catch (error) {
      return { error: error as Error }
    }
  }
}))

// Helpers pour les actions CRM
export const useCRMActions = () => {
  const { 
    updateUserRole, 
    updateUserStatus, 
    updateValidationStatus,
    incrementDevis,
    incrementAchats
  } = useCRMStore()

  const promoteUser = async (userId: string, onSuccess?: () => void, onError?: (error: string) => void) => {
    const { error } = await updateUserRole(userId, 'member')
    
    if (error) {
      onError?.(error.message)
    } else {
      onSuccess?.()
    }
  }

  const demoteUser = async (userId: string, onSuccess?: () => void, onError?: (error: string) => void) => {
    const { error } = await updateUserRole(userId, 'guest')
    
    if (error) {
      onError?.(error.message)
    } else {
      onSuccess?.()
    }
  }

  const blacklistUser = async (userId: string, onSuccess?: () => void, onError?: (error: string) => void) => {
    const { error } = await updateUserStatus(userId, 'black')
    
    if (error) {
      onError?.(error.message)
    } else {
      onSuccess?.()
    }
  }

  return {
    promoteUser,
    demoteUser,
    blacklistUser,
    updateValidationStatus,
    incrementDevis,
    incrementAchats
  }
}
