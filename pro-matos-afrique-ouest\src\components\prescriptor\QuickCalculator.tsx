'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Calculator,
  Zap,
  AlertTriangle,
  CheckCircle,
  Info,
  Download,
  Copy,
  RefreshCw
} from 'lucide-react'

interface CalculationResult {
  section_minimale: number
  section_normalisee: number
  chute_tension_reelle: number
  intensite_admissible: number
  validation_status: 'valid' | 'warning' | 'error'
  recommendations: string[]
  warnings: string[]
}

export default function QuickCalculator() {
  const [inputs, setInputs] = useState({
    courant: 32,
    longueur: 50,
    chute_tension_max: 3,
    mode_pose: 'enterré',
    materiau: 'cuivre',
    temperature: 30
  })
  
  const [result, setResult] = useState<CalculationResult | null>(null)
  const [isCalculating, setIsCalculating] = useState(false)
  const [showDetails, setShowDetails] = useState(false)

  // Sections normalisées en mm²
  const sectionsNormalisees = [1.5, 2.5, 4, 6, 10, 16, 25, 35, 50, 70, 95, 120, 150, 185, 240, 300, 400, 500]
  
  // Intensités admissibles selon le mode de pose (simplifié)
  const intensitesAdmissibles = {
    'enterré': [23, 31, 42, 54, 75, 100, 133, 171, 207, 258, 319, 367, 412, 456, 542, 621, 689, 754],
    'aérien': [18, 24, 32, 41, 57, 76, 101, 129, 157, 196, 246, 284, 319, 353, 419, 483, 540, 593],
    'goulotte': [15, 20, 27, 35, 48, 64, 85, 108, 132, 165, 207, 239, 268, 297, 352, 406, 454, 498],
    'chemin_cables': [20, 27, 36, 46, 64, 85, 113, 145, 176, 220, 275, 317, 356, 394, 467, 538, 601, 660]
  }

  const calculateCableSection = () => {
    setIsCalculating(true)
    
    // Simulation de calcul avec délai
    setTimeout(() => {
      const { courant, longueur, chute_tension_max, mode_pose, materiau, temperature } = inputs
      
      // Résistivité du matériau (Ω.mm²/m)
      const resistivite = materiau === 'cuivre' ? 0.0225 : 0.037 // aluminium
      
      // Tension nominale (V)
      const tension = 400 // Triphasé
      
      // Calcul de la section minimale pour la chute de tension
      // ΔU = ρ × L × I / S  =>  S = ρ × L × I / ΔU
      const chute_tension_absolue = (tension * chute_tension_max) / 100
      const section_minimale_chute = (resistivite * longueur * courant) / chute_tension_absolue
      
      // Facteur de correction température
      const facteur_temperature = temperature > 30 ? 0.87 : 1.0
      
      // Section minimale pour l'intensité admissible
      const intensites = intensitesAdmissibles[mode_pose as keyof typeof intensitesAdmissibles]
      let section_minimale_intensite = sectionsNormalisees[0]
      
      for (let i = 0; i < intensites.length; i++) {
        if ((intensites[i] * facteur_temperature) >= courant) {
          section_minimale_intensite = sectionsNormalisees[i]
          break
        }
      }
      
      // Section minimale = max des deux critères
      const section_minimale = Math.max(section_minimale_chute, section_minimale_intensite)
      
      // Section normalisée supérieure
      let section_normalisee = sectionsNormalisees[sectionsNormalisees.length - 1]
      for (const section of sectionsNormalisees) {
        if (section >= section_minimale) {
          section_normalisee = section
          break
        }
      }
      
      // Chute de tension réelle avec la section normalisée
      const chute_tension_reelle = (resistivite * longueur * courant) / (section_normalisee * tension) * 100
      
      // Intensité admissible de la section choisie
      const index_section = sectionsNormalisees.indexOf(section_normalisee)
      const intensite_admissible = intensites[index_section] * facteur_temperature
      
      // Validation et recommandations
      let validation_status: 'valid' | 'warning' | 'error' = 'valid'
      const recommendations: string[] = []
      const warnings: string[] = []
      
      if (chute_tension_reelle > chute_tension_max) {
        validation_status = 'error'
        warnings.push(`Chute de tension dépassée: ${chute_tension_reelle.toFixed(2)}% > ${chute_tension_max}%`)
      } else if (chute_tension_reelle > chute_tension_max * 0.8) {
        validation_status = 'warning'
        warnings.push(`Chute de tension proche de la limite: ${chute_tension_reelle.toFixed(2)}%`)
      }
      
      if (intensite_admissible < courant * 1.1) {
        validation_status = 'warning'
        warnings.push('Marge de sécurité faible sur l\'intensité admissible')
      }
      
      // Recommandations
      if (longueur > 100) {
        recommendations.push('Considérer un câble de section supérieure pour les grandes longueurs')
      }
      
      if (mode_pose === 'enterré') {
        recommendations.push('Prévoir une protection mécanique adaptée')
      }
      
      recommendations.push(`Vérifier la compatibilité avec les bornes (section max: ${section_normalisee}mm²)`)
      
      setResult({
        section_minimale: Math.round(section_minimale * 100) / 100,
        section_normalisee,
        chute_tension_reelle: Math.round(chute_tension_reelle * 100) / 100,
        intensite_admissible: Math.round(intensite_admissible),
        validation_status,
        recommendations,
        warnings
      })
      
      setIsCalculating(false)
    }, 1500)
  }

  const resetCalculator = () => {
    setInputs({
      courant: 32,
      longueur: 50,
      chute_tension_max: 3,
      mode_pose: 'enterré',
      materiau: 'cuivre',
      temperature: 30
    })
    setResult(null)
    setShowDetails(false)
  }

  const exportResult = () => {
    if (!result) return
    
    const reportContent = `
CALCUL DE SECTION DE CÂBLE - Pro Matos Afrique Ouest
=====================================================

PARAMÈTRES D'ENTRÉE:
- Courant nominal: ${inputs.courant} A
- Longueur: ${inputs.longueur} m
- Chute de tension max: ${inputs.chute_tension_max} %
- Mode de pose: ${inputs.mode_pose}
- Matériau: ${inputs.materiau}
- Température ambiante: ${inputs.temperature} °C

RÉSULTATS:
- Section minimale calculée: ${result.section_minimale} mm²
- Section normalisée: ${result.section_normalisee} mm²
- Chute de tension réelle: ${result.chute_tension_reelle} %
- Intensité admissible: ${result.intensite_admissible} A

RECOMMANDATIONS:
${result.recommendations.map(r => `- ${r}`).join('\n')}

${result.warnings.length > 0 ? `AVERTISSEMENTS:\n${result.warnings.map(w => `- ${w}`).join('\n')}` : ''}

Calcul effectué selon la norme NF C 15-100
Généré le ${new Date().toLocaleString('fr-FR')}
    `.trim()
    
    const blob = new Blob([reportContent], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `calcul-section-cable-${Date.now()}.txt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const copyResult = () => {
    if (!result) return
    
    const text = `Section câble: ${result.section_normalisee}mm² (${inputs.courant}A, ${inputs.longueur}m, ΔU=${result.chute_tension_reelle}%)`
    navigator.clipboard.writeText(text)
  }

  useEffect(() => {
    // Calcul automatique quand les paramètres changent
    if (inputs.courant > 0 && inputs.longueur > 0) {
      const timer = setTimeout(() => {
        calculateCableSection()
      }, 500)
      return () => clearTimeout(timer)
    }
  }, [inputs])

  return (
    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-blue-900 flex items-center space-x-2">
          <Calculator className="h-6 w-6" />
          <span>Calculatrice Rapide - Section de Câble</span>
        </h3>
        <div className="flex space-x-2">
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="px-3 py-1 text-xs bg-blue-100 text-blue-800 rounded-full hover:bg-blue-200 transition-colors"
          >
            {showDetails ? 'Masquer' : 'Détails'}
          </button>
          <button
            onClick={resetCalculator}
            className="p-1 text-blue-600 hover:text-blue-700"
            title="Réinitialiser"
          >
            <RefreshCw className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Paramètres d'entrée */}
      <div className="grid md:grid-cols-3 lg:grid-cols-6 gap-4 mb-4">
        <div>
          <label className="block text-sm font-medium text-blue-800 mb-1">
            Courant (A)
          </label>
          <input
            type="number"
            value={inputs.courant}
            onChange={(e) => setInputs(prev => ({ ...prev, courant: Number(e.target.value) }))}
            className="w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-400"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-blue-800 mb-1">
            Longueur (m)
          </label>
          <input
            type="number"
            value={inputs.longueur}
            onChange={(e) => setInputs(prev => ({ ...prev, longueur: Number(e.target.value) }))}
            className="w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-400"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-blue-800 mb-1">
            ΔU max (%)
          </label>
          <input
            type="number"
            step="0.1"
            value={inputs.chute_tension_max}
            onChange={(e) => setInputs(prev => ({ ...prev, chute_tension_max: Number(e.target.value) }))}
            className="w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-400"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-blue-800 mb-1">
            Mode de pose
          </label>
          <select
            value={inputs.mode_pose}
            onChange={(e) => setInputs(prev => ({ ...prev, mode_pose: e.target.value }))}
            className="w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-400"
          >
            <option value="enterré">Enterré</option>
            <option value="aérien">Aérien</option>
            <option value="goulotte">Goulotte</option>
            <option value="chemin_cables">Chemin câbles</option>
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium text-blue-800 mb-1">
            Matériau
          </label>
          <select
            value={inputs.materiau}
            onChange={(e) => setInputs(prev => ({ ...prev, materiau: e.target.value }))}
            className="w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-400"
          >
            <option value="cuivre">Cuivre</option>
            <option value="aluminium">Aluminium</option>
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium text-blue-800 mb-1">
            Temp. (°C)
          </label>
          <input
            type="number"
            value={inputs.temperature}
            onChange={(e) => setInputs(prev => ({ ...prev, temperature: Number(e.target.value) }))}
            className="w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-400"
          />
        </div>
      </div>

      {/* Résultats */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-lg p-4 border border-blue-200"
      >
        {isCalculating ? (
          <div className="text-center py-8">
            <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-blue-700">Calcul en cours selon NF C 15-100...</p>
          </div>
        ) : result ? (
          <div>
            <div className="flex items-center justify-between mb-4">
              <div className="text-center flex-1">
                <div className={`text-3xl font-bold ${
                  result.validation_status === 'valid' ? 'text-green-600' :
                  result.validation_status === 'warning' ? 'text-orange-600' :
                  'text-red-600'
                }`}>
                  {result.section_normalisee} mm²
                </div>
                <div className="text-sm text-blue-700">Section recommandée</div>
              </div>
              
              <div className="flex items-center space-x-2">
                {result.validation_status === 'valid' && <CheckCircle className="h-6 w-6 text-green-600" />}
                {result.validation_status === 'warning' && <AlertTriangle className="h-6 w-6 text-orange-600" />}
                {result.validation_status === 'error' && <AlertTriangle className="h-6 w-6 text-red-600" />}
                
                <div className="flex space-x-1">
                  <button
                    onClick={copyResult}
                    className="p-2 text-blue-600 hover:text-blue-700"
                    title="Copier"
                  >
                    <Copy className="h-4 w-4" />
                  </button>
                  <button
                    onClick={exportResult}
                    className="p-2 text-blue-600 hover:text-blue-700"
                    title="Exporter"
                  >
                    <Download className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>

            {showDetails && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                className="space-y-3"
              >
                <div className="grid md:grid-cols-3 gap-4 text-sm">
                  <div className="bg-blue-50 p-3 rounded">
                    <div className="font-medium text-blue-800">Section minimale</div>
                    <div className="text-blue-900">{result.section_minimale} mm²</div>
                  </div>
                  <div className="bg-blue-50 p-3 rounded">
                    <div className="font-medium text-blue-800">Chute tension réelle</div>
                    <div className="text-blue-900">{result.chute_tension_reelle}%</div>
                  </div>
                  <div className="bg-blue-50 p-3 rounded">
                    <div className="font-medium text-blue-800">Intensité admissible</div>
                    <div className="text-blue-900">{result.intensite_admissible} A</div>
                  </div>
                </div>

                {result.warnings.length > 0 && (
                  <div className="bg-orange-50 border border-orange-200 rounded p-3">
                    <div className="flex items-center space-x-2 mb-2">
                      <AlertTriangle className="h-4 w-4 text-orange-600" />
                      <span className="font-medium text-orange-800">Avertissements</span>
                    </div>
                    <ul className="text-sm text-orange-700 space-y-1">
                      {result.warnings.map((warning, index) => (
                        <li key={index}>• {warning}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {result.recommendations.length > 0 && (
                  <div className="bg-green-50 border border-green-200 rounded p-3">
                    <div className="flex items-center space-x-2 mb-2">
                      <Info className="h-4 w-4 text-green-600" />
                      <span className="font-medium text-green-800">Recommandations</span>
                    </div>
                    <ul className="text-sm text-green-700 space-y-1">
                      {result.recommendations.map((rec, index) => (
                        <li key={index}>• {rec}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </motion.div>
            )}
          </div>
        ) : (
          <div className="text-center py-4 text-blue-700">
            Modifiez les paramètres pour lancer le calcul automatique
          </div>
        )}
      </motion.div>
    </div>
  )
}
