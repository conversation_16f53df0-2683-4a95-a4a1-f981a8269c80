import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { BadgeService, BadgeConfig } from '@/lib/services/badgeService'

export async function GET(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })

    // Vérifier l'authentification
    const { data: { session }, error: authError } = await supabase.auth.getSession()
    
    if (authError || !session) {
      return NextResponse.json(
        { error: 'Non authentifié' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const format = searchParams.get('format') || 'svg'
    const variant = searchParams.get('variant') || 'full' // 'full' | 'compact'
    const download = searchParams.get('download') === 'true'

    // Récupérer les informations utilisateur
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('full_name, email, role, created_at')
      .eq('id', session.user.id)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Utilisateur non trouvé' },
        { status: 404 }
      )
    }

    // Vérifier que l'utilisateur a accès aux badges (membre ou plus)
    if (user.role === 'guest') {
      return NextResponse.json(
        { error: 'Accès membre requis pour générer un badge' },
        { status: 403 }
      )
    }

    // Préparer la configuration du badge
    const badgeConfig: BadgeConfig = {
      userName: user.full_name || user.email.split('@')[0],
      userRole: user.role as 'member' | 'vip' | 'admin',
      memberSince: new Date(user.created_at),
      specialties: [], // TODO: Récupérer depuis le profil utilisateur
      achievements: [], // TODO: Récupérer les achievements
      customText: searchParams.get('customText') || undefined
    }

    // Valider la configuration
    const validation = BadgeService.validateConfig(badgeConfig)
    if (!validation.valid) {
      return NextResponse.json(
        { error: 'Configuration invalide', details: validation.errors },
        { status: 400 }
      )
    }

    // Générer le badge
    let svg: string
    if (variant === 'compact') {
      svg = BadgeService.generateCompactBadge(badgeConfig)
    } else {
      svg = BadgeService.generateBadge(badgeConfig)
    }

    // Si c'est un téléchargement, retourner le fichier directement
    if (download) {
      const filename = `badge-${user.role}-${Date.now()}.svg`
      
      return new NextResponse(svg, {
        headers: {
          'Content-Type': 'image/svg+xml',
          'Content-Disposition': `attachment; filename="${filename}"`,
          'Cache-Control': 'no-cache'
        }
      })
    }

    // Sinon, retourner les données JSON
    return NextResponse.json({
      success: true,
      data: {
        svg,
        dataUrl: BadgeService.svgToDataUrl(svg),
        config: badgeConfig,
        variant,
        format
      }
    })

  } catch (error) {
    console.error('Erreur API badge:', error)
    return NextResponse.json(
      { error: 'Erreur serveur' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })

    // Vérifier l'authentification
    const { data: { session }, error: authError } = await supabase.auth.getSession()
    
    if (authError || !session) {
      return NextResponse.json(
        { error: 'Non authentifié' },
        { status: 401 }
      )
    }

    const { customText, specialties, achievements, variant } = await request.json()

    // Récupérer les informations utilisateur
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('full_name, email, role, created_at')
      .eq('id', session.user.id)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Utilisateur non trouvé' },
        { status: 404 }
      )
    }

    // Vérifier l'accès
    if (user.role === 'guest') {
      return NextResponse.json(
        { error: 'Accès membre requis pour personnaliser un badge' },
        { status: 403 }
      )
    }

    // Préparer la configuration personnalisée
    const badgeConfig: BadgeConfig = {
      userName: user.full_name || user.email.split('@')[0],
      userRole: user.role as 'member' | 'vip' | 'admin',
      memberSince: new Date(user.created_at),
      specialties: specialties || [],
      achievements: achievements || [],
      customText: customText
    }

    // Valider la configuration
    const validation = BadgeService.validateConfig(badgeConfig)
    if (!validation.valid) {
      return NextResponse.json(
        { error: 'Configuration invalide', details: validation.errors },
        { status: 400 }
      )
    }

    // Générer le badge personnalisé
    let svg: string
    if (variant === 'compact') {
      svg = BadgeService.generateCompactBadge(badgeConfig)
    } else {
      svg = BadgeService.generateBadge(badgeConfig)
    }

    // Optionnel: Sauvegarder la configuration personnalisée
    await supabase
      .from('user_badge_configs')
      .upsert({
        user_id: session.user.id,
        custom_text: customText,
        specialties: specialties,
        achievements: achievements,
        updated_at: new Date().toISOString()
      })
      .catch(error => {
        // Ne pas faire échouer si la table n'existe pas
        console.log('Info: Table user_badge_configs non trouvée, ignoré')
      })

    return NextResponse.json({
      success: true,
      data: {
        svg,
        dataUrl: BadgeService.svgToDataUrl(svg),
        config: badgeConfig
      },
      message: 'Badge personnalisé généré avec succès'
    })

  } catch (error) {
    console.error('Erreur API badge personnalisé:', error)
    return NextResponse.json(
      { error: 'Erreur serveur' },
      { status: 500 }
    )
  }
}
