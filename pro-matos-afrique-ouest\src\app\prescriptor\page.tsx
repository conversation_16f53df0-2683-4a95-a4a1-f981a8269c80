'use client'

import { motion } from 'framer-motion'
import { 
  ArrowLeft,
  FileText,
  Calculator,
  Award,
  Building,
  Users,
  TrendingUp,
  CheckCircle,
  Clock
} from 'lucide-react'
import Link from 'next/link'
import PrescriptorModule from '@/components/prescriptor/PrescriptorModule'
import StandardsControl from '@/components/prescriptor/StandardsControl'
import GlobalNavigation from '@/components/navigation/GlobalNavigation'
import ResponsiveLayout from '@/components/layout/ResponsiveLayout'
import { AnimatedGrid } from '@/components/ui/AnimatedCard'

export default function PrescriptorPage() {
  const stats = [
    {
      label: 'Templates Disponibles',
      value: '47',
      change: '+5',
      changeType: 'increase',
      icon: <FileText className="h-6 w-6" />
    },
    {
      label: 'Projets Actifs',
      value: '23',
      change: '+8',
      changeType: 'increase',
      icon: <Building className="h-6 w-6" />
    },
    {
      label: 'Calculs Automatisés',
      value: '156',
      change: '+12',
      changeType: 'increase',
      icon: <Calculator className="h-6 w-6" />
    },
    {
      label: 'Certificats Émis',
      value: '89',
      change: '+15',
      changeType: 'increase',
      icon: <Award className="h-6 w-6" />
    }
  ]

  const prescriptionFlow = [
    {
      step: 1,
      title: 'Sélection Template',
      description: 'Choisissez un template adapté à votre projet',
      icon: <FileText className="h-6 w-6" />,
      color: 'from-blue-500 to-blue-600'
    },
    {
      step: 2,
      title: 'Calculs Automatiques',
      description: 'Les calculs se font automatiquement selon les normes',
      icon: <Calculator className="h-6 w-6" />,
      color: 'from-green-500 to-green-600'
    },
    {
      step: 3,
      title: 'Validation Technique',
      description: 'Vérification de conformité et recommandations',
      icon: <CheckCircle className="h-6 w-6" />,
      color: 'from-purple-500 to-purple-600'
    },
    {
      step: 4,
      title: 'Certification',
      description: 'Génération du certificat de conformité officiel',
      icon: <Award className="h-6 w-6" />,
      color: 'from-amber-500 to-amber-600'
    }
  ]

  return (
    <>
      <GlobalNavigation />
      <ResponsiveLayout
        title="Module Prescripteur Professionnel"
        subtitle="Kits de prescription, calculs automatisés et certification de conformité pour imposer vos standards"
        actions={
          <Link
            href="/"
            className="flex items-center space-x-2 text-slate-700 hover:text-amber-600 transition-colors"
          >
            <ArrowLeft className="h-5 w-5" />
            <span className="hidden sm:inline">Retour à l'accueil</span>
          </Link>
        }
      >
        {/* Statistiques rapides */}
        <AnimatedGrid className="grid-cols-1 md:grid-cols-2 lg:grid-cols-4 mb-8">
          {stats.map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.05 }}
              className="industrial-card p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">{stat.label}</p>
                  <div className="flex items-baseline space-x-2">
                    <p className="text-2xl font-bold text-slate-900">{stat.value}</p>
                    <span className={`text-sm font-medium ${
                      stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {stat.change}
                    </span>
                  </div>
                </div>
                <div className="p-3 bg-amber-100 rounded-lg text-amber-600">
                  {stat.icon}
                </div>
              </div>
            </motion.div>
          ))}
        </AnimatedGrid>

        {/* Processus de prescription */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mb-8"
        >
          <h2 className="text-2xl font-bold text-slate-900 mb-6">Processus de Prescription Automatisé</h2>
          <AnimatedGrid className="md:grid-cols-4">
            {prescriptionFlow.map((step, index) => (
              <motion.div
                key={step.step}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3 + index * 0.1 }}
                className="relative"
              >
                <div className="industrial-card p-6 text-center">
                  <div className={`w-16 h-16 bg-gradient-to-r ${step.color} rounded-full flex items-center justify-center text-white mx-auto mb-4`}>
                    {step.icon}
                  </div>
                  <div className="absolute -top-2 -left-2 w-8 h-8 bg-amber-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
                    {step.step}
                  </div>
                  <h3 className="text-lg font-bold text-slate-900 mb-2">{step.title}</h3>
                  <p className="text-sm text-slate-600">{step.description}</p>
                </div>

                {/* Flèche de connexion */}
                {index < prescriptionFlow.length - 1 && (
                  <div className="hidden md:block absolute top-1/2 -right-3 transform -translate-y-1/2">
                    <div className="w-6 h-0.5 bg-amber-400"></div>
                    <div className="absolute -right-1 -top-1 w-2 h-2 bg-amber-400 rotate-45"></div>
                  </div>
                )}
              </motion.div>
            ))}
          </AnimatedGrid>
        </motion.div>

        {/* Contrôle des standards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <StandardsControl />
        </motion.div>

        {/* Module Prescripteur principal */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <PrescriptorModule />
        </motion.div>

        {/* Section avantages stratégiques */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="mt-8 bg-gradient-to-r from-slate-900 to-slate-800 text-white rounded-lg p-8"
        >
          <h3 className="text-2xl font-bold mb-6 text-center">
            Contrôlez les Prescriptions = Contrôlez le Marché
          </h3>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-amber-400 to-amber-500 rounded-lg flex items-center justify-center mx-auto mb-4">
                <FileText className="h-8 w-8 text-slate-900" />
              </div>
              <h4 className="text-xl font-bold mb-3">Templates Standardisés</h4>
              <p className="text-slate-300">
                Vos templates deviennent la référence. Les architectes et ingénieurs utilisent VOS standards.
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-amber-400 to-amber-500 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Calculator className="h-8 w-8 text-slate-900" />
              </div>
              <h4 className="text-xl font-bold mb-3">Calculs Certifiés</h4>
              <p className="text-slate-300">
                Vos calculs font autorité. Impossible de prescrire sans passer par votre validation technique.
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-amber-400 to-amber-500 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Award className="h-8 w-8 text-slate-900" />
              </div>
              <h4 className="text-xl font-bold mb-3">Certification Obligatoire</h4>
              <p className="text-slate-300">
                Vos certificats deviennent indispensables. Aucun projet sérieux sans votre validation.
              </p>
            </div>
          </div>
        </motion.div>
      </ResponsiveLayout>
    </>
  )
}
