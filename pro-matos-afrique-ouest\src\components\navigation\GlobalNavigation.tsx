'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Home,
  Database,
  Users,
  FileText,
  Award,
  Settings,
  Menu,
  X,
  ChevronDown,
  Bell,
  Search,
  User,
  Crown,
  Zap
} from 'lucide-react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'

interface GlobalNavigationProps {
  className?: string
}

export default function GlobalNavigation({ className = '' }: GlobalNavigationProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false)
  const [notifications, setNotifications] = useState(3)
  const pathname = usePathname()

  const navigationItems = [
    {
      name: 'Accueil',
      href: '/',
      icon: <Home className="h-5 w-5" />,
      description: 'Tableau de bord principal'
    },
    {
      name: 'Hub Information',
      href: '/hub',
      icon: <Database className="h-5 w-5" />,
      description: 'Centre d\'information temps réel',
      badge: 'LIVE'
    },
    {
      name: 'Expert Conseil',
      href: '/expert',
      icon: <Users className="h-5 w-5" />,
      description: 'Consultation et validation technique',
      badge: 'PRO'
    },
    {
      name: 'Module Prescripteur',
      href: '/prescriptor',
      icon: <FileText className="h-5 w-5" />,
      description: 'Templates et certification',
      badge: 'PREMIUM'
    },
    {
      name: 'Club Membre',
      href: '/club',
      icon: <Crown className="h-5 w-5" />,
      description: 'Réseau exclusif professionnel',
      badge: 'VIP'
    }
  ]

  const userProfile = {
    name: 'Jean Kouassi',
    role: 'Ingénieur Électricien',
    company: 'SODECI',
    level: 'Gold',
    avatar: '/api/placeholder/40/40'
  }

  const quickActions = [
    { name: 'Nouveau Projet', href: '/prescriptor?action=new', icon: <FileText className="h-4 w-4" /> },
    { name: 'Consultation Express', href: '/expert?mode=express', icon: <Zap className="h-4 w-4" /> },
    { name: 'Alertes Hub', href: '/hub?tab=alerts', icon: <Bell className="h-4 w-4" /> }
  ]

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isProfileMenuOpen) {
        setIsProfileMenuOpen(false)
      }
    }

    document.addEventListener('click', handleClickOutside)
    return () => document.removeEventListener('click', handleClickOutside)
  }, [isProfileMenuOpen])

  const isActive = (href: string) => {
    if (href === '/') return pathname === '/'
    return pathname.startsWith(href)
  }

  return (
    <nav className={`bg-white/95 backdrop-blur-md border-b border-gray-200 sticky top-0 z-50 ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo et branding */}
          <div className="flex items-center space-x-4">
            <Link href="/" className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-amber-400 to-amber-500 rounded-lg flex items-center justify-center">
                <Zap className="h-6 w-6 text-slate-900" />
              </div>
              <div className="hidden sm:block">
                <h1 className="text-xl font-bold text-slate-900">Pro Matos</h1>
                <p className="text-xs text-slate-600">Afrique Ouest</p>
              </div>
            </Link>
          </div>

          {/* Navigation principale - Desktop */}
          <div className="hidden lg:flex items-center space-x-1">
            {navigationItems.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className={`relative px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 group ${
                  isActive(item.href)
                    ? 'bg-amber-100 text-amber-900'
                    : 'text-slate-700 hover:bg-slate-100 hover:text-slate-900'
                }`}
              >
                <div className="flex items-center space-x-2">
                  {item.icon}
                  <span>{item.name}</span>
                  {item.badge && (
                    <span className={`px-2 py-0.5 text-xs font-bold rounded-full ${
                      item.badge === 'LIVE' ? 'bg-red-100 text-red-700' :
                      item.badge === 'PRO' ? 'bg-blue-100 text-blue-700' :
                      item.badge === 'PREMIUM' ? 'bg-purple-100 text-purple-700' :
                      'bg-amber-100 text-amber-700'
                    }`}>
                      {item.badge}
                    </span>
                  )}
                </div>
                
                {/* Tooltip */}
                <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-3 py-2 bg-slate-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap">
                  {item.description}
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-b-slate-900"></div>
                </div>
              </Link>
            ))}
          </div>

          {/* Actions rapides et profil */}
          <div className="flex items-center space-x-4">
            {/* Barre de recherche rapide */}
            <div className="hidden md:block relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
              <input
                type="text"
                placeholder="Recherche rapide..."
                className="pl-10 pr-4 py-2 w-64 border border-slate-300 rounded-lg focus:ring-2 focus:ring-amber-400 focus:border-transparent text-sm"
              />
            </div>

            {/* Actions rapides */}
            <div className="hidden lg:flex items-center space-x-2">
              {quickActions.map((action) => (
                <Link
                  key={action.name}
                  href={action.href}
                  className="p-2 text-slate-600 hover:text-amber-600 hover:bg-amber-50 rounded-lg transition-colors"
                  title={action.name}
                >
                  {action.icon}
                </Link>
              ))}
            </div>

            {/* Notifications */}
            <button className="relative p-2 text-slate-600 hover:text-amber-600 hover:bg-amber-50 rounded-lg transition-colors">
              <Bell className="h-5 w-5" />
              {notifications > 0 && (
                <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                  {notifications}
                </span>
              )}
            </button>

            {/* Profil utilisateur */}
            <div className="relative">
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  setIsProfileMenuOpen(!isProfileMenuOpen)
                }}
                className="flex items-center space-x-3 p-2 rounded-lg hover:bg-slate-100 transition-colors"
              >
                <div className="w-8 h-8 bg-gradient-to-r from-amber-400 to-amber-500 rounded-full flex items-center justify-center">
                  <User className="h-4 w-4 text-slate-900" />
                </div>
                <div className="hidden sm:block text-left">
                  <div className="text-sm font-medium text-slate-900">{userProfile.name}</div>
                  <div className="text-xs text-slate-600">{userProfile.level}</div>
                </div>
                <ChevronDown className="h-4 w-4 text-slate-600" />
              </button>

              {/* Menu profil */}
              <AnimatePresence>
                {isProfileMenuOpen && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute right-0 top-full mt-2 w-64 bg-white rounded-lg shadow-lg border border-slate-200 py-2"
                  >
                    <div className="px-4 py-3 border-b border-slate-200">
                      <div className="font-medium text-slate-900">{userProfile.name}</div>
                      <div className="text-sm text-slate-600">{userProfile.role}</div>
                      <div className="text-sm text-slate-600">{userProfile.company}</div>
                      <div className="mt-2">
                        <span className="px-2 py-1 bg-amber-100 text-amber-800 text-xs font-medium rounded-full">
                          Niveau {userProfile.level}
                        </span>
                      </div>
                    </div>
                    
                    <div className="py-2">
                      <Link href="/profile" className="block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100">
                        Mon Profil
                      </Link>
                      <Link href="/settings" className="block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100">
                        Paramètres
                      </Link>
                      <Link href="/billing" className="block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100">
                        Facturation
                      </Link>
                      <div className="border-t border-slate-200 mt-2 pt-2">
                        <button className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                          Déconnexion
                        </button>
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Menu mobile */}
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="lg:hidden p-2 text-slate-600 hover:text-slate-900"
            >
              {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>

        {/* Menu mobile */}
        <AnimatePresence>
          {isMobileMenuOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="lg:hidden border-t border-slate-200 py-4"
            >
              <div className="space-y-2">
                {navigationItems.map((item) => (
                  <Link
                    key={item.name}
                    href={item.href}
                    onClick={() => setIsMobileMenuOpen(false)}
                    className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors ${
                      isActive(item.href)
                        ? 'bg-amber-100 text-amber-900'
                        : 'text-slate-700 hover:bg-slate-100'
                    }`}
                  >
                    {item.icon}
                    <div className="flex-1">
                      <div className="font-medium">{item.name}</div>
                      <div className="text-sm text-slate-600">{item.description}</div>
                    </div>
                    {item.badge && (
                      <span className={`px-2 py-1 text-xs font-bold rounded-full ${
                        item.badge === 'LIVE' ? 'bg-red-100 text-red-700' :
                        item.badge === 'PRO' ? 'bg-blue-100 text-blue-700' :
                        item.badge === 'PREMIUM' ? 'bg-purple-100 text-purple-700' :
                        'bg-amber-100 text-amber-700'
                      }`}>
                        {item.badge}
                      </span>
                    )}
                  </Link>
                ))}
              </div>

              {/* Actions rapides mobile */}
              <div className="mt-4 pt-4 border-t border-slate-200">
                <div className="px-4 mb-3">
                  <div className="text-sm font-medium text-slate-900">Actions Rapides</div>
                </div>
                <div className="space-y-2">
                  {quickActions.map((action) => (
                    <Link
                      key={action.name}
                      href={action.href}
                      onClick={() => setIsMobileMenuOpen(false)}
                      className="flex items-center space-x-3 px-4 py-2 text-slate-700 hover:bg-slate-100 rounded-lg"
                    >
                      {action.icon}
                      <span>{action.name}</span>
                    </Link>
                  ))}
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </nav>
  )
}
