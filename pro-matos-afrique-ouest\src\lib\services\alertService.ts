import { supabase } from '@/lib/supabase/client'
import { EmailService } from './emailService'

export interface Alert {
  id: number
  title: string
  body: string
  type: 'info' | 'warning' | 'critical' | 'promo'
  category: string
  is_active: boolean
  created_at: string
  isSubscribed?: boolean
}

export interface AlertSubscription {
  user_id: string
  alert_id: number
  subscribed_at: string
}

export interface AlertStats {
  total: number
  byType: Record<string, number>
  byCategory: Record<string, number>
  recent: number
}

export class AlertService {
  /**
   * Récupère toutes les alertes actives avec statut d'abonnement
   */
  static async getAlertsWithSubscriptions(userId?: string): Promise<{ 
    success: boolean
    data?: Alert[]
    error?: string 
  }> {
    try {
      let query = supabase
        .from('alerts')
        .select(`
          *,
          user_alerts!left(user_id)
        `)
        .eq('is_active', true)
        .order('created_at', { ascending: false })

      const { data: alerts, error } = await query

      if (error) {
        return { success: false, error: error.message }
      }

      // Marquer les alertes auxquelles l'utilisateur est abonné
      const alertsWithSubscription = alerts?.map(alert => ({
        ...alert,
        isSubscribed: userId ? alert.user_alerts?.some((sub: any) => sub.user_id === userId) : false,
        user_alerts: undefined // Nettoyer les données
      })) || []

      return { success: true, data: alertsWithSubscription }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erreur inconnue'
      }
    }
  }

  /**
   * Récupère les alertes par catégorie
   */
  static async getAlertsByCategory(
    category: string,
    userId?: string
  ): Promise<{ success: boolean; data?: Alert[]; error?: string }> {
    try {
      const { data: alerts, error } = await supabase
        .from('alerts')
        .select(`
          *,
          user_alerts!left(user_id)
        `)
        .eq('is_active', true)
        .eq('category', category)
        .order('created_at', { ascending: false })

      if (error) {
        return { success: false, error: error.message }
      }

      const alertsWithSubscription = alerts?.map(alert => ({
        ...alert,
        isSubscribed: userId ? alert.user_alerts?.some((sub: any) => sub.user_id === userId) : false,
        user_alerts: undefined
      })) || []

      return { success: true, data: alertsWithSubscription }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erreur inconnue'
      }
    }
  }

  /**
   * S'abonner à une alerte
   */
  static async subscribeToAlert(
    userId: string,
    alertId: number
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // Vérifier que l'alerte existe et est active
      const { data: alert, error: alertError } = await supabase
        .from('alerts')
        .select('id, title, body, type, category')
        .eq('id', alertId)
        .eq('is_active', true)
        .single()

      if (alertError || !alert) {
        return { success: false, error: 'Alerte non trouvée ou inactive' }
      }

      // Vérifier si déjà abonné
      const { data: existing } = await supabase
        .from('user_alerts')
        .select('user_id')
        .eq('user_id', userId)
        .eq('alert_id', alertId)
        .single()

      if (existing) {
        return { success: false, error: 'Déjà abonné à cette alerte' }
      }

      // Créer l'abonnement
      const { error: subscribeError } = await supabase
        .from('user_alerts')
        .insert({
          user_id: userId,
          alert_id: alertId
        })

      if (subscribeError) {
        return { success: false, error: subscribeError.message }
      }

      // Récupérer les infos utilisateur pour notification
      const { data: user } = await supabase
        .from('users')
        .select('email, full_name')
        .eq('id', userId)
        .single()

      // Envoyer notification d'abonnement (optionnel)
      if (user) {
        await EmailService.sendAlertNotification({
          userEmail: user.email,
          userName: user.full_name || user.email,
          alertTitle: alert.title,
          alertBody: alert.body,
          alertType: alert.type,
          alertCategory: alert.category
        })
      }

      return { success: true }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erreur abonnement'
      }
    }
  }

  /**
   * Se désabonner d'une alerte
   */
  static async unsubscribeFromAlert(
    userId: string,
    alertId: number
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await supabase
        .from('user_alerts')
        .delete()
        .eq('user_id', userId)
        .eq('alert_id', alertId)

      if (error) {
        return { success: false, error: error.message }
      }

      return { success: true }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erreur désabonnement'
      }
    }
  }

  /**
   * Récupère les abonnements d'un utilisateur
   */
  static async getUserSubscriptions(userId: string): Promise<{
    success: boolean
    data?: Alert[]
    error?: string
  }> {
    try {
      const { data: subscriptions, error } = await supabase
        .from('user_alerts')
        .select(`
          alert_id,
          subscribed_at,
          alerts (
            id,
            title,
            body,
            type,
            category,
            is_active,
            created_at
          )
        `)
        .eq('user_id', userId)

      if (error) {
        return { success: false, error: error.message }
      }

      const alerts = subscriptions?.map(sub => ({
        ...sub.alerts,
        isSubscribed: true,
        subscribed_at: sub.subscribed_at
      })).filter(alert => alert.is_active) || []

      return { success: true, data: alerts }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erreur récupération abonnements'
      }
    }
  }

  /**
   * Récupère les statistiques des alertes
   */
  static async getAlertStats(): Promise<{
    success: boolean
    data?: AlertStats
    error?: string
  }> {
    try {
      const { data: alerts, error } = await supabase
        .from('alerts')
        .select('type, category, created_at')
        .eq('is_active', true)

      if (error) {
        return { success: false, error: error.message }
      }

      const now = new Date()
      const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)

      const stats: AlertStats = {
        total: alerts?.length || 0,
        byType: {},
        byCategory: {},
        recent: 0
      }

      alerts?.forEach(alert => {
        // Par type
        stats.byType[alert.type] = (stats.byType[alert.type] || 0) + 1
        
        // Par catégorie
        stats.byCategory[alert.category] = (stats.byCategory[alert.category] || 0) + 1
        
        // Récentes (7 derniers jours)
        if (new Date(alert.created_at) > weekAgo) {
          stats.recent++
        }
      })

      return { success: true, data: stats }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erreur statistiques'
      }
    }
  }

  /**
   * Crée une nouvelle alerte (admin seulement)
   */
  static async createAlert(alert: {
    title: string
    body: string
    type: 'info' | 'warning' | 'critical' | 'promo'
    category: string
  }): Promise<{ success: boolean; data?: Alert; error?: string }> {
    try {
      const { data: newAlert, error } = await supabase
        .from('alerts')
        .insert({
          title: alert.title,
          body: alert.body,
          type: alert.type,
          category: alert.category,
          is_active: true
        })
        .select()
        .single()

      if (error) {
        return { success: false, error: error.message }
      }

      return { success: true, data: newAlert }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erreur création alerte'
      }
    }
  }
}
