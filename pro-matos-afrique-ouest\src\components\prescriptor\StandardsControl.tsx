'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Shield,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Award,
  FileText,
  Calculator,
  Users,
  TrendingUp,
  Lock,
  Crown
} from 'lucide-react'

export default function StandardsControl() {
  const [selectedStandard, setSelectedStandard] = useState('pro_matos')

  const standards = {
    pro_matos: {
      name: 'Standards Pro Matos',
      description: 'Méthodes et calculs certifiés Pro Matos Afrique Ouest',
      status: 'imposed',
      adoption: 87,
      color: 'from-amber-500 to-amber-600',
      icon: <Crown className="h-6 w-6" />
    },
    nf_c15100: {
      name: 'NF C 15-100',
      description: 'Norme française pour installations électriques BT',
      status: 'integrated',
      adoption: 65,
      color: 'from-blue-500 to-blue-600',
      icon: <Shield className="h-6 w-6" />
    },
    iec_60364: {
      name: 'IEC 60364',
      description: 'Norme internationale installations électriques',
      status: 'referenced',
      adoption: 45,
      color: 'from-green-500 to-green-600',
      icon: <FileText className="h-6 w-6" />
    },
    local_standards: {
      name: 'Normes Locales',
      description: 'Standards nationaux Afrique de l\'Ouest',
      status: 'obsolete',
      adoption: 23,
      color: 'from-gray-400 to-gray-500',
      icon: <XCircle className="h-6 w-6" />
    }
  }

  const controlMetrics = [
    {
      label: 'Templates Utilisés',
      value: '2,847',
      change: '+34%',
      description: 'Projets créés avec nos templates',
      icon: <FileText className="h-5 w-5" />
    },
    {
      label: 'Calculs Validés',
      value: '15,692',
      change: '+67%',
      description: 'Calculs effectués via nos moteurs',
      icon: <Calculator className="h-5 w-5" />
    },
    {
      label: 'Professionnels Formés',
      value: '1,234',
      change: '+89%',
      description: 'Ingénieurs certifiés à nos méthodes',
      icon: <Users className="h-5 w-5" />
    },
    {
      label: 'Certificats Émis',
      value: '567',
      change: '+156%',
      description: 'Projets certifiés conformes',
      icon: <Award className="h-5 w-5" />
    }
  ]

  const adoptionSteps = [
    {
      phase: 'Introduction',
      description: 'Lancement des standards Pro Matos',
      completion: 100,
      status: 'complete'
    },
    {
      phase: 'Adoption Précoce',
      description: 'Premiers utilisateurs et retours',
      completion: 100,
      status: 'complete'
    },
    {
      phase: 'Croissance',
      description: 'Expansion et formation massive',
      completion: 87,
      status: 'current'
    },
    {
      phase: 'Domination',
      description: 'Standard de facto du marché',
      completion: 45,
      status: 'future'
    },
    {
      phase: 'Monopole',
      description: 'Contrôle total de l\'écosystème',
      completion: 12,
      status: 'future'
    }
  ]

  const competitorAnalysis = [
    {
      name: 'Schneider Electric',
      marketShare: 35,
      proMatosIntegration: 78,
      status: 'partner',
      trend: 'increasing'
    },
    {
      name: 'ABB',
      marketShare: 28,
      proMatosIntegration: 65,
      status: 'partner',
      trend: 'increasing'
    },
    {
      name: 'Legrand',
      marketShare: 22,
      proMatosIntegration: 82,
      status: 'partner',
      trend: 'stable'
    },
    {
      name: 'Bureaux d\'études locaux',
      marketShare: 15,
      proMatosIntegration: 34,
      status: 'resistance',
      trend: 'decreasing'
    }
  ]

  return (
    <div className="space-y-8">
      {/* Header de contrôle */}
      <div className="bg-gradient-to-r from-amber-50 to-yellow-50 rounded-lg p-6 border border-amber-200">
        <div className="flex items-center space-x-4 mb-4">
          <Crown className="h-8 w-8 text-amber-600" />
          <div>
            <h2 className="text-2xl font-bold text-amber-900">Contrôle des Standards du Marché</h2>
            <p className="text-amber-700">Imposer nos méthodes comme référence incontournable</p>
          </div>
        </div>
        
        <div className="grid md:grid-cols-4 gap-4">
          {controlMetrics.map((metric, index) => (
            <motion.div
              key={metric.label}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-white rounded-lg p-4 border border-amber-200"
            >
              <div className="flex items-center space-x-2 mb-2">
                <div className="p-2 bg-amber-100 rounded-lg text-amber-600">
                  {metric.icon}
                </div>
                <div>
                  <div className="text-lg font-bold text-amber-900">{metric.value}</div>
                  <div className="text-xs text-amber-700">{metric.label}</div>
                </div>
              </div>
              <div className="text-xs text-green-600 font-medium">{metric.change}</div>
              <div className="text-xs text-amber-600 mt-1">{metric.description}</div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Comparaison des standards */}
      <div className="grid lg:grid-cols-2 gap-8">
        <div>
          <h3 className="text-xl font-bold text-gray-900 mb-4">Standards en Compétition</h3>
          <div className="space-y-3">
            {Object.entries(standards).map(([key, standard]) => (
              <button
                key={key}
                onClick={() => setSelectedStandard(key)}
                className={`w-full p-4 rounded-lg border-2 transition-all text-left ${
                  selectedStandard === key
                    ? 'border-amber-400 bg-amber-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-10 h-10 bg-gradient-to-r ${standard.color} rounded-lg flex items-center justify-center text-white`}>
                      {standard.icon}
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">{standard.name}</h4>
                      <p className="text-sm text-gray-600">{standard.description}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-gray-900">{standard.adoption}%</div>
                    <div className="text-xs text-gray-500">Adoption</div>
                  </div>
                </div>
                
                <div className="mt-3">
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full bg-gradient-to-r ${standard.color}`}
                      style={{ width: `${standard.adoption}%` }}
                    ></div>
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>

        <div>
          <h3 className="text-xl font-bold text-gray-900 mb-4">Phases de Domination</h3>
          <div className="space-y-4">
            {adoptionSteps.map((step, index) => (
              <div key={step.phase} className="flex items-center space-x-4">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  step.status === 'complete' ? 'bg-green-500 text-white' :
                  step.status === 'current' ? 'bg-amber-500 text-white' :
                  'bg-gray-300 text-gray-600'
                }`}>
                  {step.status === 'complete' ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : step.status === 'current' ? (
                    <TrendingUp className="h-4 w-4" />
                  ) : (
                    <Lock className="h-4 w-4" />
                  )}
                </div>
                
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-1">
                    <h4 className="font-medium text-gray-900">{step.phase}</h4>
                    <span className="text-sm font-medium text-gray-600">{step.completion}%</span>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{step.description}</p>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full ${
                        step.status === 'complete' ? 'bg-green-500' :
                        step.status === 'current' ? 'bg-amber-500' :
                        'bg-gray-400'
                      }`}
                      style={{ width: `${step.completion}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Analyse concurrentielle */}
      <div>
        <h3 className="text-xl font-bold text-gray-900 mb-4">Intégration Pro Matos par Concurrent</h3>
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <div className="grid grid-cols-4 gap-4 p-4 bg-gray-50 border-b border-gray-200 font-medium text-gray-700">
            <div>Concurrent</div>
            <div>Part de Marché</div>
            <div>Intégration Pro Matos</div>
            <div>Tendance</div>
          </div>
          
          {competitorAnalysis.map((competitor, index) => (
            <motion.div
              key={competitor.name}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className="grid grid-cols-4 gap-4 p-4 border-b border-gray-100 hover:bg-gray-50"
            >
              <div className="flex items-center space-x-2">
                <div className={`w-3 h-3 rounded-full ${
                  competitor.status === 'partner' ? 'bg-green-500' :
                  competitor.status === 'resistance' ? 'bg-red-500' :
                  'bg-yellow-500'
                }`}></div>
                <span className="font-medium">{competitor.name}</span>
              </div>
              
              <div>
                <div className="text-sm font-medium">{competitor.marketShare}%</div>
                <div className="w-full bg-gray-200 rounded-full h-1 mt-1">
                  <div 
                    className="h-1 rounded-full bg-blue-500"
                    style={{ width: `${competitor.marketShare}%` }}
                  ></div>
                </div>
              </div>
              
              <div>
                <div className="text-sm font-medium">{competitor.proMatosIntegration}%</div>
                <div className="w-full bg-gray-200 rounded-full h-1 mt-1">
                  <div 
                    className="h-1 rounded-full bg-amber-500"
                    style={{ width: `${competitor.proMatosIntegration}%` }}
                  ></div>
                </div>
              </div>
              
              <div className="flex items-center space-x-1">
                {competitor.trend === 'increasing' ? (
                  <TrendingUp className="h-4 w-4 text-green-500" />
                ) : competitor.trend === 'decreasing' ? (
                  <TrendingUp className="h-4 w-4 text-red-500 rotate-180" />
                ) : (
                  <div className="h-4 w-4 bg-gray-400 rounded-full"></div>
                )}
                <span className="text-sm capitalize">{competitor.trend}</span>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Stratégie de contrôle */}
      <div className="bg-gradient-to-r from-slate-900 to-slate-800 text-white rounded-lg p-6">
        <h3 className="text-xl font-bold mb-4">Stratégie de Contrôle Total</h3>
        <div className="grid md:grid-cols-3 gap-6">
          <div>
            <h4 className="font-semibold mb-2 flex items-center space-x-2">
              <FileText className="h-5 w-5 text-amber-400" />
              <span>Phase 1: Standards</span>
            </h4>
            <ul className="text-sm text-slate-300 space-y-1">
              <li>• Templates deviennent référence</li>
              <li>• Calculs certifiés obligatoires</li>
              <li>• Formation aux méthodes Pro Matos</li>
            </ul>
          </div>
          <div>
            <h4 className="font-semibold mb-2 flex items-center space-x-2">
              <Users className="h-5 w-5 text-amber-400" />
              <span>Phase 2: Réseau</span>
            </h4>
            <ul className="text-sm text-slate-300 space-y-1">
              <li>• Certification obligatoire</li>
              <li>• Club membre exclusif</li>
              <li>• Partenariats stratégiques</li>
            </ul>
          </div>
          <div>
            <h4 className="font-semibold mb-2 flex items-center space-x-2">
              <Crown className="h-5 w-5 text-amber-400" />
              <span>Phase 3: Domination</span>
            </h4>
            <ul className="text-sm text-slate-300 space-y-1">
              <li>• Contrôle de l'information</li>
              <li>• Validation technique obligatoire</li>
              <li>• Barrière à l'entrée naturelle</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
