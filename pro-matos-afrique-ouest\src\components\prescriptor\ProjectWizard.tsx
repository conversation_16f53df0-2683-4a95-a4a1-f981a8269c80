'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  ArrowRight,
  ArrowLeft,
  CheckCircle,
  FileText,
  User,
  MapPin,
  Calculator,
  Save,
  X
} from 'lucide-react'
import { useStore } from '@/store/useStore'
import { PrescriptorService } from '@/services/prescriptorService'

interface ProjectWizardProps {
  onClose: () => void
  onComplete: (project: any) => void
}

export default function ProjectWizard({ onClose, onComplete }: ProjectWizardProps) {
  const { prescriptionTemplates, addPrescriptionProject } = useStore()
  const [currentStep, setCurrentStep] = useState(1)
  const [projectData, setProjectData] = useState({
    template_id: '',
    project_name: '',
    client_name: '',
    client_company: '',
    project_description: '',
    location: '',
    technical_data: {} as Record<string, any>,
    notes: ''
  })

  const steps = [
    {
      id: 1,
      title: 'Template',
      description: 'Choisir le template de prescription',
      icon: <FileText className="h-5 w-5" />
    },
    {
      id: 2,
      title: 'Informations',
      description: 'Détails du projet et client',
      icon: <User className="h-5 w-5" />
    },
    {
      id: 3,
      title: 'Paramètres',
      description: 'Données techniques du projet',
      icon: <Calculator className="h-5 w-5" />
    },
    {
      id: 4,
      title: 'Validation',
      description: 'Vérification et création',
      icon: <CheckCircle className="h-5 w-5" />
    }
  ]

  const selectedTemplate = prescriptionTemplates.find(t => t.id === projectData.template_id)

  const nextStep = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const canProceed = () => {
    switch (currentStep) {
      case 1:
        return projectData.template_id !== ''
      case 2:
        return projectData.project_name && projectData.client_name && projectData.location
      case 3:
        return selectedTemplate ? 
          selectedTemplate.required_fields.every(field => projectData.technical_data[field]) :
          true
      default:
        return true
    }
  }

  const createProject = () => {
    if (!selectedTemplate) return

    // Calculer les valeurs automatiquement
    const calculatedValues = PrescriptorService.calculateProjectValues(
      projectData.technical_data, 
      selectedTemplate
    )

    const newProject = {
      id: `project_${Date.now()}`,
      user_id: 'current_user',
      template_id: projectData.template_id,
      project_name: projectData.project_name,
      client_name: projectData.client_name,
      client_company: projectData.client_company,
      project_description: projectData.project_description,
      location: projectData.location,
      project_data: projectData.technical_data,
      calculated_values: calculatedValues,
      selected_products: [],
      total_cost: Math.round(calculatedValues.puissance_totale * 50000) || 1000000, // Estimation
      status: 'draft' as const,
      compliance_verified: false,
      warranty_terms: 'Garantie standard 2 ans',
      notes: projectData.notes,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }

    addPrescriptionProject(newProject)
    onComplete(newProject)
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Sélectionner un Template de Prescription
            </h3>
            <div className="grid gap-4">
              {prescriptionTemplates.map((template) => (
                <button
                  key={template.id}
                  onClick={() => setProjectData(prev => ({ ...prev, template_id: template.id }))}
                  className={`p-4 rounded-lg border-2 transition-all text-left ${
                    projectData.template_id === template.id
                      ? 'border-amber-400 bg-amber-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-start space-x-3">
                    <div className={`w-10 h-10 bg-gradient-to-r ${
                      template.category === 'electrical' ? 'from-blue-500 to-blue-600' :
                      template.category === 'energy' ? 'from-green-500 to-green-600' :
                      'from-purple-500 to-purple-600'
                    } rounded-lg flex items-center justify-center text-white`}>
                      <FileText className="h-5 w-5" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900 mb-1">{template.name}</h4>
                      <p className="text-sm text-gray-600 mb-2">{template.description}</p>
                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        <span>Niveau: {template.complexity_level}</span>
                        <span>★ {template.rating}</span>
                        <span>{template.usage_count} utilisations</span>
                      </div>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>
        )

      case 2:
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Informations du Projet
            </h3>
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nom du projet *
                </label>
                <input
                  type="text"
                  value={projectData.project_name}
                  onChange={(e) => setProjectData(prev => ({ ...prev, project_name: e.target.value }))}
                  placeholder="Ex: Installation électrique bureau"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nom du client *
                </label>
                <input
                  type="text"
                  value={projectData.client_name}
                  onChange={(e) => setProjectData(prev => ({ ...prev, client_name: e.target.value }))}
                  placeholder="Ex: Jean Dupont"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Entreprise du client
                </label>
                <input
                  type="text"
                  value={projectData.client_company}
                  onChange={(e) => setProjectData(prev => ({ ...prev, client_company: e.target.value }))}
                  placeholder="Ex: SODECI"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Localisation *
                </label>
                <input
                  type="text"
                  value={projectData.location}
                  onChange={(e) => setProjectData(prev => ({ ...prev, location: e.target.value }))}
                  placeholder="Ex: Abidjan, Côte d'Ivoire"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description du projet
              </label>
              <textarea
                value={projectData.project_description}
                onChange={(e) => setProjectData(prev => ({ ...prev, project_description: e.target.value }))}
                rows={3}
                placeholder="Décrivez brièvement le projet..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400"
              />
            </div>
          </div>
        )

      case 3:
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Paramètres Techniques
            </h3>
            {selectedTemplate && (
              <div className="space-y-4">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                  <h4 className="font-medium text-blue-900 mb-2">Template: {selectedTemplate.name}</h4>
                  <p className="text-sm text-blue-700">{selectedTemplate.description}</p>
                </div>
                
                <div className="grid md:grid-cols-2 gap-4">
                  {selectedTemplate.required_fields.map((field) => (
                    <div key={field}>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {field.replace('_', ' ')} *
                      </label>
                      <input
                        type="number"
                        value={projectData.technical_data[field] || ''}
                        onChange={(e) => setProjectData(prev => ({
                          ...prev,
                          technical_data: {
                            ...prev.technical_data,
                            [field]: Number(e.target.value)
                          }
                        }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400"
                      />
                    </div>
                  ))}
                </div>
                
                {selectedTemplate.optional_fields.length > 0 && (
                  <>
                    <h4 className="font-medium text-gray-900 mt-6 mb-3">Paramètres optionnels</h4>
                    <div className="grid md:grid-cols-2 gap-4">
                      {selectedTemplate.optional_fields.map((field) => (
                        <div key={field}>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            {field.replace('_', ' ')}
                          </label>
                          <input
                            type="number"
                            value={projectData.technical_data[field] || ''}
                            onChange={(e) => setProjectData(prev => ({
                              ...prev,
                              technical_data: {
                                ...prev.technical_data,
                                [field]: Number(e.target.value)
                              }
                            }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400"
                          />
                        </div>
                      ))}
                    </div>
                  </>
                )}
              </div>
            )}
          </div>
        )

      case 4:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Validation du Projet
            </h3>
            
            <div className="bg-gray-50 rounded-lg p-4 space-y-3">
              <div className="grid md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Template:</span>
                  <p>{selectedTemplate?.name}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Projet:</span>
                  <p>{projectData.project_name}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Client:</span>
                  <p>{projectData.client_name} {projectData.client_company && `- ${projectData.client_company}`}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Localisation:</span>
                  <p>{projectData.location}</p>
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Notes additionnelles
              </label>
              <textarea
                value={projectData.notes}
                onChange={(e) => setProjectData(prev => ({ ...prev, notes: e.target.value }))}
                rows={3}
                placeholder="Notes ou commentaires sur le projet..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400"
              />
            </div>

            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span className="font-medium text-green-800">Prêt à créer</span>
              </div>
              <p className="text-sm text-green-700">
                Le projet sera créé avec les calculs automatiques selon le template sélectionné.
              </p>
            </div>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-2xl font-bold text-gray-900">Nouveau Projet de Prescription</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Progress Steps */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                  currentStep >= step.id
                    ? 'bg-amber-500 border-amber-500 text-white'
                    : 'border-gray-300 text-gray-500'
                }`}>
                  {currentStep > step.id ? (
                    <CheckCircle className="h-5 w-5" />
                  ) : (
                    step.icon
                  )}
                </div>
                <div className="ml-3">
                  <p className={`text-sm font-medium ${
                    currentStep >= step.id ? 'text-amber-600' : 'text-gray-500'
                  }`}>
                    {step.title}
                  </p>
                  <p className="text-xs text-gray-500">{step.description}</p>
                </div>
                {index < steps.length - 1 && (
                  <div className={`w-16 h-0.5 mx-4 ${
                    currentStep > step.id ? 'bg-amber-500' : 'bg-gray-300'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-96">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.2 }}
            >
              {renderStepContent()}
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200">
          <button
            onClick={prevStep}
            disabled={currentStep === 1}
            className="flex items-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Précédent</span>
          </button>

          <div className="text-sm text-gray-500">
            Étape {currentStep} sur {steps.length}
          </div>

          {currentStep < steps.length ? (
            <button
              onClick={nextStep}
              disabled={!canProceed()}
              className="flex items-center space-x-2 btn-premium disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span>Suivant</span>
              <ArrowRight className="h-4 w-4" />
            </button>
          ) : (
            <button
              onClick={createProject}
              className="flex items-center space-x-2 btn-premium"
            >
              <Save className="h-4 w-4" />
              <span>Créer le Projet</span>
            </button>
          )}
        </div>
      </motion.div>
    </div>
  )
}
