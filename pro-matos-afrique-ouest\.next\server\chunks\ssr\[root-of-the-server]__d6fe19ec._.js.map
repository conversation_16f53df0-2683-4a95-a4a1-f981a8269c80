{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/store/useStore.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { persist } from 'zustand/middleware'\nimport { User, Product, Alert, MarketAlert, StockUpdate, TrainingEvent, NewsUpdate } from '@/lib/supabase'\n\ninterface AppState {\n  // User state\n  user: User | null\n  isAuthenticated: boolean\n  \n  // Products state\n  products: Product[]\n  filteredProducts: Product[]\n  searchQuery: string\n  selectedCategory: string\n  \n  // Alerts state\n  alerts: Alert[]\n  unreadAlertsCount: number\n\n  // Hub d'Information state\n  marketAlerts: MarketAlert[]\n  stockUpdates: StockUpdate[]\n  trainingEvents: TrainingEvent[]\n  newsUpdates: NewsUpdate[]\n  realTimeConnected: boolean\n  lastUpdateTime: string\n\n  // UI state\n  isLoading: boolean\n  isDarkMode: boolean\n  sidebarOpen: boolean\n  \n  // Actions\n  setUser: (user: User | null) => void\n  setProducts: (products: Product[]) => void\n  setSearchQuery: (query: string) => void\n  setSelectedCategory: (category: string) => void\n  setAlerts: (alerts: <PERSON><PERSON>[]) => void\n  markAlertAsRead: (alertId: string) => void\n\n  // Hub d'Information actions\n  setMarketAlerts: (alerts: MarketAlert[]) => void\n  addMarketAlert: (alert: MarketAlert) => void\n  setStockUpdates: (updates: StockUpdate[]) => void\n  addStockUpdate: (update: StockUpdate) => void\n  setTrainingEvents: (events: TrainingEvent[]) => void\n  setNewsUpdates: (news: NewsUpdate[]) => void\n  addNewsUpdate: (news: NewsUpdate) => void\n  setRealTimeConnected: (connected: boolean) => void\n  updateLastUpdateTime: () => void\n\n  setLoading: (loading: boolean) => void\n  toggleDarkMode: () => void\n  toggleSidebar: () => void\n  filterProducts: () => void\n}\n\nexport const useStore = create<AppState>()(\n  persist(\n    (set, get) => ({\n      // Initial state\n      user: null,\n      isAuthenticated: false,\n      products: [],\n      filteredProducts: [],\n      searchQuery: '',\n      selectedCategory: '',\n      alerts: [],\n      unreadAlertsCount: 0,\n\n      // Hub d'Information initial state\n      marketAlerts: [],\n      stockUpdates: [],\n      trainingEvents: [],\n      newsUpdates: [],\n      realTimeConnected: false,\n      lastUpdateTime: new Date().toISOString(),\n\n      isLoading: false,\n      isDarkMode: false,\n      sidebarOpen: false,\n\n      // Actions\n      setUser: (user) => set({ \n        user, \n        isAuthenticated: !!user \n      }),\n\n      setProducts: (products) => {\n        set({ products })\n        get().filterProducts()\n      },\n\n      setSearchQuery: (searchQuery) => {\n        set({ searchQuery })\n        get().filterProducts()\n      },\n\n      setSelectedCategory: (selectedCategory) => {\n        set({ selectedCategory })\n        get().filterProducts()\n      },\n\n      setAlerts: (alerts) => {\n        const unreadAlertsCount = alerts.filter(alert => !alert.is_read).length\n        set({ alerts, unreadAlertsCount })\n      },\n\n      markAlertAsRead: (alertId) => {\n        const alerts = get().alerts.map(alert =>\n          alert.id === alertId ? { ...alert, is_read: true } : alert\n        )\n        const unreadAlertsCount = alerts.filter(alert => !alert.is_read).length\n        set({ alerts, unreadAlertsCount })\n      },\n\n      // Hub d'Information actions\n      setMarketAlerts: (marketAlerts) => {\n        set({ marketAlerts })\n        get().updateLastUpdateTime()\n      },\n\n      addMarketAlert: (alert) => {\n        const marketAlerts = [alert, ...get().marketAlerts].slice(0, 50) // Garder les 50 plus récentes\n        set({ marketAlerts })\n        get().updateLastUpdateTime()\n      },\n\n      setStockUpdates: (stockUpdates) => {\n        set({ stockUpdates })\n        get().updateLastUpdateTime()\n      },\n\n      addStockUpdate: (update) => {\n        const stockUpdates = [update, ...get().stockUpdates].slice(0, 100) // Garder les 100 plus récentes\n        set({ stockUpdates })\n        get().updateLastUpdateTime()\n      },\n\n      setTrainingEvents: (trainingEvents) => set({ trainingEvents }),\n\n      setNewsUpdates: (newsUpdates) => set({ newsUpdates }),\n\n      addNewsUpdate: (news) => {\n        const newsUpdates = [news, ...get().newsUpdates].slice(0, 20) // Garder les 20 plus récentes\n        set({ newsUpdates })\n        get().updateLastUpdateTime()\n      },\n\n      setRealTimeConnected: (realTimeConnected) => set({ realTimeConnected }),\n\n      updateLastUpdateTime: () => set({ lastUpdateTime: new Date().toISOString() }),\n\n      setLoading: (isLoading) => set({ isLoading }),\n\n      toggleDarkMode: () => set((state) => ({ \n        isDarkMode: !state.isDarkMode \n      })),\n\n      toggleSidebar: () => set((state) => ({ \n        sidebarOpen: !state.sidebarOpen \n      })),\n\n      filterProducts: () => {\n        const { products, searchQuery, selectedCategory } = get()\n        \n        let filtered = products\n\n        if (selectedCategory) {\n          filtered = filtered.filter(product => \n            product.category === selectedCategory\n          )\n        }\n\n        if (searchQuery) {\n          const query = searchQuery.toLowerCase()\n          filtered = filtered.filter(product =>\n            product.name.toLowerCase().includes(query) ||\n            product.description.toLowerCase().includes(query) ||\n            product.brand.toLowerCase().includes(query) ||\n            product.model.toLowerCase().includes(query)\n          )\n        }\n\n        set({ filteredProducts: filtered })\n      },\n    }),\n    {\n      name: 'pro-matos-storage',\n      partialize: (state) => ({\n        user: state.user,\n        isAuthenticated: state.isAuthenticated,\n        isDarkMode: state.isDarkMode,\n      }),\n    }\n  )\n)\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAwDO,MAAM,WAAW,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC3B,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,MAAM;QACN,iBAAiB;QACjB,UAAU,EAAE;QACZ,kBAAkB,EAAE;QACpB,aAAa;QACb,kBAAkB;QAClB,QAAQ,EAAE;QACV,mBAAmB;QAEnB,kCAAkC;QAClC,cAAc,EAAE;QAChB,cAAc,EAAE;QAChB,gBAAgB,EAAE;QAClB,aAAa,EAAE;QACf,mBAAmB;QACnB,gBAAgB,IAAI,OAAO,WAAW;QAEtC,WAAW;QACX,YAAY;QACZ,aAAa;QAEb,UAAU;QACV,SAAS,CAAC,OAAS,IAAI;gBACrB;gBACA,iBAAiB,CAAC,CAAC;YACrB;QAEA,aAAa,CAAC;YACZ,IAAI;gBAAE;YAAS;YACf,MAAM,cAAc;QACtB;QAEA,gBAAgB,CAAC;YACf,IAAI;gBAAE;YAAY;YAClB,MAAM,cAAc;QACtB;QAEA,qBAAqB,CAAC;YACpB,IAAI;gBAAE;YAAiB;YACvB,MAAM,cAAc;QACtB;QAEA,WAAW,CAAC;YACV,MAAM,oBAAoB,OAAO,MAAM,CAAC,CAAA,QAAS,CAAC,MAAM,OAAO,EAAE,MAAM;YACvE,IAAI;gBAAE;gBAAQ;YAAkB;QAClC;QAEA,iBAAiB,CAAC;YAChB,MAAM,SAAS,MAAM,MAAM,CAAC,GAAG,CAAC,CAAA,QAC9B,MAAM,EAAE,KAAK,UAAU;oBAAE,GAAG,KAAK;oBAAE,SAAS;gBAAK,IAAI;YAEvD,MAAM,oBAAoB,OAAO,MAAM,CAAC,CAAA,QAAS,CAAC,MAAM,OAAO,EAAE,MAAM;YACvE,IAAI;gBAAE;gBAAQ;YAAkB;QAClC;QAEA,4BAA4B;QAC5B,iBAAiB,CAAC;YAChB,IAAI;gBAAE;YAAa;YACnB,MAAM,oBAAoB;QAC5B;QAEA,gBAAgB,CAAC;YACf,MAAM,eAAe;gBAAC;mBAAU,MAAM,YAAY;aAAC,CAAC,KAAK,CAAC,GAAG,IAAI,8BAA8B;;YAC/F,IAAI;gBAAE;YAAa;YACnB,MAAM,oBAAoB;QAC5B;QAEA,iBAAiB,CAAC;YAChB,IAAI;gBAAE;YAAa;YACnB,MAAM,oBAAoB;QAC5B;QAEA,gBAAgB,CAAC;YACf,MAAM,eAAe;gBAAC;mBAAW,MAAM,YAAY;aAAC,CAAC,KAAK,CAAC,GAAG,KAAK,+BAA+B;;YAClG,IAAI;gBAAE;YAAa;YACnB,MAAM,oBAAoB;QAC5B;QAEA,mBAAmB,CAAC,iBAAmB,IAAI;gBAAE;YAAe;QAE5D,gBAAgB,CAAC,cAAgB,IAAI;gBAAE;YAAY;QAEnD,eAAe,CAAC;YACd,MAAM,cAAc;gBAAC;mBAAS,MAAM,WAAW;aAAC,CAAC,KAAK,CAAC,GAAG,IAAI,8BAA8B;;YAC5F,IAAI;gBAAE;YAAY;YAClB,MAAM,oBAAoB;QAC5B;QAEA,sBAAsB,CAAC,oBAAsB,IAAI;gBAAE;YAAkB;QAErE,sBAAsB,IAAM,IAAI;gBAAE,gBAAgB,IAAI,OAAO,WAAW;YAAG;QAE3E,YAAY,CAAC,YAAc,IAAI;gBAAE;YAAU;QAE3C,gBAAgB,IAAM,IAAI,CAAC,QAAU,CAAC;oBACpC,YAAY,CAAC,MAAM,UAAU;gBAC/B,CAAC;QAED,eAAe,IAAM,IAAI,CAAC,QAAU,CAAC;oBACnC,aAAa,CAAC,MAAM,WAAW;gBACjC,CAAC;QAED,gBAAgB;YACd,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,gBAAgB,EAAE,GAAG;YAEpD,IAAI,WAAW;YAEf,IAAI,kBAAkB;gBACpB,WAAW,SAAS,MAAM,CAAC,CAAA,UACzB,QAAQ,QAAQ,KAAK;YAEzB;YAEA,IAAI,aAAa;gBACf,MAAM,QAAQ,YAAY,WAAW;gBACrC,WAAW,SAAS,MAAM,CAAC,CAAA,UACzB,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,UACpC,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,UAC3C,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,UACrC,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC;YAEzC;YAEA,IAAI;gBAAE,kBAAkB;YAAS;QACnC;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,iBAAiB,MAAM,eAAe;YACtC,YAAY,MAAM,UAAU;QAC9B,CAAC;AACH", "debugId": null}}, {"offset": {"line": 202, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/services/hubService.ts"], "sourcesContent": ["import { MarketAlert, StockUpdate, PriceHistory, TrainingEvent, NewsUpdate, Product } from '@/lib/supabase'\n\n// Service pour simuler les données temps réel du Hub d'Information\nexport class HubService {\n  \n  // Simulation des alertes marché en temps réel\n  static generateMarketAlerts(): MarketAlert[] {\n    return [\n      {\n        id: '1',\n        type: 'stock_low',\n        title: 'Stock Faible - Disjoncteurs Schneider',\n        message: 'Les disjoncteurs Schneider Electric C60N 32A sont en stock faible chez 3 fournisseurs à Abidjan',\n        severity: 'medium',\n        category: 'Protection électrique',\n        product_id: 'prod_001',\n        affected_regions: ['Abidjan', 'Bouaké'],\n        is_active: true,\n        created_at: new Date(Date.now() - 2 * 60 * 1000).toISOString(), // Il y a 2 minutes\n      },\n      {\n        id: '2',\n        type: 'price_change',\n        title: 'Baisse de Prix - Câbles Nexans',\n        message: 'Réduction de 15% sur les câbles Nexans U1000R2V 3x2.5mm² chez ElectroDistrib',\n        severity: 'low',\n        category: 'Câblage',\n        affected_regions: ['Dakar', 'Thiès'],\n        is_active: true,\n        created_at: new Date(Date.now() - 15 * 60 * 1000).toISOString(), // Il y a 15 minutes\n      },\n      {\n        id: '3',\n        type: 'new_product',\n        title: 'Nouveau Produit - Onduleurs APC',\n        message: 'Arrivée des nouveaux onduleurs APC Smart-UPS 3000VA avec technologie lithium',\n        severity: 'high',\n        category: 'Alimentation',\n        affected_regions: ['Accra', 'Kumasi', 'Abidjan'],\n        is_active: true,\n        created_at: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // Il y a 30 minutes\n      },\n      {\n        id: '4',\n        type: 'stock_out',\n        title: 'Rupture de Stock - Transformateurs',\n        message: 'Rupture totale des transformateurs 400/230V 63kVA dans la région de Bamako',\n        severity: 'critical',\n        category: 'Transformation',\n        affected_regions: ['Bamako', 'Ségou'],\n        is_active: true,\n        created_at: new Date(Date.now() - 45 * 60 * 1000).toISOString(), // Il y a 45 minutes\n      },\n      {\n        id: '5',\n        type: 'market_trend',\n        title: 'Tendance Marché - Énergie Solaire',\n        message: 'Forte demande pour les équipements solaires : +40% ce trimestre en Afrique de l\\'Ouest',\n        severity: 'medium',\n        category: 'Énergie renouvelable',\n        affected_regions: ['Ouagadougou', 'Niamey', 'Abidjan'],\n        is_active: true,\n        created_at: new Date(Date.now() - 60 * 60 * 1000).toISOString(), // Il y a 1 heure\n      }\n    ]\n  }\n\n  // Simulation des mises à jour de stock\n  static generateStockUpdates(): StockUpdate[] {\n    return [\n      {\n        id: '1',\n        product_id: 'prod_001',\n        supplier_id: 'sup_001',\n        previous_quantity: 150,\n        current_quantity: 45,\n        location: 'Abidjan - Zone Industrielle',\n        update_type: 'decrease',\n        created_at: new Date(Date.now() - 5 * 60 * 1000).toISOString(),\n      },\n      {\n        id: '2',\n        product_id: 'prod_002',\n        supplier_id: 'sup_002',\n        previous_quantity: 0,\n        current_quantity: 200,\n        location: 'Dakar - Entrepôt Central',\n        update_type: 'restock',\n        created_at: new Date(Date.now() - 10 * 60 * 1000).toISOString(),\n      },\n      {\n        id: '3',\n        product_id: 'prod_003',\n        supplier_id: 'sup_003',\n        previous_quantity: 25,\n        current_quantity: 0,\n        location: 'Bamako - Dépôt Nord',\n        update_type: 'out_of_stock',\n        created_at: new Date(Date.now() - 20 * 60 * 1000).toISOString(),\n      }\n    ]\n  }\n\n  // Simulation des événements de formation\n  static generateTrainingEvents(): TrainingEvent[] {\n    return [\n      {\n        id: '1',\n        title: 'Certification Schneider Electric - Installations BT',\n        description: 'Formation certifiante sur les installations basse tension selon les normes NF C 15-100',\n        type: 'certification',\n        instructor: 'Ing. Kouassi Yao (Schneider Electric)',\n        start_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // Dans 7 jours\n        end_date: new Date(Date.now() + 9 * 24 * 60 * 60 * 1000).toISOString(), // Dans 9 jours\n        location: 'Abidjan - Centre de Formation Schneider',\n        is_virtual: false,\n        max_participants: 20,\n        current_participants: 12,\n        membership_required: 'silver',\n        registration_fee: 150000, // 150,000 FCFA\n        status: 'upcoming',\n        created_at: new Date().toISOString(),\n      },\n      {\n        id: '2',\n        title: 'Webinaire - Énergie Solaire et Stockage',\n        description: 'Les dernières innovations en matière de panneaux solaires et batteries lithium',\n        type: 'webinar',\n        instructor: 'Dr. Aminata Traoré (Expert Énergie)',\n        start_date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(), // Dans 3 jours\n        end_date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000 + 2 * 60 * 60 * 1000).toISOString(), // 2h plus tard\n        location: 'En ligne',\n        is_virtual: true,\n        max_participants: 100,\n        current_participants: 67,\n        membership_required: 'bronze',\n        registration_fee: 0,\n        status: 'upcoming',\n        created_at: new Date().toISOString(),\n      },\n      {\n        id: '3',\n        title: 'Atelier Pratique - Maintenance Préventive',\n        description: 'Techniques de maintenance préventive pour équipements électriques industriels',\n        type: 'workshop',\n        instructor: 'Équipe Technique ABB',\n        start_date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(), // Dans 14 jours\n        end_date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000 + 6 * 60 * 60 * 1000).toISOString(), // 6h plus tard\n        location: 'Dakar - Centre Technique ABB',\n        is_virtual: false,\n        max_participants: 15,\n        current_participants: 8,\n        membership_required: 'gold',\n        registration_fee: 200000, // 200,000 FCFA\n        status: 'upcoming',\n        created_at: new Date().toISOString(),\n      }\n    ]\n  }\n\n  // Simulation des actualités du secteur\n  static generateNewsUpdates(): NewsUpdate[] {\n    return [\n      {\n        id: '1',\n        title: 'Nouvelle réglementation sur les installations photovoltaïques en Côte d\\'Ivoire',\n        content: 'Le gouvernement ivoirien vient d\\'adopter de nouvelles normes pour les installations solaires...',\n        summary: 'Nouvelles normes techniques et administratives pour le photovoltaïque',\n        category: 'regulation',\n        author: 'Ministère de l\\'Énergie CI',\n        source_url: 'https://energie.gouv.ci/nouvelles-normes-pv',\n        image_url: '/images/news/regulation-pv.jpg',\n        tags: ['réglementation', 'photovoltaïque', 'côte d\\'ivoire'],\n        is_featured: true,\n        published_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // Il y a 2 heures\n        created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n      },\n      {\n        id: '2',\n        title: 'Schneider Electric ouvre un nouveau centre de distribution à Accra',\n        content: 'Le leader mondial de la gestion de l\\'énergie inaugure son plus grand entrepôt...',\n        summary: 'Nouveau hub logistique pour améliorer la disponibilité des produits',\n        category: 'company',\n        author: 'Schneider Electric',\n        source_url: 'https://schneider-electric.com/accra-center',\n        image_url: '/images/news/schneider-accra.jpg',\n        tags: ['schneider electric', 'ghana', 'logistique'],\n        is_featured: false,\n        published_at: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // Il y a 6 heures\n        created_at: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),\n      },\n      {\n        id: '3',\n        title: 'Innovation : Nouveaux compteurs intelligents pour l\\'Afrique de l\\'Ouest',\n        content: 'Une startup sénégalaise développe des compteurs connectés adaptés aux réseaux locaux...',\n        summary: 'Technologie IoT adaptée aux défis énergétiques régionaux',\n        category: 'technology',\n        author: 'TechAfrique',\n        source_url: 'https://techafrique.com/compteurs-intelligents',\n        image_url: '/images/news/smart-meters.jpg',\n        tags: ['innovation', 'iot', 'compteurs', 'sénégal'],\n        is_featured: true,\n        published_at: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(), // Il y a 12 heures\n        created_at: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),\n      }\n    ]\n  }\n\n  // Simulation des produits avec données temps réel\n  static generateProducts(): Product[] {\n    return [\n      {\n        id: 'prod_001',\n        name: 'Disjoncteur C60N 32A',\n        description: 'Disjoncteur modulaire Schneider Electric C60N courbe C 32A',\n        category: 'Protection électrique',\n        brand: 'Schneider Electric',\n        model: 'C60N-C32',\n        price: 45000, // 45,000 FCFA\n        stock_quantity: 45,\n        technical_specs: {\n          'Courant nominal': '32A',\n          'Courbe de déclenchement': 'C',\n          'Pouvoir de coupure': '6kA',\n          'Nombre de pôles': '1',\n          'Norme': 'NF EN 60898'\n        },\n        images: ['/images/products/c60n-32a.jpg'],\n        qr_code: 'QR_C60N_32A_001',\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString(),\n      },\n      {\n        id: 'prod_002',\n        name: 'Câble U1000R2V 3x2.5mm²',\n        description: 'Câble d\\'énergie Nexans U1000R2V 3 conducteurs 2.5mm²',\n        category: 'Câblage',\n        brand: 'Nexans',\n        model: 'U1000R2V-3x2.5',\n        price: 2500, // 2,500 FCFA/mètre\n        stock_quantity: 200,\n        technical_specs: {\n          'Section': '3x2.5mm²',\n          'Tension nominale': '1000V',\n          'Type d\\'isolation': 'PRC',\n          'Température de service': '-40°C à +90°C',\n          'Norme': 'NF C 32-321'\n        },\n        images: ['/images/products/cable-u1000r2v.jpg'],\n        qr_code: 'QR_U1000R2V_3x2.5_002',\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString(),\n      }\n    ]\n  }\n\n  // Méthode pour simuler les mises à jour temps réel\n  static subscribeToRealTimeUpdates(callback: (data: any) => void) {\n    // Simulation d'un WebSocket ou Server-Sent Events\n    const interval = setInterval(() => {\n      const updateType = Math.random()\n      \n      if (updateType < 0.3) {\n        // Nouvelle alerte marché\n        callback({\n          type: 'market_alert',\n          data: this.generateMarketAlerts()[0]\n        })\n      } else if (updateType < 0.6) {\n        // Mise à jour de stock\n        callback({\n          type: 'stock_update',\n          data: this.generateStockUpdates()[0]\n        })\n      } else {\n        // Nouvelle actualité\n        callback({\n          type: 'news_update',\n          data: this.generateNewsUpdates()[0]\n        })\n      }\n    }, 30000) // Toutes les 30 secondes\n\n    return () => clearInterval(interval)\n  }\n}\n"], "names": [], "mappings": ";;;AAGO,MAAM;IAEX,8CAA8C;IAC9C,OAAO,uBAAsC;QAC3C,OAAO;YACL;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,UAAU;gBACV,UAAU;gBACV,YAAY;gBACZ,kBAAkB;oBAAC;oBAAW;iBAAS;gBACvC,WAAW;gBACX,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,MAAM,WAAW;YAC9D;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,UAAU;gBACV,UAAU;gBACV,kBAAkB;oBAAC;oBAAS;iBAAQ;gBACpC,WAAW;gBACX,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,MAAM,WAAW;YAC/D;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,UAAU;gBACV,UAAU;gBACV,kBAAkB;oBAAC;oBAAS;oBAAU;iBAAU;gBAChD,WAAW;gBACX,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,MAAM,WAAW;YAC/D;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,UAAU;gBACV,UAAU;gBACV,kBAAkB;oBAAC;oBAAU;iBAAQ;gBACrC,WAAW;gBACX,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,MAAM,WAAW;YAC/D;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,UAAU;gBACV,UAAU;gBACV,kBAAkB;oBAAC;oBAAe;oBAAU;iBAAU;gBACtD,WAAW;gBACX,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,MAAM,WAAW;YAC/D;SACD;IACH;IAEA,uCAAuC;IACvC,OAAO,uBAAsC;QAC3C,OAAO;YACL;gBACE,IAAI;gBACJ,YAAY;gBACZ,aAAa;gBACb,mBAAmB;gBACnB,kBAAkB;gBAClB,UAAU;gBACV,aAAa;gBACb,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,MAAM,WAAW;YAC9D;YACA;gBACE,IAAI;gBACJ,YAAY;gBACZ,aAAa;gBACb,mBAAmB;gBACnB,kBAAkB;gBAClB,UAAU;gBACV,aAAa;gBACb,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,MAAM,WAAW;YAC/D;YACA;gBACE,IAAI;gBACJ,YAAY;gBACZ,aAAa;gBACb,mBAAmB;gBACnB,kBAAkB;gBAClB,UAAU;gBACV,aAAa;gBACb,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,MAAM,WAAW;YAC/D;SACD;IACH;IAEA,yCAAyC;IACzC,OAAO,yBAA0C;QAC/C,OAAO;YACL;gBACE,IAAI;gBACJ,OAAO;gBACP,aAAa;gBACb,MAAM;gBACN,YAAY;gBACZ,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;gBACtE,UAAU,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;gBACpE,UAAU;gBACV,YAAY;gBACZ,kBAAkB;gBAClB,sBAAsB;gBACtB,qBAAqB;gBACrB,kBAAkB;gBAClB,QAAQ;gBACR,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,aAAa;gBACb,MAAM;gBACN,YAAY;gBACZ,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;gBACtE,UAAU,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,OAAO,IAAI,KAAK,KAAK,MAAM,WAAW;gBACzF,UAAU;gBACV,YAAY;gBACZ,kBAAkB;gBAClB,sBAAsB;gBACtB,qBAAqB;gBACrB,kBAAkB;gBAClB,QAAQ;gBACR,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,aAAa;gBACb,MAAM;gBACN,YAAY;gBACZ,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW;gBACvE,UAAU,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,IAAI,KAAK,KAAK,MAAM,WAAW;gBAC1F,UAAU;gBACV,YAAY;gBACZ,kBAAkB;gBAClB,sBAAsB;gBACtB,qBAAqB;gBACrB,kBAAkB;gBAClB,QAAQ;gBACR,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;IACH;IAEA,uCAAuC;IACvC,OAAO,sBAAoC;QACzC,OAAO;YACL;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;gBACT,SAAS;gBACT,UAAU;gBACV,QAAQ;gBACR,YAAY;gBACZ,WAAW;gBACX,MAAM;oBAAC;oBAAkB;oBAAkB;iBAAiB;gBAC5D,aAAa;gBACb,cAAc,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;gBACnE,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;YACnE;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;gBACT,SAAS;gBACT,UAAU;gBACV,QAAQ;gBACR,YAAY;gBACZ,WAAW;gBACX,MAAM;oBAAC;oBAAsB;oBAAS;iBAAa;gBACnD,aAAa;gBACb,cAAc,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;gBACnE,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;YACnE;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;gBACT,SAAS;gBACT,UAAU;gBACV,QAAQ;gBACR,YAAY;gBACZ,WAAW;gBACX,MAAM;oBAAC;oBAAc;oBAAO;oBAAa;iBAAU;gBACnD,aAAa;gBACb,cAAc,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW;gBACpE,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW;YACpE;SACD;IACH;IAEA,kDAAkD;IAClD,OAAO,mBAA8B;QACnC,OAAO;YACL;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,UAAU;gBACV,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,gBAAgB;gBAChB,iBAAiB;oBACf,mBAAmB;oBACnB,2BAA2B;oBAC3B,sBAAsB;oBACtB,mBAAmB;oBACnB,SAAS;gBACX;gBACA,QAAQ;oBAAC;iBAAgC;gBACzC,SAAS;gBACT,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,UAAU;gBACV,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,gBAAgB;gBAChB,iBAAiB;oBACf,WAAW;oBACX,oBAAoB;oBACpB,qBAAqB;oBACrB,0BAA0B;oBAC1B,SAAS;gBACX;gBACA,QAAQ;oBAAC;iBAAsC;gBAC/C,SAAS;gBACT,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;IACH;IAEA,mDAAmD;IACnD,OAAO,2BAA2B,QAA6B,EAAE;QAC/D,kDAAkD;QAClD,MAAM,WAAW,YAAY;YAC3B,MAAM,aAAa,KAAK,MAAM;YAE9B,IAAI,aAAa,KAAK;gBACpB,yBAAyB;gBACzB,SAAS;oBACP,MAAM;oBACN,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC,EAAE;gBACtC;YACF,OAAO,IAAI,aAAa,KAAK;gBAC3B,uBAAuB;gBACvB,SAAS;oBACP,MAAM;oBACN,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC,EAAE;gBACtC;YACF,OAAO;gBACL,qBAAqB;gBACrB,SAAS;oBACP,MAAM;oBACN,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC,EAAE;gBACrC;YACF;QACF,GAAG,OAAO,yBAAyB;;QAEnC,OAAO,IAAM,cAAc;IAC7B;AACF", "debugId": null}}, {"offset": {"line": 521, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPrice(price: number): string {\n  return new Intl.NumberFormat('fr-FR', {\n    style: 'currency',\n    currency: 'XOF',\n    minimumFractionDigits: 0,\n  }).format(price)\n}\n\nexport function formatDate(date: string | Date): string {\n  return new Intl.DateTimeFormat('fr-FR', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(new Date(date))\n}\n\nexport function getMembershipColor(level: string): string {\n  switch (level) {\n    case 'bronze':\n      return 'text-amber-600 bg-amber-50'\n    case 'silver':\n      return 'text-gray-600 bg-gray-50'\n    case 'gold':\n      return 'text-yellow-600 bg-yellow-50'\n    case 'platinum':\n      return 'text-purple-600 bg-purple-50'\n    default:\n      return 'text-gray-600 bg-gray-50'\n  }\n}\n\nexport function generateQRCode(productId: string): string {\n  // Simulation d'un QR code - à remplacer par une vraie génération\n  return `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(productId)}`\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa;IACvC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,mBAAmB,KAAa;IAC9C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,eAAe,SAAiB;IAC9C,iEAAiE;IACjE,OAAO,CAAC,8DAA8D,EAAE,mBAAmB,YAAY;AACzG;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 583, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/components/hub/InformationHub.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { \n  Bell, \n  TrendingUp, \n  Package, \n  AlertTriangle, \n  Calendar,\n  Newspaper,\n  Wifi,\n  WifiOff,\n  Clock,\n  Filter,\n  Search,\n  RefreshCw\n} from 'lucide-react'\nimport { useStore } from '@/store/useStore'\nimport { HubService } from '@/services/hubService'\nimport { formatDate } from '@/lib/utils'\nimport { MarketAlert, StockUpdate, TrainingEvent, NewsUpdate } from '@/lib/supabase'\n\ninterface InformationHubProps {\n  className?: string\n}\n\nexport default function InformationHub({ className = '' }: InformationHubProps) {\n  const {\n    marketAlerts,\n    stockUpdates,\n    trainingEvents,\n    newsUpdates,\n    realTimeConnected,\n    lastUpdateTime,\n    setMarketAlerts,\n    addMarketAlert,\n    setStockUpdates,\n    addStockUpdate,\n    setTrainingEvents,\n    setNewsUpdates,\n    addNewsUpdate,\n    setRealTimeConnected,\n    updateLastUpdateTime\n  } = useStore()\n\n  const [activeTab, setActiveTab] = useState<'alerts' | 'stocks' | 'training' | 'news'>('alerts')\n  const [searchQuery, setSearchQuery] = useState('')\n  const [filterSeverity, setFilterSeverity] = useState<'all' | 'low' | 'medium' | 'high' | 'critical'>('all')\n\n  // Initialisation des données\n  useEffect(() => {\n    const loadInitialData = () => {\n      setMarketAlerts(HubService.generateMarketAlerts())\n      setStockUpdates(HubService.generateStockUpdates())\n      setTrainingEvents(HubService.generateTrainingEvents())\n      setNewsUpdates(HubService.generateNewsUpdates())\n    }\n\n    loadInitialData()\n\n    // Simulation de la connexion temps réel\n    setRealTimeConnected(true)\n    \n    // Abonnement aux mises à jour temps réel\n    const unsubscribe = HubService.subscribeToRealTimeUpdates((update) => {\n      switch (update.type) {\n        case 'market_alert':\n          addMarketAlert(update.data)\n          break\n        case 'stock_update':\n          addStockUpdate(update.data)\n          break\n        case 'news_update':\n          addNewsUpdate(update.data)\n          break\n      }\n    })\n\n    return () => {\n      unsubscribe()\n      setRealTimeConnected(false)\n    }\n  }, [])\n\n  // Filtrage des alertes\n  const filteredAlerts = marketAlerts.filter(alert => {\n    const matchesSearch = searchQuery === '' || \n      alert.title.toLowerCase().includes(searchQuery.toLowerCase()) ||\n      alert.message.toLowerCase().includes(searchQuery.toLowerCase())\n    \n    const matchesSeverity = filterSeverity === 'all' || alert.severity === filterSeverity\n    \n    return matchesSearch && matchesSeverity && alert.is_active\n  })\n\n  const getSeverityColor = (severity: string) => {\n    switch (severity) {\n      case 'critical': return 'bg-red-100 text-red-800 border-red-200'\n      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200'\n      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200'\n      case 'low': return 'bg-blue-100 text-blue-800 border-blue-200'\n      default: return 'bg-gray-100 text-gray-800 border-gray-200'\n    }\n  }\n\n  const getUpdateTypeIcon = (type: string) => {\n    switch (type) {\n      case 'restock': return <TrendingUp className=\"h-4 w-4 text-green-600\" />\n      case 'decrease': return <TrendingUp className=\"h-4 w-4 text-orange-600 rotate-180\" />\n      case 'out_of_stock': return <AlertTriangle className=\"h-4 w-4 text-red-600\" />\n      default: return <Package className=\"h-4 w-4 text-blue-600\" />\n    }\n  }\n\n  const tabs = [\n    { id: 'alerts', label: 'Alertes Marché', icon: Bell, count: filteredAlerts.length },\n    { id: 'stocks', label: 'Stocks', icon: Package, count: stockUpdates.length },\n    { id: 'training', label: 'Formations', icon: Calendar, count: trainingEvents.length },\n    { id: 'news', label: 'Actualités', icon: Newspaper, count: newsUpdates.length }\n  ]\n\n  return (\n    <div className={`bg-white rounded-lg shadow-lg ${className}`}>\n      {/* Header avec statut temps réel */}\n      <div className=\"p-6 border-b border-gray-200\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h2 className=\"text-2xl font-bold text-gray-900\">Hub d'Information</h2>\n            <p className=\"text-gray-600 mt-1\">Veille marché en temps réel</p>\n          </div>\n          \n          <div className=\"flex items-center space-x-4\">\n            {/* Statut connexion */}\n            <div className=\"flex items-center space-x-2\">\n              {realTimeConnected ? (\n                <>\n                  <Wifi className=\"h-5 w-5 text-green-600\" />\n                  <span className=\"text-sm text-green-600 font-medium\">Connecté</span>\n                </>\n              ) : (\n                <>\n                  <WifiOff className=\"h-5 w-5 text-red-600\" />\n                  <span className=\"text-sm text-red-600 font-medium\">Déconnecté</span>\n                </>\n              )}\n            </div>\n            \n            {/* Dernière mise à jour */}\n            <div className=\"flex items-center space-x-2 text-sm text-gray-500\">\n              <Clock className=\"h-4 w-4\" />\n              <span>Mis à jour {formatDate(lastUpdateTime)}</span>\n            </div>\n            \n            {/* Bouton actualiser */}\n            <button \n              onClick={updateLastUpdateTime}\n              className=\"p-2 rounded-lg hover:bg-gray-100 transition-colors\"\n            >\n              <RefreshCw className=\"h-5 w-5 text-gray-600\" />\n            </button>\n          </div>\n        </div>\n\n        {/* Barre de recherche et filtres */}\n        <div className=\"mt-4 flex items-center space-x-4\">\n          <div className=\"flex-1 relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n            <input\n              type=\"text\"\n              placeholder=\"Rechercher dans le hub...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400 focus:border-transparent\"\n            />\n          </div>\n          \n          {activeTab === 'alerts' && (\n            <select\n              value={filterSeverity}\n              onChange={(e) => setFilterSeverity(e.target.value as any)}\n              className=\"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400 focus:border-transparent\"\n            >\n              <option value=\"all\">Toutes les priorités</option>\n              <option value=\"critical\">Critique</option>\n              <option value=\"high\">Élevée</option>\n              <option value=\"medium\">Moyenne</option>\n              <option value=\"low\">Faible</option>\n            </select>\n          )}\n        </div>\n      </div>\n\n      {/* Onglets */}\n      <div className=\"border-b border-gray-200\">\n        <nav className=\"flex space-x-8 px-6\">\n          {tabs.map((tab) => {\n            const Icon = tab.icon\n            const isActive = activeTab === tab.id\n            \n            return (\n              <button\n                key={tab.id}\n                onClick={() => setActiveTab(tab.id as any)}\n                className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${\n                  isActive\n                    ? 'border-amber-500 text-amber-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                <Icon className=\"h-5 w-5\" />\n                <span>{tab.label}</span>\n                {tab.count > 0 && (\n                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                    isActive ? 'bg-amber-100 text-amber-800' : 'bg-gray-100 text-gray-600'\n                  }`}>\n                    {tab.count}\n                  </span>\n                )}\n              </button>\n            )\n          })}\n        </nav>\n      </div>\n\n      {/* Contenu des onglets */}\n      <div className=\"p-6\">\n        <AnimatePresence mode=\"wait\">\n          {activeTab === 'alerts' && (\n            <motion.div\n              key=\"alerts\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -20 }}\n              className=\"space-y-4\"\n            >\n              {filteredAlerts.length === 0 ? (\n                <div className=\"text-center py-8 text-gray-500\">\n                  <Bell className=\"h-12 w-12 mx-auto mb-4 text-gray-300\" />\n                  <p>Aucune alerte correspondant aux critères</p>\n                </div>\n              ) : (\n                filteredAlerts.map((alert) => (\n                  <motion.div\n                    key={alert.id}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    className={`p-4 rounded-lg border ${getSeverityColor(alert.severity)}`}\n                  >\n                    <div className=\"flex items-start justify-between\">\n                      <div className=\"flex-1\">\n                        <div className=\"flex items-center space-x-2 mb-2\">\n                          <span className=\"text-xs font-medium uppercase tracking-wide\">\n                            {alert.type.replace('_', ' ')}\n                          </span>\n                          <span className=\"text-xs text-gray-500\">\n                            {formatDate(alert.created_at)}\n                          </span>\n                        </div>\n                        <h3 className=\"font-semibold mb-1\">{alert.title}</h3>\n                        <p className=\"text-sm mb-2\">{alert.message}</p>\n                        <div className=\"flex items-center space-x-4 text-xs text-gray-600\">\n                          <span>Catégorie: {alert.category}</span>\n                          <span>Régions: {alert.affected_regions.join(', ')}</span>\n                        </div>\n                      </div>\n                    </div>\n                  </motion.div>\n                ))\n              )}\n            </motion.div>\n          )}\n\n          {activeTab === 'stocks' && (\n            <motion.div\n              key=\"stocks\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -20 }}\n              className=\"space-y-4\"\n            >\n              {stockUpdates.length === 0 ? (\n                <div className=\"text-center py-8 text-gray-500\">\n                  <Package className=\"h-12 w-12 mx-auto mb-4 text-gray-300\" />\n                  <p>Aucune mise à jour de stock récente</p>\n                </div>\n              ) : (\n                stockUpdates.map((update) => (\n                  <motion.div\n                    key={update.id}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    className=\"p-4 rounded-lg border border-gray-200 bg-gray-50\"\n                  >\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center space-x-3\">\n                        {getUpdateTypeIcon(update.update_type)}\n                        <div>\n                          <h3 className=\"font-semibold text-gray-900\">\n                            Produit #{update.product_id}\n                          </h3>\n                          <p className=\"text-sm text-gray-600\">{update.location}</p>\n                        </div>\n                      </div>\n                      <div className=\"text-right\">\n                        <div className=\"text-lg font-bold text-gray-900\">\n                          {update.previous_quantity} → {update.current_quantity}\n                        </div>\n                        <div className=\"text-xs text-gray-500\">\n                          {formatDate(update.created_at)}\n                        </div>\n                      </div>\n                    </div>\n                  </motion.div>\n                ))\n              )}\n            </motion.div>\n          )}\n\n          {activeTab === 'training' && (\n            <motion.div\n              key=\"training\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -20 }}\n              className=\"space-y-4\"\n            >\n              {trainingEvents.length === 0 ? (\n                <div className=\"text-center py-8 text-gray-500\">\n                  <Calendar className=\"h-12 w-12 mx-auto mb-4 text-gray-300\" />\n                  <p>Aucun événement de formation programmé</p>\n                </div>\n              ) : (\n                trainingEvents.map((event) => (\n                  <motion.div\n                    key={event.id}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    className=\"p-6 rounded-lg border border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50\"\n                  >\n                    <div className=\"flex items-start justify-between\">\n                      <div className=\"flex-1\">\n                        <div className=\"flex items-center space-x-2 mb-2\">\n                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                            event.type === 'certification' ? 'bg-purple-100 text-purple-800' :\n                            event.type === 'webinar' ? 'bg-green-100 text-green-800' :\n                            event.type === 'workshop' ? 'bg-blue-100 text-blue-800' :\n                            'bg-gray-100 text-gray-800'\n                          }`}>\n                            {event.type}\n                          </span>\n                          {event.membership_required && (\n                            <span className=\"px-2 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800\">\n                              {event.membership_required}+\n                            </span>\n                          )}\n                        </div>\n                        <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                          {event.title}\n                        </h3>\n                        <p className=\"text-gray-600 mb-3\">{event.description}</p>\n                        <div className=\"grid grid-cols-2 gap-4 text-sm text-gray-600\">\n                          <div>\n                            <span className=\"font-medium\">Instructeur:</span> {event.instructor}\n                          </div>\n                          <div>\n                            <span className=\"font-medium\">Lieu:</span> {event.location}\n                          </div>\n                          <div>\n                            <span className=\"font-medium\">Date:</span> {formatDate(event.start_date)}\n                          </div>\n                          <div>\n                            <span className=\"font-medium\">Participants:</span> {event.current_participants}/{event.max_participants}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"text-right ml-4\">\n                        {event.registration_fee > 0 ? (\n                          <div className=\"text-lg font-bold text-gray-900\">\n                            {event.registration_fee.toLocaleString()} FCFA\n                          </div>\n                        ) : (\n                          <div className=\"text-lg font-bold text-green-600\">Gratuit</div>\n                        )}\n                        <button className=\"mt-2 px-4 py-2 bg-amber-500 text-white rounded-lg hover:bg-amber-600 transition-colors\">\n                          S'inscrire\n                        </button>\n                      </div>\n                    </div>\n                  </motion.div>\n                ))\n              )}\n            </motion.div>\n          )}\n\n          {activeTab === 'news' && (\n            <motion.div\n              key=\"news\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -20 }}\n              className=\"space-y-6\"\n            >\n              {newsUpdates.length === 0 ? (\n                <div className=\"text-center py-8 text-gray-500\">\n                  <Newspaper className=\"h-12 w-12 mx-auto mb-4 text-gray-300\" />\n                  <p>Aucune actualité récente</p>\n                </div>\n              ) : (\n                newsUpdates.map((news) => (\n                  <motion.div\n                    key={news.id}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    className={`p-6 rounded-lg border border-gray-200 ${\n                      news.is_featured ? 'bg-gradient-to-r from-amber-50 to-yellow-50 border-amber-200' : 'bg-white'\n                    }`}\n                  >\n                    <div className=\"flex items-start space-x-4\">\n                      {news.image_url && (\n                        <div className=\"w-24 h-24 bg-gray-200 rounded-lg flex-shrink-0\"></div>\n                      )}\n                      <div className=\"flex-1\">\n                        <div className=\"flex items-center space-x-2 mb-2\">\n                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                            news.category === 'regulation' ? 'bg-red-100 text-red-800' :\n                            news.category === 'technology' ? 'bg-blue-100 text-blue-800' :\n                            news.category === 'company' ? 'bg-green-100 text-green-800' :\n                            news.category === 'market' ? 'bg-purple-100 text-purple-800' :\n                            'bg-gray-100 text-gray-800'\n                          }`}>\n                            {news.category}\n                          </span>\n                          {news.is_featured && (\n                            <span className=\"px-2 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800\">\n                              À la une\n                            </span>\n                          )}\n                          <span className=\"text-xs text-gray-500\">\n                            {formatDate(news.published_at)}\n                          </span>\n                        </div>\n                        <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                          {news.title}\n                        </h3>\n                        <p className=\"text-gray-600 mb-3\">{news.summary}</p>\n                        <div className=\"flex items-center justify-between\">\n                          <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n                            <span>Par {news.author}</span>\n                            <div className=\"flex space-x-1\">\n                              {news.tags.slice(0, 3).map((tag) => (\n                                <span key={tag} className=\"px-2 py-1 bg-gray-100 rounded text-xs\">\n                                  #{tag}\n                                </span>\n                              ))}\n                            </div>\n                          </div>\n                          {news.source_url && (\n                            <a\n                              href={news.source_url}\n                              target=\"_blank\"\n                              rel=\"noopener noreferrer\"\n                              className=\"text-amber-600 hover:text-amber-700 text-sm font-medium\"\n                            >\n                              Lire la suite →\n                            </a>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  </motion.div>\n                ))\n              )}\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AACA;AACA;AApBA;;;;;;;;AA2Be,SAAS,eAAe,EAAE,YAAY,EAAE,EAAuB;IAC5E,MAAM,EACJ,YAAY,EACZ,YAAY,EACZ,cAAc,EACd,WAAW,EACX,iBAAiB,EACjB,cAAc,EACd,eAAe,EACf,cAAc,EACd,eAAe,EACf,cAAc,EACd,iBAAiB,EACjB,cAAc,EACd,aAAa,EACb,oBAAoB,EACpB,oBAAoB,EACrB,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAEX,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6C;IACtF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkD;IAErG,6BAA6B;IAC7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB;YACtB,gBAAgB,6HAAA,CAAA,aAAU,CAAC,oBAAoB;YAC/C,gBAAgB,6HAAA,CAAA,aAAU,CAAC,oBAAoB;YAC/C,kBAAkB,6HAAA,CAAA,aAAU,CAAC,sBAAsB;YACnD,eAAe,6HAAA,CAAA,aAAU,CAAC,mBAAmB;QAC/C;QAEA;QAEA,wCAAwC;QACxC,qBAAqB;QAErB,yCAAyC;QACzC,MAAM,cAAc,6HAAA,CAAA,aAAU,CAAC,0BAA0B,CAAC,CAAC;YACzD,OAAQ,OAAO,IAAI;gBACjB,KAAK;oBACH,eAAe,OAAO,IAAI;oBAC1B;gBACF,KAAK;oBACH,eAAe,OAAO,IAAI;oBAC1B;gBACF,KAAK;oBACH,cAAc,OAAO,IAAI;oBACzB;YACJ;QACF;QAEA,OAAO;YACL;YACA,qBAAqB;QACvB;IACF,GAAG,EAAE;IAEL,uBAAuB;IACvB,MAAM,iBAAiB,aAAa,MAAM,CAAC,CAAA;QACzC,MAAM,gBAAgB,gBAAgB,MACpC,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC1D,MAAM,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QAE9D,MAAM,kBAAkB,mBAAmB,SAAS,MAAM,QAAQ,KAAK;QAEvE,OAAO,iBAAiB,mBAAmB,MAAM,SAAS;IAC5D;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAO,OAAO;YACnB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBAAW,qBAAO,8OAAC,kNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC7C,KAAK;gBAAY,qBAAO,8OAAC,kNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC9C,KAAK;gBAAgB,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YACrD;gBAAS,qBAAO,8OAAC,wMAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;QACrC;IACF;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAU,OAAO;YAAkB,MAAM,kMAAA,CAAA,OAAI;YAAE,OAAO,eAAe,MAAM;QAAC;QAClF;YAAE,IAAI;YAAU,OAAO;YAAU,MAAM,wMAAA,CAAA,UAAO;YAAE,OAAO,aAAa,MAAM;QAAC;QAC3E;YAAE,IAAI;YAAY,OAAO;YAAc,MAAM,0MAAA,CAAA,WAAQ;YAAE,OAAO,eAAe,MAAM;QAAC;QACpF;YAAE,IAAI;YAAQ,OAAO;YAAc,MAAM,4MAAA,CAAA,YAAS;YAAE,OAAO,YAAY,MAAM;QAAC;KAC/E;IAED,qBACE,8OAAC;QAAI,WAAW,CAAC,8BAA8B,EAAE,WAAW;;0BAE1D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;;0CAGpC,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;kDACZ,kCACC;;8DACE,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;oDAAK,WAAU;8DAAqC;;;;;;;yEAGvD;;8DACE,8OAAC,4MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,8OAAC;oDAAK,WAAU;8DAAmC;;;;;;;;;;;;;kDAMzD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;;oDAAK;oDAAY,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE;;;;;;;;;;;;;kDAI/B,8OAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAM3B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;;;;;;;4BAIb,cAAc,0BACb,8OAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;gCACjD,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAM;;;;;;kDACpB,8OAAC;wCAAO,OAAM;kDAAW;;;;;;kDACzB,8OAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,8OAAC;wCAAO,OAAM;kDAAS;;;;;;kDACvB,8OAAC;wCAAO,OAAM;kDAAM;;;;;;;;;;;;;;;;;;;;;;;;0BAO5B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC;wBACT,MAAM,OAAO,IAAI,IAAI;wBACrB,MAAM,WAAW,cAAc,IAAI,EAAE;wBAErC,qBACE,8OAAC;4BAEC,SAAS,IAAM,aAAa,IAAI,EAAE;4BAClC,WAAW,CAAC,uFAAuF,EACjG,WACI,oCACA,8EACJ;;8CAEF,8OAAC;oCAAK,WAAU;;;;;;8CAChB,8OAAC;8CAAM,IAAI,KAAK;;;;;;gCACf,IAAI,KAAK,GAAG,mBACX,8OAAC;oCAAK,WAAW,CAAC,2CAA2C,EAC3D,WAAW,gCAAgC,6BAC3C;8CACC,IAAI,KAAK;;;;;;;2BAdT,IAAI,EAAE;;;;;oBAmBjB;;;;;;;;;;;0BAKJ,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,yLAAA,CAAA,kBAAe;oBAAC,MAAK;;wBACnB,cAAc,0BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,MAAM;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC3B,WAAU;sCAET,eAAe,MAAM,KAAK,kBACzB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;kDAAE;;;;;;;;;;;uCAGL,eAAe,GAAG,CAAC,CAAC,sBAClB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,WAAW,CAAC,sBAAsB,EAAE,iBAAiB,MAAM,QAAQ,GAAG;8CAEtE,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEACb,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK;;;;;;sEAE3B,8OAAC;4DAAK,WAAU;sEACb,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,MAAM,UAAU;;;;;;;;;;;;8DAGhC,8OAAC;oDAAG,WAAU;8DAAsB,MAAM,KAAK;;;;;;8DAC/C,8OAAC;oDAAE,WAAU;8DAAgB,MAAM,OAAO;;;;;;8DAC1C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;gEAAK;gEAAY,MAAM,QAAQ;;;;;;;sEAChC,8OAAC;;gEAAK;gEAAU,MAAM,gBAAgB,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;;;mCAnB7C,MAAM,EAAE;;;;;2BAdf;;;;;wBA2CP,cAAc,0BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,MAAM;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC3B,WAAU;sCAET,aAAa,MAAM,KAAK,kBACvB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,8OAAC;kDAAE;;;;;;;;;;;uCAGL,aAAa,GAAG,CAAC,CAAC,uBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;oDACZ,kBAAkB,OAAO,WAAW;kEACrC,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;;oEAA8B;oEAChC,OAAO,UAAU;;;;;;;0EAE7B,8OAAC;gEAAE,WAAU;0EAAyB,OAAO,QAAQ;;;;;;;;;;;;;;;;;;0DAGzD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;4DACZ,OAAO,iBAAiB;4DAAC;4DAAI,OAAO,gBAAgB;;;;;;;kEAEvD,8OAAC;wDAAI,WAAU;kEACZ,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,OAAO,UAAU;;;;;;;;;;;;;;;;;;mCApB9B,OAAO,EAAE;;;;;2BAdhB;;;;;wBA4CP,cAAc,4BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,MAAM;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC3B,WAAU;sCAET,eAAe,MAAM,KAAK,kBACzB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;kDAAE;;;;;;;;;;;uCAGL,eAAe,GAAG,CAAC,CAAC,sBAClB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAW,CAAC,2CAA2C,EAC3D,MAAM,IAAI,KAAK,kBAAkB,kCACjC,MAAM,IAAI,KAAK,YAAY,gCAC3B,MAAM,IAAI,KAAK,aAAa,8BAC5B,6BACA;0EACC,MAAM,IAAI;;;;;;4DAEZ,MAAM,mBAAmB,kBACxB,8OAAC;gEAAK,WAAU;;oEACb,MAAM,mBAAmB;oEAAC;;;;;;;;;;;;;kEAIjC,8OAAC;wDAAG,WAAU;kEACX,MAAM,KAAK;;;;;;kEAEd,8OAAC;wDAAE,WAAU;kEAAsB,MAAM,WAAW;;;;;;kEACpD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAK,WAAU;kFAAc;;;;;;oEAAmB;oEAAE,MAAM,UAAU;;;;;;;0EAErE,8OAAC;;kFACC,8OAAC;wEAAK,WAAU;kFAAc;;;;;;oEAAY;oEAAE,MAAM,QAAQ;;;;;;;0EAE5D,8OAAC;;kFACC,8OAAC;wEAAK,WAAU;kFAAc;;;;;;oEAAY;oEAAE,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,MAAM,UAAU;;;;;;;0EAEzE,8OAAC;;kFACC,8OAAC;wEAAK,WAAU;kFAAc;;;;;;oEAAoB;oEAAE,MAAM,oBAAoB;oEAAC;oEAAE,MAAM,gBAAgB;;;;;;;;;;;;;;;;;;;0DAI7G,8OAAC;gDAAI,WAAU;;oDACZ,MAAM,gBAAgB,GAAG,kBACxB,8OAAC;wDAAI,WAAU;;4DACZ,MAAM,gBAAgB,CAAC,cAAc;4DAAG;;;;;;6EAG3C,8OAAC;wDAAI,WAAU;kEAAmC;;;;;;kEAEpD,8OAAC;wDAAO,WAAU;kEAAyF;;;;;;;;;;;;;;;;;;mCAjD1G,MAAM,EAAE;;;;;2BAdf;;;;;wBA0EP,cAAc,wBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,MAAM;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC3B,WAAU;sCAET,YAAY,MAAM,KAAK,kBACtB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4MAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,8OAAC;kDAAE;;;;;;;;;;;uCAGL,YAAY,GAAG,CAAC,CAAC,qBACf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,WAAW,CAAC,sCAAsC,EAChD,KAAK,WAAW,GAAG,iEAAiE,YACpF;8CAEF,cAAA,8OAAC;wCAAI,WAAU;;4CACZ,KAAK,SAAS,kBACb,8OAAC;gDAAI,WAAU;;;;;;0DAEjB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAW,CAAC,2CAA2C,EAC3D,KAAK,QAAQ,KAAK,eAAe,4BACjC,KAAK,QAAQ,KAAK,eAAe,8BACjC,KAAK,QAAQ,KAAK,YAAY,gCAC9B,KAAK,QAAQ,KAAK,WAAW,kCAC7B,6BACA;0EACC,KAAK,QAAQ;;;;;;4DAEf,KAAK,WAAW,kBACf,8OAAC;gEAAK,WAAU;0EAAyE;;;;;;0EAI3F,8OAAC;gEAAK,WAAU;0EACb,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,YAAY;;;;;;;;;;;;kEAGjC,8OAAC;wDAAG,WAAU;kEACX,KAAK,KAAK;;;;;;kEAEb,8OAAC;wDAAE,WAAU;kEAAsB,KAAK,OAAO;;;;;;kEAC/C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;4EAAK;4EAAK,KAAK,MAAM;;;;;;;kFACtB,8OAAC;wEAAI,WAAU;kFACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBAC1B,8OAAC;gFAAe,WAAU;;oFAAwC;oFAC9D;;+EADO;;;;;;;;;;;;;;;;4DAMhB,KAAK,UAAU,kBACd,8OAAC;gEACC,MAAM,KAAK,UAAU;gEACrB,QAAO;gEACP,KAAI;gEACJ,WAAU;0EACX;;;;;;;;;;;;;;;;;;;;;;;;mCApDJ,KAAK,EAAE;;;;;2BAdd;;;;;;;;;;;;;;;;;;;;;;AAkFlB", "debugId": null}}, {"offset": {"line": 1730, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/app/hub/page.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { \n  ArrowLeft,\n  BarChart3,\n  TrendingUp,\n  Users,\n  Zap\n} from 'lucide-react'\nimport Link from 'next/link'\nimport InformationHub from '@/components/hub/InformationHub'\nimport RealTimeNotifications from '@/components/hub/RealTimeNotifications'\n\nexport default function HubPage() {\n  const stats = [\n    {\n      label: 'Alertes Actives',\n      value: '12',\n      change: '+3',\n      changeType: 'increase',\n      icon: <Zap className=\"h-6 w-6\" />\n    },\n    {\n      label: 'Mises à Jour Stock',\n      value: '47',\n      change: '+8',\n      changeType: 'increase',\n      icon: <BarChart3 className=\"h-6 w-6\" />\n    },\n    {\n      label: 'Formations Disponibles',\n      value: '6',\n      change: '+2',\n      changeType: 'increase',\n      icon: <Users className=\"h-6 w-6\" />\n    },\n    {\n      label: 'Actualités',\n      value: '23',\n      change: '+5',\n      changeType: 'increase',\n      icon: <TrendingUp className=\"h-6 w-6\" />\n    }\n  ]\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100\">\n      {/* Header */}\n      <header className=\"bg-white/80 backdrop-blur-md border-b border-slate-200 sticky top-0 z-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-16\">\n            <div className=\"flex items-center space-x-4\">\n              <Link \n                href=\"/\"\n                className=\"flex items-center space-x-2 text-slate-700 hover:text-amber-600 transition-colors\"\n              >\n                <ArrowLeft className=\"h-5 w-5\" />\n                <span>Retour à l'accueil</span>\n              </Link>\n            </div>\n            \n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-amber-400 to-amber-500 rounded-lg flex items-center justify-center\">\n                <Zap className=\"h-5 w-5 text-slate-900\" />\n              </div>\n              <div>\n                <h1 className=\"text-lg font-bold text-slate-900\">Pro Matos</h1>\n                <p className=\"text-xs text-slate-600\">Hub d'Information</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Contenu principal */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Titre et description */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"mb-8\"\n        >\n          <h1 className=\"text-3xl font-bold text-slate-900 mb-2\">\n            Hub d'Information et Veille\n          </h1>\n          <p className=\"text-lg text-slate-600\">\n            Votre centre de contrôle pour maîtriser l'écosystème électrique en temps réel\n          </p>\n        </motion.div>\n\n        {/* Statistiques rapides */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.1 }}\n          className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\"\n        >\n          {stats.map((stat, index) => (\n            <motion.div\n              key={stat.label}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.1 + index * 0.05 }}\n              className=\"industrial-card p-6\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-slate-600\">{stat.label}</p>\n                  <div className=\"flex items-baseline space-x-2\">\n                    <p className=\"text-2xl font-bold text-slate-900\">{stat.value}</p>\n                    <span className={`text-sm font-medium ${\n                      stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'\n                    }`}>\n                      {stat.change}\n                    </span>\n                  </div>\n                </div>\n                <div className=\"p-3 bg-amber-100 rounded-lg text-amber-600\">\n                  {stat.icon}\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* Hub d'Information principal */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.2 }}\n        >\n          <InformationHub />\n        </motion.div>\n\n        {/* Section d'aide */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.3 }}\n          className=\"mt-8 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200\"\n        >\n          <h3 className=\"text-lg font-semibold text-blue-900 mb-2\">\n            💡 Comment utiliser le Hub d'Information\n          </h3>\n          <div className=\"grid md:grid-cols-2 gap-4 text-sm text-blue-800\">\n            <div>\n              <h4 className=\"font-medium mb-1\">Alertes Marché</h4>\n              <p>Surveillez les ruptures de stock, variations de prix et nouveaux produits en temps réel</p>\n            </div>\n            <div>\n              <h4 className=\"font-medium mb-1\">Mises à Jour Stock</h4>\n              <p>Suivez les mouvements de stock chez tous vos fournisseurs partenaires</p>\n            </div>\n            <div>\n              <h4 className=\"font-medium mb-1\">Formations</h4>\n              <p>Accédez aux formations exclusives et certifications selon votre niveau d'adhésion</p>\n            </div>\n            <div>\n              <h4 className=\"font-medium mb-1\">Actualités</h4>\n              <p>Restez informé des dernières tendances et réglementations du secteur</p>\n            </div>\n          </div>\n        </motion.div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AACA;AAXA;;;;;;AAce,SAAS;IACtB,MAAM,QAAQ;QACZ;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,oBAAM,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;QACvB;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,oBAAM,8OAAC,kNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;QAC7B;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;QACzB;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,oBAAM,8OAAC,kNAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;QAC9B;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;0CAIV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;;;;;;kDAEjB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhD,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CAGvD,8OAAC;gCAAE,WAAU;0CAAyB;;;;;;;;;;;;kCAMxC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;wBACzB,WAAU;kCAET,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO,MAAM,QAAQ;gCAAK;gCACxC,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAsC,KAAK,KAAK;;;;;;8DAC7D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAqC,KAAK,KAAK;;;;;;sEAC5D,8OAAC;4DAAK,WAAW,CAAC,oBAAoB,EACpC,KAAK,UAAU,KAAK,aAAa,mBAAmB,gBACpD;sEACC,KAAK,MAAM;;;;;;;;;;;;;;;;;;sDAIlB,8OAAC;4CAAI,WAAU;sDACZ,KAAK,IAAI;;;;;;;;;;;;+BAnBT,KAAK,KAAK;;;;;;;;;;kCA2BrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;kCAEzB,cAAA,8OAAC,2IAAA,CAAA,UAAc;;;;;;;;;;kCAIjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;wBACzB,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAGzD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAmB;;;;;;0DACjC,8OAAC;0DAAE;;;;;;;;;;;;kDAEL,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAmB;;;;;;0DACjC,8OAAC;0DAAE;;;;;;;;;;;;kDAEL,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAmB;;;;;;0DACjC,8OAAC;0DAAE;;;;;;;;;;;;kDAEL,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAmB;;;;;;0DACjC,8OAAC;0DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjB", "debugId": null}}]}