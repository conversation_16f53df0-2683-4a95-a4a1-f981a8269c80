'use client'

import { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Users, 
  TrendingUp, 
  TrendingDown,
  Shield,
  Crown,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  Filter,
  Search,
  MoreHorizontal
} from 'lucide-react'
import { useAuthStore, usePermissions } from '@/lib/stores/authStore'
import { useCRMStore, useCRMActions } from '@/lib/stores/crmStore'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { formatDate } from '@/lib/utils'
import { toast } from 'sonner'
import DashboardLayout from '@/components/layout/DashboardLayout'

export default function CRMPage() {
  const { user } = useAuthStore()
  const permissions = usePermissions()
  const { users, validations, loading, fetchUsers, fetchValidations } = useCRMStore()
  const { promoteUser, demoteUser, blacklistUser, updateValidationStatus, incrementDevis, incrementAchats } = useCRMActions()
  const [searchQuery, setSearchQuery] = useState('')
  const [filterRole, setFilterRole] = useState<string>('all')
  const [filterStatus, setFilterStatus] = useState<string>('all')

  useEffect(() => {
    if (permissions.isAdmin) {
      fetchUsers()
      fetchValidations()
    }
  }, [permissions.isAdmin, fetchUsers, fetchValidations])

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.full_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         user.company?.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesRole = filterRole === 'all' || user.role === filterRole
    const matchesStatus = filterStatus === 'all' || user.statut === filterStatus
    
    return matchesSearch && matchesRole && matchesStatus
  })

  const handleUserAction = async (userId: string, action: string) => {
    const user = users.find(u => u.id === userId)
    if (!user) return

    switch (action) {
      case 'promote':
        await promoteUser(
          userId,
          () => toast.success(`${user.full_name} promu membre`),
          (error) => toast.error(`Erreur: ${error}`)
        )
        break
      case 'demote':
        await demoteUser(
          userId,
          () => toast.success(`${user.full_name} rétrogradé`),
          (error) => toast.error(`Erreur: ${error}`)
        )
        break
      case 'blacklist':
        await blacklistUser(
          userId,
          () => toast.success(`${user.full_name} blacklisté`),
          (error) => toast.error(`Erreur: ${error}`)
        )
        break
      case 'add_devis':
        await incrementDevis(
          userId,
          () => toast.success('Devis ajouté'),
          (error) => toast.error(`Erreur: ${error}`)
        )
        break
      case 'add_achat':
        await incrementAchats(
          userId,
          () => toast.success('Achat ajouté'),
          (error) => toast.error(`Erreur: ${error}`)
        )
        break
    }
  }

  const handleValidationAction = async (validationId: number, status: string, notes?: string) => {
    await updateValidationStatus(
      validationId,
      status as any,
      notes,
      () => toast.success(`Validation ${status.toLowerCase()}`),
      (error) => toast.error(`Erreur: ${error}`)
    )
  }

  const getRoleBadge = (role: string) => {
    switch (role) {
      case 'admin':
        return <Badge className="bg-red-100 text-red-800 border-red-200">Admin</Badge>
      case 'vip':
        return <Badge className="bg-amber-100 text-amber-800 border-amber-200">VIP</Badge>
      case 'member':
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Membre</Badge>
      default:
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">Invité</Badge>
    }
  }

  const getStatusBadge = (statut: string) => {
    switch (statut) {
      case 'white':
        return <Badge className="bg-green-100 text-green-800 border-green-200">Blanc</Badge>
      case 'grey':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Gris</Badge>
      case 'black':
        return <Badge className="bg-red-100 text-red-800 border-red-200">Noir</Badge>
      default:
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">Inconnu</Badge>
    }
  }

  const getValidationStatusIcon = (status: string) => {
    switch (status) {
      case 'Validé':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'Refusé':
        return <XCircle className="h-5 w-5 text-red-500" />
      default:
        return <Clock className="h-5 w-5 text-yellow-500" />
    }
  }

  if (!permissions.isAdmin) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[60vh]">
          <Card className="w-full max-w-md">
            <CardContent className="p-6 text-center">
              <Shield className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Accès Administrateur Requis
              </h3>
              <p className="text-gray-600 mb-4">
                Cette section est réservée aux administrateurs de la plateforme.
              </p>
              <Button variant="outline" onClick={() => window.history.back()}>
                Retour
              </Button>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0"
        >
          <div>
            <div className="flex items-center space-x-3 mb-2">
              <Shield className="h-8 w-8 text-red-500" />
              <h1 className="text-3xl font-bold text-gray-900">CRM Administration</h1>
              <Badge className="bg-red-100 text-red-800 border-red-200">
                ADMIN
              </Badge>
            </div>
            <p className="text-gray-600">
              Gestion des utilisateurs et des validations
            </p>
          </div>
        </motion.div>

        {/* Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-4 gap-6"
        >
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <Users className="h-8 w-8 text-blue-500" />
                <div>
                  <p className="text-2xl font-bold text-gray-900">{users.length}</p>
                  <p className="text-sm text-gray-600">Utilisateurs total</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <Crown className="h-8 w-8 text-amber-500" />
                <div>
                  <p className="text-2xl font-bold text-gray-900">
                    {users.filter(u => u.role === 'vip').length}
                  </p>
                  <p className="text-sm text-gray-600">Membres VIP</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-8 w-8 text-yellow-500" />
                <div>
                  <p className="text-2xl font-bold text-gray-900">
                    {users.filter(u => u.statut === 'grey').length}
                  </p>
                  <p className="text-sm text-gray-600">Utilisateurs gris</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <Clock className="h-8 w-8 text-green-500" />
                <div>
                  <p className="text-2xl font-bold text-gray-900">
                    {validations.filter(v => v.status === 'En cours').length}
                  </p>
                  <p className="text-sm text-gray-600">Validations en attente</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="flex flex-col space-y-4 md:flex-row md:items-center md:space-y-0 md:space-x-4"
        >
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Rechercher un utilisateur..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <Select value={filterRole} onValueChange={setFilterRole}>
            <SelectTrigger className="w-full md:w-48">
              <SelectValue placeholder="Rôle" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tous les rôles</SelectItem>
              <SelectItem value="guest">Invités</SelectItem>
              <SelectItem value="member">Membres</SelectItem>
              <SelectItem value="vip">VIP</SelectItem>
              <SelectItem value="admin">Admins</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={filterStatus} onValueChange={setFilterStatus}>
            <SelectTrigger className="w-full md:w-48">
              <SelectValue placeholder="Statut" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tous les statuts</SelectItem>
              <SelectItem value="white">Blanc</SelectItem>
              <SelectItem value="grey">Gris</SelectItem>
              <SelectItem value="black">Noir</SelectItem>
            </SelectContent>
          </Select>
        </motion.div>

        {/* Users Table */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card>
            <CardHeader>
              <CardTitle>Gestion des Utilisateurs</CardTitle>
              <CardDescription>
                Liste complète des utilisateurs avec actions de gestion
              </CardDescription>
            </CardHeader>
            
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-4">Utilisateur</th>
                      <th className="text-left p-4">Rôle</th>
                      <th className="text-left p-4">Statut</th>
                      <th className="text-left p-4">Devis</th>
                      <th className="text-left p-4">Achats</th>
                      <th className="text-left p-4">Inscription</th>
                      <th className="text-left p-4">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {loading ? (
                      Array.from({ length: 5 }).map((_, index) => (
                        <tr key={index} className="border-b animate-pulse">
                          <td className="p-4">
                            <div className="h-4 bg-gray-200 rounded"></div>
                          </td>
                          <td className="p-4">
                            <div className="h-4 bg-gray-200 rounded w-16"></div>
                          </td>
                          <td className="p-4">
                            <div className="h-4 bg-gray-200 rounded w-16"></div>
                          </td>
                          <td className="p-4">
                            <div className="h-4 bg-gray-200 rounded w-8"></div>
                          </td>
                          <td className="p-4">
                            <div className="h-4 bg-gray-200 rounded w-8"></div>
                          </td>
                          <td className="p-4">
                            <div className="h-4 bg-gray-200 rounded w-20"></div>
                          </td>
                          <td className="p-4">
                            <div className="h-4 bg-gray-200 rounded w-16"></div>
                          </td>
                        </tr>
                      ))
                    ) : filteredUsers.length === 0 ? (
                      <tr>
                        <td colSpan={7} className="p-8 text-center text-gray-500">
                          Aucun utilisateur trouvé
                        </td>
                      </tr>
                    ) : (
                      filteredUsers.map((user) => (
                        <tr key={user.id} className="border-b hover:bg-gray-50">
                          <td className="p-4">
                            <div>
                              <div className="font-medium text-gray-900">
                                {user.full_name || 'Nom non renseigné'}
                              </div>
                              <div className="text-sm text-gray-500">{user.email}</div>
                              {user.company && (
                                <div className="text-xs text-gray-400">{user.company}</div>
                              )}
                            </div>
                          </td>
                          <td className="p-4">
                            {getRoleBadge(user.role)}
                          </td>
                          <td className="p-4">
                            {getStatusBadge(user.statut)}
                          </td>
                          <td className="p-4">
                            <div className="flex items-center space-x-2">
                              <span className="font-medium">{user.devis_demandes}</span>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleUserAction(user.id, 'add_devis')}
                              >
                                +
                              </Button>
                            </div>
                          </td>
                          <td className="p-4">
                            <div className="flex items-center space-x-2">
                              <span className="font-medium">{user.achats}</span>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleUserAction(user.id, 'add_achat')}
                              >
                                +
                              </Button>
                            </div>
                          </td>
                          <td className="p-4">
                            <span className="text-sm text-gray-500">
                              {formatDate(user.created_at)}
                            </span>
                          </td>
                          <td className="p-4">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent>
                                <DropdownMenuItem
                                  onClick={() => handleUserAction(user.id, 'promote')}
                                  disabled={user.role === 'admin'}
                                >
                                  <TrendingUp className="h-4 w-4 mr-2" />
                                  Promouvoir
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => handleUserAction(user.id, 'demote')}
                                  disabled={user.role === 'guest'}
                                >
                                  <TrendingDown className="h-4 w-4 mr-2" />
                                  Rétrograder
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => handleUserAction(user.id, 'blacklist')}
                                  disabled={user.statut === 'black'}
                                  className="text-red-600"
                                >
                                  <XCircle className="h-4 w-4 mr-2" />
                                  Blacklister
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </DashboardLayout>
  )
}
