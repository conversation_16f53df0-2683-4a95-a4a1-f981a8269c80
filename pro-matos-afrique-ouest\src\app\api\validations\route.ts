import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'

export async function GET(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })

    // Vérifier l'authentification
    const { data: { session }, error: authError } = await supabase.auth.getSession()
    
    if (authError || !session) {
      return NextResponse.json(
        { error: 'Non authentifié' },
        { status: 401 }
      )
    }

    // Récupérer les validations de l'utilisateur
    const { data: validations, error } = await supabase
      .from('validations')
      .select('*')
      .eq('user_id', session.user.id)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Erreur récupération validations:', error)
      return NextResponse.json(
        { error: 'Erreur lors de la récupération des validations' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: validations
    })

  } catch (error) {
    console.error('Erreur API validations:', error)
    return NextResponse.json(
      { error: 'Erreur serveur' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })

    // Vérifier l'authentification
    const { data: { session }, error: authError } = await supabase.auth.getSession()
    
    if (authError || !session) {
      return NextResponse.json(
        { error: 'Non authentifié' },
        { status: 401 }
      )
    }

    // Vérifier les permissions admin
    const { data: user } = await supabase
      .from('users')
      .select('role')
      .eq('id', session.user.id)
      .single()

    if (!user || user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Permissions administrateur requises' },
        { status: 403 }
      )
    }

    const { validationId, status, adminNotes } = await request.json()

    if (!validationId || !status) {
      return NextResponse.json(
        { error: 'ID de validation et statut requis' },
        { status: 400 }
      )
    }

    // Mettre à jour la validation
    const { data: validation, error } = await supabase
      .from('validations')
      .update({
        status,
        admin_notes: adminNotes,
        updated_at: new Date().toISOString()
      })
      .eq('id', validationId)
      .select()
      .single()

    if (error) {
      console.error('Erreur mise à jour validation:', error)
      return NextResponse.json(
        { error: 'Erreur lors de la mise à jour' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: validation,
      message: 'Validation mise à jour avec succès'
    })

  } catch (error) {
    console.error('Erreur API update validation:', error)
    return NextResponse.json(
      { error: 'Erreur serveur' },
      { status: 500 }
    )
  }
}
