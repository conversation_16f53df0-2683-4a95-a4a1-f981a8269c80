'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  FileText,
  Download,
  Share2,
  Printer,
  CheckCircle,
  AlertTriangle,
  Info,
  Settings,
  Calendar,
  User,
  Building,
  Zap
} from 'lucide-react'
import { useStore } from '@/store/useStore'
import { ExpertService } from '@/services/expertService'
import { formatDate } from '@/lib/utils'

interface ReportGeneratorProps {
  type: 'validation' | 'compatibility' | 'installation' | 'maintenance' | 'compliance'
  data?: any
  onClose?: () => void
}

export default function ReportGenerator({ type, data, onClose }: ReportGeneratorProps) {
  const { user, addTechnicalReport } = useStore()
  const [reportConfig, setReportConfig] = useState({
    includeRecommendations: true,
    includeWarnings: true,
    includeImages: true,
    includeSpecs: true,
    format: 'pdf',
    language: 'fr'
  })
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedReport, setGeneratedReport] = useState<any>(null)

  const reportTypes = {
    validation: {
      title: 'Rapport de Validation Technique',
      description: 'Validation complète de documents techniques selon les normes',
      icon: <CheckCircle className="h-6 w-6" />,
      color: 'from-green-500 to-green-600'
    },
    compatibility: {
      title: 'Rapport de Compatibilité',
      description: 'Analyse de compatibilité entre composants électriques',
      icon: <Zap className="h-6 w-6" />,
      color: 'from-blue-500 to-blue-600'
    },
    installation: {
      title: 'Rapport d\'Installation',
      description: 'Guide d\'installation et mise en service',
      icon: <Settings className="h-6 w-6" />,
      color: 'from-purple-500 to-purple-600'
    },
    maintenance: {
      title: 'Rapport de Maintenance',
      description: 'Plan de maintenance préventive et corrective',
      icon: <Settings className="h-6 w-6" />,
      color: 'from-orange-500 to-orange-600'
    },
    compliance: {
      title: 'Rapport de Conformité',
      description: 'Vérification de conformité aux normes en vigueur',
      icon: <CheckCircle className="h-6 w-6" />,
      color: 'from-red-500 to-red-600'
    }
  }

  const currentType = reportTypes[type]

  const generateReport = async () => {
    setIsGenerating(true)
    
    // Simulation de génération de rapport
    setTimeout(() => {
      const reportData = {
        ...data,
        config: reportConfig,
        metadata: {
          generatedBy: user?.full_name || 'Utilisateur',
          generatedAt: new Date().toISOString(),
          reportId: `RPT_${Date.now()}`,
          version: '1.0'
        },
        recommendations: [
          'Vérifier la conformité aux normes NF C 15-100',
          'Prévoir une protection différentielle adaptée',
          'Respecter les distances de sécurité minimales',
          'Effectuer les tests de continuité requis'
        ],
        warnings: [
          'Attention aux risques de surchauffe',
          'Vérifier la compatibilité des indices de protection',
          'Respecter les conditions d\'environnement'
        ],
        compliance_status: 'compliant'
      }

      const report = ExpertService.generateTechnicalReport(type, reportData)
      setGeneratedReport(report)
      addTechnicalReport(report)
      setIsGenerating(false)
    }, 3000)
  }

  const downloadReport = () => {
    // Simulation de téléchargement
    const blob = new Blob(['Rapport technique généré'], { type: 'application/pdf' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `rapport-${type}-${Date.now()}.pdf`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 max-w-4xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <div className={`w-12 h-12 bg-gradient-to-r ${currentType.color} rounded-lg flex items-center justify-center text-white`}>
            {currentType.icon}
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">{currentType.title}</h2>
            <p className="text-gray-600">{currentType.description}</p>
          </div>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ×
          </button>
        )}
      </div>

      {!generatedReport ? (
        <>
          {/* Configuration du rapport */}
          <div className="grid md:grid-cols-2 gap-6 mb-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Configuration du Rapport</h3>
              
              <div className="space-y-3">
                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={reportConfig.includeRecommendations}
                    onChange={(e) => setReportConfig(prev => ({ ...prev, includeRecommendations: e.target.checked }))}
                    className="rounded border-gray-300 text-amber-600 focus:ring-amber-500"
                  />
                  <span className="text-sm text-gray-700">Inclure les recommandations</span>
                </label>
                
                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={reportConfig.includeWarnings}
                    onChange={(e) => setReportConfig(prev => ({ ...prev, includeWarnings: e.target.checked }))}
                    className="rounded border-gray-300 text-amber-600 focus:ring-amber-500"
                  />
                  <span className="text-sm text-gray-700">Inclure les avertissements</span>
                </label>
                
                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={reportConfig.includeImages}
                    onChange={(e) => setReportConfig(prev => ({ ...prev, includeImages: e.target.checked }))}
                    className="rounded border-gray-300 text-amber-600 focus:ring-amber-500"
                  />
                  <span className="text-sm text-gray-700">Inclure les images et schémas</span>
                </label>
                
                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={reportConfig.includeSpecs}
                    onChange={(e) => setReportConfig(prev => ({ ...prev, includeSpecs: e.target.checked }))}
                    className="rounded border-gray-300 text-amber-600 focus:ring-amber-500"
                  />
                  <span className="text-sm text-gray-700">Inclure les spécifications techniques</span>
                </label>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Options d'Export</h3>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Format</label>
                <select
                  value={reportConfig.format}
                  onChange={(e) => setReportConfig(prev => ({ ...prev, format: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400"
                >
                  <option value="pdf">PDF</option>
                  <option value="docx">Word (DOCX)</option>
                  <option value="html">HTML</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Langue</label>
                <select
                  value={reportConfig.language}
                  onChange={(e) => setReportConfig(prev => ({ ...prev, language: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400"
                >
                  <option value="fr">Français</option>
                  <option value="en">English</option>
                </select>
              </div>
            </div>
          </div>

          {/* Aperçu du contenu */}
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">Aperçu du Contenu</h3>
            <div className="space-y-2 text-sm text-gray-700">
              <div className="flex items-center space-x-2">
                <Info className="h-4 w-4 text-blue-500" />
                <span>Informations générales et métadonnées</span>
              </div>
              <div className="flex items-center space-x-2">
                <FileText className="h-4 w-4 text-gray-500" />
                <span>Analyse technique détaillée</span>
              </div>
              {reportConfig.includeRecommendations && (
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span>Recommandations d'amélioration</span>
                </div>
              )}
              {reportConfig.includeWarnings && (
                <div className="flex items-center space-x-2">
                  <AlertTriangle className="h-4 w-4 text-orange-500" />
                  <span>Avertissements et précautions</span>
                </div>
              )}
              <div className="flex items-center space-x-2">
                <User className="h-4 w-4 text-purple-500" />
                <span>Signature et certification</span>
              </div>
            </div>
          </div>

          {/* Bouton de génération */}
          <div className="flex justify-center">
            <button
              onClick={generateReport}
              disabled={isGenerating}
              className="btn-premium px-8 py-3 text-lg disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isGenerating ? (
                <div className="flex items-center space-x-2">
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>Génération en cours...</span>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <FileText className="h-5 w-5" />
                  <span>Générer le Rapport</span>
                </div>
              )}
            </button>
          </div>
        </>
      ) : (
        /* Rapport généré */
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          {/* Succès */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <CheckCircle className="h-6 w-6 text-green-600" />
              <div>
                <h3 className="font-semibold text-green-900">Rapport généré avec succès !</h3>
                <p className="text-sm text-green-700">
                  Votre rapport technique est prêt à être téléchargé ou partagé.
                </p>
              </div>
            </div>
          </div>

          {/* Informations du rapport */}
          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <h3 className="font-semibold text-gray-900">Informations du Rapport</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">ID du rapport:</span>
                  <span className="font-medium">{generatedReport.id}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Type:</span>
                  <span className="font-medium">{generatedReport.report_type}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Généré le:</span>
                  <span className="font-medium">{formatDate(generatedReport.created_at)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Statut:</span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    generatedReport.compliance_status === 'compliant' ? 'bg-green-100 text-green-800' :
                    generatedReport.compliance_status === 'non_compliant' ? 'bg-red-100 text-red-800' :
                    'bg-yellow-100 text-yellow-800'
                  }`}>
                    {generatedReport.compliance_status}
                  </span>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <h3 className="font-semibold text-gray-900">Actions</h3>
              <div className="space-y-2">
                <button
                  onClick={downloadReport}
                  className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <Download className="h-4 w-4" />
                  <span>Télécharger PDF</span>
                </button>
                <button className="w-full flex items-center justify-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                  <Share2 className="h-4 w-4" />
                  <span>Partager</span>
                </button>
                <button className="w-full flex items-center justify-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                  <Printer className="h-4 w-4" />
                  <span>Imprimer</span>
                </button>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  )
}
