{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/components/navigation/GlobalNavigation.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { \n  Home,\n  Database,\n  Users,\n  FileText,\n  Award,\n  Settings,\n  Menu,\n  X,\n  ChevronDown,\n  Bell,\n  Search,\n  User,\n  Crown,\n  Zap\n} from 'lucide-react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\n\ninterface GlobalNavigationProps {\n  className?: string\n}\n\nexport default function GlobalNavigation({ className = '' }: GlobalNavigationProps) {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)\n  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false)\n  const [notifications, setNotifications] = useState(3)\n  const pathname = usePathname()\n\n  const navigationItems = [\n    {\n      name: 'Accueil',\n      href: '/',\n      icon: <Home className=\"h-5 w-5\" />,\n      description: 'Tableau de bord principal'\n    },\n    {\n      name: 'Hub Information',\n      href: '/hub',\n      icon: <Database className=\"h-5 w-5\" />,\n      description: 'Centre d\\'information temps réel',\n      badge: 'LIVE'\n    },\n    {\n      name: 'Expert Conseil',\n      href: '/expert',\n      icon: <Users className=\"h-5 w-5\" />,\n      description: 'Consultation et validation technique',\n      badge: 'PRO'\n    },\n    {\n      name: 'Module Prescripteur',\n      href: '/prescriptor',\n      icon: <FileText className=\"h-5 w-5\" />,\n      description: 'Templates et certification',\n      badge: 'PREMIUM'\n    },\n    {\n      name: 'Club Membre',\n      href: '/club',\n      icon: <Crown className=\"h-5 w-5\" />,\n      description: 'Réseau exclusif professionnel',\n      badge: 'VIP'\n    }\n  ]\n\n  const userProfile = {\n    name: 'Jean Kouassi',\n    role: 'Ingénieur Électricien',\n    company: 'SODECI',\n    level: 'Gold',\n    avatar: '/api/placeholder/40/40'\n  }\n\n  const quickActions = [\n    { name: 'Nouveau Projet', href: '/prescriptor?action=new', icon: <FileText className=\"h-4 w-4\" /> },\n    { name: 'Consultation Express', href: '/expert?mode=express', icon: <Zap className=\"h-4 w-4\" /> },\n    { name: 'Alertes Hub', href: '/hub?tab=alerts', icon: <Bell className=\"h-4 w-4\" /> }\n  ]\n\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (isProfileMenuOpen) {\n        setIsProfileMenuOpen(false)\n      }\n    }\n\n    document.addEventListener('click', handleClickOutside)\n    return () => document.removeEventListener('click', handleClickOutside)\n  }, [isProfileMenuOpen])\n\n  const isActive = (href: string) => {\n    if (href === '/') return pathname === '/'\n    return pathname.startsWith(href)\n  }\n\n  return (\n    <nav className={`bg-white/95 backdrop-blur-md border-b border-gray-200 sticky top-0 z-50 ${className}`}>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo et branding */}\n          <div className=\"flex items-center space-x-4\">\n            <Link href=\"/\" className=\"flex items-center space-x-3\">\n              <div className=\"w-10 h-10 bg-gradient-to-r from-amber-400 to-amber-500 rounded-lg flex items-center justify-center\">\n                <Zap className=\"h-6 w-6 text-slate-900\" />\n              </div>\n              <div className=\"hidden sm:block\">\n                <h1 className=\"text-xl font-bold text-slate-900\">Pro Matos</h1>\n                <p className=\"text-xs text-slate-600\">Afrique Ouest</p>\n              </div>\n            </Link>\n          </div>\n\n          {/* Navigation principale - Desktop */}\n          <div className=\"hidden lg:flex items-center space-x-1\">\n            {navigationItems.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className={`relative px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 group ${\n                  isActive(item.href)\n                    ? 'bg-amber-100 text-amber-900'\n                    : 'text-slate-700 hover:bg-slate-100 hover:text-slate-900'\n                }`}\n              >\n                <div className=\"flex items-center space-x-2\">\n                  {item.icon}\n                  <span>{item.name}</span>\n                  {item.badge && (\n                    <span className={`px-2 py-0.5 text-xs font-bold rounded-full ${\n                      item.badge === 'LIVE' ? 'bg-red-100 text-red-700' :\n                      item.badge === 'PRO' ? 'bg-blue-100 text-blue-700' :\n                      item.badge === 'PREMIUM' ? 'bg-purple-100 text-purple-700' :\n                      'bg-amber-100 text-amber-700'\n                    }`}>\n                      {item.badge}\n                    </span>\n                  )}\n                </div>\n                \n                {/* Tooltip */}\n                <div className=\"absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-3 py-2 bg-slate-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap\">\n                  {item.description}\n                  <div className=\"absolute bottom-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-b-slate-900\"></div>\n                </div>\n              </Link>\n            ))}\n          </div>\n\n          {/* Actions rapides et profil */}\n          <div className=\"flex items-center space-x-4\">\n            {/* Barre de recherche rapide */}\n            <div className=\"hidden md:block relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400\" />\n              <input\n                type=\"text\"\n                placeholder=\"Recherche rapide...\"\n                className=\"pl-10 pr-4 py-2 w-64 border border-slate-300 rounded-lg focus:ring-2 focus:ring-amber-400 focus:border-transparent text-sm\"\n              />\n            </div>\n\n            {/* Actions rapides */}\n            <div className=\"hidden lg:flex items-center space-x-2\">\n              {quickActions.map((action) => (\n                <Link\n                  key={action.name}\n                  href={action.href}\n                  className=\"p-2 text-slate-600 hover:text-amber-600 hover:bg-amber-50 rounded-lg transition-colors\"\n                  title={action.name}\n                >\n                  {action.icon}\n                </Link>\n              ))}\n            </div>\n\n            {/* Notifications */}\n            <button className=\"relative p-2 text-slate-600 hover:text-amber-600 hover:bg-amber-50 rounded-lg transition-colors\">\n              <Bell className=\"h-5 w-5\" />\n              {notifications > 0 && (\n                <span className=\"absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\">\n                  {notifications}\n                </span>\n              )}\n            </button>\n\n            {/* Profil utilisateur */}\n            <div className=\"relative\">\n              <button\n                onClick={(e) => {\n                  e.stopPropagation()\n                  setIsProfileMenuOpen(!isProfileMenuOpen)\n                }}\n                className=\"flex items-center space-x-3 p-2 rounded-lg hover:bg-slate-100 transition-colors\"\n              >\n                <div className=\"w-8 h-8 bg-gradient-to-r from-amber-400 to-amber-500 rounded-full flex items-center justify-center\">\n                  <User className=\"h-4 w-4 text-slate-900\" />\n                </div>\n                <div className=\"hidden sm:block text-left\">\n                  <div className=\"text-sm font-medium text-slate-900\">{userProfile.name}</div>\n                  <div className=\"text-xs text-slate-600\">{userProfile.level}</div>\n                </div>\n                <ChevronDown className=\"h-4 w-4 text-slate-600\" />\n              </button>\n\n              {/* Menu profil */}\n              <AnimatePresence>\n                {isProfileMenuOpen && (\n                  <motion.div\n                    initial={{ opacity: 0, y: -10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    exit={{ opacity: 0, y: -10 }}\n                    className=\"absolute right-0 top-full mt-2 w-64 bg-white rounded-lg shadow-lg border border-slate-200 py-2\"\n                  >\n                    <div className=\"px-4 py-3 border-b border-slate-200\">\n                      <div className=\"font-medium text-slate-900\">{userProfile.name}</div>\n                      <div className=\"text-sm text-slate-600\">{userProfile.role}</div>\n                      <div className=\"text-sm text-slate-600\">{userProfile.company}</div>\n                      <div className=\"mt-2\">\n                        <span className=\"px-2 py-1 bg-amber-100 text-amber-800 text-xs font-medium rounded-full\">\n                          Niveau {userProfile.level}\n                        </span>\n                      </div>\n                    </div>\n                    \n                    <div className=\"py-2\">\n                      <Link href=\"/profile\" className=\"block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100\">\n                        Mon Profil\n                      </Link>\n                      <Link href=\"/settings\" className=\"block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100\">\n                        Paramètres\n                      </Link>\n                      <Link href=\"/billing\" className=\"block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100\">\n                        Facturation\n                      </Link>\n                      <div className=\"border-t border-slate-200 mt-2 pt-2\">\n                        <button className=\"block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50\">\n                          Déconnexion\n                        </button>\n                      </div>\n                    </div>\n                  </motion.div>\n                )}\n              </AnimatePresence>\n            </div>\n\n            {/* Menu mobile */}\n            <button\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n              className=\"lg:hidden p-2 text-slate-600 hover:text-slate-900\"\n            >\n              {isMobileMenuOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\n            </button>\n          </div>\n        </div>\n\n        {/* Menu mobile */}\n        <AnimatePresence>\n          {isMobileMenuOpen && (\n            <motion.div\n              initial={{ opacity: 0, height: 0 }}\n              animate={{ opacity: 1, height: 'auto' }}\n              exit={{ opacity: 0, height: 0 }}\n              className=\"lg:hidden border-t border-slate-200 py-4\"\n            >\n              <div className=\"space-y-2\">\n                {navigationItems.map((item) => (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    onClick={() => setIsMobileMenuOpen(false)}\n                    className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors ${\n                      isActive(item.href)\n                        ? 'bg-amber-100 text-amber-900'\n                        : 'text-slate-700 hover:bg-slate-100'\n                    }`}\n                  >\n                    {item.icon}\n                    <div className=\"flex-1\">\n                      <div className=\"font-medium\">{item.name}</div>\n                      <div className=\"text-sm text-slate-600\">{item.description}</div>\n                    </div>\n                    {item.badge && (\n                      <span className={`px-2 py-1 text-xs font-bold rounded-full ${\n                        item.badge === 'LIVE' ? 'bg-red-100 text-red-700' :\n                        item.badge === 'PRO' ? 'bg-blue-100 text-blue-700' :\n                        item.badge === 'PREMIUM' ? 'bg-purple-100 text-purple-700' :\n                        'bg-amber-100 text-amber-700'\n                      }`}>\n                        {item.badge}\n                      </span>\n                    )}\n                  </Link>\n                ))}\n              </div>\n\n              {/* Actions rapides mobile */}\n              <div className=\"mt-4 pt-4 border-t border-slate-200\">\n                <div className=\"px-4 mb-3\">\n                  <div className=\"text-sm font-medium text-slate-900\">Actions Rapides</div>\n                </div>\n                <div className=\"space-y-2\">\n                  {quickActions.map((action) => (\n                    <Link\n                      key={action.name}\n                      href={action.href}\n                      onClick={() => setIsMobileMenuOpen(false)}\n                      className=\"flex items-center space-x-3 px-4 py-2 text-slate-700 hover:bg-slate-100 rounded-lg\"\n                    >\n                      {action.icon}\n                      <span>{action.name}</span>\n                    </Link>\n                  ))}\n                </div>\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AACA;;;AArBA;;;;;;AA2Be,SAAS,iBAAiB,EAAE,YAAY,EAAE,EAAyB;;IAChF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,kBAAkB;QACtB;YACE,MAAM;YACN,MAAM;YACN,oBAAM,6LAAC,sMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,oBAAM,6LAAC,iNAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,aAAa;YACb,OAAO;QACT;KACD;IAED,MAAM,cAAc;QAClB,MAAM;QACN,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IAEA,MAAM,eAAe;QACnB;YAAE,MAAM;YAAkB,MAAM;YAA2B,oBAAM,6LAAC,iNAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;QAAa;QAClG;YAAE,MAAM;YAAwB,MAAM;YAAwB,oBAAM,6LAAC,mMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;QAAa;QAChG;YAAE,MAAM;YAAe,MAAM;YAAmB,oBAAM,6LAAC,qMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;QAAa;KACpF;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;iEAAqB,CAAC;oBAC1B,IAAI,mBAAmB;wBACrB,qBAAqB;oBACvB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,SAAS;YACnC;8CAAO,IAAM,SAAS,mBAAmB,CAAC,SAAS;;QACrD;qCAAG;QAAC;KAAkB;IAEtB,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,KAAK,OAAO,aAAa;QACtC,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,wEAAwE,EAAE,WAAW;kBACpG,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;;;;;;kDAEjB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAyB;;;;;;;;;;;;;;;;;;;;;;;sCAM5C,6LAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC,oFAAoF,EAC9F,SAAS,KAAK,IAAI,IACd,gCACA,0DACJ;;sDAEF,6LAAC;4CAAI,WAAU;;gDACZ,KAAK,IAAI;8DACV,6LAAC;8DAAM,KAAK,IAAI;;;;;;gDACf,KAAK,KAAK,kBACT,6LAAC;oDAAK,WAAW,CAAC,2CAA2C,EAC3D,KAAK,KAAK,KAAK,SAAS,4BACxB,KAAK,KAAK,KAAK,QAAQ,8BACvB,KAAK,KAAK,KAAK,YAAY,kCAC3B,+BACA;8DACC,KAAK,KAAK;;;;;;;;;;;;sDAMjB,6LAAC;4CAAI,WAAU;;gDACZ,KAAK,WAAW;8DACjB,6LAAC;oDAAI,WAAU;;;;;;;;;;;;;mCA1BZ,KAAK,IAAI;;;;;;;;;;sCAiCpB,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;;;;;;;8CAKd,6LAAC;oCAAI,WAAU;8CACZ,aAAa,GAAG,CAAC,CAAC,uBACjB,6LAAC,+JAAA,CAAA,UAAI;4CAEH,MAAM,OAAO,IAAI;4CACjB,WAAU;4CACV,OAAO,OAAO,IAAI;sDAEjB,OAAO,IAAI;2CALP,OAAO,IAAI;;;;;;;;;;8CAWtB,6LAAC;oCAAO,WAAU;;sDAChB,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCACf,gBAAgB,mBACf,6LAAC;4CAAK,WAAU;sDACb;;;;;;;;;;;;8CAMP,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,CAAC;gDACR,EAAE,eAAe;gDACjB,qBAAqB,CAAC;4CACxB;4CACA,WAAU;;8DAEV,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAsC,YAAY,IAAI;;;;;;sEACrE,6LAAC;4DAAI,WAAU;sEAA0B,YAAY,KAAK;;;;;;;;;;;;8DAE5D,6LAAC,uNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;;sDAIzB,6LAAC,4LAAA,CAAA,kBAAe;sDACb,mCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC9B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,MAAM;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC3B,WAAU;;kEAEV,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAA8B,YAAY,IAAI;;;;;;0EAC7D,6LAAC;gEAAI,WAAU;0EAA0B,YAAY,IAAI;;;;;;0EACzD,6LAAC;gEAAI,WAAU;0EAA0B,YAAY,OAAO;;;;;;0EAC5D,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAK,WAAU;;wEAAyE;wEAC/E,YAAY,KAAK;;;;;;;;;;;;;;;;;;kEAK/B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,+JAAA,CAAA,UAAI;gEAAC,MAAK;gEAAW,WAAU;0EAA4D;;;;;;0EAG5F,6LAAC,+JAAA,CAAA,UAAI;gEAAC,MAAK;gEAAY,WAAU;0EAA4D;;;;;;0EAG7F,6LAAC,+JAAA,CAAA,UAAI;gEAAC,MAAK;gEAAW,WAAU;0EAA4D;;;;;;0EAG5F,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAO,WAAU;8EAAwE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAWtG,6LAAC;oCACC,SAAS,IAAM,oBAAoB,CAAC;oCACpC,WAAU;8CAET,iCAAmB,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;6DAAe,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAMtE,6LAAC,4LAAA,CAAA,kBAAe;8BACb,kCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBACjC,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAO;wBACtC,MAAM;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBAC9B,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;0CACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,6LAAC,+JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,SAAS,IAAM,oBAAoB;wCACnC,WAAW,CAAC,mEAAmE,EAC7E,SAAS,KAAK,IAAI,IACd,gCACA,qCACJ;;4CAED,KAAK,IAAI;0DACV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAe,KAAK,IAAI;;;;;;kEACvC,6LAAC;wDAAI,WAAU;kEAA0B,KAAK,WAAW;;;;;;;;;;;;4CAE1D,KAAK,KAAK,kBACT,6LAAC;gDAAK,WAAW,CAAC,yCAAyC,EACzD,KAAK,KAAK,KAAK,SAAS,4BACxB,KAAK,KAAK,KAAK,QAAQ,8BACvB,KAAK,KAAK,KAAK,YAAY,kCAC3B,+BACA;0DACC,KAAK,KAAK;;;;;;;uCArBV,KAAK,IAAI;;;;;;;;;;0CA6BpB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDAAqC;;;;;;;;;;;kDAEtD,6LAAC;wCAAI,WAAU;kDACZ,aAAa,GAAG,CAAC,CAAC,uBACjB,6LAAC,+JAAA,CAAA,UAAI;gDAEH,MAAM,OAAO,IAAI;gDACjB,SAAS,IAAM,oBAAoB;gDACnC,WAAU;;oDAET,OAAO,IAAI;kEACZ,6LAAC;kEAAM,OAAO,IAAI;;;;;;;+CANb,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBtC;GAzSwB;;QAIL,qIAAA,CAAA,cAAW;;;KAJN", "debugId": null}}, {"offset": {"line": 751, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/components/layout/ResponsiveLayout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { motion } from 'framer-motion'\nimport { \n  ChevronLeft,\n  ChevronRight,\n  Grid,\n  List,\n  Maximize2,\n  Minimize2,\n  Smartphone,\n  Tablet,\n  Monitor\n} from 'lucide-react'\n\ninterface ResponsiveLayoutProps {\n  children: React.ReactNode\n  title?: string\n  subtitle?: string\n  sidebar?: React.ReactNode\n  actions?: React.ReactNode\n  className?: string\n}\n\nexport default function ResponsiveLayout({ \n  children, \n  title, \n  subtitle, \n  sidebar, \n  actions,\n  className = '' \n}: ResponsiveLayoutProps) {\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)\n  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')\n  const [isFullscreen, setIsFullscreen] = useState(false)\n  const [screenSize, setScreenSize] = useState<'mobile' | 'tablet' | 'desktop'>('desktop')\n\n  useEffect(() => {\n    const handleResize = () => {\n      if (typeof window === 'undefined') return\n\n      const width = window.innerWidth\n      if (width < 768) {\n        setScreenSize('mobile')\n        setSidebarCollapsed(true)\n      } else if (width < 1024) {\n        setScreenSize('tablet')\n        setSidebarCollapsed(false)\n      } else {\n        setScreenSize('desktop')\n        setSidebarCollapsed(false)\n      }\n    }\n\n    handleResize()\n    if (typeof window !== 'undefined') {\n      window.addEventListener('resize', handleResize)\n      return () => window.removeEventListener('resize', handleResize)\n    }\n  }, [])\n\n  const toggleFullscreen = () => {\n    if (typeof document === 'undefined') return\n\n    if (!document.fullscreenElement) {\n      document.documentElement.requestFullscreen()\n      setIsFullscreen(true)\n    } else {\n      document.exitFullscreen()\n      setIsFullscreen(false)\n    }\n  }\n\n  return (\n    <div className={`min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 ${className}`}>\n      {/* Header responsive */}\n      <div className=\"bg-white/80 backdrop-blur-md border-b border-slate-200 sticky top-16 z-40\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-16\">\n            {/* Titre et contrôles */}\n            <div className=\"flex items-center space-x-4\">\n              {sidebar && (\n                <button\n                  type=\"button\"\n                  onClick={() => setSidebarCollapsed(!sidebarCollapsed)}\n                  className=\"p-2 text-slate-600 hover:text-slate-900 hover:bg-slate-100 rounded-lg transition-colors lg:hidden\"\n                >\n                  {sidebarCollapsed ? <ChevronRight className=\"h-5 w-5\" /> : <ChevronLeft className=\"h-5 w-5\" />}\n                </button>\n              )}\n              \n              <div>\n                {title && (\n                  <h1 className=\"text-xl font-bold text-slate-900 truncate max-w-xs sm:max-w-none\">\n                    {title}\n                  </h1>\n                )}\n                {subtitle && (\n                  <p className=\"text-sm text-slate-600 hidden sm:block\">\n                    {subtitle}\n                  </p>\n                )}\n              </div>\n            </div>\n\n            {/* Contrôles d'affichage */}\n            <div className=\"flex items-center space-x-2\">\n              {/* Indicateur de taille d'écran */}\n              <div className=\"hidden md:flex items-center space-x-1 px-3 py-1 bg-slate-100 rounded-lg\">\n                {screenSize === 'mobile' && <Smartphone className=\"h-4 w-4 text-slate-600\" />}\n                {screenSize === 'tablet' && <Tablet className=\"h-4 w-4 text-slate-600\" />}\n                {screenSize === 'desktop' && <Monitor className=\"h-4 w-4 text-slate-600\" />}\n                <span className=\"text-xs text-slate-600 capitalize\">{screenSize}</span>\n              </div>\n\n              {/* Mode d'affichage */}\n              <div className=\"hidden sm:flex items-center bg-slate-100 rounded-lg p-1\">\n                <button\n                  type=\"button\"\n                  onClick={() => setViewMode('grid')}\n                  className={`p-2 rounded transition-colors ${\n                    viewMode === 'grid'\n                      ? 'bg-white text-slate-900 shadow-sm'\n                      : 'text-slate-600 hover:text-slate-900'\n                  }`}\n                  title=\"Vue grille\"\n                >\n                  <Grid className=\"h-4 w-4\" />\n                </button>\n                <button\n                  type=\"button\"\n                  onClick={() => setViewMode('list')}\n                  className={`p-2 rounded transition-colors ${\n                    viewMode === 'list'\n                      ? 'bg-white text-slate-900 shadow-sm'\n                      : 'text-slate-600 hover:text-slate-900'\n                  }`}\n                  title=\"Vue liste\"\n                >\n                  <List className=\"h-4 w-4\" />\n                </button>\n              </div>\n\n              {/* Plein écran */}\n              <button\n                type=\"button\"\n                onClick={toggleFullscreen}\n                className=\"p-2 text-slate-600 hover:text-slate-900 hover:bg-slate-100 rounded-lg transition-colors hidden lg:block\"\n                title={isFullscreen ? 'Quitter le plein écran' : 'Plein écran'}\n              >\n                {isFullscreen ? <Minimize2 className=\"h-4 w-4\" /> : <Maximize2 className=\"h-4 w-4\" />}\n              </button>\n\n              {/* Actions personnalisées */}\n              {actions && (\n                <div className=\"flex items-center space-x-2\">\n                  {actions}\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Contenu principal */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n        <div className=\"flex gap-6\">\n          {/* Sidebar */}\n          {sidebar && (\n            <motion.aside\n              initial={false}\n              animate={{\n                width: sidebarCollapsed ? 0 : screenSize === 'mobile' ? '100%' : '320px',\n                opacity: sidebarCollapsed ? 0 : 1\n              }}\n              transition={{ duration: 0.3 }}\n              className={`${\n                screenSize === 'mobile' \n                  ? 'fixed inset-0 z-50 bg-white' \n                  : 'relative'\n              } overflow-hidden`}\n            >\n              {!sidebarCollapsed && (\n                <div className=\"h-full\">\n                  {screenSize === 'mobile' && (\n                    <div className=\"flex items-center justify-between p-4 border-b border-slate-200\">\n                      <h2 className=\"text-lg font-semibold text-slate-900\">Menu</h2>\n                      <button\n                        type=\"button\"\n                        onClick={() => setSidebarCollapsed(true)}\n                        className=\"p-2 text-slate-600 hover:text-slate-900\"\n                        title=\"Fermer le menu\"\n                      >\n                        <ChevronLeft className=\"h-5 w-5\" />\n                      </button>\n                    </div>\n                  )}\n                  <div className={screenSize === 'mobile' ? 'p-4' : ''}>\n                    {sidebar}\n                  </div>\n                </div>\n              )}\n            </motion.aside>\n          )}\n\n          {/* Contenu principal */}\n          <main className=\"flex-1 min-w-0\">\n            <div className={`${viewMode === 'grid' ? 'space-y-6' : 'space-y-4'}`}>\n              {children}\n            </div>\n          </main>\n        </div>\n      </div>\n\n      {/* Overlay mobile pour sidebar */}\n      {screenSize === 'mobile' && !sidebarCollapsed && (\n        <div\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-40\"\n          onClick={() => setSidebarCollapsed(true)}\n        />\n      )}\n\n      {/* Indicateurs de responsive design */}\n      <div className=\"fixed bottom-4 right-4 z-50\">\n        <div className=\"bg-slate-900 text-white px-3 py-2 rounded-lg text-xs font-mono\">\n          <div className=\"flex items-center space-x-2\">\n            {screenSize === 'mobile' && <Smartphone className=\"h-3 w-3\" />}\n            {screenSize === 'tablet' && <Tablet className=\"h-3 w-3\" />}\n            {screenSize === 'desktop' && <Monitor className=\"h-3 w-3\" />}\n            <span>{typeof window !== 'undefined' ? window.innerWidth : 0}px</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Breakpoints indicator (dev only) */}\n      <div className=\"fixed bottom-4 left-4 z-50 bg-slate-900 text-white px-3 py-2 rounded-lg text-xs font-mono\">\n        <div className=\"sm:hidden\">XS (&lt;640px)</div>\n        <div className=\"hidden sm:block md:hidden\">SM (640px+)</div>\n        <div className=\"hidden md:block lg:hidden\">MD (768px+)</div>\n        <div className=\"hidden lg:block xl:hidden\">LG (1024px+)</div>\n        <div className=\"hidden xl:block 2xl:hidden\">XL (1280px+)</div>\n        <div className=\"hidden 2xl:block\">2XL (1536px+)</div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAyBe,SAAS,iBAAiB,EACvC,QAAQ,EACR,KAAK,EACL,QAAQ,EACR,OAAO,EACP,OAAO,EACP,YAAY,EAAE,EACQ;;IACtB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmC;IAE9E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;2DAAe;oBACnB,uCAAmC;;oBAAK;oBAExC,MAAM,QAAQ,OAAO,UAAU;oBAC/B,IAAI,QAAQ,KAAK;wBACf,cAAc;wBACd,oBAAoB;oBACtB,OAAO,IAAI,QAAQ,MAAM;wBACvB,cAAc;wBACd,oBAAoB;oBACtB,OAAO;wBACL,cAAc;wBACd,oBAAoB;oBACtB;gBACF;;YAEA;YACA,wCAAmC;gBACjC,OAAO,gBAAgB,CAAC,UAAU;gBAClC;kDAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;YACpD;QACF;qCAAG,EAAE;IAEL,MAAM,mBAAmB;QACvB,IAAI,OAAO,aAAa,aAAa;QAErC,IAAI,CAAC,SAAS,iBAAiB,EAAE;YAC/B,SAAS,eAAe,CAAC,iBAAiB;YAC1C,gBAAgB;QAClB,OAAO;YACL,SAAS,cAAc;YACvB,gBAAgB;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,0DAA0D,EAAE,WAAW;;0BAEtF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;oCACZ,yBACC,6LAAC;wCACC,MAAK;wCACL,SAAS,IAAM,oBAAoB,CAAC;wCACpC,WAAU;kDAET,iCAAmB,6LAAC,yNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;iEAAe,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;kDAItF,6LAAC;;4CACE,uBACC,6LAAC;gDAAG,WAAU;0DACX;;;;;;4CAGJ,0BACC,6LAAC;gDAAE,WAAU;0DACV;;;;;;;;;;;;;;;;;;0CAOT,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;4CACZ,eAAe,0BAAY,6LAAC,iNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CACjD,eAAe,0BAAY,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAC7C,eAAe,2BAAa,6LAAC,2MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DAChD,6LAAC;gDAAK,WAAU;0DAAqC;;;;;;;;;;;;kDAIvD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,SAAS,IAAM,YAAY;gDAC3B,WAAW,CAAC,8BAA8B,EACxC,aAAa,SACT,sCACA,uCACJ;gDACF,OAAM;0DAEN,cAAA,6LAAC,4MAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,6LAAC;gDACC,MAAK;gDACL,SAAS,IAAM,YAAY;gDAC3B,WAAW,CAAC,8BAA8B,EACxC,aAAa,SACT,sCACA,uCACJ;gDACF,OAAM;0DAEN,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAKpB,6LAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;wCACV,OAAO,eAAe,2BAA2B;kDAEhD,6BAAe,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;iEAAe,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;oCAI1E,yBACC,6LAAC;wCAAI,WAAU;kDACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;wBAEZ,yBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,KAAK;4BACX,SAAS;4BACT,SAAS;gCACP,OAAO,mBAAmB,IAAI,eAAe,WAAW,SAAS;gCACjE,SAAS,mBAAmB,IAAI;4BAClC;4BACA,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAW,GACT,eAAe,WACX,gCACA,WACL,gBAAgB,CAAC;sCAEjB,CAAC,kCACA,6LAAC;gCAAI,WAAU;;oCACZ,eAAe,0BACd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAuC;;;;;;0DACrD,6LAAC;gDACC,MAAK;gDACL,SAAS,IAAM,oBAAoB;gDACnC,WAAU;gDACV,OAAM;0DAEN,cAAA,6LAAC,uNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAI7B,6LAAC;wCAAI,WAAW,eAAe,WAAW,QAAQ;kDAC/C;;;;;;;;;;;;;;;;;sCAQX,6LAAC;4BAAK,WAAU;sCACd,cAAA,6LAAC;gCAAI,WAAW,GAAG,aAAa,SAAS,cAAc,aAAa;0CACjE;;;;;;;;;;;;;;;;;;;;;;YAOR,eAAe,YAAY,CAAC,kCAC3B,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,oBAAoB;;;;;;0BAKvC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,eAAe,0BAAY,6LAAC,iNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;4BACjD,eAAe,0BAAY,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAC7C,eAAe,2BAAa,6LAAC,2MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CAChD,6LAAC;;oCAAM,uCAAgC,OAAO,UAAU;oCAAK;;;;;;;;;;;;;;;;;;;;;;;0BAMnE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAAY;;;;;;kCAC3B,6LAAC;wBAAI,WAAU;kCAA4B;;;;;;kCAC3C,6LAAC;wBAAI,WAAU;kCAA4B;;;;;;kCAC3C,6LAAC;wBAAI,WAAU;kCAA4B;;;;;;kCAC3C,6LAAC;wBAAI,WAAU;kCAA6B;;;;;;kCAC5C,6LAAC;wBAAI,WAAU;kCAAmB;;;;;;;;;;;;;;;;;;AAI1C;GA7NwB;KAAA", "debugId": null}}, {"offset": {"line": 1255, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/components/integration/EcosystemHub.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { \n  ArrowRight,\n  Database,\n  Users,\n  FileText,\n  Crown,\n  Zap,\n  TrendingUp,\n  AlertTriangle,\n  CheckCircle,\n  Clock,\n  Star,\n  Link as LinkIcon,\n  ExternalLink,\n  RefreshCw\n} from 'lucide-react'\nimport Link from 'next/link'\n\ninterface EcosystemHubProps {\n  currentModule: 'hub' | 'expert' | 'prescriptor' | 'club'\n  className?: string\n}\n\nexport default function EcosystemHub({ currentModule, className = '' }: EcosystemHubProps) {\n  const [crossModuleData, setCrossModuleData] = useState<any>(null)\n  const [isLoading, setIsLoading] = useState(true)\n\n  const modules = {\n    hub: {\n      name: 'Hub Information',\n      icon: <Database className=\"h-6 w-6\" />,\n      color: 'from-blue-500 to-blue-600',\n      description: 'Centre d\\'information temps réel',\n      status: 'active',\n      lastUpdate: '2 min',\n      metrics: { alerts: 12, updates: 47, trends: 8 }\n    },\n    expert: {\n      name: 'Expert Conseil',\n      icon: <Users className=\"h-6 w-6\" />,\n      color: 'from-green-500 to-green-600',\n      description: 'Consultation et validation technique',\n      status: 'active',\n      lastUpdate: '5 min',\n      metrics: { consultations: 23, validations: 15, experts: 8 }\n    },\n    prescriptor: {\n      name: 'Module Prescripteur',\n      icon: <FileText className=\"h-6 w-6\" />,\n      color: 'from-purple-500 to-purple-600',\n      description: 'Templates et certification',\n      status: 'active',\n      lastUpdate: '1 min',\n      metrics: { templates: 47, projects: 23, certificates: 89 }\n    },\n    club: {\n      name: 'Club Membre',\n      icon: <Crown className=\"h-6 w-6\" />,\n      color: 'from-amber-500 to-amber-600',\n      description: 'Réseau exclusif professionnel',\n      status: 'premium',\n      lastUpdate: '3 min',\n      metrics: { members: 1234, events: 12, benefits: 25 }\n    }\n  }\n\n  const integrationFlows = [\n    {\n      from: 'hub',\n      to: 'expert',\n      title: 'Alerte → Consultation',\n      description: 'Alertes techniques redirigées vers experts',\n      count: 15,\n      trend: '+23%',\n      examples: [\n        'Panne transformateur → Expert électrique',\n        'Nouveau produit → Validation technique',\n        'Norme mise à jour → Conseil réglementaire'\n      ]\n    },\n    {\n      from: 'expert',\n      to: 'prescriptor',\n      title: 'Validation → Template',\n      description: 'Conseils d\\'experts intégrés aux templates',\n      count: 28,\n      trend: '+45%',\n      examples: [\n        'Conseil technique → Template personnalisé',\n        'Validation produit → Calcul automatisé',\n        'Recommandation → Standard imposé'\n      ]\n    },\n    {\n      from: 'prescriptor',\n      to: 'club',\n      title: 'Certification → Réseau',\n      description: 'Projets certifiés partagés au club',\n      count: 34,\n      trend: '+67%',\n      examples: [\n        'Projet certifié → Showcase membre',\n        'Template validé → Formation exclusive',\n        'Innovation → Partage réseau'\n      ]\n    },\n    {\n      from: 'club',\n      to: 'hub',\n      title: 'Réseau → Information',\n      description: 'Insights membres alimentent le hub',\n      count: 42,\n      trend: '+89%',\n      examples: [\n        'Retour terrain → Alerte précoce',\n        'Tendance marché → Information exclusive',\n        'Innovation membre → Veille technologique'\n      ]\n    }\n  ]\n\n  const crossModuleActions = {\n    hub: [\n      {\n        title: 'Consulter un Expert',\n        description: 'Obtenir une validation technique sur cette alerte',\n        action: '/expert?source=hub&alert=',\n        icon: <Users className=\"h-4 w-4\" />,\n        color: 'bg-green-100 text-green-700'\n      },\n      {\n        title: 'Créer Template',\n        description: 'Transformer cette info en template de prescription',\n        action: '/prescriptor?action=create&source=hub',\n        icon: <FileText className=\"h-4 w-4\" />,\n        color: 'bg-purple-100 text-purple-700'\n      }\n    ],\n    expert: [\n      {\n        title: 'Voir Alertes Hub',\n        description: 'Consulter les dernières alertes techniques',\n        action: '/hub?filter=technical',\n        icon: <Database className=\"h-4 w-4\" />,\n        color: 'bg-blue-100 text-blue-700'\n      },\n      {\n        title: 'Générer Template',\n        description: 'Créer un template basé sur cette consultation',\n        action: '/prescriptor?action=generate&consultation=',\n        icon: <FileText className=\"h-4 w-4\" />,\n        color: 'bg-purple-100 text-purple-700'\n      }\n    ],\n    prescriptor: [\n      {\n        title: 'Consulter Expert',\n        description: 'Valider ce template avec un expert',\n        action: '/expert?mode=validation&template=',\n        icon: <Users className=\"h-4 w-4\" />,\n        color: 'bg-green-100 text-green-700'\n      },\n      {\n        title: 'Partager au Club',\n        description: 'Présenter ce projet au réseau membre',\n        action: '/club?action=share&project=',\n        icon: <Crown className=\"h-4 w-4\" />,\n        color: 'bg-amber-100 text-amber-700'\n      }\n    ],\n    club: [\n      {\n        title: 'Alertes Exclusives',\n        description: 'Accéder aux informations réservées aux membres',\n        action: '/hub?level=premium',\n        icon: <Database className=\"h-4 w-4\" />,\n        color: 'bg-blue-100 text-blue-700'\n      },\n      {\n        title: 'Expert Dédié',\n        description: 'Consultation prioritaire avec expert membre',\n        action: '/expert?priority=member',\n        icon: <Users className=\"h-4 w-4\" />,\n        color: 'bg-green-100 text-green-700'\n      }\n    ]\n  }\n\n  const recentIntegrations = [\n    {\n      type: 'hub_to_expert',\n      title: 'Alerte transformateur → Consultation urgente',\n      time: '5 min',\n      status: 'completed',\n      impact: 'Panne évitée chez 3 clients'\n    },\n    {\n      type: 'expert_to_prescriptor',\n      title: 'Validation LED → Template automatisé',\n      time: '12 min',\n      status: 'in_progress',\n      impact: 'Nouveau standard créé'\n    },\n    {\n      type: 'prescriptor_to_club',\n      title: 'Projet solaire → Showcase membre',\n      time: '1h',\n      status: 'completed',\n      impact: '15 nouveaux projets générés'\n    },\n    {\n      type: 'club_to_hub',\n      title: 'Retour terrain → Alerte préventive',\n      time: '2h',\n      status: 'completed',\n      impact: 'Tendance détectée en avance'\n    }\n  ]\n\n  useEffect(() => {\n    // Simulation de chargement des données cross-module\n    setIsLoading(true)\n    setTimeout(() => {\n      setCrossModuleData({\n        totalIntegrations: 147,\n        activeFlows: 23,\n        efficiency: 94,\n        userSatisfaction: 4.8\n      })\n      setIsLoading(false)\n    }, 1500)\n  }, [])\n\n  const getCurrentModuleActions = () => {\n    return crossModuleActions[currentModule] || []\n  }\n\n  const getIntegrationStatus = (from: string, to: string) => {\n    if (from === currentModule || to === currentModule) {\n      return 'active'\n    }\n    return 'available'\n  }\n\n  return (\n    <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>\n      {/* Header */}\n      <div className=\"flex items-center justify-between mb-6\">\n        <div className=\"flex items-center space-x-4\">\n          <div className=\"w-12 h-12 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center text-white\">\n            <LinkIcon className=\"h-6 w-6\" />\n          </div>\n          <div>\n            <h2 className=\"text-2xl font-bold text-gray-900\">Intégration Écosystème</h2>\n            <p className=\"text-gray-600\">Flux de données et actions cross-module</p>\n          </div>\n        </div>\n        \n        <div className=\"flex items-center space-x-4\">\n          {isLoading ? (\n            <div className=\"flex items-center space-x-2 text-gray-500\">\n              <RefreshCw className=\"h-4 w-4 animate-spin\" />\n              <span className=\"text-sm\">Synchronisation...</span>\n            </div>\n          ) : (\n            <div className=\"text-right\">\n              <div className=\"text-lg font-bold text-indigo-600\">{crossModuleData?.efficiency}%</div>\n              <div className=\"text-sm text-gray-600\">Efficacité</div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Vue d'ensemble des modules */}\n      <div className=\"grid md:grid-cols-4 gap-4 mb-8\">\n        {Object.entries(modules).map(([key, module]) => (\n          <motion.div\n            key={key}\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            className={`p-4 rounded-lg border-2 transition-all ${\n              currentModule === key\n                ? 'border-indigo-400 bg-indigo-50'\n                : 'border-gray-200 hover:border-gray-300'\n            }`}\n          >\n            <div className=\"flex items-center space-x-3 mb-3\">\n              <div className={`w-10 h-10 bg-gradient-to-r ${module.color} rounded-lg flex items-center justify-center text-white`}>\n                {module.icon}\n              </div>\n              <div>\n                <h3 className=\"font-semibold text-gray-900\">{module.name}</h3>\n                <p className=\"text-xs text-gray-600\">{module.description}</p>\n              </div>\n            </div>\n            \n            <div className=\"flex items-center justify-between text-sm\">\n              <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                module.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-amber-100 text-amber-800'\n              }`}>\n                {module.status}\n              </span>\n              <span className=\"text-gray-500\">Màj: {module.lastUpdate}</span>\n            </div>\n          </motion.div>\n        ))}\n      </div>\n\n      {/* Flux d'intégration */}\n      <div className=\"mb-8\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Flux d'Intégration Actifs</h3>\n        <div className=\"grid md:grid-cols-2 gap-6\">\n          {integrationFlows.map((flow, index) => (\n            <motion.div\n              key={`${flow.from}-${flow.to}`}\n              initial={{ opacity: 0, x: -20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ delay: index * 0.1 }}\n              className={`p-4 rounded-lg border ${\n                getIntegrationStatus(flow.from, flow.to) === 'active'\n                  ? 'border-indigo-200 bg-indigo-50'\n                  : 'border-gray-200 bg-gray-50'\n              }`}\n            >\n              <div className=\"flex items-center justify-between mb-3\">\n                <div className=\"flex items-center space-x-2\">\n                  <div className={`w-6 h-6 bg-gradient-to-r ${modules[flow.from as keyof typeof modules].color} rounded flex items-center justify-center`}>\n                    <div className=\"w-2 h-2 bg-white rounded-full\"></div>\n                  </div>\n                  <ArrowRight className=\"h-4 w-4 text-gray-400\" />\n                  <div className={`w-6 h-6 bg-gradient-to-r ${modules[flow.to as keyof typeof modules].color} rounded flex items-center justify-center`}>\n                    <div className=\"w-2 h-2 bg-white rounded-full\"></div>\n                  </div>\n                </div>\n                <div className=\"text-right\">\n                  <div className=\"text-lg font-bold text-gray-900\">{flow.count}</div>\n                  <div className=\"text-xs text-green-600 font-medium\">{flow.trend}</div>\n                </div>\n              </div>\n              \n              <h4 className=\"font-semibold text-gray-900 mb-1\">{flow.title}</h4>\n              <p className=\"text-sm text-gray-600 mb-3\">{flow.description}</p>\n              \n              <div className=\"space-y-1\">\n                {flow.examples.slice(0, 2).map((example, i) => (\n                  <div key={i} className=\"text-xs text-gray-500 flex items-center space-x-1\">\n                    <div className=\"w-1 h-1 bg-gray-400 rounded-full\"></div>\n                    <span>{example}</span>\n                  </div>\n                ))}\n              </div>\n            </motion.div>\n          ))}\n        </div>\n      </div>\n\n      {/* Actions cross-module pour le module actuel */}\n      <div className=\"mb-8\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n          Actions Disponibles depuis {modules[currentModule].name}\n        </h3>\n        <div className=\"grid md:grid-cols-2 gap-4\">\n          {getCurrentModuleActions().map((action, index) => (\n            <Link\n              key={index}\n              href={action.action}\n              className=\"p-4 rounded-lg border border-gray-200 hover:border-gray-300 transition-all group\"\n            >\n              <div className=\"flex items-start space-x-3\">\n                <div className={`p-2 rounded-lg ${action.color}`}>\n                  {action.icon}\n                </div>\n                <div className=\"flex-1\">\n                  <h4 className=\"font-semibold text-gray-900 group-hover:text-indigo-600 transition-colors\">\n                    {action.title}\n                  </h4>\n                  <p className=\"text-sm text-gray-600\">{action.description}</p>\n                </div>\n                <ExternalLink className=\"h-4 w-4 text-gray-400 group-hover:text-indigo-600 transition-colors\" />\n              </div>\n            </Link>\n          ))}\n        </div>\n      </div>\n\n      {/* Intégrations récentes */}\n      <div>\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Intégrations Récentes</h3>\n        <div className=\"space-y-3\">\n          {recentIntegrations.map((integration, index) => (\n            <motion.div\n              key={index}\n              initial={{ opacity: 0, y: 10 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: index * 0.05 }}\n              className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\"\n            >\n              <div className=\"flex items-center space-x-3\">\n                <div className={`w-2 h-2 rounded-full ${\n                  integration.status === 'completed' ? 'bg-green-500' :\n                  integration.status === 'in_progress' ? 'bg-blue-500' :\n                  'bg-gray-400'\n                }`}></div>\n                <div>\n                  <h4 className=\"font-medium text-gray-900\">{integration.title}</h4>\n                  <p className=\"text-sm text-gray-600\">{integration.impact}</p>\n                </div>\n              </div>\n              <div className=\"text-right\">\n                <div className=\"text-sm text-gray-500\">{integration.time}</div>\n                <div className={`text-xs font-medium ${\n                  integration.status === 'completed' ? 'text-green-600' :\n                  integration.status === 'in_progress' ? 'text-blue-600' :\n                  'text-gray-600'\n                }`}>\n                  {integration.status}\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;;;AApBA;;;;;AA2Be,SAAS,aAAa,EAAE,aAAa,EAAE,YAAY,EAAE,EAAqB;;IACvF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAC5D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,UAAU;QACd,KAAK;YACH,MAAM;YACN,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,OAAO;YACP,aAAa;YACb,QAAQ;YACR,YAAY;YACZ,SAAS;gBAAE,QAAQ;gBAAI,SAAS;gBAAI,QAAQ;YAAE;QAChD;QACA,QAAQ;YACN,MAAM;YACN,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,aAAa;YACb,QAAQ;YACR,YAAY;YACZ,SAAS;gBAAE,eAAe;gBAAI,aAAa;gBAAI,SAAS;YAAE;QAC5D;QACA,aAAa;YACX,MAAM;YACN,oBAAM,6LAAC,iNAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,OAAO;YACP,aAAa;YACb,QAAQ;YACR,YAAY;YACZ,SAAS;gBAAE,WAAW;gBAAI,UAAU;gBAAI,cAAc;YAAG;QAC3D;QACA,MAAM;YACJ,MAAM;YACN,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,aAAa;YACb,QAAQ;YACR,YAAY;YACZ,SAAS;gBAAE,SAAS;gBAAM,QAAQ;gBAAI,UAAU;YAAG;QACrD;IACF;IAEA,MAAM,mBAAmB;QACvB;YACE,MAAM;YACN,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;aACD;QACH;QACA;YACE,MAAM;YACN,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;aACD;QACH;QACA;YACE,MAAM;YACN,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;aACD;QACH;QACA;YACE,MAAM;YACN,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;aACD;QACH;KACD;IAED,MAAM,qBAAqB;QACzB,KAAK;YACH;gBACE,OAAO;gBACP,aAAa;gBACb,QAAQ;gBACR,oBAAM,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;gBACvB,OAAO;YACT;YACA;gBACE,OAAO;gBACP,aAAa;gBACb,QAAQ;gBACR,oBAAM,6LAAC,iNAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;gBAC1B,OAAO;YACT;SACD;QACD,QAAQ;YACN;gBACE,OAAO;gBACP,aAAa;gBACb,QAAQ;gBACR,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;gBAC1B,OAAO;YACT;YACA;gBACE,OAAO;gBACP,aAAa;gBACb,QAAQ;gBACR,oBAAM,6LAAC,iNAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;gBAC1B,OAAO;YACT;SACD;QACD,aAAa;YACX;gBACE,OAAO;gBACP,aAAa;gBACb,QAAQ;gBACR,oBAAM,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;gBACvB,OAAO;YACT;YACA;gBACE,OAAO;gBACP,aAAa;gBACb,QAAQ;gBACR,oBAAM,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;gBACvB,OAAO;YACT;SACD;QACD,MAAM;YACJ;gBACE,OAAO;gBACP,aAAa;gBACb,QAAQ;gBACR,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;gBAC1B,OAAO;YACT;YACA;gBACE,OAAO;gBACP,aAAa;gBACb,QAAQ;gBACR,oBAAM,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;gBACvB,OAAO;YACT;SACD;IACH;IAEA,MAAM,qBAAqB;QACzB;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;KACD;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,oDAAoD;YACpD,aAAa;YACb;0CAAW;oBACT,mBAAmB;wBACjB,mBAAmB;wBACnB,aAAa;wBACb,YAAY;wBACZ,kBAAkB;oBACpB;oBACA,aAAa;gBACf;yCAAG;QACL;iCAAG,EAAE;IAEL,MAAM,0BAA0B;QAC9B,OAAO,kBAAkB,CAAC,cAAc,IAAI,EAAE;IAChD;IAEA,MAAM,uBAAuB,CAAC,MAAc;QAC1C,IAAI,SAAS,iBAAiB,OAAO,eAAe;YAClD,OAAO;QACT;QACA,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,kCAAkC,EAAE,WAAW;;0BAE9D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qMAAA,CAAA,OAAQ;oCAAC,WAAU;;;;;;;;;;;0CAEtB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;kCAIjC,6LAAC;wBAAI,WAAU;kCACZ,0BACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,6LAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;iDAG5B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;wCAAqC,iBAAiB;wCAAW;;;;;;;8CAChF,6LAAC;oCAAI,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;0BAO/C,6LAAC;gBAAI,WAAU;0BACZ,OAAO,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,KAAK,OAAO,iBACzC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,WAAW,CAAC,uCAAuC,EACjD,kBAAkB,MACd,mCACA,yCACJ;;0CAEF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAW,CAAC,2BAA2B,EAAE,OAAO,KAAK,CAAC,uDAAuD,CAAC;kDAChH,OAAO,IAAI;;;;;;kDAEd,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA+B,OAAO,IAAI;;;;;;0DACxD,6LAAC;gDAAE,WAAU;0DAAyB,OAAO,WAAW;;;;;;;;;;;;;;;;;;0CAI5D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAW,CAAC,2CAA2C,EAC3D,OAAO,MAAM,KAAK,WAAW,gCAAgC,+BAC7D;kDACC,OAAO,MAAM;;;;;;kDAEhB,6LAAC;wCAAK,WAAU;;4CAAgB;4CAAM,OAAO,UAAU;;;;;;;;;;;;;;uBAzBpD;;;;;;;;;;0BAgCX,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAI,WAAU;kCACZ,iBAAiB,GAAG,CAAC,CAAC,MAAM,sBAC3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO,QAAQ;gCAAI;gCACjC,WAAW,CAAC,sBAAsB,EAChC,qBAAqB,KAAK,IAAI,EAAE,KAAK,EAAE,MAAM,WACzC,mCACA,8BACJ;;kDAEF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,CAAC,yBAAyB,EAAE,OAAO,CAAC,KAAK,IAAI,CAAyB,CAAC,KAAK,CAAC,yCAAyC,CAAC;kEACrI,cAAA,6LAAC;4DAAI,WAAU;;;;;;;;;;;kEAEjB,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;kEACtB,6LAAC;wDAAI,WAAW,CAAC,yBAAyB,EAAE,OAAO,CAAC,KAAK,EAAE,CAAyB,CAAC,KAAK,CAAC,yCAAyC,CAAC;kEACnI,cAAA,6LAAC;4DAAI,WAAU;;;;;;;;;;;;;;;;;0DAGnB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAmC,KAAK,KAAK;;;;;;kEAC5D,6LAAC;wDAAI,WAAU;kEAAsC,KAAK,KAAK;;;;;;;;;;;;;;;;;;kDAInE,6LAAC;wCAAG,WAAU;kDAAoC,KAAK,KAAK;;;;;;kDAC5D,6LAAC;wCAAE,WAAU;kDAA8B,KAAK,WAAW;;;;;;kDAE3D,6LAAC;wCAAI,WAAU;kDACZ,KAAK,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,kBACvC,6LAAC;gDAAY,WAAU;;kEACrB,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;kEAAM;;;;;;;+CAFC;;;;;;;;;;;+BA/BT,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;;;;;;;;;;;;;;;;0BA2CtC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;4BAA2C;4BAC3B,OAAO,CAAC,cAAc,CAAC,IAAI;;;;;;;kCAEzD,6LAAC;wBAAI,WAAU;kCACZ,0BAA0B,GAAG,CAAC,CAAC,QAAQ,sBACtC,6LAAC,+JAAA,CAAA,UAAI;gCAEH,MAAM,OAAO,MAAM;gCACnB,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAW,CAAC,eAAe,EAAE,OAAO,KAAK,EAAE;sDAC7C,OAAO,IAAI;;;;;;sDAEd,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DACX,OAAO,KAAK;;;;;;8DAEf,6LAAC;oDAAE,WAAU;8DAAyB,OAAO,WAAW;;;;;;;;;;;;sDAE1D,6LAAC,yNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;+BAdrB;;;;;;;;;;;;;;;;0BAsBb,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAI,WAAU;kCACZ,mBAAmB,GAAG,CAAC,CAAC,aAAa,sBACpC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO,QAAQ;gCAAK;gCAClC,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAW,CAAC,qBAAqB,EACpC,YAAY,MAAM,KAAK,cAAc,iBACrC,YAAY,MAAM,KAAK,gBAAgB,gBACvC,eACA;;;;;;0DACF,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6B,YAAY,KAAK;;;;;;kEAC5D,6LAAC;wDAAE,WAAU;kEAAyB,YAAY,MAAM;;;;;;;;;;;;;;;;;;kDAG5D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAyB,YAAY,IAAI;;;;;;0DACxD,6LAAC;gDAAI,WAAW,CAAC,oBAAoB,EACnC,YAAY,MAAM,KAAK,cAAc,mBACrC,YAAY,MAAM,KAAK,gBAAgB,kBACvC,iBACA;0DACC,YAAY,MAAM;;;;;;;;;;;;;+BAxBlB;;;;;;;;;;;;;;;;;;;;;;AAiCnB;GAjZwB;KAAA", "debugId": null}}, {"offset": {"line": 2173, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/components/integration/SmartLinks.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { motion } from 'framer-motion'\nimport { \n  ArrowRight,\n  ExternalLink,\n  Zap,\n  Database,\n  Users,\n  FileText,\n  Crown,\n  TrendingUp,\n  AlertTriangle,\n  CheckCircle,\n  Clock,\n  Star\n} from 'lucide-react'\nimport Link from 'next/link'\n\ninterface SmartLinksProps {\n  context: {\n    module: 'hub' | 'expert' | 'prescriptor' | 'club'\n    data?: any\n    action?: string\n  }\n  className?: string\n}\n\nexport default function SmartLinks({ context, className = '' }: SmartLinksProps) {\n  const [hoveredLink, setHoveredLink] = useState<string | null>(null)\n\n  const generateSmartLinks = () => {\n    const { module, data, action } = context\n\n    switch (module) {\n      case 'hub':\n        return [\n          {\n            id: 'hub-to-expert',\n            title: 'Consulter un Expert',\n            description: 'Obtenir une validation technique sur cette alerte',\n            href: `/expert?source=hub&alert=${data?.alertId || 'current'}`,\n            icon: <Users className=\"h-5 w-5\" />,\n            color: 'from-green-500 to-green-600',\n            priority: 'high',\n            estimatedTime: '15 min',\n            cost: '25,000 FCFA'\n          },\n          {\n            id: 'hub-to-prescriptor',\n            title: 'Créer Template',\n            description: 'Transformer cette information en template de prescription',\n            href: `/prescriptor?action=create&source=hub&data=${data?.type || 'alert'}`,\n            icon: <FileText className=\"h-5 w-5\" />,\n            color: 'from-purple-500 to-purple-600',\n            priority: 'medium',\n            estimatedTime: '30 min',\n            cost: 'Inclus'\n          },\n          {\n            id: 'hub-to-club',\n            title: 'Partager au Réseau',\n            description: 'Diffuser cette information aux membres du club',\n            href: `/club?action=share&content=${data?.id || 'current'}`,\n            icon: <Crown className=\"h-5 w-5\" />,\n            color: 'from-amber-500 to-amber-600',\n            priority: 'low',\n            estimatedTime: '5 min',\n            cost: 'Membre requis'\n          }\n        ]\n\n      case 'expert':\n        return [\n          {\n            id: 'expert-to-hub',\n            title: 'Voir Alertes Techniques',\n            description: 'Consulter les dernières alertes nécessitant expertise',\n            href: `/hub?filter=technical&priority=expert`,\n            icon: <Database className=\"h-5 w-5\" />,\n            color: 'from-blue-500 to-blue-600',\n            priority: 'high',\n            estimatedTime: '10 min',\n            cost: 'Gratuit'\n          },\n          {\n            id: 'expert-to-prescriptor',\n            title: 'Générer Template',\n            description: 'Créer un template basé sur cette consultation',\n            href: `/prescriptor?action=generate&consultation=${data?.consultationId || 'current'}`,\n            icon: <FileText className=\"h-5 w-5\" />,\n            color: 'from-purple-500 to-purple-600',\n            priority: 'high',\n            estimatedTime: '20 min',\n            cost: 'Inclus'\n          },\n          {\n            id: 'expert-to-club',\n            title: 'Présenter au Club',\n            description: 'Partager cette expertise avec le réseau professionnel',\n            href: `/club?action=present&expertise=${data?.expertiseId || 'current'}`,\n            icon: <Crown className=\"h-5 w-5\" />,\n            color: 'from-amber-500 to-amber-600',\n            priority: 'medium',\n            estimatedTime: '15 min',\n            cost: 'Membre requis'\n          }\n        ]\n\n      case 'prescriptor':\n        return [\n          {\n            id: 'prescriptor-to-expert',\n            title: 'Valider avec Expert',\n            description: 'Faire valider ce template par un expert certifié',\n            href: `/expert?mode=validation&template=${data?.templateId || 'current'}`,\n            icon: <Users className=\"h-5 w-5\" />,\n            color: 'from-green-500 to-green-600',\n            priority: 'high',\n            estimatedTime: '30 min',\n            cost: '35,000 FCFA'\n          },\n          {\n            id: 'prescriptor-to-hub',\n            title: 'Surveiller Tendances',\n            description: 'Suivre les tendances liées à ce type de projet',\n            href: `/hub?track=${data?.category || 'electrical'}&project=${data?.projectId || 'current'}`,\n            icon: <Database className=\"h-5 w-5\" />,\n            color: 'from-blue-500 to-blue-600',\n            priority: 'medium',\n            estimatedTime: '5 min',\n            cost: 'Gratuit'\n          },\n          {\n            id: 'prescriptor-to-club',\n            title: 'Showcase Projet',\n            description: 'Présenter ce projet certifié au réseau membre',\n            href: `/club?action=showcase&project=${data?.projectId || 'current'}`,\n            icon: <Crown className=\"h-5 w-5\" />,\n            color: 'from-amber-500 to-amber-600',\n            priority: 'high',\n            estimatedTime: '10 min',\n            cost: 'Membre requis'\n          }\n        ]\n\n      case 'club':\n        return [\n          {\n            id: 'club-to-hub',\n            title: 'Alertes Exclusives',\n            description: 'Accéder aux informations réservées aux membres',\n            href: `/hub?level=premium&member=true`,\n            icon: <Database className=\"h-5 w-5\" />,\n            color: 'from-blue-500 to-blue-600',\n            priority: 'high',\n            estimatedTime: '5 min',\n            cost: 'Inclus membre'\n          },\n          {\n            id: 'club-to-expert',\n            title: 'Expert Dédié',\n            description: 'Consultation prioritaire avec expert membre',\n            href: `/expert?priority=member&dedicated=true`,\n            icon: <Users className=\"h-5 w-5\" />,\n            color: 'from-green-500 to-green-600',\n            priority: 'high',\n            estimatedTime: '15 min',\n            cost: 'Tarif préférentiel'\n          },\n          {\n            id: 'club-to-prescriptor',\n            title: 'Templates VIP',\n            description: 'Accéder aux templates exclusifs membres',\n            href: `/prescriptor?level=vip&member=true`,\n            icon: <FileText className=\"h-5 w-5\" />,\n            color: 'from-purple-500 to-purple-600',\n            priority: 'medium',\n            estimatedTime: '10 min',\n            cost: 'Inclus membre'\n          }\n        ]\n\n      default:\n        return []\n    }\n  }\n\n  const smartLinks = generateSmartLinks()\n\n  const getPriorityColor = (priority: string) => {\n    switch (priority) {\n      case 'high':\n        return 'border-red-200 bg-red-50'\n      case 'medium':\n        return 'border-yellow-200 bg-yellow-50'\n      case 'low':\n        return 'border-green-200 bg-green-50'\n      default:\n        return 'border-gray-200 bg-gray-50'\n    }\n  }\n\n  const getPriorityIcon = (priority: string) => {\n    switch (priority) {\n      case 'high':\n        return <AlertTriangle className=\"h-4 w-4 text-red-600\" />\n      case 'medium':\n        return <Clock className=\"h-4 w-4 text-yellow-600\" />\n      case 'low':\n        return <CheckCircle className=\"h-4 w-4 text-green-600\" />\n      default:\n        return <Star className=\"h-4 w-4 text-gray-600\" />\n    }\n  }\n\n  if (smartLinks.length === 0) return null\n\n  return (\n    <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>\n      <div className=\"flex items-center space-x-3 mb-6\">\n        <div className=\"w-10 h-10 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center text-white\">\n          <Zap className=\"h-5 w-5\" />\n        </div>\n        <div>\n          <h3 className=\"text-lg font-semibold text-gray-900\">Actions Intelligentes</h3>\n          <p className=\"text-sm text-gray-600\">Suggestions basées sur votre contexte actuel</p>\n        </div>\n      </div>\n\n      <div className=\"space-y-4\">\n        {smartLinks.map((link, index) => (\n          <motion.div\n            key={link.id}\n            initial={{ opacity: 0, x: -20 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ delay: index * 0.1 }}\n            onMouseEnter={() => setHoveredLink(link.id)}\n            onMouseLeave={() => setHoveredLink(null)}\n          >\n            <Link\n              href={link.href}\n              className={`block p-4 rounded-lg border-2 transition-all duration-300 hover:shadow-md ${\n                hoveredLink === link.id \n                  ? 'border-indigo-400 bg-indigo-50 transform scale-[1.02]' \n                  : getPriorityColor(link.priority)\n              }`}\n            >\n              <div className=\"flex items-start justify-between\">\n                <div className=\"flex items-start space-x-4 flex-1\">\n                  <div className={`w-12 h-12 bg-gradient-to-r ${link.color} rounded-lg flex items-center justify-center text-white flex-shrink-0`}>\n                    {link.icon}\n                  </div>\n                  \n                  <div className=\"flex-1 min-w-0\">\n                    <div className=\"flex items-center space-x-2 mb-1\">\n                      <h4 className=\"font-semibold text-gray-900 truncate\">{link.title}</h4>\n                      {getPriorityIcon(link.priority)}\n                    </div>\n                    <p className=\"text-sm text-gray-600 mb-3 line-clamp-2\">{link.description}</p>\n                    \n                    <div className=\"flex items-center space-x-4 text-xs text-gray-500\">\n                      <div className=\"flex items-center space-x-1\">\n                        <Clock className=\"h-3 w-3\" />\n                        <span>{link.estimatedTime}</span>\n                      </div>\n                      <div className=\"flex items-center space-x-1\">\n                        <span className=\"font-medium\">Coût:</span>\n                        <span>{link.cost}</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                \n                <div className=\"flex items-center space-x-2 ml-4\">\n                  <motion.div\n                    animate={{\n                      x: hoveredLink === link.id ? 5 : 0\n                    }}\n                    transition={{ duration: 0.2 }}\n                  >\n                    <ArrowRight className=\"h-5 w-5 text-gray-400\" />\n                  </motion.div>\n                  <ExternalLink className=\"h-4 w-4 text-gray-400\" />\n                </div>\n              </div>\n            </Link>\n          </motion.div>\n        ))}\n      </div>\n\n      {/* Statistiques d'utilisation */}\n      <div className=\"mt-6 pt-6 border-t border-gray-200\">\n        <div className=\"flex items-center justify-between text-sm text-gray-500\">\n          <span>Actions suggérées aujourd'hui</span>\n          <div className=\"flex items-center space-x-4\">\n            <span className=\"flex items-center space-x-1\">\n              <TrendingUp className=\"h-4 w-4 text-green-500\" />\n              <span className=\"text-green-600 font-medium\">+23%</span>\n            </span>\n            <span>47 utilisées</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;;;AAlBA;;;;;AA6Be,SAAS,WAAW,EAAE,OAAO,EAAE,YAAY,EAAE,EAAmB;;IAC7E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE9D,MAAM,qBAAqB;QACzB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG;QAEjC,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL;wBACE,IAAI;wBACJ,OAAO;wBACP,aAAa;wBACb,MAAM,CAAC,yBAAyB,EAAE,MAAM,WAAW,WAAW;wBAC9D,oBAAM,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBACvB,OAAO;wBACP,UAAU;wBACV,eAAe;wBACf,MAAM;oBACR;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,aAAa;wBACb,MAAM,CAAC,2CAA2C,EAAE,MAAM,QAAQ,SAAS;wBAC3E,oBAAM,6LAAC,iNAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAC1B,OAAO;wBACP,UAAU;wBACV,eAAe;wBACf,MAAM;oBACR;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,aAAa;wBACb,MAAM,CAAC,2BAA2B,EAAE,MAAM,MAAM,WAAW;wBAC3D,oBAAM,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBACvB,OAAO;wBACP,UAAU;wBACV,eAAe;wBACf,MAAM;oBACR;iBACD;YAEH,KAAK;gBACH,OAAO;oBACL;wBACE,IAAI;wBACJ,OAAO;wBACP,aAAa;wBACb,MAAM,CAAC,qCAAqC,CAAC;wBAC7C,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAC1B,OAAO;wBACP,UAAU;wBACV,eAAe;wBACf,MAAM;oBACR;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,aAAa;wBACb,MAAM,CAAC,0CAA0C,EAAE,MAAM,kBAAkB,WAAW;wBACtF,oBAAM,6LAAC,iNAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAC1B,OAAO;wBACP,UAAU;wBACV,eAAe;wBACf,MAAM;oBACR;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,aAAa;wBACb,MAAM,CAAC,+BAA+B,EAAE,MAAM,eAAe,WAAW;wBACxE,oBAAM,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBACvB,OAAO;wBACP,UAAU;wBACV,eAAe;wBACf,MAAM;oBACR;iBACD;YAEH,KAAK;gBACH,OAAO;oBACL;wBACE,IAAI;wBACJ,OAAO;wBACP,aAAa;wBACb,MAAM,CAAC,iCAAiC,EAAE,MAAM,cAAc,WAAW;wBACzE,oBAAM,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBACvB,OAAO;wBACP,UAAU;wBACV,eAAe;wBACf,MAAM;oBACR;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,aAAa;wBACb,MAAM,CAAC,WAAW,EAAE,MAAM,YAAY,aAAa,SAAS,EAAE,MAAM,aAAa,WAAW;wBAC5F,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAC1B,OAAO;wBACP,UAAU;wBACV,eAAe;wBACf,MAAM;oBACR;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,aAAa;wBACb,MAAM,CAAC,8BAA8B,EAAE,MAAM,aAAa,WAAW;wBACrE,oBAAM,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBACvB,OAAO;wBACP,UAAU;wBACV,eAAe;wBACf,MAAM;oBACR;iBACD;YAEH,KAAK;gBACH,OAAO;oBACL;wBACE,IAAI;wBACJ,OAAO;wBACP,aAAa;wBACb,MAAM,CAAC,8BAA8B,CAAC;wBACtC,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAC1B,OAAO;wBACP,UAAU;wBACV,eAAe;wBACf,MAAM;oBACR;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,aAAa;wBACb,MAAM,CAAC,sCAAsC,CAAC;wBAC9C,oBAAM,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBACvB,OAAO;wBACP,UAAU;wBACV,eAAe;wBACf,MAAM;oBACR;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,aAAa;wBACb,MAAM,CAAC,kCAAkC,CAAC;wBAC1C,oBAAM,6LAAC,iNAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAC1B,OAAO;wBACP,UAAU;wBACV,eAAe;wBACf,MAAM;oBACR;iBACD;YAEH;gBACE,OAAO,EAAE;QACb;IACF;IAEA,MAAM,aAAa;IAEnB,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,IAAI,WAAW,MAAM,KAAK,GAAG,OAAO;IAEpC,qBACE,6LAAC;QAAI,WAAW,CAAC,kCAAkC,EAAE,WAAW;;0BAC9D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;;;;;;kCAEjB,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;0BAIzC,6LAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO,QAAQ;wBAAI;wBACjC,cAAc,IAAM,eAAe,KAAK,EAAE;wBAC1C,cAAc,IAAM,eAAe;kCAEnC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAM,KAAK,IAAI;4BACf,WAAW,CAAC,0EAA0E,EACpF,gBAAgB,KAAK,EAAE,GACnB,0DACA,iBAAiB,KAAK,QAAQ,GAClC;sCAEF,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAW,CAAC,2BAA2B,EAAE,KAAK,KAAK,CAAC,qEAAqE,CAAC;0DAC5H,KAAK,IAAI;;;;;;0DAGZ,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAAwC,KAAK,KAAK;;;;;;4DAC/D,gBAAgB,KAAK,QAAQ;;;;;;;kEAEhC,6LAAC;wDAAE,WAAU;kEAA2C,KAAK,WAAW;;;;;;kEAExE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,6LAAC;kFAAM,KAAK,aAAa;;;;;;;;;;;;0EAE3B,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAc;;;;;;kFAC9B,6LAAC;kFAAM,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAMxB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDACP,GAAG,gBAAgB,KAAK,EAAE,GAAG,IAAI;gDACnC;gDACA,YAAY;oDAAE,UAAU;gDAAI;0DAE5B,cAAA,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;0DAExB,6LAAC,yNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;uBAlDzB,KAAK,EAAE;;;;;;;;;;0BA2DlB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;sCAAK;;;;;;sCACN,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;;sDACd,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,6LAAC;4CAAK,WAAU;sDAA6B;;;;;;;;;;;;8CAE/C,6LAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlB;GAtRwB;KAAA", "debugId": null}}, {"offset": {"line": 2794, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/components/club/NetworkEffect.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { motion } from 'framer-motion'\nimport { \n  Users,\n  TrendingUp,\n  Network,\n  Zap,\n  Crown,\n  Star,\n  Award,\n  Shield,\n  Target,\n  Rocket,\n  Globe,\n  Lock\n} from 'lucide-react'\n\nexport default function NetworkEffect() {\n  const [activeConnections, setActiveConnections] = useState(0)\n  const [networkValue, setNetworkValue] = useState(0)\n\n  const networkMetrics = [\n    {\n      label: 'Connexions Actives',\n      value: activeConnections,\n      unit: '',\n      color: 'text-blue-600',\n      icon: <Network className=\"h-5 w-5\" />\n    },\n    {\n      label: 'Valeur Réseau',\n      value: networkValue,\n      unit: 'M FCFA',\n      color: 'text-green-600',\n      icon: <TrendingUp className=\"h-5 w-5\" />\n    },\n    {\n      label: 'Projets Générés',\n      value: Math.floor(activeConnections * 2.3),\n      unit: '',\n      color: 'text-purple-600',\n      icon: <Rocket className=\"h-5 w-5\" />\n    },\n    {\n      label: 'Opportunités',\n      value: Math.floor(activeConnections * 1.7),\n      unit: '',\n      color: 'text-amber-600',\n      icon: <Target className=\"h-5 w-5\" />\n    }\n  ]\n\n  const membershipLevels = [\n    { name: 'Bronze', members: 456, multiplier: 1, color: 'from-amber-600 to-amber-700' },\n    { name: 'Silver', members: 389, multiplier: 2.5, color: 'from-gray-400 to-gray-500' },\n    { name: 'Gold', members: 267, multiplier: 5, color: 'from-yellow-400 to-yellow-500' },\n    { name: 'Platinum', members: 122, multiplier: 10, color: 'from-purple-400 to-purple-500' }\n  ]\n\n  const networkBenefits = [\n    {\n      title: 'Effet Métcalfe',\n      description: 'La valeur du réseau croît exponentiellement avec le nombre d\\'utilisateurs',\n      formula: 'Valeur = n²',\n      impact: 'Chaque nouveau membre augmente la valeur pour TOUS',\n      icon: <Globe className=\"h-6 w-6\" />\n    },\n    {\n      title: 'Exclusivité Renforcée',\n      description: 'Plus le club grandit, plus il devient difficile de rester en dehors',\n      formula: 'Pression = Membres × Influence',\n      impact: 'Être exclu devient un handicap concurrentiel majeur',\n      icon: <Lock className=\"h-6 w-6\" />\n    },\n    {\n      title: 'Synergie Multiplicatrice',\n      description: 'Les connexions entre membres créent des opportunités exponentielles',\n      formula: 'Opportunités = Connexions × Synergies',\n      impact: 'Chaque relation ouvre de nouvelles possibilités',\n      icon: <Zap className=\"h-6 w-6\" />\n    },\n    {\n      title: 'Barrière Naturelle',\n      description: 'Le coût de sortie augmente avec l\\'intégration au réseau',\n      formula: 'Coût Sortie = Connexions × Dépendance',\n      impact: 'Quitter le club = perdre son réseau professionnel',\n      icon: <Shield className=\"h-6 w-6\" />\n    }\n  ]\n\n  const successStories = [\n    {\n      member: 'Ing. Kouame Yao',\n      level: 'Platinum',\n      story: 'Grâce au réseau club, j\\'ai obtenu 15 nouveaux contrats en 6 mois',\n      value: '45M FCFA',\n      connections: 89\n    },\n    {\n      member: 'Fatou Diallo',\n      level: 'Gold',\n      story: 'Les recommandations membres m\\'ont permis de doubler mon CA',\n      value: '28M FCFA',\n      connections: 67\n    },\n    {\n      member: 'Jean Kouassi',\n      level: 'Gold',\n      story: 'Partenariat stratégique trouvé via le club, expansion régionale réussie',\n      value: '52M FCFA',\n      connections: 78\n    }\n  ]\n\n  useEffect(() => {\n    // Simulation de l'effet réseau en temps réel\n    const interval = setInterval(() => {\n      const totalMembers = membershipLevels.reduce((sum, level) => sum + level.members, 0)\n      const weightedConnections = membershipLevels.reduce((sum, level) => \n        sum + (level.members * level.multiplier), 0\n      )\n      \n      setActiveConnections(weightedConnections)\n      // Formule de Métcalfe simplifiée : Valeur = n² / 1000 (en millions)\n      setNetworkValue(Math.floor((weightedConnections * weightedConnections) / 1000))\n    }, 100)\n\n    return () => clearInterval(interval)\n  }, [])\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-lg p-6\">\n      {/* Header */}\n      <div className=\"flex items-center space-x-4 mb-6\">\n        <div className=\"w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center text-white\">\n          <Network className=\"h-6 w-6\" />\n        </div>\n        <div>\n          <h2 className=\"text-2xl font-bold text-gray-900\">Effet Réseau en Temps Réel</h2>\n          <p className=\"text-gray-600\">Visualisation de la puissance du Club Pro Matos</p>\n        </div>\n      </div>\n\n      {/* Métriques réseau */}\n      <div className=\"grid md:grid-cols-4 gap-6 mb-8\">\n        {networkMetrics.map((metric, index) => (\n          <motion.div\n            key={metric.label}\n            initial={{ opacity: 0, scale: 0.9 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ delay: index * 0.1 }}\n            className=\"bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg p-4 border\"\n          >\n            <div className=\"flex items-center space-x-2 mb-2\">\n              <div className={`${metric.color}`}>\n                {metric.icon}\n              </div>\n              <span className=\"text-sm font-medium text-gray-700\">{metric.label}</span>\n            </div>\n            <div className=\"text-2xl font-bold text-gray-900\">\n              {metric.value.toLocaleString()}{metric.unit}\n            </div>\n          </motion.div>\n        ))}\n      </div>\n\n      {/* Visualisation des niveaux */}\n      <div className=\"mb-8\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Répartition par Niveau</h3>\n        <div className=\"grid md:grid-cols-4 gap-4\">\n          {membershipLevels.map((level, index) => (\n            <motion.div\n              key={level.name}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.4 + index * 0.1 }}\n              className=\"text-center\"\n            >\n              <div className={`w-16 h-16 bg-gradient-to-r ${level.color} rounded-full flex items-center justify-center mx-auto mb-3`}>\n                <Crown className=\"h-8 w-8 text-white\" />\n              </div>\n              <h4 className=\"font-semibold text-gray-900\">{level.name}</h4>\n              <p className=\"text-lg font-bold text-gray-900\">{level.members}</p>\n              <p className=\"text-xs text-gray-500\">membres</p>\n              <p className=\"text-xs text-purple-600 font-medium\">×{level.multiplier} influence</p>\n            </motion.div>\n          ))}\n        </div>\n      </div>\n\n      {/* Avantages de l'effet réseau */}\n      <div className=\"mb-8\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Mécanismes de l'Effet Réseau</h3>\n        <div className=\"grid md:grid-cols-2 gap-6\">\n          {networkBenefits.map((benefit, index) => (\n            <motion.div\n              key={benefit.title}\n              initial={{ opacity: 0, x: -20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ delay: 0.8 + index * 0.1 }}\n              className=\"bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg p-4 border border-purple-200\"\n            >\n              <div className=\"flex items-start space-x-3\">\n                <div className=\"w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center text-purple-600 flex-shrink-0\">\n                  {benefit.icon}\n                </div>\n                <div className=\"flex-1\">\n                  <h4 className=\"font-semibold text-purple-900 mb-1\">{benefit.title}</h4>\n                  <p className=\"text-sm text-purple-700 mb-2\">{benefit.description}</p>\n                  <div className=\"bg-white rounded p-2 mb-2\">\n                    <code className=\"text-xs text-purple-800 font-mono\">{benefit.formula}</code>\n                  </div>\n                  <p className=\"text-xs text-purple-600 font-medium\">{benefit.impact}</p>\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n      </div>\n\n      {/* Success stories */}\n      <div className=\"mb-8\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Témoignages de Réussite</h3>\n        <div className=\"space-y-4\">\n          {successStories.map((story, index) => (\n            <motion.div\n              key={story.member}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 1.2 + index * 0.1 }}\n              className=\"bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-4 border border-green-200\"\n            >\n              <div className=\"flex items-start justify-between\">\n                <div className=\"flex-1\">\n                  <div className=\"flex items-center space-x-3 mb-2\">\n                    <h4 className=\"font-semibold text-green-900\">{story.member}</h4>\n                    <span className=\"px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full\">\n                      {story.level}\n                    </span>\n                  </div>\n                  <p className=\"text-sm text-green-700 mb-2 italic\">\"{story.story}\"</p>\n                  <div className=\"flex items-center space-x-4 text-xs text-green-600\">\n                    <span className=\"flex items-center space-x-1\">\n                      <TrendingUp className=\"h-3 w-3\" />\n                      <span>Valeur générée: {story.value}</span>\n                    </span>\n                    <span className=\"flex items-center space-x-1\">\n                      <Users className=\"h-3 w-3\" />\n                      <span>{story.connections} connexions actives</span>\n                    </span>\n                  </div>\n                </div>\n                <div className=\"ml-4\">\n                  <div className=\"flex items-center space-x-1\">\n                    {[...Array(5)].map((_, i) => (\n                      <Star key={i} className=\"h-4 w-4 text-yellow-500 fill-current\" />\n                    ))}\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n      </div>\n\n      {/* Call to action */}\n      <div className=\"bg-gradient-to-r from-purple-900 to-indigo-900 text-white rounded-lg p-6 text-center\">\n        <h3 className=\"text-xl font-bold mb-2\">Rejoignez l'Élite Électrique</h3>\n        <p className=\"text-purple-200 mb-4\">\n          Plus vous attendez, plus la barrière à l'entrée augmente\n        </p>\n        <div className=\"flex items-center justify-center space-x-6 mb-4\">\n          <div className=\"text-center\">\n            <div className=\"text-2xl font-bold text-purple-300\">{activeConnections.toLocaleString()}</div>\n            <div className=\"text-xs text-purple-400\">Connexions actives</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-2xl font-bold text-purple-300\">{networkValue}M</div>\n            <div className=\"text-xs text-purple-400\">Valeur réseau FCFA</div>\n          </div>\n        </div>\n        <button className=\"bg-amber-500 text-slate-900 px-8 py-3 rounded-lg font-bold hover:bg-amber-400 transition-colors\">\n          Rejoindre Maintenant\n        </button>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAmBe,SAAS;;IACtB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,iBAAiB;QACrB;YACE,OAAO;YACP,OAAO;YACP,MAAM;YACN,OAAO;YACP,oBAAM,6LAAC,2MAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;QAC3B;QACA;YACE,OAAO;YACP,OAAO;YACP,MAAM;YACN,OAAO;YACP,oBAAM,6LAAC,qNAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;QAC9B;QACA;YACE,OAAO;YACP,OAAO,KAAK,KAAK,CAAC,oBAAoB;YACtC,MAAM;YACN,OAAO;YACP,oBAAM,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;QAC1B;QACA;YACE,OAAO;YACP,OAAO,KAAK,KAAK,CAAC,oBAAoB;YACtC,MAAM;YACN,OAAO;YACP,oBAAM,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;QAC1B;KACD;IAED,MAAM,mBAAmB;QACvB;YAAE,MAAM;YAAU,SAAS;YAAK,YAAY;YAAG,OAAO;QAA8B;QACpF;YAAE,MAAM;YAAU,SAAS;YAAK,YAAY;YAAK,OAAO;QAA4B;QACpF;YAAE,MAAM;YAAQ,SAAS;YAAK,YAAY;YAAG,OAAO;QAAgC;QACpF;YAAE,MAAM;YAAY,SAAS;YAAK,YAAY;YAAI,OAAO;QAAgC;KAC1F;IAED,MAAM,kBAAkB;QACtB;YACE,OAAO;YACP,aAAa;YACb,SAAS;YACT,QAAQ;YACR,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;QACzB;QACA;YACE,OAAO;YACP,aAAa;YACb,SAAS;YACT,QAAQ;YACR,oBAAM,6LAAC,qMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;QACxB;QACA;YACE,OAAO;YACP,aAAa;YACb,SAAS;YACT,QAAQ;YACR,oBAAM,6LAAC,mMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;QACvB;QACA;YACE,OAAO;YACP,aAAa;YACb,SAAS;YACT,QAAQ;YACR,oBAAM,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;QAC1B;KACD;IAED,MAAM,iBAAiB;QACrB;YACE,QAAQ;YACR,OAAO;YACP,OAAO;YACP,OAAO;YACP,aAAa;QACf;QACA;YACE,QAAQ;YACR,OAAO;YACP,OAAO;YACP,OAAO;YACP,aAAa;QACf;QACA;YACE,QAAQ;YACR,OAAO;YACP,OAAO;YACP,OAAO;YACP,aAAa;QACf;KACD;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,6CAA6C;YAC7C,MAAM,WAAW;oDAAY;oBAC3B,MAAM,eAAe,iBAAiB,MAAM;yEAAC,CAAC,KAAK,QAAU,MAAM,MAAM,OAAO;wEAAE;oBAClF,MAAM,sBAAsB,iBAAiB,MAAM;gFAAC,CAAC,KAAK,QACxD,MAAO,MAAM,OAAO,GAAG,MAAM,UAAU;+EAAG;oBAG5C,qBAAqB;oBACrB,oEAAoE;oBACpE,gBAAgB,KAAK,KAAK,CAAC,AAAC,sBAAsB,sBAAuB;gBAC3E;mDAAG;YAEH;2CAAO,IAAM,cAAc;;QAC7B;kCAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;;;;;;kCAErB,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;0BAKjC,6LAAC;gBAAI,WAAU;0BACZ,eAAe,GAAG,CAAC,CAAC,QAAQ,sBAC3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAI;wBAClC,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAE;wBAChC,YAAY;4BAAE,OAAO,QAAQ;wBAAI;wBACjC,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAW,GAAG,OAAO,KAAK,EAAE;kDAC9B,OAAO,IAAI;;;;;;kDAEd,6LAAC;wCAAK,WAAU;kDAAqC,OAAO,KAAK;;;;;;;;;;;;0CAEnE,6LAAC;gCAAI,WAAU;;oCACZ,OAAO,KAAK,CAAC,cAAc;oCAAI,OAAO,IAAI;;;;;;;;uBAbxC,OAAO,KAAK;;;;;;;;;;0BAoBvB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAI,WAAU;kCACZ,iBAAiB,GAAG,CAAC,CAAC,OAAO,sBAC5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO,MAAM,QAAQ;gCAAI;gCACvC,WAAU;;kDAEV,6LAAC;wCAAI,WAAW,CAAC,2BAA2B,EAAE,MAAM,KAAK,CAAC,2DAA2D,CAAC;kDACpH,cAAA,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,6LAAC;wCAAG,WAAU;kDAA+B,MAAM,IAAI;;;;;;kDACvD,6LAAC;wCAAE,WAAU;kDAAmC,MAAM,OAAO;;;;;;kDAC7D,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;kDACrC,6LAAC;wCAAE,WAAU;;4CAAsC;4CAAE,MAAM,UAAU;4CAAC;;;;;;;;+BAZjE,MAAM,IAAI;;;;;;;;;;;;;;;;0BAmBvB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAC,SAAS,sBAC7B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO,MAAM,QAAQ;gCAAI;gCACvC,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,QAAQ,IAAI;;;;;;sDAEf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAsC,QAAQ,KAAK;;;;;;8DACjE,6LAAC;oDAAE,WAAU;8DAAgC,QAAQ,WAAW;;;;;;8DAChE,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAAqC,QAAQ,OAAO;;;;;;;;;;;8DAEtE,6LAAC;oDAAE,WAAU;8DAAuC,QAAQ,MAAM;;;;;;;;;;;;;;;;;;+BAhBjE,QAAQ,KAAK;;;;;;;;;;;;;;;;0BAyB1B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAI,WAAU;kCACZ,eAAe,GAAG,CAAC,CAAC,OAAO,sBAC1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO,MAAM,QAAQ;gCAAI;gCACvC,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAgC,MAAM,MAAM;;;;;;sEAC1D,6LAAC;4DAAK,WAAU;sEACb,MAAM,KAAK;;;;;;;;;;;;8DAGhB,6LAAC;oDAAE,WAAU;;wDAAqC;wDAAE,MAAM,KAAK;wDAAC;;;;;;;8DAChE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;;8EACd,6LAAC,qNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;8EACtB,6LAAC;;wEAAK;wEAAiB,MAAM,KAAK;;;;;;;;;;;;;sEAEpC,6LAAC;4DAAK,WAAU;;8EACd,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,6LAAC;;wEAAM,MAAM,WAAW;wEAAC;;;;;;;;;;;;;;;;;;;;;;;;;sDAI/B,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACZ;uDAAI,MAAM;iDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,qMAAA,CAAA,OAAI;wDAAS,WAAU;uDAAb;;;;;;;;;;;;;;;;;;;;;+BA7Bd,MAAM,MAAM;;;;;;;;;;;;;;;;0BAwCzB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyB;;;;;;kCACvC,6LAAC;wBAAE,WAAU;kCAAuB;;;;;;kCAGpC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAsC,kBAAkB,cAAc;;;;;;kDACrF,6LAAC;wCAAI,WAAU;kDAA0B;;;;;;;;;;;;0CAE3C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;4CAAsC;4CAAa;;;;;;;kDAClE,6LAAC;wCAAI,WAAU;kDAA0B;;;;;;;;;;;;;;;;;;kCAG7C,6LAAC;wBAAO,WAAU;kCAAkG;;;;;;;;;;;;;;;;;;AAM5H;GA9QwB;KAAA", "debugId": null}}, {"offset": {"line": 3609, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/app/club/page.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { \n  ArrowLeft,\n  Crown,\n  Users,\n  Calendar,\n  Award,\n  Star,\n  TrendingUp,\n  Shield,\n  Zap,\n  MessageCircle,\n  Video,\n  FileText,\n  Database\n} from 'lucide-react'\nimport Link from 'next/link'\nimport GlobalNavigation from '@/components/navigation/GlobalNavigation'\nimport ResponsiveLayout from '@/components/layout/ResponsiveLayout'\nimport EcosystemHub from '@/components/integration/EcosystemHub'\nimport SmartLinks from '@/components/integration/SmartLinks'\nimport NetworkEffect from '@/components/club/NetworkEffect'\n\nexport default function ClubPage() {\n  const memberStats = [\n    {\n      label: 'Membres Actifs',\n      value: '1,234',\n      change: '+89',\n      changeType: 'increase',\n      icon: <Users className=\"h-6 w-6\" />\n    },\n    {\n      label: 'Événements ce Mois',\n      value: '12',\n      change: '+3',\n      changeType: 'increase',\n      icon: <Calendar className=\"h-6 w-6\" />\n    },\n    {\n      label: 'Projets Partagés',\n      value: '567',\n      change: '+156',\n      changeType: 'increase',\n      icon: <FileText className=\"h-6 w-6\" />\n    },\n    {\n      label: 'Note Satisfaction',\n      value: '4.9',\n      change: '+0.1',\n      changeType: 'increase',\n      icon: <Star className=\"h-6 w-6\" />\n    }\n  ]\n\n  const membershipTiers = [\n    {\n      name: 'Bronze',\n      members: 456,\n      color: 'from-amber-600 to-amber-700',\n      benefits: ['Accès forum', 'Newsletter mensuelle', 'Support email'],\n      price: 'Gratuit'\n    },\n    {\n      name: 'Silver',\n      members: 389,\n      color: 'from-gray-400 to-gray-500',\n      benefits: ['Webinaires exclusifs', 'Templates avancés', 'Chat support'],\n      price: '25,000 FCFA/mois'\n    },\n    {\n      name: 'Gold',\n      members: 267,\n      color: 'from-yellow-400 to-yellow-500',\n      benefits: ['Consultation directe', 'Certification officielle', 'Événements VIP'],\n      price: '75,000 FCFA/mois'\n    },\n    {\n      name: 'Platinum',\n      members: 122,\n      color: 'from-purple-400 to-purple-500',\n      benefits: ['Expert dédié', 'Accès illimité', 'Formation personnalisée'],\n      price: '150,000 FCFA/mois'\n    }\n  ]\n\n  const upcomingEvents = [\n    {\n      id: 1,\n      title: 'Webinaire: Nouvelles Normes NF C 15-100',\n      date: '2024-01-20',\n      time: '14:00',\n      type: 'webinar',\n      level: 'silver',\n      attendees: 89,\n      maxAttendees: 100\n    },\n    {\n      id: 2,\n      title: 'Formation: Installation Photovoltaïque',\n      date: '2024-01-25',\n      time: '09:00',\n      type: 'training',\n      level: 'gold',\n      attendees: 23,\n      maxAttendees: 25\n    },\n    {\n      id: 3,\n      title: 'Networking: Rencontre Professionnels Abidjan',\n      date: '2024-01-30',\n      time: '18:00',\n      type: 'networking',\n      level: 'platinum',\n      attendees: 15,\n      maxAttendees: 20\n    }\n  ]\n\n  const showcaseProjects = [\n    {\n      id: 1,\n      title: 'Centre Commercial Plateau - Installation Complète',\n      author: 'Ing. Kouame Yao',\n      company: 'SODECI',\n      level: 'platinum',\n      likes: 47,\n      comments: 12,\n      views: 234,\n      image: '/api/placeholder/300/200'\n    },\n    {\n      id: 2,\n      title: 'Résidence Solaire Cocody - 50kW',\n      author: 'Fatou Diallo',\n      company: 'SolarTech CI',\n      level: 'gold',\n      likes: 38,\n      comments: 8,\n      views: 189,\n      image: '/api/placeholder/300/200'\n    },\n    {\n      id: 3,\n      title: 'Usine Automatisée Yopougon',\n      author: 'Jean Kouassi',\n      company: 'AutoElec',\n      level: 'gold',\n      likes: 52,\n      comments: 15,\n      views: 298,\n      image: '/api/placeholder/300/200'\n    }\n  ]\n\n  const getEventTypeIcon = (type: string) => {\n    switch (type) {\n      case 'webinar':\n        return <Video className=\"h-5 w-5\" />\n      case 'training':\n        return <Award className=\"h-5 w-5\" />\n      case 'networking':\n        return <Users className=\"h-5 w-5\" />\n      default:\n        return <Calendar className=\"h-5 w-5\" />\n    }\n  }\n\n  const getEventTypeColor = (type: string) => {\n    switch (type) {\n      case 'webinar':\n        return 'bg-blue-100 text-blue-800'\n      case 'training':\n        return 'bg-green-100 text-green-800'\n      case 'networking':\n        return 'bg-purple-100 text-purple-800'\n      default:\n        return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const getLevelColor = (level: string) => {\n    switch (level) {\n      case 'bronze':\n        return 'bg-amber-100 text-amber-800'\n      case 'silver':\n        return 'bg-gray-100 text-gray-800'\n      case 'gold':\n        return 'bg-yellow-100 text-yellow-800'\n      case 'platinum':\n        return 'bg-purple-100 text-purple-800'\n      default:\n        return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  return (\n    <>\n      <GlobalNavigation />\n      <ResponsiveLayout\n        title=\"Club Membre Exclusif\"\n        subtitle=\"Réseau professionnel fermé pour l'élite de l'électrique en Afrique de l'Ouest\"\n        sidebar={\n          <SmartLinks \n            context={{\n              module: 'club',\n              data: { memberId: 'current', level: 'gold' },\n              action: 'view'\n            }}\n          />\n        }\n        actions={\n          <Link \n            href=\"/\"\n            className=\"flex items-center space-x-2 text-slate-700 hover:text-amber-600 transition-colors\"\n          >\n            <ArrowLeft className=\"h-5 w-5\" />\n            <span className=\"hidden sm:inline\">Retour à l'accueil</span>\n          </Link>\n        }\n      >\n        {/* Statistiques du club */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n          {memberStats.map((stat, index) => (\n            <motion.div\n              key={stat.label}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: index * 0.05 }}\n              className=\"industrial-card p-6\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-slate-600\">{stat.label}</p>\n                  <div className=\"flex items-baseline space-x-2\">\n                    <p className=\"text-2xl font-bold text-slate-900\">{stat.value}</p>\n                    <span className={`text-sm font-medium ${\n                      stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'\n                    }`}>\n                      +{stat.change}\n                    </span>\n                  </div>\n                </div>\n                <div className=\"p-3 bg-amber-100 rounded-lg text-amber-600\">\n                  {stat.icon}\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Effet réseau */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.4 }}\n          className=\"mb-8\"\n        >\n          <NetworkEffect />\n        </motion.div>\n\n        {/* Intégration écosystème */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.5 }}\n          className=\"mb-8\"\n        >\n          <EcosystemHub currentModule=\"club\" />\n        </motion.div>\n\n        {/* Niveaux d'adhésion */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.5 }}\n          className=\"mb-8\"\n        >\n          <h2 className=\"text-2xl font-bold text-slate-900 mb-6\">Niveaux d'Adhésion</h2>\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            {membershipTiers.map((tier, index) => (\n              <motion.div\n                key={tier.name}\n                initial={{ opacity: 0, scale: 0.9 }}\n                animate={{ opacity: 1, scale: 1 }}\n                transition={{ delay: 0.6 + index * 0.1 }}\n                className=\"industrial-card p-6 text-center\"\n              >\n                <div className={`w-16 h-16 bg-gradient-to-r ${tier.color} rounded-full flex items-center justify-center mx-auto mb-4`}>\n                  <Crown className=\"h-8 w-8 text-white\" />\n                </div>\n                \n                <h3 className=\"text-xl font-bold text-gray-900 mb-2\">{tier.name}</h3>\n                <p className=\"text-lg font-bold text-amber-600 mb-4\">{tier.price}</p>\n                \n                <div className=\"text-sm text-gray-600 mb-4\">\n                  <span className=\"font-medium\">{tier.members}</span> membres actifs\n                </div>\n                \n                <ul className=\"text-sm text-gray-600 space-y-2 mb-6\">\n                  {tier.benefits.map((benefit, i) => (\n                    <li key={i} className=\"flex items-center space-x-2\">\n                      <Shield className=\"h-4 w-4 text-green-500 flex-shrink-0\" />\n                      <span>{benefit}</span>\n                    </li>\n                  ))}\n                </ul>\n                \n                <button className=\"w-full btn-premium text-sm py-2\">\n                  {tier.name === 'Bronze' ? 'Rejoindre' : 'Passer au ' + tier.name}\n                </button>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n\n        {/* Événements à venir */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.8 }}\n          className=\"mb-8\"\n        >\n          <h2 className=\"text-2xl font-bold text-slate-900 mb-6\">Événements Exclusifs</h2>\n          <div className=\"space-y-4\">\n            {upcomingEvents.map((event, index) => (\n              <motion.div\n                key={event.id}\n                initial={{ opacity: 0, x: -20 }}\n                animate={{ opacity: 1, x: 0 }}\n                transition={{ delay: 0.9 + index * 0.1 }}\n                className=\"industrial-card p-6\"\n              >\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center space-x-3 mb-3\">\n                      <span className={`px-3 py-1 rounded-full text-sm font-medium flex items-center space-x-1 ${getEventTypeColor(event.type)}`}>\n                        {getEventTypeIcon(event.type)}\n                        <span className=\"capitalize\">{event.type}</span>\n                      </span>\n                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getLevelColor(event.level)}`}>\n                        {event.level}+ requis\n                      </span>\n                    </div>\n                    \n                    <h3 className=\"text-lg font-bold text-gray-900 mb-2\">{event.title}</h3>\n                    <div className=\"flex items-center space-x-4 text-sm text-gray-600 mb-3\">\n                      <span>📅 {event.date}</span>\n                      <span>🕐 {event.time}</span>\n                      <span>👥 {event.attendees}/{event.maxAttendees} inscrits</span>\n                    </div>\n                    \n                    <div className=\"w-full bg-gray-200 rounded-full h-2 mb-3\">\n                      <div \n                        className=\"bg-amber-500 h-2 rounded-full\"\n                        style={{ width: `${(event.attendees / event.maxAttendees) * 100}%` }}\n                      ></div>\n                    </div>\n                  </div>\n                  \n                  <div className=\"ml-6\">\n                    <button className=\"px-6 py-2 bg-amber-600 text-white rounded-lg hover:bg-amber-700 transition-colors\">\n                      S'inscrire\n                    </button>\n                  </div>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n\n        {/* Projets showcase */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 1.1 }}\n        >\n          <h2 className=\"text-2xl font-bold text-slate-900 mb-6\">Projets Showcase Membres</h2>\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {showcaseProjects.map((project, index) => (\n              <motion.div\n                key={project.id}\n                initial={{ opacity: 0, scale: 0.9 }}\n                animate={{ opacity: 1, scale: 1 }}\n                transition={{ delay: 1.2 + index * 0.1 }}\n                className=\"industrial-card overflow-hidden\"\n              >\n                <div className=\"h-48 bg-gradient-to-r from-gray-200 to-gray-300 flex items-center justify-center\">\n                  <Zap className=\"h-16 w-16 text-gray-400\" />\n                </div>\n                \n                <div className=\"p-6\">\n                  <div className=\"flex items-center justify-between mb-3\">\n                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getLevelColor(project.level)}`}>\n                      {project.level}\n                    </span>\n                    <div className=\"flex items-center space-x-3 text-sm text-gray-500\">\n                      <span className=\"flex items-center space-x-1\">\n                        <Star className=\"h-4 w-4\" />\n                        <span>{project.likes}</span>\n                      </span>\n                      <span className=\"flex items-center space-x-1\">\n                        <MessageCircle className=\"h-4 w-4\" />\n                        <span>{project.comments}</span>\n                      </span>\n                    </div>\n                  </div>\n                  \n                  <h3 className=\"font-bold text-gray-900 mb-2 line-clamp-2\">{project.title}</h3>\n                  <p className=\"text-sm text-gray-600 mb-1\">{project.author}</p>\n                  <p className=\"text-xs text-gray-500 mb-4\">{project.company}</p>\n                  \n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-xs text-gray-500\">{project.views} vues</span>\n                    <button className=\"text-amber-600 hover:text-amber-700 font-medium text-sm\">\n                      Voir détails →\n                    </button>\n                  </div>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n      </ResponsiveLayout>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AACA;AACA;AACA;AACA;AACA;AAvBA;;;;;;;;;;AAyBe,SAAS;IACtB,MAAM,cAAc;QAClB;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;QACzB;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;QAC5B;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,oBAAM,6LAAC,iNAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;QAC5B;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,oBAAM,6LAAC,qMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;QACxB;KACD;IAED,MAAM,kBAAkB;QACtB;YACE,MAAM;YACN,SAAS;YACT,OAAO;YACP,UAAU;gBAAC;gBAAe;gBAAwB;aAAgB;YAClE,OAAO;QACT;QACA;YACE,MAAM;YACN,SAAS;YACT,OAAO;YACP,UAAU;gBAAC;gBAAwB;gBAAqB;aAAe;YACvE,OAAO;QACT;QACA;YACE,MAAM;YACN,SAAS;YACT,OAAO;YACP,UAAU;gBAAC;gBAAwB;gBAA4B;aAAiB;YAChF,OAAO;QACT;QACA;YACE,MAAM;YACN,SAAS;YACT,OAAO;YACP,UAAU;gBAAC;gBAAgB;gBAAkB;aAA0B;YACvE,OAAO;QACT;KACD;IAED,MAAM,iBAAiB;QACrB;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,MAAM;YACN,MAAM;YACN,OAAO;YACP,WAAW;YACX,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,MAAM;YACN,MAAM;YACN,OAAO;YACP,WAAW;YACX,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,MAAM;YACN,MAAM;YACN,OAAO;YACP,WAAW;YACX,cAAc;QAChB;KACD;IAED,MAAM,mBAAmB;QACvB;YACE,IAAI;YACJ,OAAO;YACP,QAAQ;YACR,SAAS;YACT,OAAO;YACP,OAAO;YACP,UAAU;YACV,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,QAAQ;YACR,SAAS;YACT,OAAO;YACP,OAAO;YACP,UAAU;YACV,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,QAAQ;YACR,SAAS;YACT,OAAO;YACP,OAAO;YACP,UAAU;YACV,OAAO;YACP,OAAO;QACT;KACD;IAED,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B;gBACE,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;QAC/B;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE;;0BACE,6LAAC,uJAAA,CAAA,UAAgB;;;;;0BACjB,6LAAC,mJAAA,CAAA,UAAgB;gBACf,OAAM;gBACN,UAAS;gBACT,uBACE,6LAAC,kJAAA,CAAA,UAAU;oBACT,SAAS;wBACP,QAAQ;wBACR,MAAM;4BAAE,UAAU;4BAAW,OAAO;wBAAO;wBAC3C,QAAQ;oBACV;;;;;;gBAGJ,uBACE,6LAAC,+JAAA,CAAA,UAAI;oBACH,MAAK;oBACL,WAAU;;sCAEV,6LAAC,mNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,6LAAC;4BAAK,WAAU;sCAAmB;;;;;;;;;;;;;kCAKvC,6LAAC;wBAAI,WAAU;kCACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO,QAAQ;gCAAK;gCAClC,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAsC,KAAK,KAAK;;;;;;8DAC7D,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAqC,KAAK,KAAK;;;;;;sEAC5D,6LAAC;4DAAK,WAAW,CAAC,oBAAoB,EACpC,KAAK,UAAU,KAAK,aAAa,mBAAmB,gBACpD;;gEAAE;gEACA,KAAK,MAAM;;;;;;;;;;;;;;;;;;;sDAInB,6LAAC;4CAAI,WAAU;sDACZ,KAAK,IAAI;;;;;;;;;;;;+BAnBT,KAAK,KAAK;;;;;;;;;;kCA2BrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;wBACzB,WAAU;kCAEV,cAAA,6LAAC,8IAAA,CAAA,UAAa;;;;;;;;;;kCAIhB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;wBACzB,WAAU;kCAEV,cAAA,6LAAC,oJAAA,CAAA,UAAY;4BAAC,eAAc;;;;;;;;;;;kCAI9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;wBACzB,WAAU;;0CAEV,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAI,WAAU;0CACZ,gBAAgB,GAAG,CAAC,CAAC,MAAM,sBAC1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAClC,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCAChC,YAAY;4CAAE,OAAO,MAAM,QAAQ;wCAAI;wCACvC,WAAU;;0DAEV,6LAAC;gDAAI,WAAW,CAAC,2BAA2B,EAAE,KAAK,KAAK,CAAC,2DAA2D,CAAC;0DACnH,cAAA,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAGnB,6LAAC;gDAAG,WAAU;0DAAwC,KAAK,IAAI;;;;;;0DAC/D,6LAAC;gDAAE,WAAU;0DAAyC,KAAK,KAAK;;;;;;0DAEhE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAe,KAAK,OAAO;;;;;;oDAAQ;;;;;;;0DAGrD,6LAAC;gDAAG,WAAU;0DACX,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,kBAC3B,6LAAC;wDAAW,WAAU;;0EACpB,6LAAC,yMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,6LAAC;0EAAM;;;;;;;uDAFA;;;;;;;;;;0DAOb,6LAAC;gDAAO,WAAU;0DACf,KAAK,IAAI,KAAK,WAAW,cAAc,eAAe,KAAK,IAAI;;;;;;;uCA3B7D,KAAK,IAAI;;;;;;;;;;;;;;;;kCAmCtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;wBACzB,WAAU;;0CAEV,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAI,WAAU;0CACZ,eAAe,GAAG,CAAC,CAAC,OAAO,sBAC1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO,MAAM,QAAQ;wCAAI;wCACvC,WAAU;kDAEV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAW,CAAC,uEAAuE,EAAE,kBAAkB,MAAM,IAAI,GAAG;;wEACvH,iBAAiB,MAAM,IAAI;sFAC5B,6LAAC;4EAAK,WAAU;sFAAc,MAAM,IAAI;;;;;;;;;;;;8EAE1C,6LAAC;oEAAK,WAAW,CAAC,2CAA2C,EAAE,cAAc,MAAM,KAAK,GAAG;;wEACxF,MAAM,KAAK;wEAAC;;;;;;;;;;;;;sEAIjB,6LAAC;4DAAG,WAAU;sEAAwC,MAAM,KAAK;;;;;;sEACjE,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;;wEAAK;wEAAI,MAAM,IAAI;;;;;;;8EACpB,6LAAC;;wEAAK;wEAAI,MAAM,IAAI;;;;;;;8EACpB,6LAAC;;wEAAK;wEAAI,MAAM,SAAS;wEAAC;wEAAE,MAAM,YAAY;wEAAC;;;;;;;;;;;;;sEAGjD,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,OAAO,GAAG,AAAC,MAAM,SAAS,GAAG,MAAM,YAAY,GAAI,IAAI,CAAC,CAAC;gEAAC;;;;;;;;;;;;;;;;;8DAKzE,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAO,WAAU;kEAAoF;;;;;;;;;;;;;;;;;uCAlCrG,MAAM,EAAE;;;;;;;;;;;;;;;;kCA6CrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;;0CAEzB,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAI,WAAU;0CACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAClC,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCAChC,YAAY;4CAAE,OAAO,MAAM,QAAQ;wCAAI;wCACvC,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;;;;;;0DAGjB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAW,CAAC,2CAA2C,EAAE,cAAc,QAAQ,KAAK,GAAG;0EAC1F,QAAQ,KAAK;;;;;;0EAEhB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;;0FACd,6LAAC,qMAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;0FAChB,6LAAC;0FAAM,QAAQ,KAAK;;;;;;;;;;;;kFAEtB,6LAAC;wEAAK,WAAU;;0FACd,6LAAC,2NAAA,CAAA,gBAAa;gFAAC,WAAU;;;;;;0FACzB,6LAAC;0FAAM,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;kEAK7B,6LAAC;wDAAG,WAAU;kEAA6C,QAAQ,KAAK;;;;;;kEACxE,6LAAC;wDAAE,WAAU;kEAA8B,QAAQ,MAAM;;;;;;kEACzD,6LAAC;wDAAE,WAAU;kEAA8B,QAAQ,OAAO;;;;;;kEAE1D,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;;oEAAyB,QAAQ,KAAK;oEAAC;;;;;;;0EACvD,6LAAC;gEAAO,WAAU;0EAA0D;;;;;;;;;;;;;;;;;;;uCAjC3E,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AA6C/B;KAnZwB", "debugId": null}}]}