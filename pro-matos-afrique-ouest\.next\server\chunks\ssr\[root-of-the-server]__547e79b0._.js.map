{"version": 3, "sources": [], "sections": [{"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/lib/supabase/client.ts"], "sourcesContent": ["import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'\nimport { Database } from '@/lib/types/database'\n\nexport const supabase = createClientComponentClient<Database>()\n\n// Helper pour les uploads de fichiers\nexport const uploadFile = async (\n  bucket: string,\n  path: string,\n  file: File\n): Promise<{ data: { path: string } | null; error: Error | null }> => {\n  try {\n    const { data, error } = await supabase.storage\n      .from(bucket)\n      .upload(path, file, {\n        cacheControl: '3600',\n        upsert: false\n      })\n\n    if (error) throw error\n\n    return { data, error: null }\n  } catch (error) {\n    return { data: null, error: error as <PERSON>rro<PERSON> }\n  }\n}\n\n// Helper pour obtenir l'URL publique d'un fichier\nexport const getPublicUrl = (bucket: string, path: string): string => {\n  const { data } = supabase.storage.from(bucket).getPublicUrl(path)\n  return data.publicUrl\n}\n\n// Helper pour télécharger un fichier\nexport const downloadFile = async (bucket: string, path: string) => {\n  try {\n    const { data, error } = await supabase.storage\n      .from(bucket)\n      .download(path)\n\n    if (error) throw error\n\n    return { data, error: null }\n  } catch (error) {\n    return { data: null, error: error as Error }\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAGO,MAAM,WAAW,CAAA,GAAA,wKAAA,CAAA,8BAA2B,AAAD;AAG3C,MAAM,aAAa,OACxB,QACA,MACA;IAEA,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,OAAO,CAC3C,IAAI,CAAC,QACL,MAAM,CAAC,MAAM,MAAM;YAClB,cAAc;YACd,QAAQ;QACV;QAEF,IAAI,OAAO,MAAM;QAEjB,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,MAAM;YAAM,OAAO;QAAe;IAC7C;AACF;AAGO,MAAM,eAAe,CAAC,QAAgB;IAC3C,MAAM,EAAE,IAAI,EAAE,GAAG,SAAS,OAAO,CAAC,IAAI,CAAC,QAAQ,YAAY,CAAC;IAC5D,OAAO,KAAK,SAAS;AACvB;AAGO,MAAM,eAAe,OAAO,QAAgB;IACjD,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,OAAO,CAC3C,IAAI,CAAC,QACL,QAAQ,CAAC;QAEZ,IAAI,OAAO,MAAM;QAEjB,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,MAAM;YAAM,OAAO;QAAe;IAC7C;AACF", "debugId": null}}, {"offset": {"line": 155, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/lib/stores/authStore.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { User as SupabaseUser } from '@supabase/auth-helpers-nextjs'\nimport { User } from '@/lib/types/database'\nimport { supabase } from '@/lib/supabase/client'\n\ninterface AuthState {\n  user: SupabaseUser | null\n  profile: User | null\n  loading: boolean\n  signIn: (email: string) => Promise<{ error: Error | null }>\n  signOut: () => Promise<void>\n  updateProfile: (updates: Partial<User>) => Promise<{ error: Error | null }>\n  fetchProfile: () => Promise<void>\n  initialize: () => Promise<void>\n}\n\nexport const useAuthStore = create<AuthState>((set, get) => ({\n  user: null,\n  profile: null,\n  loading: true,\n\n  signIn: async (email: string) => {\n    try {\n      const { error } = await supabase.auth.signInWithOtp({\n        email,\n        options: {\n          emailRedirectTo: `${window.location.origin}/auth/callback`\n        }\n      })\n\n      if (error) throw error\n\n      return { error: null }\n    } catch (error) {\n      return { error: error as Error }\n    }\n  },\n\n  signOut: async () => {\n    await supabase.auth.signOut()\n    set({ user: null, profile: null })\n  },\n\n  updateProfile: async (updates: Partial<User>) => {\n    try {\n      const { user } = get()\n      if (!user) throw new Error('Non authentifié')\n\n      const { error } = await supabase\n        .from('users')\n        .update({ ...updates, updated_at: new Date().toISOString() })\n        .eq('id', user.id)\n\n      if (error) throw error\n\n      // Mettre à jour le profil local\n      const { profile } = get()\n      if (profile) {\n        set({ profile: { ...profile, ...updates } })\n      }\n\n      return { error: null }\n    } catch (error) {\n      return { error: error as Error }\n    }\n  },\n\n  fetchProfile: async () => {\n    try {\n      const { user } = get()\n      if (!user) return\n\n      const { data, error } = await supabase\n        .from('users')\n        .select('*')\n        .eq('id', user.id)\n        .single()\n\n      if (error) throw error\n\n      set({ profile: data })\n    } catch (error) {\n      console.error('Erreur lors du chargement du profil:', error)\n    }\n  },\n\n  initialize: async () => {\n    try {\n      set({ loading: true })\n\n      // Récupérer la session actuelle\n      const { data: { session } } = await supabase.auth.getSession()\n      \n      if (session?.user) {\n        set({ user: session.user })\n        await get().fetchProfile()\n      }\n\n      // Écouter les changements d'authentification\n      supabase.auth.onAuthStateChange(async (event, session) => {\n        if (session?.user) {\n          set({ user: session.user })\n          await get().fetchProfile()\n        } else {\n          set({ user: null, profile: null })\n        }\n      })\n\n    } catch (error) {\n      console.error('Erreur lors de l\\'initialisation:', error)\n    } finally {\n      set({ loading: false })\n    }\n  }\n}))\n\n// Hook pour vérifier les permissions\nexport const usePermissions = () => {\n  const { profile } = useAuthStore()\n  \n  return {\n    isGuest: !profile || profile.role === 'guest',\n    isMember: profile?.role === 'member' || profile?.role === 'vip' || profile?.role === 'admin',\n    isVip: profile?.role === 'vip' || profile?.role === 'admin',\n    isAdmin: profile?.role === 'admin',\n    canAccessValidation: profile && (profile.statut !== 'grey' || profile.devis_demandes <= 3),\n    canAccessKits: profile?.role === 'member' || profile?.role === 'vip' || profile?.role === 'admin',\n    canAccessClub: profile?.role === 'vip' || profile?.role === 'admin',\n    canAccessCRM: profile?.role === 'admin'\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AAGA;;;AAaO,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EAAa,CAAC,KAAK,MAAQ,CAAC;QAC3D,MAAM;QACN,SAAS;QACT,SAAS;QAET,QAAQ,OAAO;YACb,IAAI;gBACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,gIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,aAAa,CAAC;oBAClD;oBACA,SAAS;wBACP,iBAAiB,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC;oBAC5D;gBACF;gBAEA,IAAI,OAAO,MAAM;gBAEjB,OAAO;oBAAE,OAAO;gBAAK;YACvB,EAAE,OAAO,OAAO;gBACd,OAAO;oBAAE,OAAO;gBAAe;YACjC;QACF;QAEA,SAAS;YACP,MAAM,gIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;YAC3B,IAAI;gBAAE,MAAM;gBAAM,SAAS;YAAK;QAClC;QAEA,eAAe,OAAO;YACpB,IAAI;gBACF,MAAM,EAAE,IAAI,EAAE,GAAG;gBACjB,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;gBAE3B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,gIAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,SACL,MAAM,CAAC;oBAAE,GAAG,OAAO;oBAAE,YAAY,IAAI,OAAO,WAAW;gBAAG,GAC1D,EAAE,CAAC,MAAM,KAAK,EAAE;gBAEnB,IAAI,OAAO,MAAM;gBAEjB,gCAAgC;gBAChC,MAAM,EAAE,OAAO,EAAE,GAAG;gBACpB,IAAI,SAAS;oBACX,IAAI;wBAAE,SAAS;4BAAE,GAAG,OAAO;4BAAE,GAAG,OAAO;wBAAC;oBAAE;gBAC5C;gBAEA,OAAO;oBAAE,OAAO;gBAAK;YACvB,EAAE,OAAO,OAAO;gBACd,OAAO;oBAAE,OAAO;gBAAe;YACjC;QACF;QAEA,cAAc;YACZ,IAAI;gBACF,MAAM,EAAE,IAAI,EAAE,GAAG;gBACjB,IAAI,CAAC,MAAM;gBAEX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,gIAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;gBAET,IAAI,OAAO,MAAM;gBAEjB,IAAI;oBAAE,SAAS;gBAAK;YACtB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wCAAwC;YACxD;QACF;QAEA,YAAY;YACV,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBAEpB,gCAAgC;gBAChC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,gIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;gBAE5D,IAAI,SAAS,MAAM;oBACjB,IAAI;wBAAE,MAAM,QAAQ,IAAI;oBAAC;oBACzB,MAAM,MAAM,YAAY;gBAC1B;gBAEA,6CAA6C;gBAC7C,gIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,OAAO;oBAC5C,IAAI,SAAS,MAAM;wBACjB,IAAI;4BAAE,MAAM,QAAQ,IAAI;wBAAC;wBACzB,MAAM,MAAM,YAAY;oBAC1B,OAAO;wBACL,IAAI;4BAAE,MAAM;4BAAM,SAAS;wBAAK;oBAClC;gBACF;YAEF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qCAAqC;YACrD,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;IACF,CAAC;AAGM,MAAM,iBAAiB;IAC5B,MAAM,EAAE,OAAO,EAAE,GAAG;IAEpB,OAAO;QACL,SAAS,CAAC,WAAW,QAAQ,IAAI,KAAK;QACtC,UAAU,SAAS,SAAS,YAAY,SAAS,SAAS,SAAS,SAAS,SAAS;QACrF,OAAO,SAAS,SAAS,SAAS,SAAS,SAAS;QACpD,SAAS,SAAS,SAAS;QAC3B,qBAAqB,WAAW,CAAC,QAAQ,MAAM,KAAK,UAAU,QAAQ,cAAc,IAAI,CAAC;QACzF,eAAe,SAAS,SAAS,YAAY,SAAS,SAAS,SAAS,SAAS,SAAS;QAC1F,eAAe,SAAS,SAAS,SAAS,SAAS,SAAS;QAC5D,cAAc,SAAS,SAAS;IAClC;AACF", "debugId": null}}, {"offset": {"line": 288, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/components/providers/AuthProvider.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useAuthStore } from '@/lib/stores/authStore'\n\ninterface AuthProviderProps {\n  children: React.ReactNode\n}\n\nexport default function AuthProvider({ children }: AuthProviderProps) {\n  const { initialize } = useAuthStore()\n\n  useEffect(() => {\n    initialize()\n  }, [initialize])\n\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AASe,SAAS,aAAa,EAAE,QAAQ,EAAqB;IAClE,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD;IAElC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAW;IAEf,qBAAO;kBAAG;;AACZ", "debugId": null}}]}