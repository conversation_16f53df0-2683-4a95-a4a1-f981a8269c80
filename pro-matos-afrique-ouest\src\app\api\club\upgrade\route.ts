import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { StripeService } from '@/lib/services/stripeService'

export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })

    // Vérifier l'authentification
    const { data: { session }, error: authError } = await supabase.auth.getSession()
    
    if (authError || !session) {
      return NextResponse.json(
        { error: 'Non authentifié' },
        { status: 401 }
      )
    }

    const { productId } = await request.json()

    if (!productId) {
      return NextResponse.json(
        { error: 'ID du produit requis' },
        { status: 400 }
      )
    }

    // Vérifier que le produit existe
    const product = StripeService.getProduct(productId)
    if (!product) {
      return NextResponse.json(
        { error: 'Produit non trouvé' },
        { status: 404 }
      )
    }

    // Récupérer les informations utilisateur
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('role, email, full_name')
      .eq('id', session.user.id)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Utilisateur non trouvé' },
        { status: 404 }
      )
    }

    // Vérifier si l'utilisateur n'est pas déjà VIP
    if (user.role === 'vip' || user.role === 'admin') {
      return NextResponse.json(
        { error: 'Vous êtes déjà membre VIP ou administrateur' },
        { status: 400 }
      )
    }

    // URLs de redirection
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'
    const successUrl = `${baseUrl}/club/upgrade-success?session_id={CHECKOUT_SESSION_ID}`
    const cancelUrl = `${baseUrl}/club?upgrade=cancelled`

    // Créer la session de checkout Stripe
    const checkoutResult = await StripeService.createCheckoutSession(
      productId,
      session.user.id,
      user.email,
      successUrl,
      cancelUrl
    )

    if (!checkoutResult.success) {
      return NextResponse.json(
        { error: checkoutResult.error || 'Erreur lors de la création du checkout' },
        { status: 500 }
      )
    }

    // Enregistrer la tentative d'upgrade en base (optionnel)
    await supabase
      .from('upgrade_attempts')
      .insert({
        user_id: session.user.id,
        product_id: productId,
        session_id: checkoutResult.sessionId,
        status: 'pending',
        created_at: new Date().toISOString()
      })
      .catch(error => {
        // Ne pas faire échouer si la table n'existe pas encore
        console.log('Info: Table upgrade_attempts non trouvée, ignoré')
      })

    return NextResponse.json({
      success: true,
      checkoutUrl: checkoutResult.checkoutUrl,
      sessionId: checkoutResult.sessionId,
      product: {
        name: product.name,
        price: product.price,
        currency: product.currency,
        interval: product.interval
      }
    })

  } catch (error) {
    console.error('Erreur API upgrade:', error)
    return NextResponse.json(
      { error: 'Erreur serveur' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })

    // Vérifier l'authentification
    const { data: { session }, error: authError } = await supabase.auth.getSession()
    
    if (authError || !session) {
      return NextResponse.json(
        { error: 'Non authentifié' },
        { status: 401 }
      )
    }

    // Récupérer tous les produits VIP disponibles
    const products = StripeService.getAllProducts()
    const savings = StripeService.calculateYearlySavings()

    // Récupérer le statut actuel de l'utilisateur
    const { data: user } = await supabase
      .from('users')
      .select('role, created_at')
      .eq('id', session.user.id)
      .single()

    return NextResponse.json({
      success: true,
      data: {
        products,
        savings,
        currentRole: user?.role || 'guest',
        memberSince: user?.created_at,
        canUpgrade: !['vip', 'admin'].includes(user?.role || 'guest')
      }
    })

  } catch (error) {
    console.error('Erreur API get upgrade info:', error)
    return NextResponse.json(
      { error: 'Erreur serveur' },
      { status: 500 }
    )
  }
}
