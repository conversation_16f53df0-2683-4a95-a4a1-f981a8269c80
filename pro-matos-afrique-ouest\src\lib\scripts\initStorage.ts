import { FileService } from '@/lib/services/fileService'

/**
 * Script d'initialisation du stockage Supabase
 * À exécuter une fois pour créer tous les buckets nécessaires
 */
export async function initializeSupabaseStorage() {
  console.log('🚀 Initialisation du stockage Supabase...')
  
  try {
    await FileService.createBuckets()
    console.log('✅ Tous les buckets ont été créés avec succès')
    return { success: true }
  } catch (error) {
    console.error('❌ Erreur lors de l\'initialisation:', error)
    return { success: false, error }
  }
}

// Fonction utilitaire pour vérifier l'état des buckets
export async function checkStorageStatus() {
  console.log('🔍 Vérification de l\'état du stockage...')
  
  const buckets = Object.values(FileService.BUCKETS)
  const status: Record<string, boolean> = {}
  
  for (const bucket of buckets) {
    try {
      // Tenter de lister les fichiers pour vérifier l'existence
      const { supabase } = await import('@/lib/supabase/client')
      const { data, error } = await supabase.storage.from(bucket).list('', { limit: 1 })
      
      status[bucket] = !error
      console.log(`${status[bucket] ? '✅' : '❌'} Bucket ${bucket}: ${status[bucket] ? 'OK' : error?.message}`)
    } catch (error) {
      status[bucket] = false
      console.log(`❌ Bucket ${bucket}: Erreur - ${error}`)
    }
  }
  
  return status
}

// Si ce script est exécuté directement
if (typeof window === 'undefined' && require.main === module) {
  initializeSupabaseStorage()
    .then(result => {
      if (result.success) {
        console.log('🎉 Initialisation terminée avec succès')
        process.exit(0)
      } else {
        console.error('💥 Échec de l\'initialisation')
        process.exit(1)
      }
    })
    .catch(error => {
      console.error('💥 Erreur fatale:', error)
      process.exit(1)
    })
}
