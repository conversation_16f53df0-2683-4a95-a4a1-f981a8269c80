import * as React from "react"
import { cn } from "@/lib/utils"

interface AvatarProps extends React.HTMLAttributes<HTMLDivElement> {
  src?: string
  alt?: string
  fallback?: string
}

const Avatar = React.forwardRef<HTMLDivElement, AvatarProps>(
  ({ className, src, alt, fallback, children, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(
        "relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full bg-gray-100",
        className
      )}
      {...props}
    >
      {src ? (
        <img src={src} alt={alt} className="aspect-square h-full w-full object-cover" />
      ) : (
        <div className="flex h-full w-full items-center justify-center text-sm font-medium text-gray-600">
          {fallback || children}
        </div>
      )}
    </div>
  )
)
Avatar.displayName = "Avatar"

// Pour compatibilité
const AvatarImage = ({ src, alt, ...props }: { src?: string; alt?: string }) => null
const AvatarFallback = ({ children, ...props }: { children: React.ReactNode }) => <>{children}</>

export { Avatar, AvatarImage, AvatarFallback }
