import type { <PERSON>ada<PERSON>, Viewport } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Pro Matos Afrique Ouest - Hub Professionnel Matériels Électriques",
  description: "La plateforme incontournable pour les professionnels du matériel électrique en Afrique de l'Ouest. Veille, conseil technique, prescriptions et réseau exclusif.",
  keywords: "matériel électrique, Afrique de l'Ouest, professionnel, conseil technique, prescription, veille marché",
  authors: [{ name: "Pro Matos Afrique Ouest" }],
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="fr" className="h-full">
      <body className={`${inter.className} h-full bg-gray-50 antialiased`}>
        <div id="root" className="h-full">
          {children}
        </div>
      </body>
    </html>
  );
}
