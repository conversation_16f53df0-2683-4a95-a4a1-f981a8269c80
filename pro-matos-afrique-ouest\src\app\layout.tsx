import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import AuthProvider from "@/components/providers/AuthProvider";
import { Toaster } from "sonner";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Pro Matos Afrique Ouest - Hub Professionnel Matériels Électriques",
  description: "La plateforme incontournable pour les professionnels du matériel électrique en Afrique de l'Ouest. Veille, conseil technique, prescriptions et réseau exclusif.",
  keywords: "matériel électrique, Afrique de l'Ouest, professionnel, conseil technique, prescription, veille marché",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="fr">
      <body className={`${inter.className} antialiased`}>
        <AuthProvider>
          {children}
          <Toaster position="top-right" />
        </AuthProvider>
      </body>
    </html>
  );
}
