import { MarketAlert, StockUpdate, PriceHistory, TrainingEvent, NewsUpdate, Product } from '@/lib/supabase'

// Service pour simuler les données temps réel du Hub d'Information
export class HubService {
  
  // Simulation des alertes marché en temps réel
  static generateMarketAlerts(): MarketAlert[] {
    return [
      {
        id: '1',
        type: 'stock_low',
        title: 'Stock Faible - Disjoncteurs Schneider',
        message: 'Les disjoncteurs Schneider Electric C60N 32A sont en stock faible chez 3 fournisseurs à Abidjan',
        severity: 'medium',
        category: 'Protection électrique',
        product_id: 'prod_001',
        affected_regions: ['Abidjan', 'Bouaké'],
        is_active: true,
        created_at: new Date(Date.now() - 2 * 60 * 1000).toISOString(), // Il y a 2 minutes
      },
      {
        id: '2',
        type: 'price_change',
        title: 'Baisse de Prix - Câbles Nexans',
        message: 'Réduction de 15% sur les câbles Nexans U1000R2V 3x2.5mm² chez ElectroDistrib',
        severity: 'low',
        category: 'Câblage',
        affected_regions: ['Dakar', 'Thiès'],
        is_active: true,
        created_at: new Date(Date.now() - 15 * 60 * 1000).toISOString(), // Il y a 15 minutes
      },
      {
        id: '3',
        type: 'new_product',
        title: 'Nouveau Produit - Onduleurs APC',
        message: 'Arrivée des nouveaux onduleurs APC Smart-UPS 3000VA avec technologie lithium',
        severity: 'high',
        category: 'Alimentation',
        affected_regions: ['Accra', 'Kumasi', 'Abidjan'],
        is_active: true,
        created_at: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // Il y a 30 minutes
      },
      {
        id: '4',
        type: 'stock_out',
        title: 'Rupture de Stock - Transformateurs',
        message: 'Rupture totale des transformateurs 400/230V 63kVA dans la région de Bamako',
        severity: 'critical',
        category: 'Transformation',
        affected_regions: ['Bamako', 'Ségou'],
        is_active: true,
        created_at: new Date(Date.now() - 45 * 60 * 1000).toISOString(), // Il y a 45 minutes
      },
      {
        id: '5',
        type: 'market_trend',
        title: 'Tendance Marché - Énergie Solaire',
        message: 'Forte demande pour les équipements solaires : +40% ce trimestre en Afrique de l\'Ouest',
        severity: 'medium',
        category: 'Énergie renouvelable',
        affected_regions: ['Ouagadougou', 'Niamey', 'Abidjan'],
        is_active: true,
        created_at: new Date(Date.now() - 60 * 60 * 1000).toISOString(), // Il y a 1 heure
      }
    ]
  }

  // Simulation des mises à jour de stock
  static generateStockUpdates(): StockUpdate[] {
    return [
      {
        id: '1',
        product_id: 'prod_001',
        supplier_id: 'sup_001',
        previous_quantity: 150,
        current_quantity: 45,
        location: 'Abidjan - Zone Industrielle',
        update_type: 'decrease',
        created_at: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
      },
      {
        id: '2',
        product_id: 'prod_002',
        supplier_id: 'sup_002',
        previous_quantity: 0,
        current_quantity: 200,
        location: 'Dakar - Entrepôt Central',
        update_type: 'restock',
        created_at: new Date(Date.now() - 10 * 60 * 1000).toISOString(),
      },
      {
        id: '3',
        product_id: 'prod_003',
        supplier_id: 'sup_003',
        previous_quantity: 25,
        current_quantity: 0,
        location: 'Bamako - Dépôt Nord',
        update_type: 'out_of_stock',
        created_at: new Date(Date.now() - 20 * 60 * 1000).toISOString(),
      }
    ]
  }

  // Simulation des événements de formation
  static generateTrainingEvents(): TrainingEvent[] {
    return [
      {
        id: '1',
        title: 'Certification Schneider Electric - Installations BT',
        description: 'Formation certifiante sur les installations basse tension selon les normes NF C 15-100',
        type: 'certification',
        instructor: 'Ing. Kouassi Yao (Schneider Electric)',
        start_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // Dans 7 jours
        end_date: new Date(Date.now() + 9 * 24 * 60 * 60 * 1000).toISOString(), // Dans 9 jours
        location: 'Abidjan - Centre de Formation Schneider',
        is_virtual: false,
        max_participants: 20,
        current_participants: 12,
        membership_required: 'silver',
        registration_fee: 150000, // 150,000 FCFA
        status: 'upcoming',
        created_at: new Date().toISOString(),
      },
      {
        id: '2',
        title: 'Webinaire - Énergie Solaire et Stockage',
        description: 'Les dernières innovations en matière de panneaux solaires et batteries lithium',
        type: 'webinar',
        instructor: 'Dr. Aminata Traoré (Expert Énergie)',
        start_date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(), // Dans 3 jours
        end_date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000 + 2 * 60 * 60 * 1000).toISOString(), // 2h plus tard
        location: 'En ligne',
        is_virtual: true,
        max_participants: 100,
        current_participants: 67,
        membership_required: 'bronze',
        registration_fee: 0,
        status: 'upcoming',
        created_at: new Date().toISOString(),
      },
      {
        id: '3',
        title: 'Atelier Pratique - Maintenance Préventive',
        description: 'Techniques de maintenance préventive pour équipements électriques industriels',
        type: 'workshop',
        instructor: 'Équipe Technique ABB',
        start_date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(), // Dans 14 jours
        end_date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000 + 6 * 60 * 60 * 1000).toISOString(), // 6h plus tard
        location: 'Dakar - Centre Technique ABB',
        is_virtual: false,
        max_participants: 15,
        current_participants: 8,
        membership_required: 'gold',
        registration_fee: 200000, // 200,000 FCFA
        status: 'upcoming',
        created_at: new Date().toISOString(),
      }
    ]
  }

  // Simulation des actualités du secteur
  static generateNewsUpdates(): NewsUpdate[] {
    return [
      {
        id: '1',
        title: 'Nouvelle réglementation sur les installations photovoltaïques en Côte d\'Ivoire',
        content: 'Le gouvernement ivoirien vient d\'adopter de nouvelles normes pour les installations solaires...',
        summary: 'Nouvelles normes techniques et administratives pour le photovoltaïque',
        category: 'regulation',
        author: 'Ministère de l\'Énergie CI',
        source_url: 'https://energie.gouv.ci/nouvelles-normes-pv',
        image_url: '/images/news/regulation-pv.jpg',
        tags: ['réglementation', 'photovoltaïque', 'côte d\'ivoire'],
        is_featured: true,
        published_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // Il y a 2 heures
        created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      },
      {
        id: '2',
        title: 'Schneider Electric ouvre un nouveau centre de distribution à Accra',
        content: 'Le leader mondial de la gestion de l\'énergie inaugure son plus grand entrepôt...',
        summary: 'Nouveau hub logistique pour améliorer la disponibilité des produits',
        category: 'company',
        author: 'Schneider Electric',
        source_url: 'https://schneider-electric.com/accra-center',
        image_url: '/images/news/schneider-accra.jpg',
        tags: ['schneider electric', 'ghana', 'logistique'],
        is_featured: false,
        published_at: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // Il y a 6 heures
        created_at: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
      },
      {
        id: '3',
        title: 'Innovation : Nouveaux compteurs intelligents pour l\'Afrique de l\'Ouest',
        content: 'Une startup sénégalaise développe des compteurs connectés adaptés aux réseaux locaux...',
        summary: 'Technologie IoT adaptée aux défis énergétiques régionaux',
        category: 'technology',
        author: 'TechAfrique',
        source_url: 'https://techafrique.com/compteurs-intelligents',
        image_url: '/images/news/smart-meters.jpg',
        tags: ['innovation', 'iot', 'compteurs', 'sénégal'],
        is_featured: true,
        published_at: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(), // Il y a 12 heures
        created_at: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),
      }
    ]
  }

  // Simulation des produits avec données temps réel
  static generateProducts(): Product[] {
    return [
      {
        id: 'prod_001',
        name: 'Disjoncteur C60N 32A',
        description: 'Disjoncteur modulaire Schneider Electric C60N courbe C 32A',
        category: 'Protection électrique',
        brand: 'Schneider Electric',
        model: 'C60N-C32',
        price: 45000, // 45,000 FCFA
        stock_quantity: 45,
        technical_specs: {
          'Courant nominal': '32A',
          'Courbe de déclenchement': 'C',
          'Pouvoir de coupure': '6kA',
          'Nombre de pôles': '1',
          'Norme': 'NF EN 60898'
        },
        images: ['/images/products/c60n-32a.jpg'],
        qr_code: 'QR_C60N_32A_001',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        id: 'prod_002',
        name: 'Câble U1000R2V 3x2.5mm²',
        description: 'Câble d\'énergie Nexans U1000R2V 3 conducteurs 2.5mm²',
        category: 'Câblage',
        brand: 'Nexans',
        model: 'U1000R2V-3x2.5',
        price: 2500, // 2,500 FCFA/mètre
        stock_quantity: 200,
        technical_specs: {
          'Section': '3x2.5mm²',
          'Tension nominale': '1000V',
          'Type d\'isolation': 'PRC',
          'Température de service': '-40°C à +90°C',
          'Norme': 'NF C 32-321'
        },
        images: ['/images/products/cable-u1000r2v.jpg'],
        qr_code: 'QR_U1000R2V_3x2.5_002',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }
    ]
  }

  // Méthode pour simuler les mises à jour temps réel
  static subscribeToRealTimeUpdates(callback: (data: any) => void) {
    // Simulation d'un WebSocket ou Server-Sent Events
    const interval = setInterval(() => {
      const updateType = Math.random()
      
      if (updateType < 0.3) {
        // Nouvelle alerte marché
        callback({
          type: 'market_alert',
          data: this.generateMarketAlerts()[0]
        })
      } else if (updateType < 0.6) {
        // Mise à jour de stock
        callback({
          type: 'stock_update',
          data: this.generateStockUpdates()[0]
        })
      } else {
        // Nouvelle actualité
        callback({
          type: 'news_update',
          data: this.generateNewsUpdates()[0]
        })
      }
    }, 30000) // Toutes les 30 secondes

    return () => clearInterval(interval)
  }
}
