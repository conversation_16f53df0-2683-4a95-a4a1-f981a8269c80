'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Users, 
  TrendingUp, 
  TrendingDown,
  ArrowLeft,
  Search,
  Filter,
  MoreHorizontal,
  UserCheck,
  UserX,
  Crown,
  Shield,
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { toast } from 'sonner'
import { useAuthStore } from '@/lib/stores/authStore'
import { useCRMStore } from '@/lib/stores/crmStore'
import { supabase } from '@/lib/supabase/client'
import GlobalNavigation from '@/components/navigation/GlobalNavigation'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

interface User {
  id: string
  email: string
  role: 'guest' | 'member' | 'vip' | 'admin'
  full_name: string | null
  company: string | null
  devis_demandes: number
  achats: number
  statut: 'white' | 'grey' | 'black'
  created_at: string
  updated_at: string
}

export default function CRMPage() {
  const { user: currentUser } = useAuthStore()
  const { users, validations, loading, fetchUsers, fetchValidations } = useCRMStore()
  const [searchQuery, setSearchQuery] = useState('')
  const [roleFilter, setRoleFilter] = useState<string>('all')
  const [statutFilter, setStatutFilter] = useState<string>('all')
  const [updatingUser, setUpdatingUser] = useState<string | null>(null)

  useEffect(() => {
    if (currentUser?.role === 'admin') {
      fetchUsers()
      fetchValidations()
    }
  }, [currentUser, fetchUsers, fetchValidations])

  // Redirection si pas admin
  if (currentUser?.role !== 'admin') {
    return (
      <>
        <GlobalNavigation />
        <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center">
          <Card className="max-w-md">
            <CardContent className="p-8 text-center">
              <Shield className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Accès Restreint</h2>
              <p className="text-gray-600 mb-4">Cette page est réservée aux administrateurs.</p>
              <Link href="/hub">
                <Button>Retour au Hub</Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </>
    )
  }

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         (user.full_name?.toLowerCase().includes(searchQuery.toLowerCase())) ||
                         (user.company?.toLowerCase().includes(searchQuery.toLowerCase()))
    const matchesRole = roleFilter === 'all' || user.role === roleFilter
    const matchesStatut = statutFilter === 'all' || user.statut === statutFilter
    return matchesSearch && matchesRole && matchesStatut
  })

  const updateUserRole = async (userId: string, newRole: 'guest' | 'member' | 'vip' | 'admin') => {
    setUpdatingUser(userId)
    toast.loading('Mise à jour du rôle...', { id: 'update-role' })

    try {
      const { error } = await supabase
        .from('users')
        .update({ role: newRole, updated_at: new Date().toISOString() })
        .eq('id', userId)

      if (error) throw error

      await fetchUsers()
      toast.success('Rôle mis à jour avec succès', { id: 'update-role' })
    } catch (error: any) {
      console.error('Erreur lors de la mise à jour du rôle:', error)
      toast.error('Erreur lors de la mise à jour', { id: 'update-role' })
    } finally {
      setUpdatingUser(null)
    }
  }

  const updateUserStatut = async (userId: string, newStatut: 'white' | 'grey' | 'black') => {
    setUpdatingUser(userId)
    toast.loading('Mise à jour du statut...', { id: 'update-statut' })

    try {
      const { error } = await supabase
        .from('users')
        .update({ statut: newStatut, updated_at: new Date().toISOString() })
        .eq('id', userId)

      if (error) throw error

      await fetchUsers()
      toast.success('Statut mis à jour avec succès', { id: 'update-statut' })
    } catch (error: any) {
      console.error('Erreur lors de la mise à jour du statut:', error)
      toast.error('Erreur lors de la mise à jour', { id: 'update-statut' })
    } finally {
      setUpdatingUser(null)
    }
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin': return 'bg-red-100 text-red-800'
      case 'vip': return 'bg-amber-100 text-amber-800'
      case 'member': return 'bg-blue-100 text-blue-800'
      case 'guest': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatutColor = (statut: string) => {
    switch (statut) {
      case 'white': return 'bg-green-100 text-green-800'
      case 'grey': return 'bg-yellow-100 text-yellow-800'
      case 'black': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin': return <Shield className="h-4 w-4" />
      case 'vip': return <Crown className="h-4 w-4" />
      case 'member': return <UserCheck className="h-4 w-4" />
      case 'guest': return <Users className="h-4 w-4" />
      default: return <Users className="h-4 w-4" />
    }
  }

  const stats = {
    total: users.length,
    admins: users.filter(u => u.role === 'admin').length,
    vips: users.filter(u => u.role === 'vip').length,
    members: users.filter(u => u.role === 'member').length,
    guests: users.filter(u => u.role === 'guest').length,
    greyUsers: users.filter(u => u.statut === 'grey').length,
    blackUsers: users.filter(u => u.statut === 'black').length,
    totalValidations: validations.length,
    pendingValidations: validations.filter(v => v.status === 'En cours').length
  }

  return (
    <>
      <GlobalNavigation />
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
        {/* Header */}
        <div className="bg-white border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Link
                  href="/hub"
                  className="flex items-center space-x-2 text-slate-700 hover:text-blue-600 transition-colors"
                >
                  <ArrowLeft className="h-5 w-5" />
                  <span>Retour au Hub</span>
                </Link>
                <div className="h-6 w-px bg-gray-300" />
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 flex items-center space-x-2">
                    <Users className="h-6 w-6" />
                    <span>Administration CRM</span>
                  </h1>
                  <p className="text-gray-600">Gestion des utilisateurs et suivi des activités</p>
                </div>
              </div>
              <Badge className="bg-red-100 text-red-800">
                <Shield className="h-4 w-4 mr-1" />
                Admin
              </Badge>
            </div>
          </div>
        </div>

        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Statistiques */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <Users className="h-8 w-8 text-blue-500" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Total Utilisateurs</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <Crown className="h-8 w-8 text-amber-500" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Membres VIP</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.vips}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <AlertTriangle className="h-8 w-8 text-yellow-500" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Utilisateurs Gris</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.greyUsers}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <Clock className="h-8 w-8 text-green-500" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Validations en Cours</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.pendingValidations}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Filtres */}
          <Card className="mb-8">
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Rechercher par email, nom ou entreprise..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Filter className="h-4 w-4 text-gray-500" />
                  <select
                    value={roleFilter}
                    onChange={(e) => setRoleFilter(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">Tous les rôles</option>
                    <option value="admin">Admin</option>
                    <option value="vip">VIP</option>
                    <option value="member">Membre</option>
                    <option value="guest">Invité</option>
                  </select>
                  <select
                    value={statutFilter}
                    onChange={(e) => setStatutFilter(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">Tous les statuts</option>
                    <option value="white">Blanc</option>
                    <option value="grey">Gris</option>
                    <option value="black">Noir</option>
                  </select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Tableau des utilisateurs */}
          <Card>
            <CardHeader>
              <CardTitle>Utilisateurs ({filteredUsers.length})</CardTitle>
              <CardDescription>
                Gérez les rôles et statuts des utilisateurs
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500" />
                  <span className="ml-2 text-gray-600">Chargement...</span>
                </div>
              ) : filteredUsers.length === 0 ? (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">Aucun utilisateur trouvé</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-gray-200">
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Utilisateur</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Rôle</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Statut</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Devis</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Achats</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Inscription</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredUsers.map((user) => (
                        <motion.tr
                          key={user.id}
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          className="border-b border-gray-100 hover:bg-gray-50"
                        >
                          <td className="py-4 px-4">
                            <div>
                              <p className="font-medium text-gray-900">{user.full_name || 'Non renseigné'}</p>
                              <p className="text-sm text-gray-600">{user.email}</p>
                              {user.company && (
                                <p className="text-xs text-gray-500">{user.company}</p>
                              )}
                            </div>
                          </td>
                          <td className="py-4 px-4">
                            <Badge className={getRoleColor(user.role)}>
                              <div className="flex items-center space-x-1">
                                {getRoleIcon(user.role)}
                                <span>{user.role.toUpperCase()}</span>
                              </div>
                            </Badge>
                          </td>
                          <td className="py-4 px-4">
                            <Badge className={getStatutColor(user.statut)}>
                              {user.statut.toUpperCase()}
                            </Badge>
                          </td>
                          <td className="py-4 px-4">
                            <span className="text-gray-900">{user.devis_demandes}</span>
                          </td>
                          <td className="py-4 px-4">
                            <span className="text-gray-900">{user.achats}</span>
                          </td>
                          <td className="py-4 px-4">
                            <span className="text-sm text-gray-600">
                              {new Date(user.created_at).toLocaleDateString('fr-FR')}
                            </span>
                          </td>
                          <td className="py-4 px-4">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button 
                                  variant="ghost" 
                                  size="sm"
                                  disabled={updatingUser === user.id}
                                >
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() => updateUserRole(user.id, 'vip')}>
                                  <TrendingUp className="h-4 w-4 mr-2" />
                                  Promouvoir VIP
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => updateUserRole(user.id, 'member')}>
                                  <UserCheck className="h-4 w-4 mr-2" />
                                  Rétrograder Membre
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => updateUserStatut(user.id, 'grey')}>
                                  <AlertTriangle className="h-4 w-4 mr-2" />
                                  Marquer Gris
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => updateUserStatut(user.id, 'black')}>
                                  <UserX className="h-4 w-4 mr-2" />
                                  Marquer Noir
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => updateUserStatut(user.id, 'white')}>
                                  <CheckCircle className="h-4 w-4 mr-2" />
                                  Réhabiliter
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </td>
                        </motion.tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </main>
      </div>
    </>
  )
}
