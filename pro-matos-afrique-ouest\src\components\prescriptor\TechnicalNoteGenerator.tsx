'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  FileText,
  Calculator,
  AlertTriangle,
  CheckCircle,
  Info,
  Lightbulb,
  Download,
  Copy,
  Edit3,
  Save,
  X
} from 'lucide-react'
import { useStore } from '@/store/useStore'
import { formatDate } from '@/lib/utils'

interface TechnicalNoteGeneratorProps {
  projectId: string
  onClose?: () => void
}

export default function TechnicalNoteGenerator({ projectId, onClose }: TechnicalNoteGeneratorProps) {
  const { addTechnicalNote } = useStore()
  const [selectedNoteType, setSelectedNoteType] = useState<'calculation' | 'specification' | 'compliance' | 'recommendation' | 'warning'>('calculation')
  const [noteTitle, setNoteTitle] = useState('')
  const [noteContent, setNoteContent] = useState('')
  const [templateSection, setTemplateSection] = useState('')
  const [referencedProducts, setReferencedProducts] = useState<string[]>([])
  const [referencedStandards, setReferencedStandards] = useState<string[]>([])
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedNotes, setGeneratedNotes] = useState<any[]>([])

  const noteTypes = [
    {
      id: 'calculation',
      label: 'Note de Calcul',
      description: 'Détails des calculs techniques et dimensionnements',
      icon: <Calculator className="h-5 w-5" />,
      color: 'from-blue-500 to-blue-600',
      bgColor: 'bg-blue-50 border-blue-200'
    },
    {
      id: 'specification',
      label: 'Spécification Technique',
      description: 'Spécifications détaillées des équipements',
      icon: <FileText className="h-5 w-5" />,
      color: 'from-green-500 to-green-600',
      bgColor: 'bg-green-50 border-green-200'
    },
    {
      id: 'compliance',
      label: 'Note de Conformité',
      description: 'Vérification de conformité aux normes',
      icon: <CheckCircle className="h-5 w-5" />,
      color: 'from-purple-500 to-purple-600',
      bgColor: 'bg-purple-50 border-purple-200'
    },
    {
      id: 'recommendation',
      label: 'Recommandation',
      description: 'Conseils et bonnes pratiques',
      icon: <Lightbulb className="h-5 w-5" />,
      color: 'from-amber-500 to-amber-600',
      bgColor: 'bg-amber-50 border-amber-200'
    },
    {
      id: 'warning',
      label: 'Avertissement',
      description: 'Points d\'attention et précautions',
      icon: <AlertTriangle className="h-5 w-5" />,
      color: 'from-red-500 to-red-600',
      bgColor: 'bg-red-50 border-red-200'
    }
  ]

  const predefinedNotes = {
    calculation: [
      {
        title: 'Calcul de la puissance totale installée',
        content: 'La puissance totale installée a été calculée selon la formule :\nP_total = Σ(P_éclairage + P_prises + P_force) × facteur_simultanéité\n\nFacteurs appliqués :\n- Facteur de simultanéité : 0.8\n- Coefficient de sécurité : 1.2\n- Facteur de puissance : 0.85',
        section: 'Calculs de puissance',
        standards: ['NF C 15-100', 'Guide UTE C 15-105']
      },
      {
        title: 'Dimensionnement des câbles principaux',
        content: 'Le dimensionnement des câbles a été effectué selon les critères :\n1. Intensité admissible (méthode de référence B)\n2. Chute de tension (≤ 3% pour l\'éclairage, ≤ 5% pour la force)\n3. Contraintes thermiques\n\nSection retenue : 185 mm² Cu pour le câble principal',
        section: 'Dimensionnement câbles',
        standards: ['NF C 15-100', 'IEC 60364-5-52']
      }
    ],
    specification: [
      {
        title: 'Spécifications des disjoncteurs de protection',
        content: 'Disjoncteurs modulaires conformes à la norme NF EN 60898 :\n- Courbe de déclenchement : Type C\n- Pouvoir de coupure : 6 kA minimum\n- Classe de limitation : 3\n- Marquage CE obligatoire\n\nMarques recommandées : Schneider Electric, Legrand, ABB',
        section: 'Protection électrique',
        standards: ['NF EN 60898', 'NF C 61-410']
      }
    ],
    compliance: [
      {
        title: 'Conformité à la norme NF C 15-100',
        content: 'L\'installation respecte les exigences de la norme NF C 15-100 :\n✓ Protection contre les contacts directs et indirects\n✓ Protection contre les surintensités\n✓ Sectionnement et commande\n✓ Choix et mise en œuvre des matériels\n✓ Vérifications et essais',
        section: 'Conformité réglementaire',
        standards: ['NF C 15-100']
      }
    ],
    recommendation: [
      {
        title: 'Recommandations de mise en œuvre',
        content: 'Pour optimiser la sécurité et la fiabilité :\n• Prévoir un tableau de répartition par étage\n• Installer des prises RJ45 dans chaque bureau\n• Prévoir l\'éclairage de sécurité selon ERP\n• Mettre en place un système de GTB\n• Prévoir la maintenance préventive',
        section: 'Recommandations générales',
        standards: ['Guide UTE', 'Règlement ERP']
      }
    ],
    warning: [
      {
        title: 'Points d\'attention particuliers',
        content: 'ATTENTION - Points critiques à respecter :\n⚠️ Vérifier la compatibilité électromagnétique\n⚠️ Respecter les distances de sécurité\n⚠️ Prévoir la ventilation des locaux techniques\n⚠️ Former le personnel à l\'exploitation\n⚠️ Tenir à jour la documentation',
        section: 'Sécurité',
        standards: ['Directive CEM', 'Code du travail']
      }
    ]
  }

  const generateAutomaticNotes = () => {
    setIsGenerating(true)
    
    setTimeout(() => {
      const notes = predefinedNotes[selectedNoteType] || []
      const generatedList = notes.map((note, index) => ({
        id: `note_${Date.now()}_${index}`,
        project_id: projectId,
        title: note.title,
        content: note.content,
        note_type: selectedNoteType,
        auto_generated: true,
        template_section: note.section,
        products_referenced: [],
        standards_referenced: note.standards,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }))
      
      setGeneratedNotes(generatedList)
      setIsGenerating(false)
    }, 2000)
  }

  const saveNote = (note: any) => {
    addTechnicalNote(note)
    setGeneratedNotes(prev => prev.filter(n => n.id !== note.id))
  }

  const saveCustomNote = () => {
    if (!noteTitle.trim() || !noteContent.trim()) return

    const customNote = {
      id: `note_${Date.now()}`,
      project_id: projectId,
      title: noteTitle,
      content: noteContent,
      note_type: selectedNoteType,
      auto_generated: false,
      template_section: templateSection,
      products_referenced: referencedProducts,
      standards_referenced: referencedStandards,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }

    addTechnicalNote(customNote)
    
    // Reset form
    setNoteTitle('')
    setNoteContent('')
    setTemplateSection('')
    setReferencedProducts([])
    setReferencedStandards([])
  }

  const currentNoteType = noteTypes.find(type => type.id === selectedNoteType)

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 max-w-6xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <div className={`w-12 h-12 bg-gradient-to-r ${currentNoteType?.color} rounded-lg flex items-center justify-center text-white`}>
            {currentNoteType?.icon}
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Générateur de Notes Techniques</h2>
            <p className="text-gray-600">Création automatisée de notes techniques professionnelles</p>
          </div>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        )}
      </div>

      {/* Sélection du type de note */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Type de Note Technique</h3>
        <div className="grid md:grid-cols-5 gap-4">
          {noteTypes.map((type) => (
            <button
              key={type.id}
              onClick={() => setSelectedNoteType(type.id as any)}
              className={`p-4 rounded-lg border-2 transition-all ${
                selectedNoteType === type.id
                  ? `${type.bgColor} border-current`
                  : 'bg-white border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className={`w-8 h-8 bg-gradient-to-r ${type.color} rounded-lg flex items-center justify-center text-white mx-auto mb-2`}>
                {type.icon}
              </div>
              <h4 className="font-medium text-sm text-gray-900 mb-1">{type.label}</h4>
              <p className="text-xs text-gray-600">{type.description}</p>
            </button>
          ))}
        </div>
      </div>

      <div className="grid lg:grid-cols-2 gap-8">
        {/* Génération automatique */}
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Génération Automatique</h3>
            <div className={`p-4 rounded-lg border ${currentNoteType?.bgColor}`}>
              <p className="text-sm text-gray-700 mb-4">
                Générez automatiquement des notes techniques standardisées pour {currentNoteType?.label.toLowerCase()}.
              </p>
              <button
                onClick={generateAutomaticNotes}
                disabled={isGenerating}
                className="btn-premium disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isGenerating ? (
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Génération...</span>
                  </div>
                ) : (
                  <div className="flex items-center space-x-2">
                    <FileText className="h-4 w-4" />
                    <span>Générer les Notes</span>
                  </div>
                )}
              </button>
            </div>
          </div>

          {/* Notes générées */}
          {generatedNotes.length > 0 && (
            <div>
              <h4 className="font-semibold text-gray-900 mb-3">Notes Générées</h4>
              <div className="space-y-3">
                {generatedNotes.map((note) => (
                  <div key={note.id} className="bg-gray-50 rounded-lg p-4 border">
                    <div className="flex items-start justify-between mb-2">
                      <h5 className="font-medium text-gray-900">{note.title}</h5>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => saveNote(note)}
                          className="p-1 text-green-600 hover:text-green-700"
                          title="Sauvegarder"
                        >
                          <Save className="h-4 w-4" />
                        </button>
                        <button
                          className="p-1 text-blue-600 hover:text-blue-700"
                          title="Copier"
                        >
                          <Copy className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 whitespace-pre-line">{note.content}</p>
                    <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500">
                      <span>Section: {note.template_section}</span>
                      <span>Normes: {note.standards_referenced.join(', ')}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Création manuelle */}
        <div className="space-y-6">
          <h3 className="text-lg font-semibold text-gray-900">Création Manuelle</h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Titre de la note
              </label>
              <input
                type="text"
                value={noteTitle}
                onChange={(e) => setNoteTitle(e.target.value)}
                placeholder="Ex: Calcul de la section des câbles"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Section du template
              </label>
              <input
                type="text"
                value={templateSection}
                onChange={(e) => setTemplateSection(e.target.value)}
                placeholder="Ex: Dimensionnement électrique"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Contenu de la note
              </label>
              <textarea
                value={noteContent}
                onChange={(e) => setNoteContent(e.target.value)}
                rows={8}
                placeholder="Rédigez le contenu technique de votre note..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Normes référencées (séparées par des virgules)
              </label>
              <input
                type="text"
                value={referencedStandards.join(', ')}
                onChange={(e) => setReferencedStandards(e.target.value.split(',').map(s => s.trim()).filter(Boolean))}
                placeholder="Ex: NF C 15-100, IEC 60364"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400"
              />
            </div>

            <button
              onClick={saveCustomNote}
              disabled={!noteTitle.trim() || !noteContent.trim()}
              className="w-full btn-premium disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Save className="h-4 w-4 mr-2" />
              Sauvegarder la Note
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
