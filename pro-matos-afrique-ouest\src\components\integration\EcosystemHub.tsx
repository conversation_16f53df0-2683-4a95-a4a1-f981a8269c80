'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  ArrowRight,
  Database,
  Users,
  FileText,
  Crown,
  Zap,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  Star,
  Link as LinkIcon,
  ExternalLink,
  RefreshCw
} from 'lucide-react'
import Link from 'next/link'

interface EcosystemHubProps {
  currentModule: 'hub' | 'expert' | 'prescriptor' | 'club'
  className?: string
}

export default function EcosystemHub({ currentModule, className = '' }: EcosystemHubProps) {
  const [crossModuleData, setCrossModuleData] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)

  const modules = {
    hub: {
      name: 'Hub Information',
      icon: <Database className="h-6 w-6" />,
      color: 'from-blue-500 to-blue-600',
      description: 'Centre d\'information temps réel',
      status: 'active',
      lastUpdate: '2 min',
      metrics: { alerts: 12, updates: 47, trends: 8 }
    },
    expert: {
      name: 'Expert Conseil',
      icon: <Users className="h-6 w-6" />,
      color: 'from-green-500 to-green-600',
      description: 'Consultation et validation technique',
      status: 'active',
      lastUpdate: '5 min',
      metrics: { consultations: 23, validations: 15, experts: 8 }
    },
    prescriptor: {
      name: 'Module Prescripteur',
      icon: <FileText className="h-6 w-6" />,
      color: 'from-purple-500 to-purple-600',
      description: 'Templates et certification',
      status: 'active',
      lastUpdate: '1 min',
      metrics: { templates: 47, projects: 23, certificates: 89 }
    },
    club: {
      name: 'Club Membre',
      icon: <Crown className="h-6 w-6" />,
      color: 'from-amber-500 to-amber-600',
      description: 'Réseau exclusif professionnel',
      status: 'premium',
      lastUpdate: '3 min',
      metrics: { members: 1234, events: 12, benefits: 25 }
    }
  }

  const integrationFlows = [
    {
      from: 'hub',
      to: 'expert',
      title: 'Alerte → Consultation',
      description: 'Alertes techniques redirigées vers experts',
      count: 15,
      trend: '+23%',
      examples: [
        'Panne transformateur → Expert électrique',
        'Nouveau produit → Validation technique',
        'Norme mise à jour → Conseil réglementaire'
      ]
    },
    {
      from: 'expert',
      to: 'prescriptor',
      title: 'Validation → Template',
      description: 'Conseils d\'experts intégrés aux templates',
      count: 28,
      trend: '+45%',
      examples: [
        'Conseil technique → Template personnalisé',
        'Validation produit → Calcul automatisé',
        'Recommandation → Standard imposé'
      ]
    },
    {
      from: 'prescriptor',
      to: 'club',
      title: 'Certification → Réseau',
      description: 'Projets certifiés partagés au club',
      count: 34,
      trend: '+67%',
      examples: [
        'Projet certifié → Showcase membre',
        'Template validé → Formation exclusive',
        'Innovation → Partage réseau'
      ]
    },
    {
      from: 'club',
      to: 'hub',
      title: 'Réseau → Information',
      description: 'Insights membres alimentent le hub',
      count: 42,
      trend: '+89%',
      examples: [
        'Retour terrain → Alerte précoce',
        'Tendance marché → Information exclusive',
        'Innovation membre → Veille technologique'
      ]
    }
  ]

  const crossModuleActions = {
    hub: [
      {
        title: 'Consulter un Expert',
        description: 'Obtenir une validation technique sur cette alerte',
        action: '/expert?source=hub&alert=',
        icon: <Users className="h-4 w-4" />,
        color: 'bg-green-100 text-green-700'
      },
      {
        title: 'Créer Template',
        description: 'Transformer cette info en template de prescription',
        action: '/prescriptor?action=create&source=hub',
        icon: <FileText className="h-4 w-4" />,
        color: 'bg-purple-100 text-purple-700'
      }
    ],
    expert: [
      {
        title: 'Voir Alertes Hub',
        description: 'Consulter les dernières alertes techniques',
        action: '/hub?filter=technical',
        icon: <Database className="h-4 w-4" />,
        color: 'bg-blue-100 text-blue-700'
      },
      {
        title: 'Générer Template',
        description: 'Créer un template basé sur cette consultation',
        action: '/prescriptor?action=generate&consultation=',
        icon: <FileText className="h-4 w-4" />,
        color: 'bg-purple-100 text-purple-700'
      }
    ],
    prescriptor: [
      {
        title: 'Consulter Expert',
        description: 'Valider ce template avec un expert',
        action: '/expert?mode=validation&template=',
        icon: <Users className="h-4 w-4" />,
        color: 'bg-green-100 text-green-700'
      },
      {
        title: 'Partager au Club',
        description: 'Présenter ce projet au réseau membre',
        action: '/club?action=share&project=',
        icon: <Crown className="h-4 w-4" />,
        color: 'bg-amber-100 text-amber-700'
      }
    ],
    club: [
      {
        title: 'Alertes Exclusives',
        description: 'Accéder aux informations réservées aux membres',
        action: '/hub?level=premium',
        icon: <Database className="h-4 w-4" />,
        color: 'bg-blue-100 text-blue-700'
      },
      {
        title: 'Expert Dédié',
        description: 'Consultation prioritaire avec expert membre',
        action: '/expert?priority=member',
        icon: <Users className="h-4 w-4" />,
        color: 'bg-green-100 text-green-700'
      }
    ]
  }

  const recentIntegrations = [
    {
      type: 'hub_to_expert',
      title: 'Alerte transformateur → Consultation urgente',
      time: '5 min',
      status: 'completed',
      impact: 'Panne évitée chez 3 clients'
    },
    {
      type: 'expert_to_prescriptor',
      title: 'Validation LED → Template automatisé',
      time: '12 min',
      status: 'in_progress',
      impact: 'Nouveau standard créé'
    },
    {
      type: 'prescriptor_to_club',
      title: 'Projet solaire → Showcase membre',
      time: '1h',
      status: 'completed',
      impact: '15 nouveaux projets générés'
    },
    {
      type: 'club_to_hub',
      title: 'Retour terrain → Alerte préventive',
      time: '2h',
      status: 'completed',
      impact: 'Tendance détectée en avance'
    }
  ]

  useEffect(() => {
    // Simulation de chargement des données cross-module
    setIsLoading(true)
    setTimeout(() => {
      setCrossModuleData({
        totalIntegrations: 147,
        activeFlows: 23,
        efficiency: 94,
        userSatisfaction: 4.8
      })
      setIsLoading(false)
    }, 1500)
  }, [])

  const getCurrentModuleActions = () => {
    return crossModuleActions[currentModule] || []
  }

  const getIntegrationStatus = (from: string, to: string) => {
    if (from === currentModule || to === currentModule) {
      return 'active'
    }
    return 'available'
  }

  return (
    <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center text-white">
            <LinkIcon className="h-6 w-6" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Intégration Écosystème</h2>
            <p className="text-gray-600">Flux de données et actions cross-module</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-4">
          {isLoading ? (
            <div className="flex items-center space-x-2 text-gray-500">
              <RefreshCw className="h-4 w-4 animate-spin" />
              <span className="text-sm">Synchronisation...</span>
            </div>
          ) : (
            <div className="text-right">
              <div className="text-lg font-bold text-indigo-600">{crossModuleData?.efficiency}%</div>
              <div className="text-sm text-gray-600">Efficacité</div>
            </div>
          )}
        </div>
      </div>

      {/* Vue d'ensemble des modules */}
      <div className="grid md:grid-cols-4 gap-4 mb-8">
        {Object.entries(modules).map(([key, module]) => (
          <motion.div
            key={key}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className={`p-4 rounded-lg border-2 transition-all ${
              currentModule === key
                ? 'border-indigo-400 bg-indigo-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center space-x-3 mb-3">
              <div className={`w-10 h-10 bg-gradient-to-r ${module.color} rounded-lg flex items-center justify-center text-white`}>
                {module.icon}
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">{module.name}</h3>
                <p className="text-xs text-gray-600">{module.description}</p>
              </div>
            </div>
            
            <div className="flex items-center justify-between text-sm">
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                module.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-amber-100 text-amber-800'
              }`}>
                {module.status}
              </span>
              <span className="text-gray-500">Màj: {module.lastUpdate}</span>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Flux d'intégration */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Flux d'Intégration Actifs</h3>
        <div className="grid md:grid-cols-2 gap-6">
          {integrationFlows.map((flow, index) => (
            <motion.div
              key={`${flow.from}-${flow.to}`}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className={`p-4 rounded-lg border ${
                getIntegrationStatus(flow.from, flow.to) === 'active'
                  ? 'border-indigo-200 bg-indigo-50'
                  : 'border-gray-200 bg-gray-50'
              }`}
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <div className={`w-6 h-6 bg-gradient-to-r ${modules[flow.from as keyof typeof modules].color} rounded flex items-center justify-center`}>
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  </div>
                  <ArrowRight className="h-4 w-4 text-gray-400" />
                  <div className={`w-6 h-6 bg-gradient-to-r ${modules[flow.to as keyof typeof modules].color} rounded flex items-center justify-center`}>
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-lg font-bold text-gray-900">{flow.count}</div>
                  <div className="text-xs text-green-600 font-medium">{flow.trend}</div>
                </div>
              </div>
              
              <h4 className="font-semibold text-gray-900 mb-1">{flow.title}</h4>
              <p className="text-sm text-gray-600 mb-3">{flow.description}</p>
              
              <div className="space-y-1">
                {flow.examples.slice(0, 2).map((example, i) => (
                  <div key={i} className="text-xs text-gray-500 flex items-center space-x-1">
                    <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
                    <span>{example}</span>
                  </div>
                ))}
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Actions cross-module pour le module actuel */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Actions Disponibles depuis {modules[currentModule].name}
        </h3>
        <div className="grid md:grid-cols-2 gap-4">
          {getCurrentModuleActions().map((action, index) => (
            <Link
              key={index}
              href={action.action}
              className="p-4 rounded-lg border border-gray-200 hover:border-gray-300 transition-all group"
            >
              <div className="flex items-start space-x-3">
                <div className={`p-2 rounded-lg ${action.color}`}>
                  {action.icon}
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-gray-900 group-hover:text-indigo-600 transition-colors">
                    {action.title}
                  </h4>
                  <p className="text-sm text-gray-600">{action.description}</p>
                </div>
                <ExternalLink className="h-4 w-4 text-gray-400 group-hover:text-indigo-600 transition-colors" />
              </div>
            </Link>
          ))}
        </div>
      </div>

      {/* Intégrations récentes */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Intégrations Récentes</h3>
        <div className="space-y-3">
          {recentIntegrations.map((integration, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.05 }}
              className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
            >
              <div className="flex items-center space-x-3">
                <div className={`w-2 h-2 rounded-full ${
                  integration.status === 'completed' ? 'bg-green-500' :
                  integration.status === 'in_progress' ? 'bg-blue-500' :
                  'bg-gray-400'
                }`}></div>
                <div>
                  <h4 className="font-medium text-gray-900">{integration.title}</h4>
                  <p className="text-sm text-gray-600">{integration.impact}</p>
                </div>
              </div>
              <div className="text-right">
                <div className="text-sm text-gray-500">{integration.time}</div>
                <div className={`text-xs font-medium ${
                  integration.status === 'completed' ? 'text-green-600' :
                  integration.status === 'in_progress' ? 'text-blue-600' :
                  'text-gray-600'
                }`}>
                  {integration.status}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  )
}
