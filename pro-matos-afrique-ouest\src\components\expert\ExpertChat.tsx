'use client'

import { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  MessageSquare,
  Send,
  Paperclip,
  X,
  User,
  Bot,
  Clock,
  CheckCircle,
  AlertCircle,
  Minimize2,
  Maximize2
} from 'lucide-react'
import { useStore } from '@/store/useStore'
import { formatDate } from '@/lib/utils'

interface ChatMessage {
  id: string
  sender: 'user' | 'expert' | 'system'
  content: string
  timestamp: string
  attachments?: string[]
  status?: 'sending' | 'sent' | 'read'
}

interface ExpertChatProps {
  expertId?: string
  consultationId?: string
  onClose?: () => void
}

export default function ExpertChat({ expertId, consultationId, onClose }: ExpertChatProps) {
  const { selectedExpert, expertProfiles } = useStore()
  const [isOpen, setIsOpen] = useState(false)
  const [isMinimized, setIsMinimized] = useState(false)
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [newMessage, setNewMessage] = useState('')
  const [isTyping, setIsTyping] = useState(false)
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('connecting')
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const expert = selectedExpert || expertProfiles.find(e => e.id === expertId)

  // Simulation de messages initiaux
  useEffect(() => {
    if (expert) {
      const initialMessages: ChatMessage[] = [
        {
          id: 'msg_1',
          sender: 'system',
          content: `Connexion établie avec ${expert.display_name}. La consultation commence maintenant.`,
          timestamp: new Date().toISOString(),
          status: 'read'
        },
        {
          id: 'msg_2',
          sender: 'expert',
          content: `Bonjour ! Je suis ${expert.display_name}, ${expert.title}. Comment puis-je vous aider aujourd'hui ?`,
          timestamp: new Date(Date.now() + 1000).toISOString(),
          status: 'read'
        }
      ]
      setMessages(initialMessages)
      setConnectionStatus('connected')
    }
  }, [expert])

  // Auto-scroll vers le bas
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  // Simulation de réponses d'expert
  const simulateExpertResponse = (userMessage: string) => {
    setIsTyping(true)
    
    setTimeout(() => {
      const responses = [
        "Excellente question ! Laissez-moi analyser votre situation...",
        "D'après votre description, je recommande de vérifier les points suivants :",
        "C'est un cas intéressant. Voici mon analyse technique :",
        "Pour résoudre ce problème, nous devons considérer plusieurs aspects :",
        "Basé sur mon expérience, voici la meilleure approche :"
      ]
      
      const randomResponse = responses[Math.floor(Math.random() * responses.length)]
      
      const expertMessage: ChatMessage = {
        id: `msg_${Date.now()}`,
        sender: 'expert',
        content: randomResponse,
        timestamp: new Date().toISOString(),
        status: 'sent'
      }
      
      setMessages(prev => [...prev, expertMessage])
      setIsTyping(false)
    }, 2000 + Math.random() * 3000) // 2-5 secondes
  }

  const sendMessage = () => {
    if (!newMessage.trim()) return

    const userMessage: ChatMessage = {
      id: `msg_${Date.now()}`,
      sender: 'user',
      content: newMessage,
      timestamp: new Date().toISOString(),
      status: 'sending'
    }

    setMessages(prev => [...prev, userMessage])
    setNewMessage('')

    // Marquer comme envoyé après un délai
    setTimeout(() => {
      setMessages(prev => 
        prev.map(msg => 
          msg.id === userMessage.id ? { ...msg, status: 'sent' } : msg
        )
      )
    }, 1000)

    // Simuler une réponse d'expert
    simulateExpertResponse(newMessage)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  const getMessageIcon = (sender: string) => {
    switch (sender) {
      case 'expert':
        return <User className="h-4 w-4" />
      case 'system':
        return <Bot className="h-4 w-4" />
      default:
        return null
    }
  }

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'sending':
        return <Clock className="h-3 w-3 text-gray-400" />
      case 'sent':
        return <CheckCircle className="h-3 w-3 text-blue-500" />
      case 'read':
        return <CheckCircle className="h-3 w-3 text-green-500" />
      default:
        return null
    }
  }

  if (!expert) {
    return (
      <div className="fixed bottom-4 right-4 bg-red-100 border border-red-300 rounded-lg p-4 text-red-800">
        <AlertCircle className="h-5 w-5 inline mr-2" />
        Expert non trouvé
      </div>
    )
  }

  return (
    <>
      {/* Bouton d'ouverture du chat */}
      {!isOpen && (
        <motion.button
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          onClick={() => setIsOpen(true)}
          className="fixed bottom-6 right-6 bg-gradient-to-r from-amber-400 to-amber-500 text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 z-50"
        >
          <MessageSquare className="h-6 w-6" />
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
            1
          </span>
        </motion.button>
      )}

      {/* Fenêtre de chat */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: 100, scale: 0.8 }}
            animate={{ 
              opacity: 1, 
              y: 0, 
              scale: 1,
              height: isMinimized ? 60 : 500
            }}
            exit={{ opacity: 0, y: 100, scale: 0.8 }}
            className="fixed bottom-6 right-6 w-96 bg-white rounded-lg shadow-2xl border border-gray-200 z-50 overflow-hidden"
          >
            {/* Header du chat */}
            <div className="bg-gradient-to-r from-amber-400 to-amber-500 text-white p-4 flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                  <User className="h-5 w-5" />
                </div>
                <div>
                  <h3 className="font-semibold">{expert.display_name}</h3>
                  <div className="flex items-center space-x-2 text-xs">
                    <div className={`w-2 h-2 rounded-full ${
                      connectionStatus === 'connected' ? 'bg-green-300' :
                      connectionStatus === 'connecting' ? 'bg-yellow-300' :
                      'bg-red-300'
                    }`}></div>
                    <span>{connectionStatus}</span>
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setIsMinimized(!isMinimized)}
                  className="p-1 hover:bg-white/20 rounded"
                >
                  {isMinimized ? <Maximize2 className="h-4 w-4" /> : <Minimize2 className="h-4 w-4" />}
                </button>
                <button
                  onClick={() => {
                    setIsOpen(false)
                    onClose?.()
                  }}
                  className="p-1 hover:bg-white/20 rounded"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            </div>

            {/* Corps du chat */}
            {!isMinimized && (
              <>
                {/* Messages */}
                <div className="h-80 overflow-y-auto p-4 space-y-4">
                  {messages.map((message) => (
                    <motion.div
                      key={message.id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                    >
                      <div className={`max-w-xs px-3 py-2 rounded-lg ${
                        message.sender === 'user' 
                          ? 'bg-amber-500 text-white' 
                          : message.sender === 'system'
                          ? 'bg-gray-100 text-gray-700'
                          : 'bg-gray-200 text-gray-900'
                      }`}>
                        {message.sender !== 'user' && (
                          <div className="flex items-center space-x-1 mb-1">
                            {getMessageIcon(message.sender)}
                            <span className="text-xs font-medium">
                              {message.sender === 'expert' ? expert.display_name : 'Système'}
                            </span>
                          </div>
                        )}
                        <p className="text-sm">{message.content}</p>
                        <div className="flex items-center justify-between mt-1">
                          <span className="text-xs opacity-70">
                            {new Date(message.timestamp).toLocaleTimeString('fr-FR', { 
                              hour: '2-digit', 
                              minute: '2-digit' 
                            })}
                          </span>
                          {message.sender === 'user' && getStatusIcon(message.status)}
                        </div>
                      </div>
                    </motion.div>
                  ))}
                  
                  {/* Indicateur de frappe */}
                  {isTyping && (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="flex justify-start"
                    >
                      <div className="bg-gray-200 text-gray-900 px-3 py-2 rounded-lg">
                        <div className="flex items-center space-x-1">
                          <User className="h-3 w-3" />
                          <span className="text-xs">{expert.display_name} écrit...</span>
                          <div className="flex space-x-1">
                            <div className="w-1 h-1 bg-gray-500 rounded-full animate-bounce"></div>
                            <div className="w-1 h-1 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                            <div className="w-1 h-1 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  )}
                  
                  <div ref={messagesEndRef} />
                </div>

                {/* Zone de saisie */}
                <div className="border-t border-gray-200 p-4">
                  <div className="flex items-center space-x-2">
                    <button className="p-2 text-gray-400 hover:text-gray-600">
                      <Paperclip className="h-4 w-4" />
                    </button>
                    <input
                      type="text"
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      onKeyPress={handleKeyPress}
                      placeholder="Tapez votre message..."
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400 focus:border-transparent text-sm"
                    />
                    <button
                      onClick={sendMessage}
                      disabled={!newMessage.trim()}
                      className="p-2 bg-amber-500 text-white rounded-lg hover:bg-amber-600 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <Send className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </>
  )
}
