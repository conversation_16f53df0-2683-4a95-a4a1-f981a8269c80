{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/store/useStore.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { persist } from 'zustand/middleware'\nimport { User, Product, Alert, MarketAlert, StockUpdate, TrainingEvent, NewsUpdate } from '@/lib/supabase'\n\ninterface AppState {\n  // User state\n  user: User | null\n  isAuthenticated: boolean\n  \n  // Products state\n  products: Product[]\n  filteredProducts: Product[]\n  searchQuery: string\n  selectedCategory: string\n  \n  // Alerts state\n  alerts: Alert[]\n  unreadAlertsCount: number\n\n  // Hub d'Information state\n  marketAlerts: MarketAlert[]\n  stockUpdates: StockUpdate[]\n  trainingEvents: TrainingEvent[]\n  newsUpdates: NewsUpdate[]\n  realTimeConnected: boolean\n  lastUpdateTime: string\n\n  // UI state\n  isLoading: boolean\n  isDarkMode: boolean\n  sidebarOpen: boolean\n  \n  // Actions\n  setUser: (user: User | null) => void\n  setProducts: (products: Product[]) => void\n  setSearchQuery: (query: string) => void\n  setSelectedCategory: (category: string) => void\n  setAlerts: (alerts: <PERSON><PERSON>[]) => void\n  markAlertAsRead: (alertId: string) => void\n\n  // Hub d'Information actions\n  setMarketAlerts: (alerts: MarketAlert[]) => void\n  addMarketAlert: (alert: MarketAlert) => void\n  setStockUpdates: (updates: StockUpdate[]) => void\n  addStockUpdate: (update: StockUpdate) => void\n  setTrainingEvents: (events: TrainingEvent[]) => void\n  setNewsUpdates: (news: NewsUpdate[]) => void\n  addNewsUpdate: (news: NewsUpdate) => void\n  setRealTimeConnected: (connected: boolean) => void\n  updateLastUpdateTime: () => void\n\n  setLoading: (loading: boolean) => void\n  toggleDarkMode: () => void\n  toggleSidebar: () => void\n  filterProducts: () => void\n}\n\nexport const useStore = create<AppState>()(\n  persist(\n    (set, get) => ({\n      // Initial state\n      user: null,\n      isAuthenticated: false,\n      products: [],\n      filteredProducts: [],\n      searchQuery: '',\n      selectedCategory: '',\n      alerts: [],\n      unreadAlertsCount: 0,\n\n      // Hub d'Information initial state\n      marketAlerts: [],\n      stockUpdates: [],\n      trainingEvents: [],\n      newsUpdates: [],\n      realTimeConnected: false,\n      lastUpdateTime: new Date().toISOString(),\n\n      isLoading: false,\n      isDarkMode: false,\n      sidebarOpen: false,\n\n      // Actions\n      setUser: (user) => set({ \n        user, \n        isAuthenticated: !!user \n      }),\n\n      setProducts: (products) => {\n        set({ products })\n        get().filterProducts()\n      },\n\n      setSearchQuery: (searchQuery) => {\n        set({ searchQuery })\n        get().filterProducts()\n      },\n\n      setSelectedCategory: (selectedCategory) => {\n        set({ selectedCategory })\n        get().filterProducts()\n      },\n\n      setAlerts: (alerts) => {\n        const unreadAlertsCount = alerts.filter(alert => !alert.is_read).length\n        set({ alerts, unreadAlertsCount })\n      },\n\n      markAlertAsRead: (alertId) => {\n        const alerts = get().alerts.map(alert =>\n          alert.id === alertId ? { ...alert, is_read: true } : alert\n        )\n        const unreadAlertsCount = alerts.filter(alert => !alert.is_read).length\n        set({ alerts, unreadAlertsCount })\n      },\n\n      // Hub d'Information actions\n      setMarketAlerts: (marketAlerts) => {\n        set({ marketAlerts })\n        get().updateLastUpdateTime()\n      },\n\n      addMarketAlert: (alert) => {\n        const marketAlerts = [alert, ...get().marketAlerts].slice(0, 50) // Garder les 50 plus récentes\n        set({ marketAlerts })\n        get().updateLastUpdateTime()\n      },\n\n      setStockUpdates: (stockUpdates) => {\n        set({ stockUpdates })\n        get().updateLastUpdateTime()\n      },\n\n      addStockUpdate: (update) => {\n        const stockUpdates = [update, ...get().stockUpdates].slice(0, 100) // Garder les 100 plus récentes\n        set({ stockUpdates })\n        get().updateLastUpdateTime()\n      },\n\n      setTrainingEvents: (trainingEvents) => set({ trainingEvents }),\n\n      setNewsUpdates: (newsUpdates) => set({ newsUpdates }),\n\n      addNewsUpdate: (news) => {\n        const newsUpdates = [news, ...get().newsUpdates].slice(0, 20) // Garder les 20 plus récentes\n        set({ newsUpdates })\n        get().updateLastUpdateTime()\n      },\n\n      setRealTimeConnected: (realTimeConnected) => set({ realTimeConnected }),\n\n      updateLastUpdateTime: () => set({ lastUpdateTime: new Date().toISOString() }),\n\n      setLoading: (isLoading) => set({ isLoading }),\n\n      toggleDarkMode: () => set((state) => ({ \n        isDarkMode: !state.isDarkMode \n      })),\n\n      toggleSidebar: () => set((state) => ({ \n        sidebarOpen: !state.sidebarOpen \n      })),\n\n      filterProducts: () => {\n        const { products, searchQuery, selectedCategory } = get()\n        \n        let filtered = products\n\n        if (selectedCategory) {\n          filtered = filtered.filter(product => \n            product.category === selectedCategory\n          )\n        }\n\n        if (searchQuery) {\n          const query = searchQuery.toLowerCase()\n          filtered = filtered.filter(product =>\n            product.name.toLowerCase().includes(query) ||\n            product.description.toLowerCase().includes(query) ||\n            product.brand.toLowerCase().includes(query) ||\n            product.model.toLowerCase().includes(query)\n          )\n        }\n\n        set({ filteredProducts: filtered })\n      },\n    }),\n    {\n      name: 'pro-matos-storage',\n      partialize: (state) => ({\n        user: state.user,\n        isAuthenticated: state.isAuthenticated,\n        isDarkMode: state.isDarkMode,\n      }),\n    }\n  )\n)\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAwDO,MAAM,WAAW,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC3B,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,MAAM;QACN,iBAAiB;QACjB,UAAU,EAAE;QACZ,kBAAkB,EAAE;QACpB,aAAa;QACb,kBAAkB;QAClB,QAAQ,EAAE;QACV,mBAAmB;QAEnB,kCAAkC;QAClC,cAAc,EAAE;QAChB,cAAc,EAAE;QAChB,gBAAgB,EAAE;QAClB,aAAa,EAAE;QACf,mBAAmB;QACnB,gBAAgB,IAAI,OAAO,WAAW;QAEtC,WAAW;QACX,YAAY;QACZ,aAAa;QAEb,UAAU;QACV,SAAS,CAAC,OAAS,IAAI;gBACrB;gBACA,iBAAiB,CAAC,CAAC;YACrB;QAEA,aAAa,CAAC;YACZ,IAAI;gBAAE;YAAS;YACf,MAAM,cAAc;QACtB;QAEA,gBAAgB,CAAC;YACf,IAAI;gBAAE;YAAY;YAClB,MAAM,cAAc;QACtB;QAEA,qBAAqB,CAAC;YACpB,IAAI;gBAAE;YAAiB;YACvB,MAAM,cAAc;QACtB;QAEA,WAAW,CAAC;YACV,MAAM,oBAAoB,OAAO,MAAM,CAAC,CAAA,QAAS,CAAC,MAAM,OAAO,EAAE,MAAM;YACvE,IAAI;gBAAE;gBAAQ;YAAkB;QAClC;QAEA,iBAAiB,CAAC;YAChB,MAAM,SAAS,MAAM,MAAM,CAAC,GAAG,CAAC,CAAA,QAC9B,MAAM,EAAE,KAAK,UAAU;oBAAE,GAAG,KAAK;oBAAE,SAAS;gBAAK,IAAI;YAEvD,MAAM,oBAAoB,OAAO,MAAM,CAAC,CAAA,QAAS,CAAC,MAAM,OAAO,EAAE,MAAM;YACvE,IAAI;gBAAE;gBAAQ;YAAkB;QAClC;QAEA,4BAA4B;QAC5B,iBAAiB,CAAC;YAChB,IAAI;gBAAE;YAAa;YACnB,MAAM,oBAAoB;QAC5B;QAEA,gBAAgB,CAAC;YACf,MAAM,eAAe;gBAAC;mBAAU,MAAM,YAAY;aAAC,CAAC,KAAK,CAAC,GAAG,IAAI,8BAA8B;;YAC/F,IAAI;gBAAE;YAAa;YACnB,MAAM,oBAAoB;QAC5B;QAEA,iBAAiB,CAAC;YAChB,IAAI;gBAAE;YAAa;YACnB,MAAM,oBAAoB;QAC5B;QAEA,gBAAgB,CAAC;YACf,MAAM,eAAe;gBAAC;mBAAW,MAAM,YAAY;aAAC,CAAC,KAAK,CAAC,GAAG,KAAK,+BAA+B;;YAClG,IAAI;gBAAE;YAAa;YACnB,MAAM,oBAAoB;QAC5B;QAEA,mBAAmB,CAAC,iBAAmB,IAAI;gBAAE;YAAe;QAE5D,gBAAgB,CAAC,cAAgB,IAAI;gBAAE;YAAY;QAEnD,eAAe,CAAC;YACd,MAAM,cAAc;gBAAC;mBAAS,MAAM,WAAW;aAAC,CAAC,KAAK,CAAC,GAAG,IAAI,8BAA8B;;YAC5F,IAAI;gBAAE;YAAY;YAClB,MAAM,oBAAoB;QAC5B;QAEA,sBAAsB,CAAC,oBAAsB,IAAI;gBAAE;YAAkB;QAErE,sBAAsB,IAAM,IAAI;gBAAE,gBAAgB,IAAI,OAAO,WAAW;YAAG;QAE3E,YAAY,CAAC,YAAc,IAAI;gBAAE;YAAU;QAE3C,gBAAgB,IAAM,IAAI,CAAC,QAAU,CAAC;oBACpC,YAAY,CAAC,MAAM,UAAU;gBAC/B,CAAC;QAED,eAAe,IAAM,IAAI,CAAC,QAAU,CAAC;oBACnC,aAAa,CAAC,MAAM,WAAW;gBACjC,CAAC;QAED,gBAAgB;YACd,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,gBAAgB,EAAE,GAAG;YAEpD,IAAI,WAAW;YAEf,IAAI,kBAAkB;gBACpB,WAAW,SAAS,MAAM,CAAC,CAAA,UACzB,QAAQ,QAAQ,KAAK;YAEzB;YAEA,IAAI,aAAa;gBACf,MAAM,QAAQ,YAAY,WAAW;gBACrC,WAAW,SAAS,MAAM,CAAC,CAAA,UACzB,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,UACpC,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,UAC3C,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,UACrC,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC;YAEzC;YAEA,IAAI;gBAAE,kBAAkB;YAAS;QACnC;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,iBAAiB,MAAM,eAAe;YACtC,YAAY,MAAM,UAAU;QAC9B,CAAC;AACH", "debugId": null}}, {"offset": {"line": 173, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/services/hubService.ts"], "sourcesContent": ["import { MarketAlert, StockUpdate, PriceHistory, TrainingEvent, NewsUpdate, Product } from '@/lib/supabase'\n\n// Service pour simuler les données temps réel du Hub d'Information\nexport class HubService {\n  \n  // Simulation des alertes marché en temps réel\n  static generateMarketAlerts(): MarketAlert[] {\n    return [\n      {\n        id: '1',\n        type: 'stock_low',\n        title: 'Stock Faible - Disjoncteurs Schneider',\n        message: 'Les disjoncteurs Schneider Electric C60N 32A sont en stock faible chez 3 fournisseurs à Abidjan',\n        severity: 'medium',\n        category: 'Protection électrique',\n        product_id: 'prod_001',\n        affected_regions: ['Abidjan', 'Bouaké'],\n        is_active: true,\n        created_at: new Date(Date.now() - 2 * 60 * 1000).toISOString(), // Il y a 2 minutes\n      },\n      {\n        id: '2',\n        type: 'price_change',\n        title: 'Baisse de Prix - Câbles Nexans',\n        message: 'Réduction de 15% sur les câbles Nexans U1000R2V 3x2.5mm² chez ElectroDistrib',\n        severity: 'low',\n        category: 'Câblage',\n        affected_regions: ['Dakar', 'Thiès'],\n        is_active: true,\n        created_at: new Date(Date.now() - 15 * 60 * 1000).toISOString(), // Il y a 15 minutes\n      },\n      {\n        id: '3',\n        type: 'new_product',\n        title: 'Nouveau Produit - Onduleurs APC',\n        message: 'Arrivée des nouveaux onduleurs APC Smart-UPS 3000VA avec technologie lithium',\n        severity: 'high',\n        category: 'Alimentation',\n        affected_regions: ['Accra', 'Kumasi', 'Abidjan'],\n        is_active: true,\n        created_at: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // Il y a 30 minutes\n      },\n      {\n        id: '4',\n        type: 'stock_out',\n        title: 'Rupture de Stock - Transformateurs',\n        message: 'Rupture totale des transformateurs 400/230V 63kVA dans la région de Bamako',\n        severity: 'critical',\n        category: 'Transformation',\n        affected_regions: ['Bamako', 'Ségou'],\n        is_active: true,\n        created_at: new Date(Date.now() - 45 * 60 * 1000).toISOString(), // Il y a 45 minutes\n      },\n      {\n        id: '5',\n        type: 'market_trend',\n        title: 'Tendance Marché - Énergie Solaire',\n        message: 'Forte demande pour les équipements solaires : +40% ce trimestre en Afrique de l\\'Ouest',\n        severity: 'medium',\n        category: 'Énergie renouvelable',\n        affected_regions: ['Ouagadougou', 'Niamey', 'Abidjan'],\n        is_active: true,\n        created_at: new Date(Date.now() - 60 * 60 * 1000).toISOString(), // Il y a 1 heure\n      }\n    ]\n  }\n\n  // Simulation des mises à jour de stock\n  static generateStockUpdates(): StockUpdate[] {\n    return [\n      {\n        id: '1',\n        product_id: 'prod_001',\n        supplier_id: 'sup_001',\n        previous_quantity: 150,\n        current_quantity: 45,\n        location: 'Abidjan - Zone Industrielle',\n        update_type: 'decrease',\n        created_at: new Date(Date.now() - 5 * 60 * 1000).toISOString(),\n      },\n      {\n        id: '2',\n        product_id: 'prod_002',\n        supplier_id: 'sup_002',\n        previous_quantity: 0,\n        current_quantity: 200,\n        location: 'Dakar - Entrepôt Central',\n        update_type: 'restock',\n        created_at: new Date(Date.now() - 10 * 60 * 1000).toISOString(),\n      },\n      {\n        id: '3',\n        product_id: 'prod_003',\n        supplier_id: 'sup_003',\n        previous_quantity: 25,\n        current_quantity: 0,\n        location: 'Bamako - Dépôt Nord',\n        update_type: 'out_of_stock',\n        created_at: new Date(Date.now() - 20 * 60 * 1000).toISOString(),\n      }\n    ]\n  }\n\n  // Simulation des événements de formation\n  static generateTrainingEvents(): TrainingEvent[] {\n    return [\n      {\n        id: '1',\n        title: 'Certification Schneider Electric - Installations BT',\n        description: 'Formation certifiante sur les installations basse tension selon les normes NF C 15-100',\n        type: 'certification',\n        instructor: 'Ing. Kouassi Yao (Schneider Electric)',\n        start_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // Dans 7 jours\n        end_date: new Date(Date.now() + 9 * 24 * 60 * 60 * 1000).toISOString(), // Dans 9 jours\n        location: 'Abidjan - Centre de Formation Schneider',\n        is_virtual: false,\n        max_participants: 20,\n        current_participants: 12,\n        membership_required: 'silver',\n        registration_fee: 150000, // 150,000 FCFA\n        status: 'upcoming',\n        created_at: new Date().toISOString(),\n      },\n      {\n        id: '2',\n        title: 'Webinaire - Énergie Solaire et Stockage',\n        description: 'Les dernières innovations en matière de panneaux solaires et batteries lithium',\n        type: 'webinar',\n        instructor: 'Dr. Aminata Traoré (Expert Énergie)',\n        start_date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(), // Dans 3 jours\n        end_date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000 + 2 * 60 * 60 * 1000).toISOString(), // 2h plus tard\n        location: 'En ligne',\n        is_virtual: true,\n        max_participants: 100,\n        current_participants: 67,\n        membership_required: 'bronze',\n        registration_fee: 0,\n        status: 'upcoming',\n        created_at: new Date().toISOString(),\n      },\n      {\n        id: '3',\n        title: 'Atelier Pratique - Maintenance Préventive',\n        description: 'Techniques de maintenance préventive pour équipements électriques industriels',\n        type: 'workshop',\n        instructor: 'Équipe Technique ABB',\n        start_date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(), // Dans 14 jours\n        end_date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000 + 6 * 60 * 60 * 1000).toISOString(), // 6h plus tard\n        location: 'Dakar - Centre Technique ABB',\n        is_virtual: false,\n        max_participants: 15,\n        current_participants: 8,\n        membership_required: 'gold',\n        registration_fee: 200000, // 200,000 FCFA\n        status: 'upcoming',\n        created_at: new Date().toISOString(),\n      }\n    ]\n  }\n\n  // Simulation des actualités du secteur\n  static generateNewsUpdates(): NewsUpdate[] {\n    return [\n      {\n        id: '1',\n        title: 'Nouvelle réglementation sur les installations photovoltaïques en Côte d\\'Ivoire',\n        content: 'Le gouvernement ivoirien vient d\\'adopter de nouvelles normes pour les installations solaires...',\n        summary: 'Nouvelles normes techniques et administratives pour le photovoltaïque',\n        category: 'regulation',\n        author: 'Ministère de l\\'Énergie CI',\n        source_url: 'https://energie.gouv.ci/nouvelles-normes-pv',\n        image_url: '/images/news/regulation-pv.jpg',\n        tags: ['réglementation', 'photovoltaïque', 'côte d\\'ivoire'],\n        is_featured: true,\n        published_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // Il y a 2 heures\n        created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n      },\n      {\n        id: '2',\n        title: 'Schneider Electric ouvre un nouveau centre de distribution à Accra',\n        content: 'Le leader mondial de la gestion de l\\'énergie inaugure son plus grand entrepôt...',\n        summary: 'Nouveau hub logistique pour améliorer la disponibilité des produits',\n        category: 'company',\n        author: 'Schneider Electric',\n        source_url: 'https://schneider-electric.com/accra-center',\n        image_url: '/images/news/schneider-accra.jpg',\n        tags: ['schneider electric', 'ghana', 'logistique'],\n        is_featured: false,\n        published_at: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // Il y a 6 heures\n        created_at: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),\n      },\n      {\n        id: '3',\n        title: 'Innovation : Nouveaux compteurs intelligents pour l\\'Afrique de l\\'Ouest',\n        content: 'Une startup sénégalaise développe des compteurs connectés adaptés aux réseaux locaux...',\n        summary: 'Technologie IoT adaptée aux défis énergétiques régionaux',\n        category: 'technology',\n        author: 'TechAfrique',\n        source_url: 'https://techafrique.com/compteurs-intelligents',\n        image_url: '/images/news/smart-meters.jpg',\n        tags: ['innovation', 'iot', 'compteurs', 'sénégal'],\n        is_featured: true,\n        published_at: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(), // Il y a 12 heures\n        created_at: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),\n      }\n    ]\n  }\n\n  // Simulation des produits avec données temps réel\n  static generateProducts(): Product[] {\n    return [\n      {\n        id: 'prod_001',\n        name: 'Disjoncteur C60N 32A',\n        description: 'Disjoncteur modulaire Schneider Electric C60N courbe C 32A',\n        category: 'Protection électrique',\n        brand: 'Schneider Electric',\n        model: 'C60N-C32',\n        price: 45000, // 45,000 FCFA\n        stock_quantity: 45,\n        technical_specs: {\n          'Courant nominal': '32A',\n          'Courbe de déclenchement': 'C',\n          'Pouvoir de coupure': '6kA',\n          'Nombre de pôles': '1',\n          'Norme': 'NF EN 60898'\n        },\n        images: ['/images/products/c60n-32a.jpg'],\n        qr_code: 'QR_C60N_32A_001',\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString(),\n      },\n      {\n        id: 'prod_002',\n        name: 'Câble U1000R2V 3x2.5mm²',\n        description: 'Câble d\\'énergie Nexans U1000R2V 3 conducteurs 2.5mm²',\n        category: 'Câblage',\n        brand: 'Nexans',\n        model: 'U1000R2V-3x2.5',\n        price: 2500, // 2,500 FCFA/mètre\n        stock_quantity: 200,\n        technical_specs: {\n          'Section': '3x2.5mm²',\n          'Tension nominale': '1000V',\n          'Type d\\'isolation': 'PRC',\n          'Température de service': '-40°C à +90°C',\n          'Norme': 'NF C 32-321'\n        },\n        images: ['/images/products/cable-u1000r2v.jpg'],\n        qr_code: 'QR_U1000R2V_3x2.5_002',\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString(),\n      }\n    ]\n  }\n\n  // Méthode pour simuler les mises à jour temps réel\n  static subscribeToRealTimeUpdates(callback: (data: any) => void) {\n    // Simulation d'un WebSocket ou Server-Sent Events\n    const interval = setInterval(() => {\n      const updateType = Math.random()\n      \n      if (updateType < 0.3) {\n        // Nouvelle alerte marché\n        callback({\n          type: 'market_alert',\n          data: this.generateMarketAlerts()[0]\n        })\n      } else if (updateType < 0.6) {\n        // Mise à jour de stock\n        callback({\n          type: 'stock_update',\n          data: this.generateStockUpdates()[0]\n        })\n      } else {\n        // Nouvelle actualité\n        callback({\n          type: 'news_update',\n          data: this.generateNewsUpdates()[0]\n        })\n      }\n    }, 30000) // Toutes les 30 secondes\n\n    return () => clearInterval(interval)\n  }\n}\n"], "names": [], "mappings": ";;;AAGO,MAAM;IAEX,8CAA8C;IAC9C,OAAO,uBAAsC;QAC3C,OAAO;YACL;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,UAAU;gBACV,UAAU;gBACV,YAAY;gBACZ,kBAAkB;oBAAC;oBAAW;iBAAS;gBACvC,WAAW;gBACX,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,MAAM,WAAW;YAC9D;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,UAAU;gBACV,UAAU;gBACV,kBAAkB;oBAAC;oBAAS;iBAAQ;gBACpC,WAAW;gBACX,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,MAAM,WAAW;YAC/D;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,UAAU;gBACV,UAAU;gBACV,kBAAkB;oBAAC;oBAAS;oBAAU;iBAAU;gBAChD,WAAW;gBACX,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,MAAM,WAAW;YAC/D;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,UAAU;gBACV,UAAU;gBACV,kBAAkB;oBAAC;oBAAU;iBAAQ;gBACrC,WAAW;gBACX,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,MAAM,WAAW;YAC/D;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,UAAU;gBACV,UAAU;gBACV,kBAAkB;oBAAC;oBAAe;oBAAU;iBAAU;gBACtD,WAAW;gBACX,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,MAAM,WAAW;YAC/D;SACD;IACH;IAEA,uCAAuC;IACvC,OAAO,uBAAsC;QAC3C,OAAO;YACL;gBACE,IAAI;gBACJ,YAAY;gBACZ,aAAa;gBACb,mBAAmB;gBACnB,kBAAkB;gBAClB,UAAU;gBACV,aAAa;gBACb,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,MAAM,WAAW;YAC9D;YACA;gBACE,IAAI;gBACJ,YAAY;gBACZ,aAAa;gBACb,mBAAmB;gBACnB,kBAAkB;gBAClB,UAAU;gBACV,aAAa;gBACb,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,MAAM,WAAW;YAC/D;YACA;gBACE,IAAI;gBACJ,YAAY;gBACZ,aAAa;gBACb,mBAAmB;gBACnB,kBAAkB;gBAClB,UAAU;gBACV,aAAa;gBACb,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,MAAM,WAAW;YAC/D;SACD;IACH;IAEA,yCAAyC;IACzC,OAAO,yBAA0C;QAC/C,OAAO;YACL;gBACE,IAAI;gBACJ,OAAO;gBACP,aAAa;gBACb,MAAM;gBACN,YAAY;gBACZ,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;gBACtE,UAAU,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;gBACpE,UAAU;gBACV,YAAY;gBACZ,kBAAkB;gBAClB,sBAAsB;gBACtB,qBAAqB;gBACrB,kBAAkB;gBAClB,QAAQ;gBACR,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,aAAa;gBACb,MAAM;gBACN,YAAY;gBACZ,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;gBACtE,UAAU,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,OAAO,IAAI,KAAK,KAAK,MAAM,WAAW;gBACzF,UAAU;gBACV,YAAY;gBACZ,kBAAkB;gBAClB,sBAAsB;gBACtB,qBAAqB;gBACrB,kBAAkB;gBAClB,QAAQ;gBACR,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,aAAa;gBACb,MAAM;gBACN,YAAY;gBACZ,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW;gBACvE,UAAU,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,IAAI,KAAK,KAAK,MAAM,WAAW;gBAC1F,UAAU;gBACV,YAAY;gBACZ,kBAAkB;gBAClB,sBAAsB;gBACtB,qBAAqB;gBACrB,kBAAkB;gBAClB,QAAQ;gBACR,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;IACH;IAEA,uCAAuC;IACvC,OAAO,sBAAoC;QACzC,OAAO;YACL;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;gBACT,SAAS;gBACT,UAAU;gBACV,QAAQ;gBACR,YAAY;gBACZ,WAAW;gBACX,MAAM;oBAAC;oBAAkB;oBAAkB;iBAAiB;gBAC5D,aAAa;gBACb,cAAc,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;gBACnE,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;YACnE;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;gBACT,SAAS;gBACT,UAAU;gBACV,QAAQ;gBACR,YAAY;gBACZ,WAAW;gBACX,MAAM;oBAAC;oBAAsB;oBAAS;iBAAa;gBACnD,aAAa;gBACb,cAAc,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;gBACnE,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;YACnE;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;gBACT,SAAS;gBACT,UAAU;gBACV,QAAQ;gBACR,YAAY;gBACZ,WAAW;gBACX,MAAM;oBAAC;oBAAc;oBAAO;oBAAa;iBAAU;gBACnD,aAAa;gBACb,cAAc,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW;gBACpE,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW;YACpE;SACD;IACH;IAEA,kDAAkD;IAClD,OAAO,mBAA8B;QACnC,OAAO;YACL;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,UAAU;gBACV,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,gBAAgB;gBAChB,iBAAiB;oBACf,mBAAmB;oBACnB,2BAA2B;oBAC3B,sBAAsB;oBACtB,mBAAmB;oBACnB,SAAS;gBACX;gBACA,QAAQ;oBAAC;iBAAgC;gBACzC,SAAS;gBACT,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,UAAU;gBACV,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,gBAAgB;gBAChB,iBAAiB;oBACf,WAAW;oBACX,oBAAoB;oBACpB,qBAAqB;oBACrB,0BAA0B;oBAC1B,SAAS;gBACX;gBACA,QAAQ;oBAAC;iBAAsC;gBAC/C,SAAS;gBACT,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;IACH;IAEA,mDAAmD;IACnD,OAAO,2BAA2B,QAA6B,EAAE;QAC/D,kDAAkD;QAClD,MAAM,WAAW,YAAY;YAC3B,MAAM,aAAa,KAAK,MAAM;YAE9B,IAAI,aAAa,KAAK;gBACpB,yBAAyB;gBACzB,SAAS;oBACP,MAAM;oBACN,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC,EAAE;gBACtC;YACF,OAAO,IAAI,aAAa,KAAK;gBAC3B,uBAAuB;gBACvB,SAAS;oBACP,MAAM;oBACN,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC,EAAE;gBACtC;YACF,OAAO;gBACL,qBAAqB;gBACrB,SAAS;oBACP,MAAM;oBACN,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC,EAAE;gBACrC;YACF;QACF,GAAG,OAAO,yBAAyB;;QAEnC,OAAO,IAAM,cAAc;IAC7B;AACF", "debugId": null}}, {"offset": {"line": 495, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPrice(price: number): string {\n  return new Intl.NumberFormat('fr-FR', {\n    style: 'currency',\n    currency: 'XOF',\n    minimumFractionDigits: 0,\n  }).format(price)\n}\n\nexport function formatDate(date: string | Date): string {\n  return new Intl.DateTimeFormat('fr-FR', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(new Date(date))\n}\n\nexport function getMembershipColor(level: string): string {\n  switch (level) {\n    case 'bronze':\n      return 'text-amber-600 bg-amber-50'\n    case 'silver':\n      return 'text-gray-600 bg-gray-50'\n    case 'gold':\n      return 'text-yellow-600 bg-yellow-50'\n    case 'platinum':\n      return 'text-purple-600 bg-purple-50'\n    default:\n      return 'text-gray-600 bg-gray-50'\n  }\n}\n\nexport function generateQRCode(productId: string): string {\n  // Simulation d'un QR code - à remplacer par une vraie génération\n  return `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(productId)}`\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa;IACvC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,mBAAmB,KAAa;IAC9C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,eAAe,SAAiB;IAC9C,iEAAiE;IACjE,OAAO,CAAC,8DAA8D,EAAE,mBAAmB,YAAY;AACzG;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 560, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/components/hub/InformationHub.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { \n  Bell, \n  TrendingUp, \n  Package, \n  AlertTriangle, \n  Calendar,\n  Newspaper,\n  Wifi,\n  WifiOff,\n  Clock,\n  Filter,\n  Search,\n  RefreshCw\n} from 'lucide-react'\nimport { useStore } from '@/store/useStore'\nimport { HubService } from '@/services/hubService'\nimport { formatDate } from '@/lib/utils'\nimport { MarketAlert, StockUpdate, TrainingEvent, NewsUpdate } from '@/lib/supabase'\n\ninterface InformationHubProps {\n  className?: string\n}\n\nexport default function InformationHub({ className = '' }: InformationHubProps) {\n  const {\n    marketAlerts,\n    stockUpdates,\n    trainingEvents,\n    newsUpdates,\n    realTimeConnected,\n    lastUpdateTime,\n    setMarketAlerts,\n    addMarketAlert,\n    setStockUpdates,\n    addStockUpdate,\n    setTrainingEvents,\n    setNewsUpdates,\n    addNewsUpdate,\n    setRealTimeConnected,\n    updateLastUpdateTime\n  } = useStore()\n\n  const [activeTab, setActiveTab] = useState<'alerts' | 'stocks' | 'training' | 'news'>('alerts')\n  const [searchQuery, setSearchQuery] = useState('')\n  const [filterSeverity, setFilterSeverity] = useState<'all' | 'low' | 'medium' | 'high' | 'critical'>('all')\n\n  // Initialisation des données\n  useEffect(() => {\n    const loadInitialData = () => {\n      setMarketAlerts(HubService.generateMarketAlerts())\n      setStockUpdates(HubService.generateStockUpdates())\n      setTrainingEvents(HubService.generateTrainingEvents())\n      setNewsUpdates(HubService.generateNewsUpdates())\n    }\n\n    loadInitialData()\n\n    // Simulation de la connexion temps réel\n    setRealTimeConnected(true)\n    \n    // Abonnement aux mises à jour temps réel\n    const unsubscribe = HubService.subscribeToRealTimeUpdates((update) => {\n      switch (update.type) {\n        case 'market_alert':\n          addMarketAlert(update.data)\n          break\n        case 'stock_update':\n          addStockUpdate(update.data)\n          break\n        case 'news_update':\n          addNewsUpdate(update.data)\n          break\n      }\n    })\n\n    return () => {\n      unsubscribe()\n      setRealTimeConnected(false)\n    }\n  }, [])\n\n  // Filtrage des alertes\n  const filteredAlerts = marketAlerts.filter(alert => {\n    const matchesSearch = searchQuery === '' || \n      alert.title.toLowerCase().includes(searchQuery.toLowerCase()) ||\n      alert.message.toLowerCase().includes(searchQuery.toLowerCase())\n    \n    const matchesSeverity = filterSeverity === 'all' || alert.severity === filterSeverity\n    \n    return matchesSearch && matchesSeverity && alert.is_active\n  })\n\n  const getSeverityColor = (severity: string) => {\n    switch (severity) {\n      case 'critical': return 'bg-red-100 text-red-800 border-red-200'\n      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200'\n      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200'\n      case 'low': return 'bg-blue-100 text-blue-800 border-blue-200'\n      default: return 'bg-gray-100 text-gray-800 border-gray-200'\n    }\n  }\n\n  const getUpdateTypeIcon = (type: string) => {\n    switch (type) {\n      case 'restock': return <TrendingUp className=\"h-4 w-4 text-green-600\" />\n      case 'decrease': return <TrendingUp className=\"h-4 w-4 text-orange-600 rotate-180\" />\n      case 'out_of_stock': return <AlertTriangle className=\"h-4 w-4 text-red-600\" />\n      default: return <Package className=\"h-4 w-4 text-blue-600\" />\n    }\n  }\n\n  const tabs = [\n    { id: 'alerts', label: 'Alertes Marché', icon: Bell, count: filteredAlerts.length },\n    { id: 'stocks', label: 'Stocks', icon: Package, count: stockUpdates.length },\n    { id: 'training', label: 'Formations', icon: Calendar, count: trainingEvents.length },\n    { id: 'news', label: 'Actualités', icon: Newspaper, count: newsUpdates.length }\n  ]\n\n  return (\n    <div className={`bg-white rounded-lg shadow-lg ${className}`}>\n      {/* Header avec statut temps réel */}\n      <div className=\"p-6 border-b border-gray-200\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h2 className=\"text-2xl font-bold text-gray-900\">Hub d'Information</h2>\n            <p className=\"text-gray-600 mt-1\">Veille marché en temps réel</p>\n          </div>\n          \n          <div className=\"flex items-center space-x-4\">\n            {/* Statut connexion */}\n            <div className=\"flex items-center space-x-2\">\n              {realTimeConnected ? (\n                <>\n                  <Wifi className=\"h-5 w-5 text-green-600\" />\n                  <span className=\"text-sm text-green-600 font-medium\">Connecté</span>\n                </>\n              ) : (\n                <>\n                  <WifiOff className=\"h-5 w-5 text-red-600\" />\n                  <span className=\"text-sm text-red-600 font-medium\">Déconnecté</span>\n                </>\n              )}\n            </div>\n            \n            {/* Dernière mise à jour */}\n            <div className=\"flex items-center space-x-2 text-sm text-gray-500\">\n              <Clock className=\"h-4 w-4\" />\n              <span>Mis à jour {formatDate(lastUpdateTime)}</span>\n            </div>\n            \n            {/* Bouton actualiser */}\n            <button \n              onClick={updateLastUpdateTime}\n              className=\"p-2 rounded-lg hover:bg-gray-100 transition-colors\"\n            >\n              <RefreshCw className=\"h-5 w-5 text-gray-600\" />\n            </button>\n          </div>\n        </div>\n\n        {/* Barre de recherche et filtres */}\n        <div className=\"mt-4 flex items-center space-x-4\">\n          <div className=\"flex-1 relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n            <input\n              type=\"text\"\n              placeholder=\"Rechercher dans le hub...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400 focus:border-transparent\"\n            />\n          </div>\n          \n          {activeTab === 'alerts' && (\n            <select\n              value={filterSeverity}\n              onChange={(e) => setFilterSeverity(e.target.value as any)}\n              className=\"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400 focus:border-transparent\"\n            >\n              <option value=\"all\">Toutes les priorités</option>\n              <option value=\"critical\">Critique</option>\n              <option value=\"high\">Élevée</option>\n              <option value=\"medium\">Moyenne</option>\n              <option value=\"low\">Faible</option>\n            </select>\n          )}\n        </div>\n      </div>\n\n      {/* Onglets */}\n      <div className=\"border-b border-gray-200\">\n        <nav className=\"flex space-x-8 px-6\">\n          {tabs.map((tab) => {\n            const Icon = tab.icon\n            const isActive = activeTab === tab.id\n            \n            return (\n              <button\n                key={tab.id}\n                onClick={() => setActiveTab(tab.id as any)}\n                className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${\n                  isActive\n                    ? 'border-amber-500 text-amber-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                <Icon className=\"h-5 w-5\" />\n                <span>{tab.label}</span>\n                {tab.count > 0 && (\n                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                    isActive ? 'bg-amber-100 text-amber-800' : 'bg-gray-100 text-gray-600'\n                  }`}>\n                    {tab.count}\n                  </span>\n                )}\n              </button>\n            )\n          })}\n        </nav>\n      </div>\n\n      {/* Contenu des onglets */}\n      <div className=\"p-6\">\n        <AnimatePresence mode=\"wait\">\n          {activeTab === 'alerts' && (\n            <motion.div\n              key=\"alerts\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -20 }}\n              className=\"space-y-4\"\n            >\n              {filteredAlerts.length === 0 ? (\n                <div className=\"text-center py-8 text-gray-500\">\n                  <Bell className=\"h-12 w-12 mx-auto mb-4 text-gray-300\" />\n                  <p>Aucune alerte correspondant aux critères</p>\n                </div>\n              ) : (\n                filteredAlerts.map((alert) => (\n                  <motion.div\n                    key={alert.id}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    className={`p-4 rounded-lg border ${getSeverityColor(alert.severity)}`}\n                  >\n                    <div className=\"flex items-start justify-between\">\n                      <div className=\"flex-1\">\n                        <div className=\"flex items-center space-x-2 mb-2\">\n                          <span className=\"text-xs font-medium uppercase tracking-wide\">\n                            {alert.type.replace('_', ' ')}\n                          </span>\n                          <span className=\"text-xs text-gray-500\">\n                            {formatDate(alert.created_at)}\n                          </span>\n                        </div>\n                        <h3 className=\"font-semibold mb-1\">{alert.title}</h3>\n                        <p className=\"text-sm mb-2\">{alert.message}</p>\n                        <div className=\"flex items-center space-x-4 text-xs text-gray-600\">\n                          <span>Catégorie: {alert.category}</span>\n                          <span>Régions: {alert.affected_regions.join(', ')}</span>\n                        </div>\n                      </div>\n                    </div>\n                  </motion.div>\n                ))\n              )}\n            </motion.div>\n          )}\n\n          {activeTab === 'stocks' && (\n            <motion.div\n              key=\"stocks\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -20 }}\n              className=\"space-y-4\"\n            >\n              {stockUpdates.length === 0 ? (\n                <div className=\"text-center py-8 text-gray-500\">\n                  <Package className=\"h-12 w-12 mx-auto mb-4 text-gray-300\" />\n                  <p>Aucune mise à jour de stock récente</p>\n                </div>\n              ) : (\n                stockUpdates.map((update) => (\n                  <motion.div\n                    key={update.id}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    className=\"p-4 rounded-lg border border-gray-200 bg-gray-50\"\n                  >\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center space-x-3\">\n                        {getUpdateTypeIcon(update.update_type)}\n                        <div>\n                          <h3 className=\"font-semibold text-gray-900\">\n                            Produit #{update.product_id}\n                          </h3>\n                          <p className=\"text-sm text-gray-600\">{update.location}</p>\n                        </div>\n                      </div>\n                      <div className=\"text-right\">\n                        <div className=\"text-lg font-bold text-gray-900\">\n                          {update.previous_quantity} → {update.current_quantity}\n                        </div>\n                        <div className=\"text-xs text-gray-500\">\n                          {formatDate(update.created_at)}\n                        </div>\n                      </div>\n                    </div>\n                  </motion.div>\n                ))\n              )}\n            </motion.div>\n          )}\n\n          {activeTab === 'training' && (\n            <motion.div\n              key=\"training\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -20 }}\n              className=\"space-y-4\"\n            >\n              {trainingEvents.length === 0 ? (\n                <div className=\"text-center py-8 text-gray-500\">\n                  <Calendar className=\"h-12 w-12 mx-auto mb-4 text-gray-300\" />\n                  <p>Aucun événement de formation programmé</p>\n                </div>\n              ) : (\n                trainingEvents.map((event) => (\n                  <motion.div\n                    key={event.id}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    className=\"p-6 rounded-lg border border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50\"\n                  >\n                    <div className=\"flex items-start justify-between\">\n                      <div className=\"flex-1\">\n                        <div className=\"flex items-center space-x-2 mb-2\">\n                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                            event.type === 'certification' ? 'bg-purple-100 text-purple-800' :\n                            event.type === 'webinar' ? 'bg-green-100 text-green-800' :\n                            event.type === 'workshop' ? 'bg-blue-100 text-blue-800' :\n                            'bg-gray-100 text-gray-800'\n                          }`}>\n                            {event.type}\n                          </span>\n                          {event.membership_required && (\n                            <span className=\"px-2 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800\">\n                              {event.membership_required}+\n                            </span>\n                          )}\n                        </div>\n                        <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                          {event.title}\n                        </h3>\n                        <p className=\"text-gray-600 mb-3\">{event.description}</p>\n                        <div className=\"grid grid-cols-2 gap-4 text-sm text-gray-600\">\n                          <div>\n                            <span className=\"font-medium\">Instructeur:</span> {event.instructor}\n                          </div>\n                          <div>\n                            <span className=\"font-medium\">Lieu:</span> {event.location}\n                          </div>\n                          <div>\n                            <span className=\"font-medium\">Date:</span> {formatDate(event.start_date)}\n                          </div>\n                          <div>\n                            <span className=\"font-medium\">Participants:</span> {event.current_participants}/{event.max_participants}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"text-right ml-4\">\n                        {event.registration_fee > 0 ? (\n                          <div className=\"text-lg font-bold text-gray-900\">\n                            {event.registration_fee.toLocaleString()} FCFA\n                          </div>\n                        ) : (\n                          <div className=\"text-lg font-bold text-green-600\">Gratuit</div>\n                        )}\n                        <button className=\"mt-2 px-4 py-2 bg-amber-500 text-white rounded-lg hover:bg-amber-600 transition-colors\">\n                          S'inscrire\n                        </button>\n                      </div>\n                    </div>\n                  </motion.div>\n                ))\n              )}\n            </motion.div>\n          )}\n\n          {activeTab === 'news' && (\n            <motion.div\n              key=\"news\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -20 }}\n              className=\"space-y-6\"\n            >\n              {newsUpdates.length === 0 ? (\n                <div className=\"text-center py-8 text-gray-500\">\n                  <Newspaper className=\"h-12 w-12 mx-auto mb-4 text-gray-300\" />\n                  <p>Aucune actualité récente</p>\n                </div>\n              ) : (\n                newsUpdates.map((news) => (\n                  <motion.div\n                    key={news.id}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    className={`p-6 rounded-lg border border-gray-200 ${\n                      news.is_featured ? 'bg-gradient-to-r from-amber-50 to-yellow-50 border-amber-200' : 'bg-white'\n                    }`}\n                  >\n                    <div className=\"flex items-start space-x-4\">\n                      {news.image_url && (\n                        <div className=\"w-24 h-24 bg-gray-200 rounded-lg flex-shrink-0\"></div>\n                      )}\n                      <div className=\"flex-1\">\n                        <div className=\"flex items-center space-x-2 mb-2\">\n                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                            news.category === 'regulation' ? 'bg-red-100 text-red-800' :\n                            news.category === 'technology' ? 'bg-blue-100 text-blue-800' :\n                            news.category === 'company' ? 'bg-green-100 text-green-800' :\n                            news.category === 'market' ? 'bg-purple-100 text-purple-800' :\n                            'bg-gray-100 text-gray-800'\n                          }`}>\n                            {news.category}\n                          </span>\n                          {news.is_featured && (\n                            <span className=\"px-2 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800\">\n                              À la une\n                            </span>\n                          )}\n                          <span className=\"text-xs text-gray-500\">\n                            {formatDate(news.published_at)}\n                          </span>\n                        </div>\n                        <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                          {news.title}\n                        </h3>\n                        <p className=\"text-gray-600 mb-3\">{news.summary}</p>\n                        <div className=\"flex items-center justify-between\">\n                          <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n                            <span>Par {news.author}</span>\n                            <div className=\"flex space-x-1\">\n                              {news.tags.slice(0, 3).map((tag) => (\n                                <span key={tag} className=\"px-2 py-1 bg-gray-100 rounded text-xs\">\n                                  #{tag}\n                                </span>\n                              ))}\n                            </div>\n                          </div>\n                          {news.source_url && (\n                            <a\n                              href={news.source_url}\n                              target=\"_blank\"\n                              rel=\"noopener noreferrer\"\n                              className=\"text-amber-600 hover:text-amber-700 text-sm font-medium\"\n                            >\n                              Lire la suite →\n                            </a>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  </motion.div>\n                ))\n              )}\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AACA;AACA;;;AApBA;;;;;;;AA2Be,SAAS,eAAe,EAAE,YAAY,EAAE,EAAuB;;IAC5E,MAAM,EACJ,YAAY,EACZ,YAAY,EACZ,cAAc,EACd,WAAW,EACX,iBAAiB,EACjB,cAAc,EACd,eAAe,EACf,cAAc,EACd,eAAe,EACf,cAAc,EACd,iBAAiB,EACjB,cAAc,EACd,aAAa,EACb,oBAAoB,EACpB,oBAAoB,EACrB,GAAG,CAAA,GAAA,2HAAA,CAAA,WAAQ,AAAD;IAEX,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6C;IACtF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkD;IAErG,6BAA6B;IAC7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;4DAAkB;oBACtB,gBAAgB,gIAAA,CAAA,aAAU,CAAC,oBAAoB;oBAC/C,gBAAgB,gIAAA,CAAA,aAAU,CAAC,oBAAoB;oBAC/C,kBAAkB,gIAAA,CAAA,aAAU,CAAC,sBAAsB;oBACnD,eAAe,gIAAA,CAAA,aAAU,CAAC,mBAAmB;gBAC/C;;YAEA;YAEA,wCAAwC;YACxC,qBAAqB;YAErB,yCAAyC;YACzC,MAAM,cAAc,gIAAA,CAAA,aAAU,CAAC,0BAA0B;wDAAC,CAAC;oBACzD,OAAQ,OAAO,IAAI;wBACjB,KAAK;4BACH,eAAe,OAAO,IAAI;4BAC1B;wBACF,KAAK;4BACH,eAAe,OAAO,IAAI;4BAC1B;wBACF,KAAK;4BACH,cAAc,OAAO,IAAI;4BACzB;oBACJ;gBACF;;YAEA;4CAAO;oBACL;oBACA,qBAAqB;gBACvB;;QACF;mCAAG,EAAE;IAEL,uBAAuB;IACvB,MAAM,iBAAiB,aAAa,MAAM,CAAC,CAAA;QACzC,MAAM,gBAAgB,gBAAgB,MACpC,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC1D,MAAM,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QAE9D,MAAM,kBAAkB,mBAAmB,SAAS,MAAM,QAAQ,KAAK;QAEvE,OAAO,iBAAiB,mBAAmB,MAAM,SAAS;IAC5D;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAO,OAAO;YACnB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBAAW,qBAAO,6LAAC,qNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC7C,KAAK;gBAAY,qBAAO,6LAAC,qNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC9C,KAAK;gBAAgB,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YACrD;gBAAS,qBAAO,6LAAC,2MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;QACrC;IACF;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAU,OAAO;YAAkB,MAAM,qMAAA,CAAA,OAAI;YAAE,OAAO,eAAe,MAAM;QAAC;QAClF;YAAE,IAAI;YAAU,OAAO;YAAU,MAAM,2MAAA,CAAA,UAAO;YAAE,OAAO,aAAa,MAAM;QAAC;QAC3E;YAAE,IAAI;YAAY,OAAO;YAAc,MAAM,6MAAA,CAAA,WAAQ;YAAE,OAAO,eAAe,MAAM;QAAC;QACpF;YAAE,IAAI;YAAQ,OAAO;YAAc,MAAM,+MAAA,CAAA,YAAS;YAAE,OAAO,YAAY,MAAM;QAAC;KAC/E;IAED,qBACE,6LAAC;QAAI,WAAW,CAAC,8BAA8B,EAAE,WAAW;;0BAE1D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,6LAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;;0CAGpC,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;kDACZ,kCACC;;8DACE,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;oDAAK,WAAU;8DAAqC;;;;;;;yEAGvD;;8DACE,6LAAC,+MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,6LAAC;oDAAK,WAAU;8DAAmC;;;;;;;;;;;;;kDAMzD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;;oDAAK;oDAAY,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE;;;;;;;;;;;;;kDAI/B,6LAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAM3B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;;;;;;;4BAIb,cAAc,0BACb,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;gCACjD,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAM;;;;;;kDACpB,6LAAC;wCAAO,OAAM;kDAAW;;;;;;kDACzB,6LAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,6LAAC;wCAAO,OAAM;kDAAS;;;;;;kDACvB,6LAAC;wCAAO,OAAM;kDAAM;;;;;;;;;;;;;;;;;;;;;;;;0BAO5B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC;wBACT,MAAM,OAAO,IAAI,IAAI;wBACrB,MAAM,WAAW,cAAc,IAAI,EAAE;wBAErC,qBACE,6LAAC;4BAEC,SAAS,IAAM,aAAa,IAAI,EAAE;4BAClC,WAAW,CAAC,uFAAuF,EACjG,WACI,oCACA,8EACJ;;8CAEF,6LAAC;oCAAK,WAAU;;;;;;8CAChB,6LAAC;8CAAM,IAAI,KAAK;;;;;;gCACf,IAAI,KAAK,GAAG,mBACX,6LAAC;oCAAK,WAAW,CAAC,2CAA2C,EAC3D,WAAW,gCAAgC,6BAC3C;8CACC,IAAI,KAAK;;;;;;;2BAdT,IAAI,EAAE;;;;;oBAmBjB;;;;;;;;;;;0BAKJ,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,4LAAA,CAAA,kBAAe;oBAAC,MAAK;;wBACnB,cAAc,0BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,MAAM;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC3B,WAAU;sCAET,eAAe,MAAM,KAAK,kBACzB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;kDAAE;;;;;;;;;;;uCAGL,eAAe,GAAG,CAAC,CAAC,sBAClB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,WAAW,CAAC,sBAAsB,EAAE,iBAAiB,MAAM,QAAQ,GAAG;8CAEtE,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEACb,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK;;;;;;sEAE3B,6LAAC;4DAAK,WAAU;sEACb,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,MAAM,UAAU;;;;;;;;;;;;8DAGhC,6LAAC;oDAAG,WAAU;8DAAsB,MAAM,KAAK;;;;;;8DAC/C,6LAAC;oDAAE,WAAU;8DAAgB,MAAM,OAAO;;;;;;8DAC1C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;gEAAK;gEAAY,MAAM,QAAQ;;;;;;;sEAChC,6LAAC;;gEAAK;gEAAU,MAAM,gBAAgB,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;;;mCAnB7C,MAAM,EAAE;;;;;2BAdf;;;;;wBA2CP,cAAc,0BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,MAAM;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC3B,WAAU;sCAET,aAAa,MAAM,KAAK,kBACvB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,2MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,6LAAC;kDAAE;;;;;;;;;;;uCAGL,aAAa,GAAG,CAAC,CAAC,uBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,WAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;oDACZ,kBAAkB,OAAO,WAAW;kEACrC,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;;oEAA8B;oEAChC,OAAO,UAAU;;;;;;;0EAE7B,6LAAC;gEAAE,WAAU;0EAAyB,OAAO,QAAQ;;;;;;;;;;;;;;;;;;0DAGzD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;4DACZ,OAAO,iBAAiB;4DAAC;4DAAI,OAAO,gBAAgB;;;;;;;kEAEvD,6LAAC;wDAAI,WAAU;kEACZ,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,OAAO,UAAU;;;;;;;;;;;;;;;;;;mCApB9B,OAAO,EAAE;;;;;2BAdhB;;;;;wBA4CP,cAAc,4BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,MAAM;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC3B,WAAU;sCAET,eAAe,MAAM,KAAK,kBACzB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;kDAAE;;;;;;;;;;;uCAGL,eAAe,GAAG,CAAC,CAAC,sBAClB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,WAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAW,CAAC,2CAA2C,EAC3D,MAAM,IAAI,KAAK,kBAAkB,kCACjC,MAAM,IAAI,KAAK,YAAY,gCAC3B,MAAM,IAAI,KAAK,aAAa,8BAC5B,6BACA;0EACC,MAAM,IAAI;;;;;;4DAEZ,MAAM,mBAAmB,kBACxB,6LAAC;gEAAK,WAAU;;oEACb,MAAM,mBAAmB;oEAAC;;;;;;;;;;;;;kEAIjC,6LAAC;wDAAG,WAAU;kEACX,MAAM,KAAK;;;;;;kEAEd,6LAAC;wDAAE,WAAU;kEAAsB,MAAM,WAAW;;;;;;kEACpD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAAc;;;;;;oEAAmB;oEAAE,MAAM,UAAU;;;;;;;0EAErE,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAAc;;;;;;oEAAY;oEAAE,MAAM,QAAQ;;;;;;;0EAE5D,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAAc;;;;;;oEAAY;oEAAE,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,MAAM,UAAU;;;;;;;0EAEzE,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAAc;;;;;;oEAAoB;oEAAE,MAAM,oBAAoB;oEAAC;oEAAE,MAAM,gBAAgB;;;;;;;;;;;;;;;;;;;0DAI7G,6LAAC;gDAAI,WAAU;;oDACZ,MAAM,gBAAgB,GAAG,kBACxB,6LAAC;wDAAI,WAAU;;4DACZ,MAAM,gBAAgB,CAAC,cAAc;4DAAG;;;;;;6EAG3C,6LAAC;wDAAI,WAAU;kEAAmC;;;;;;kEAEpD,6LAAC;wDAAO,WAAU;kEAAyF;;;;;;;;;;;;;;;;;;mCAjD1G,MAAM,EAAE;;;;;2BAdf;;;;;wBA0EP,cAAc,wBACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,MAAM;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC3B,WAAU;sCAET,YAAY,MAAM,KAAK,kBACtB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+MAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,6LAAC;kDAAE;;;;;;;;;;;uCAGL,YAAY,GAAG,CAAC,CAAC,qBACf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,WAAW,CAAC,sCAAsC,EAChD,KAAK,WAAW,GAAG,iEAAiE,YACpF;8CAEF,cAAA,6LAAC;wCAAI,WAAU;;4CACZ,KAAK,SAAS,kBACb,6LAAC;gDAAI,WAAU;;;;;;0DAEjB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAW,CAAC,2CAA2C,EAC3D,KAAK,QAAQ,KAAK,eAAe,4BACjC,KAAK,QAAQ,KAAK,eAAe,8BACjC,KAAK,QAAQ,KAAK,YAAY,gCAC9B,KAAK,QAAQ,KAAK,WAAW,kCAC7B,6BACA;0EACC,KAAK,QAAQ;;;;;;4DAEf,KAAK,WAAW,kBACf,6LAAC;gEAAK,WAAU;0EAAyE;;;;;;0EAI3F,6LAAC;gEAAK,WAAU;0EACb,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,YAAY;;;;;;;;;;;;kEAGjC,6LAAC;wDAAG,WAAU;kEACX,KAAK,KAAK;;;;;;kEAEb,6LAAC;wDAAE,WAAU;kEAAsB,KAAK,OAAO;;;;;;kEAC/C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;;4EAAK;4EAAK,KAAK,MAAM;;;;;;;kFACtB,6LAAC;wEAAI,WAAU;kFACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBAC1B,6LAAC;gFAAe,WAAU;;oFAAwC;oFAC9D;;+EADO;;;;;;;;;;;;;;;;4DAMhB,KAAK,UAAU,kBACd,6LAAC;gEACC,MAAM,KAAK,UAAU;gEACrB,QAAO;gEACP,KAAI;gEACJ,WAAU;0EACX;;;;;;;;;;;;;;;;;;;;;;;;mCApDJ,KAAK,EAAE;;;;;2BAdd;;;;;;;;;;;;;;;;;;;;;;AAkFlB;GApcwB;;QAiBlB,2HAAA,CAAA,WAAQ;;;KAjBU", "debugId": null}}, {"offset": {"line": 1728, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/components/hub/RealTimeNotifications.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { \n  X, \n  Bell, \n  AlertTriangle, \n  TrendingUp, \n  Package,\n  Newspaper,\n  CheckCircle\n} from 'lucide-react'\nimport { useStore } from '@/store/useStore'\nimport { formatDate } from '@/lib/utils'\n\ninterface Notification {\n  id: string\n  type: 'market_alert' | 'stock_update' | 'news_update' | 'system'\n  title: string\n  message: string\n  severity: 'low' | 'medium' | 'high' | 'critical'\n  timestamp: string\n  isRead: boolean\n  autoHide?: boolean\n}\n\nexport default function RealTimeNotifications() {\n  const [notifications, setNotifications] = useState<Notification[]>([])\n  const [isVisible, setIsVisible] = useState(true)\n  const { realTimeConnected } = useStore()\n\n  // Simulation de nouvelles notifications\n  useEffect(() => {\n    if (!realTimeConnected) return\n\n    const interval = setInterval(() => {\n      const notificationTypes = [\n        {\n          type: 'market_alert' as const,\n          title: 'Nouvelle Alerte Marché',\n          message: 'Stock critique détecté chez ElectroDistrib Abidjan',\n          severity: 'high' as const,\n          autoHide: false\n        },\n        {\n          type: 'stock_update' as const,\n          title: 'Mise à Jour Stock',\n          message: 'Réapprovisionnement de 200 unités - Disjoncteurs C32',\n          severity: 'medium' as const,\n          autoHide: true\n        },\n        {\n          type: 'news_update' as const,\n          title: 'Nouvelle Actualité',\n          message: 'Nouvelle réglementation photovoltaïque au Sénégal',\n          severity: 'low' as const,\n          autoHide: true\n        },\n        {\n          type: 'system' as const,\n          title: 'Système',\n          message: 'Synchronisation des données terminée avec succès',\n          severity: 'low' as const,\n          autoHide: true\n        }\n      ]\n\n      // Ajouter une notification aléatoire toutes les 45 secondes\n      if (Math.random() < 0.3) {\n        const randomNotification = notificationTypes[Math.floor(Math.random() * notificationTypes.length)]\n        const newNotification: Notification = {\n          id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n          ...randomNotification,\n          timestamp: new Date().toISOString(),\n          isRead: false\n        }\n\n        setNotifications(prev => [newNotification, ...prev.slice(0, 4)]) // Garder max 5 notifications\n\n        // Auto-hide après 5 secondes si autoHide est true\n        if (newNotification.autoHide) {\n          setTimeout(() => {\n            setNotifications(prev => prev.filter(n => n.id !== newNotification.id))\n          }, 5000)\n        }\n      }\n    }, 45000) // Toutes les 45 secondes\n\n    return () => clearInterval(interval)\n  }, [realTimeConnected])\n\n  const getNotificationIcon = (type: string) => {\n    switch (type) {\n      case 'market_alert':\n        return <AlertTriangle className=\"h-5 w-5\" />\n      case 'stock_update':\n        return <Package className=\"h-5 w-5\" />\n      case 'news_update':\n        return <Newspaper className=\"h-5 w-5\" />\n      case 'system':\n        return <CheckCircle className=\"h-5 w-5\" />\n      default:\n        return <Bell className=\"h-5 w-5\" />\n    }\n  }\n\n  const getSeverityColor = (severity: string) => {\n    switch (severity) {\n      case 'critical':\n        return 'bg-red-500 border-red-600 text-white'\n      case 'high':\n        return 'bg-orange-500 border-orange-600 text-white'\n      case 'medium':\n        return 'bg-blue-500 border-blue-600 text-white'\n      case 'low':\n        return 'bg-green-500 border-green-600 text-white'\n      default:\n        return 'bg-gray-500 border-gray-600 text-white'\n    }\n  }\n\n  const markAsRead = (id: string) => {\n    setNotifications(prev => \n      prev.map(notif => \n        notif.id === id ? { ...notif, isRead: true } : notif\n      )\n    )\n  }\n\n  const removeNotification = (id: string) => {\n    setNotifications(prev => prev.filter(notif => notif.id !== id))\n  }\n\n  const clearAllNotifications = () => {\n    setNotifications([])\n  }\n\n  if (!isVisible || notifications.length === 0) {\n    return null\n  }\n\n  return (\n    <div className=\"fixed top-4 right-4 z-50 w-96 max-w-sm\">\n      {/* Header des notifications */}\n      <div className=\"bg-white rounded-t-lg border border-gray-200 px-4 py-3 flex items-center justify-between shadow-lg\">\n        <div className=\"flex items-center space-x-2\">\n          <Bell className=\"h-5 w-5 text-amber-600\" />\n          <span className=\"font-semibold text-gray-900\">Notifications</span>\n          {realTimeConnected && (\n            <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n          )}\n        </div>\n        <div className=\"flex items-center space-x-2\">\n          {notifications.length > 1 && (\n            <button\n              onClick={clearAllNotifications}\n              className=\"text-xs text-gray-500 hover:text-gray-700\"\n            >\n              Tout effacer\n            </button>\n          )}\n          <button\n            onClick={() => setIsVisible(false)}\n            className=\"text-gray-400 hover:text-gray-600\"\n          >\n            <X className=\"h-4 w-4\" />\n          </button>\n        </div>\n      </div>\n\n      {/* Liste des notifications */}\n      <div className=\"max-h-96 overflow-y-auto\">\n        <AnimatePresence>\n          {notifications.map((notification, index) => (\n            <motion.div\n              key={notification.id}\n              initial={{ opacity: 0, x: 300, scale: 0.8 }}\n              animate={{ opacity: 1, x: 0, scale: 1 }}\n              exit={{ opacity: 0, x: 300, scale: 0.8 }}\n              transition={{ duration: 0.3, delay: index * 0.1 }}\n              className={`border-x border-gray-200 ${\n                index === notifications.length - 1 ? 'border-b rounded-b-lg' : 'border-b-0'\n              } ${\n                notification.isRead ? 'bg-gray-50' : 'bg-white'\n              } shadow-lg`}\n            >\n              <div className=\"p-4\">\n                <div className=\"flex items-start space-x-3\">\n                  <div className={`p-2 rounded-lg ${getSeverityColor(notification.severity)}`}>\n                    {getNotificationIcon(notification.type)}\n                  </div>\n                  \n                  <div className=\"flex-1 min-w-0\">\n                    <div className=\"flex items-center justify-between mb-1\">\n                      <h4 className=\"text-sm font-semibold text-gray-900 truncate\">\n                        {notification.title}\n                      </h4>\n                      <button\n                        onClick={() => removeNotification(notification.id)}\n                        className=\"text-gray-400 hover:text-gray-600 ml-2\"\n                      >\n                        <X className=\"h-3 w-3\" />\n                      </button>\n                    </div>\n                    \n                    <p className=\"text-sm text-gray-600 mb-2\">\n                      {notification.message}\n                    </p>\n                    \n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-xs text-gray-500\">\n                        {formatDate(notification.timestamp)}\n                      </span>\n                      \n                      {!notification.isRead && (\n                        <button\n                          onClick={() => markAsRead(notification.id)}\n                          className=\"text-xs text-amber-600 hover:text-amber-700 font-medium\"\n                        >\n                          Marquer comme lu\n                        </button>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </AnimatePresence>\n      </div>\n\n      {/* Bouton pour réafficher si masqué */}\n      {!isVisible && (\n        <motion.button\n          initial={{ opacity: 0, scale: 0.8 }}\n          animate={{ opacity: 1, scale: 1 }}\n          onClick={() => setIsVisible(true)}\n          className=\"fixed top-4 right-4 bg-amber-500 text-white p-3 rounded-full shadow-lg hover:bg-amber-600 transition-colors\"\n        >\n          <Bell className=\"h-5 w-5\" />\n          {notifications.filter(n => !n.isRead).length > 0 && (\n            <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\">\n              {notifications.filter(n => !n.isRead).length}\n            </span>\n          )}\n        </motion.button>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;;;AAdA;;;;;;AA2Be,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,WAAQ,AAAD;IAErC,wCAAwC;IACxC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,IAAI,CAAC,mBAAmB;YAExB,MAAM,WAAW;4DAAY;oBAC3B,MAAM,oBAAoB;wBACxB;4BACE,MAAM;4BACN,OAAO;4BACP,SAAS;4BACT,UAAU;4BACV,UAAU;wBACZ;wBACA;4BACE,MAAM;4BACN,OAAO;4BACP,SAAS;4BACT,UAAU;4BACV,UAAU;wBACZ;wBACA;4BACE,MAAM;4BACN,OAAO;4BACP,SAAS;4BACT,UAAU;4BACV,UAAU;wBACZ;wBACA;4BACE,MAAM;4BACN,OAAO;4BACP,SAAS;4BACT,UAAU;4BACV,UAAU;wBACZ;qBACD;oBAED,4DAA4D;oBAC5D,IAAI,KAAK,MAAM,KAAK,KAAK;wBACvB,MAAM,qBAAqB,iBAAiB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,kBAAkB,MAAM,EAAE;wBAClG,MAAM,kBAAgC;4BACpC,IAAI,CAAC,MAAM,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;4BACpE,GAAG,kBAAkB;4BACrB,WAAW,IAAI,OAAO,WAAW;4BACjC,QAAQ;wBACV;wBAEA;wEAAiB,CAAA,OAAQ;oCAAC;uCAAoB,KAAK,KAAK,CAAC,GAAG;iCAAG;uEAAE,6BAA6B;;wBAE9F,kDAAkD;wBAClD,IAAI,gBAAgB,QAAQ,EAAE;4BAC5B;4EAAW;oCACT;oFAAiB,CAAA,OAAQ,KAAK,MAAM;4FAAC,CAAA,IAAK,EAAE,EAAE,KAAK,gBAAgB,EAAE;;;gCACvE;2EAAG;wBACL;oBACF;gBACF;2DAAG,OAAO,yBAAyB;;YAEnC;mDAAO,IAAM,cAAc;;QAC7B;0CAAG;QAAC;KAAkB;IAEtB,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,6LAAC,2MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,6LAAC,+MAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;YAC9B,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,QACP,MAAM,EAAE,KAAK,KAAK;oBAAE,GAAG,KAAK;oBAAE,QAAQ;gBAAK,IAAI;IAGrD;IAEA,MAAM,qBAAqB,CAAC;QAC1B,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IAC7D;IAEA,MAAM,wBAAwB;QAC5B,iBAAiB,EAAE;IACrB;IAEA,IAAI,CAAC,aAAa,cAAc,MAAM,KAAK,GAAG;QAC5C,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC;gCAAK,WAAU;0CAA8B;;;;;;4BAC7C,mCACC,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAGnB,6LAAC;wBAAI,WAAU;;4BACZ,cAAc,MAAM,GAAG,mBACtB,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;0CAIH,6LAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAU;0CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAMnB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,4LAAA,CAAA,kBAAe;8BACb,cAAc,GAAG,CAAC,CAAC,cAAc,sBAChC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;gCAAK,OAAO;4BAAI;4BAC1C,SAAS;gCAAE,SAAS;gCAAG,GAAG;gCAAG,OAAO;4BAAE;4BACtC,MAAM;gCAAE,SAAS;gCAAG,GAAG;gCAAK,OAAO;4BAAI;4BACvC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,WAAW,CAAC,yBAAyB,EACnC,UAAU,cAAc,MAAM,GAAG,IAAI,0BAA0B,aAChE,CAAC,EACA,aAAa,MAAM,GAAG,eAAe,WACtC,UAAU,CAAC;sCAEZ,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAW,CAAC,eAAe,EAAE,iBAAiB,aAAa,QAAQ,GAAG;sDACxE,oBAAoB,aAAa,IAAI;;;;;;sDAGxC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEACX,aAAa,KAAK;;;;;;sEAErB,6LAAC;4DACC,SAAS,IAAM,mBAAmB,aAAa,EAAE;4DACjD,WAAU;sEAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gEAAC,WAAU;;;;;;;;;;;;;;;;;8DAIjB,6LAAC;oDAAE,WAAU;8DACV,aAAa,OAAO;;;;;;8DAGvB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEACb,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,aAAa,SAAS;;;;;;wDAGnC,CAAC,aAAa,MAAM,kBACnB,6LAAC;4DACC,SAAS,IAAM,WAAW,aAAa,EAAE;4DACzC,WAAU;sEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BA3CN,aAAa,EAAE;;;;;;;;;;;;;;;YAyD3B,CAAC,2BACA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAI;gBAClC,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAE;gBAChC,SAAS,IAAM,aAAa;gBAC5B,WAAU;;kCAEV,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBACf,cAAc,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,MAAM,EAAE,MAAM,GAAG,mBAC7C,6LAAC;wBAAK,WAAU;kCACb,cAAc,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,MAAM,EAAE,MAAM;;;;;;;;;;;;;;;;;;AAO1D;GA/NwB;;QAGQ,2HAAA,CAAA,WAAQ;;;KAHhB", "debugId": null}}, {"offset": {"line": 2175, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/app/hub/page.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { \n  ArrowLeft,\n  BarChart3,\n  TrendingUp,\n  Users,\n  Zap\n} from 'lucide-react'\nimport Link from 'next/link'\nimport InformationHub from '@/components/hub/InformationHub'\nimport RealTimeNotifications from '@/components/hub/RealTimeNotifications'\nimport RealTimeDashboard from '@/components/hub/RealTimeDashboard'\n\nexport default function HubPage() {\n  const stats = [\n    {\n      label: 'Alertes Actives',\n      value: '12',\n      change: '+3',\n      changeType: 'increase',\n      icon: <Zap className=\"h-6 w-6\" />\n    },\n    {\n      label: 'Mises à Jour Stock',\n      value: '47',\n      change: '+8',\n      changeType: 'increase',\n      icon: <BarChart3 className=\"h-6 w-6\" />\n    },\n    {\n      label: 'Formations Disponibles',\n      value: '6',\n      change: '+2',\n      changeType: 'increase',\n      icon: <Users className=\"h-6 w-6\" />\n    },\n    {\n      label: 'Actualités',\n      value: '23',\n      change: '+5',\n      changeType: 'increase',\n      icon: <TrendingUp className=\"h-6 w-6\" />\n    }\n  ]\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100\">\n      {/* Header */}\n      <header className=\"bg-white/80 backdrop-blur-md border-b border-slate-200 sticky top-0 z-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-16\">\n            <div className=\"flex items-center space-x-4\">\n              <Link \n                href=\"/\"\n                className=\"flex items-center space-x-2 text-slate-700 hover:text-amber-600 transition-colors\"\n              >\n                <ArrowLeft className=\"h-5 w-5\" />\n                <span>Retour à l'accueil</span>\n              </Link>\n            </div>\n            \n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-amber-400 to-amber-500 rounded-lg flex items-center justify-center\">\n                <Zap className=\"h-5 w-5 text-slate-900\" />\n              </div>\n              <div>\n                <h1 className=\"text-lg font-bold text-slate-900\">Pro Matos</h1>\n                <p className=\"text-xs text-slate-600\">Hub d'Information</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Contenu principal */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Titre et description */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"mb-8\"\n        >\n          <h1 className=\"text-3xl font-bold text-slate-900 mb-2\">\n            Hub d'Information et Veille\n          </h1>\n          <p className=\"text-lg text-slate-600\">\n            Votre centre de contrôle pour maîtriser l'écosystème électrique en temps réel\n          </p>\n        </motion.div>\n\n        {/* Statistiques rapides */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.1 }}\n          className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\"\n        >\n          {stats.map((stat, index) => (\n            <motion.div\n              key={stat.label}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.1 + index * 0.05 }}\n              className=\"industrial-card p-6\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-slate-600\">{stat.label}</p>\n                  <div className=\"flex items-baseline space-x-2\">\n                    <p className=\"text-2xl font-bold text-slate-900\">{stat.value}</p>\n                    <span className={`text-sm font-medium ${\n                      stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'\n                    }`}>\n                      {stat.change}\n                    </span>\n                  </div>\n                </div>\n                <div className=\"p-3 bg-amber-100 rounded-lg text-amber-600\">\n                  {stat.icon}\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* Hub d'Information principal */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.2 }}\n        >\n          <InformationHub />\n        </motion.div>\n\n        {/* Section d'aide */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.3 }}\n          className=\"mt-8 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200\"\n        >\n          <h3 className=\"text-lg font-semibold text-blue-900 mb-2\">\n            💡 Comment utiliser le Hub d'Information\n          </h3>\n          <div className=\"grid md:grid-cols-2 gap-4 text-sm text-blue-800\">\n            <div>\n              <h4 className=\"font-medium mb-1\">Alertes Marché</h4>\n              <p>Surveillez les ruptures de stock, variations de prix et nouveaux produits en temps réel</p>\n            </div>\n            <div>\n              <h4 className=\"font-medium mb-1\">Mises à Jour Stock</h4>\n              <p>Suivez les mouvements de stock chez tous vos fournisseurs partenaires</p>\n            </div>\n            <div>\n              <h4 className=\"font-medium mb-1\">Formations</h4>\n              <p>Accédez aux formations exclusives et certifications selon votre niveau d'adhésion</p>\n            </div>\n            <div>\n              <h4 className=\"font-medium mb-1\">Actualités</h4>\n              <p>Restez informé des dernières tendances et réglementations du secteur</p>\n            </div>\n          </div>\n        </motion.div>\n      </main>\n\n      {/* Notifications temps réel */}\n      <RealTimeNotifications />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AACA;AACA;AAZA;;;;;;;AAee,SAAS;IACtB,MAAM,QAAQ;QACZ;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,oBAAM,6LAAC,mMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;QACvB;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,oBAAM,6LAAC,qNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;QAC7B;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;QACzB;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,oBAAM,6LAAC,qNAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;QAC9B;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;0CAIV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;;;;;;kDAEjB,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhD,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,WAAU;;0CAEV,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CAGvD,6LAAC;gCAAE,WAAU;0CAAyB;;;;;;;;;;;;kCAMxC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;wBACzB,WAAU;kCAET,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO,MAAM,QAAQ;gCAAK;gCACxC,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAsC,KAAK,KAAK;;;;;;8DAC7D,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAqC,KAAK,KAAK;;;;;;sEAC5D,6LAAC;4DAAK,WAAW,CAAC,oBAAoB,EACpC,KAAK,UAAU,KAAK,aAAa,mBAAmB,gBACpD;sEACC,KAAK,MAAM;;;;;;;;;;;;;;;;;;sDAIlB,6LAAC;4CAAI,WAAU;sDACZ,KAAK,IAAI;;;;;;;;;;;;+BAnBT,KAAK,KAAK;;;;;;;;;;kCA2BrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;kCAEzB,cAAA,6LAAC,8IAAA,CAAA,UAAc;;;;;;;;;;kCAIjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;wBACzB,WAAU;;0CAEV,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAGzD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAmB;;;;;;0DACjC,6LAAC;0DAAE;;;;;;;;;;;;kDAEL,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAmB;;;;;;0DACjC,6LAAC;0DAAE;;;;;;;;;;;;kDAEL,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAmB;;;;;;0DACjC,6LAAC;0DAAE;;;;;;;;;;;;kDAEL,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAmB;;;;;;0DACjC,6LAAC;0DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOX,6LAAC,qJAAA,CAAA,UAAqB;;;;;;;;;;;AAG5B;KA5JwB", "debugId": null}}]}