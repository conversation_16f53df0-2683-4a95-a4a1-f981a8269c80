'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  History, 
  FileText, 
  CheckCircle, 
  Clock,
  AlertTriangle,
  ArrowLeft,
  Download,
  Eye,
  Filter,
  Search
} from 'lucide-react'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { useAuthStore } from '@/lib/stores/authStore'
import { supabase } from '@/lib/supabase/client'
import GlobalNavigation from '@/components/navigation/GlobalNavigation'

interface Validation {
  id: number
  title: string
  description: string
  project_type: string
  urgency: 'low' | 'normal' | 'high'
  status: 'En cours' | 'Validé' | 'Refusé'
  file_url: string | null
  file_name: string | null
  admin_notes: string | null
  created_at: string
  updated_at: string
}

export default function ValidationHistoryPage() {
  const { user } = useAuthStore()
  const [validations, setValidations] = useState<Validation[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<'all' | 'En cours' | 'Validé' | 'Refusé'>('all')

  useEffect(() => {
    if (user) {
      fetchValidations()
    }
  }, [user])

  const fetchValidations = async () => {
    if (!user) return

    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('validations')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })

      if (error) throw error
      setValidations(data || [])
    } catch (error) {
      console.error('Erreur lors du chargement de l\'historique:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredValidations = validations.filter(validation => {
    const matchesSearch = validation.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         validation.description.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = statusFilter === 'all' || validation.status === statusFilter
    return matchesSearch && matchesStatus
  })

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'En cours': return <Clock className="h-4 w-4 text-yellow-500" />
      case 'Validé': return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'Refusé': return <AlertTriangle className="h-4 w-4 text-red-500" />
      default: return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'En cours': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'Validé': return 'bg-green-100 text-green-800 border-green-200'
      case 'Refusé': return 'bg-red-100 text-red-800 border-red-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'high': return 'bg-red-100 text-red-800'
      case 'normal': return 'bg-yellow-100 text-yellow-800'
      case 'low': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getUrgencyLabel = (urgency: string) => {
    switch (urgency) {
      case 'high': return 'Urgent'
      case 'normal': return 'Normal'
      case 'low': return 'Faible'
      default: return 'Normal'
    }
  }

  const downloadFile = async (fileUrl: string, fileName: string) => {
    try {
      const { data } = supabase.storage
        .from('documents')
        .getPublicUrl(fileUrl)
      
      const link = document.createElement('a')
      link.href = data.publicUrl
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    } catch (error) {
      console.error('Erreur lors du téléchargement:', error)
    }
  }

  const stats = {
    total: validations.length,
    enCours: validations.filter(v => v.status === 'En cours').length,
    valide: validations.filter(v => v.status === 'Validé').length,
    refuse: validations.filter(v => v.status === 'Refusé').length
  }

  return (
    <>
      <GlobalNavigation />
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
        {/* Header */}
        <div className="bg-white border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Link
                  href="/validation"
                  className="flex items-center space-x-2 text-slate-700 hover:text-blue-600 transition-colors"
                >
                  <ArrowLeft className="h-5 w-5" />
                  <span>Retour aux Validations</span>
                </Link>
                <div className="h-6 w-px bg-gray-300" />
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 flex items-center space-x-2">
                    <History className="h-6 w-6" />
                    <span>Historique des Validations</span>
                  </h1>
                  <p className="text-gray-600">Suivez l'état de toutes vos demandes</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Statistiques */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <FileText className="h-8 w-8 text-blue-500" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Total</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <Clock className="h-8 w-8 text-yellow-500" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">En cours</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.enCours}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <CheckCircle className="h-8 w-8 text-green-500" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Validées</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.valide}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <AlertTriangle className="h-8 w-8 text-red-500" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Refusées</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.refuse}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Filtres */}
          <Card className="mb-8">
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Rechercher par titre ou description..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Filter className="h-4 w-4 text-gray-500" />
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value as any)}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">Tous les statuts</option>
                    <option value="En cours">En cours</option>
                    <option value="Validé">Validé</option>
                    <option value="Refusé">Refusé</option>
                  </select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Liste des validations */}
          <Card>
            <CardHeader>
              <CardTitle>Validations ({filteredValidations.length})</CardTitle>
              <CardDescription>
                Cliquez sur une validation pour voir les détails
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500" />
                  <span className="ml-2 text-gray-600">Chargement...</span>
                </div>
              ) : filteredValidations.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500 mb-4">
                    {searchQuery || statusFilter !== 'all' 
                      ? 'Aucune validation ne correspond à vos critères'
                      : 'Aucune validation soumise pour le moment'
                    }
                  </p>
                  <Link href="/validation">
                    <Button>Créer une validation</Button>
                  </Link>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredValidations.map((validation) => (
                    <motion.div
                      key={validation.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-3">
                            {getStatusIcon(validation.status)}
                            <h3 className="font-semibold text-gray-900 text-lg">{validation.title}</h3>
                            <Badge className={getStatusColor(validation.status)}>
                              {validation.status}
                            </Badge>
                            {validation.urgency && (
                              <Badge className={getUrgencyColor(validation.urgency)}>
                                {getUrgencyLabel(validation.urgency)}
                              </Badge>
                            )}
                          </div>
                          
                          <p className="text-gray-600 mb-3">{validation.description}</p>
                          
                          <div className="flex items-center space-x-6 text-sm text-gray-500 mb-3">
                            <span>Soumis le {new Date(validation.created_at).toLocaleDateString('fr-FR', {
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric',
                              hour: '2-digit',
                              minute: '2-digit'
                            })}</span>
                            {validation.project_type && (
                              <span>Type: {validation.project_type}</span>
                            )}
                            {validation.file_name && (
                              <span className="flex items-center space-x-1">
                                <FileText className="h-4 w-4" />
                                <span>{validation.file_name}</span>
                              </span>
                            )}
                          </div>

                          {validation.admin_notes && (
                            <div className="mt-4 p-4 bg-blue-50 rounded-md border-l-4 border-blue-400">
                              <h4 className="font-medium text-blue-900 text-sm mb-2">Notes de l'expert :</h4>
                              <p className="text-blue-800 text-sm">{validation.admin_notes}</p>
                            </div>
                          )}
                        </div>
                        
                        <div className="flex flex-col space-y-2 ml-4">
                          {validation.file_url && validation.file_name && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => downloadFile(validation.file_url!, validation.file_name!)}
                              className="flex items-center space-x-1"
                            >
                              <Download className="h-4 w-4" />
                              <span>Télécharger</span>
                            </Button>
                          )}
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </main>
      </div>
    </>
  )
}
