'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Users,
  TrendingUp,
  Network,
  Zap,
  Crown,
  Star,
  Award,
  Shield,
  Target,
  Rocket,
  Globe,
  Lock
} from 'lucide-react'

export default function NetworkEffect() {
  const [activeConnections, setActiveConnections] = useState(0)
  const [networkValue, setNetworkValue] = useState(0)

  const networkMetrics = [
    {
      label: 'Connexions Actives',
      value: activeConnections,
      unit: '',
      color: 'text-blue-600',
      icon: <Network className="h-5 w-5" />
    },
    {
      label: 'Valeur Réseau',
      value: networkValue,
      unit: 'M FCFA',
      color: 'text-green-600',
      icon: <TrendingUp className="h-5 w-5" />
    },
    {
      label: 'Projets Générés',
      value: Math.floor(activeConnections * 2.3),
      unit: '',
      color: 'text-purple-600',
      icon: <Rocket className="h-5 w-5" />
    },
    {
      label: 'Opportunités',
      value: Math.floor(activeConnections * 1.7),
      unit: '',
      color: 'text-amber-600',
      icon: <Target className="h-5 w-5" />
    }
  ]

  const membershipLevels = [
    { name: 'Bronze', members: 456, multiplier: 1, color: 'from-amber-600 to-amber-700' },
    { name: 'Silver', members: 389, multiplier: 2.5, color: 'from-gray-400 to-gray-500' },
    { name: 'Gold', members: 267, multiplier: 5, color: 'from-yellow-400 to-yellow-500' },
    { name: 'Platinum', members: 122, multiplier: 10, color: 'from-purple-400 to-purple-500' }
  ]

  const networkBenefits = [
    {
      title: 'Effet Métcalfe',
      description: 'La valeur du réseau croît exponentiellement avec le nombre d\'utilisateurs',
      formula: 'Valeur = n²',
      impact: 'Chaque nouveau membre augmente la valeur pour TOUS',
      icon: <Globe className="h-6 w-6" />
    },
    {
      title: 'Exclusivité Renforcée',
      description: 'Plus le club grandit, plus il devient difficile de rester en dehors',
      formula: 'Pression = Membres × Influence',
      impact: 'Être exclu devient un handicap concurrentiel majeur',
      icon: <Lock className="h-6 w-6" />
    },
    {
      title: 'Synergie Multiplicatrice',
      description: 'Les connexions entre membres créent des opportunités exponentielles',
      formula: 'Opportunités = Connexions × Synergies',
      impact: 'Chaque relation ouvre de nouvelles possibilités',
      icon: <Zap className="h-6 w-6" />
    },
    {
      title: 'Barrière Naturelle',
      description: 'Le coût de sortie augmente avec l\'intégration au réseau',
      formula: 'Coût Sortie = Connexions × Dépendance',
      impact: 'Quitter le club = perdre son réseau professionnel',
      icon: <Shield className="h-6 w-6" />
    }
  ]

  const successStories = [
    {
      member: 'Ing. Kouame Yao',
      level: 'Platinum',
      story: 'Grâce au réseau club, j\'ai obtenu 15 nouveaux contrats en 6 mois',
      value: '45M FCFA',
      connections: 89
    },
    {
      member: 'Fatou Diallo',
      level: 'Gold',
      story: 'Les recommandations membres m\'ont permis de doubler mon CA',
      value: '28M FCFA',
      connections: 67
    },
    {
      member: 'Jean Kouassi',
      level: 'Gold',
      story: 'Partenariat stratégique trouvé via le club, expansion régionale réussie',
      value: '52M FCFA',
      connections: 78
    }
  ]

  useEffect(() => {
    // Simulation de l'effet réseau en temps réel
    const interval = setInterval(() => {
      const totalMembers = membershipLevels.reduce((sum, level) => sum + level.members, 0)
      const weightedConnections = membershipLevels.reduce((sum, level) => 
        sum + (level.members * level.multiplier), 0
      )
      
      setActiveConnections(weightedConnections)
      // Formule de Métcalfe simplifiée : Valeur = n² / 1000 (en millions)
      setNetworkValue(Math.floor((weightedConnections * weightedConnections) / 1000))
    }, 100)

    return () => clearInterval(interval)
  }, [])

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      {/* Header */}
      <div className="flex items-center space-x-4 mb-6">
        <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center text-white">
          <Network className="h-6 w-6" />
        </div>
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Effet Réseau en Temps Réel</h2>
          <p className="text-gray-600">Visualisation de la puissance du Club Pro Matos</p>
        </div>
      </div>

      {/* Métriques réseau */}
      <div className="grid md:grid-cols-4 gap-6 mb-8">
        {networkMetrics.map((metric, index) => (
          <motion.div
            key={metric.label}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: index * 0.1 }}
            className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg p-4 border"
          >
            <div className="flex items-center space-x-2 mb-2">
              <div className={`${metric.color}`}>
                {metric.icon}
              </div>
              <span className="text-sm font-medium text-gray-700">{metric.label}</span>
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {metric.value.toLocaleString()}{metric.unit}
            </div>
          </motion.div>
        ))}
      </div>

      {/* Visualisation des niveaux */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Répartition par Niveau</h3>
        <div className="grid md:grid-cols-4 gap-4">
          {membershipLevels.map((level, index) => (
            <motion.div
              key={level.name}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 + index * 0.1 }}
              className="text-center"
            >
              <div className={`w-16 h-16 bg-gradient-to-r ${level.color} rounded-full flex items-center justify-center mx-auto mb-3`}>
                <Crown className="h-8 w-8 text-white" />
              </div>
              <h4 className="font-semibold text-gray-900">{level.name}</h4>
              <p className="text-lg font-bold text-gray-900">{level.members}</p>
              <p className="text-xs text-gray-500">membres</p>
              <p className="text-xs text-purple-600 font-medium">×{level.multiplier} influence</p>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Avantages de l'effet réseau */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Mécanismes de l'Effet Réseau</h3>
        <div className="grid md:grid-cols-2 gap-6">
          {networkBenefits.map((benefit, index) => (
            <motion.div
              key={benefit.title}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.8 + index * 0.1 }}
              className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg p-4 border border-purple-200"
            >
              <div className="flex items-start space-x-3">
                <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center text-purple-600 flex-shrink-0">
                  {benefit.icon}
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-purple-900 mb-1">{benefit.title}</h4>
                  <p className="text-sm text-purple-700 mb-2">{benefit.description}</p>
                  <div className="bg-white rounded p-2 mb-2">
                    <code className="text-xs text-purple-800 font-mono">{benefit.formula}</code>
                  </div>
                  <p className="text-xs text-purple-600 font-medium">{benefit.impact}</p>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Success stories */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Témoignages de Réussite</h3>
        <div className="space-y-4">
          {successStories.map((story, index) => (
            <motion.div
              key={story.member}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.2 + index * 0.1 }}
              className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-4 border border-green-200"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h4 className="font-semibold text-green-900">{story.member}</h4>
                    <span className="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                      {story.level}
                    </span>
                  </div>
                  <p className="text-sm text-green-700 mb-2 italic">"{story.story}"</p>
                  <div className="flex items-center space-x-4 text-xs text-green-600">
                    <span className="flex items-center space-x-1">
                      <TrendingUp className="h-3 w-3" />
                      <span>Valeur générée: {story.value}</span>
                    </span>
                    <span className="flex items-center space-x-1">
                      <Users className="h-3 w-3" />
                      <span>{story.connections} connexions actives</span>
                    </span>
                  </div>
                </div>
                <div className="ml-4">
                  <div className="flex items-center space-x-1">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="h-4 w-4 text-yellow-500 fill-current" />
                    ))}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Call to action */}
      <div className="bg-gradient-to-r from-purple-900 to-indigo-900 text-white rounded-lg p-6 text-center">
        <h3 className="text-xl font-bold mb-2">Rejoignez l'Élite Électrique</h3>
        <p className="text-purple-200 mb-4">
          Plus vous attendez, plus la barrière à l'entrée augmente
        </p>
        <div className="flex items-center justify-center space-x-6 mb-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-300">{activeConnections.toLocaleString()}</div>
            <div className="text-xs text-purple-400">Connexions actives</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-300">{networkValue}M</div>
            <div className="text-xs text-purple-400">Valeur réseau FCFA</div>
          </div>
        </div>
        <button className="bg-amber-500 text-slate-900 px-8 py-3 rounded-lg font-bold hover:bg-amber-400 transition-colors">
          Rejoindre Maintenant
        </button>
      </div>
    </div>
  )
}
