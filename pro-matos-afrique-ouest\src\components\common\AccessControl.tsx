'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Lock,
  Crown,
  Star,
  Award,
  Shield,
  AlertTriangle,
  Upgrade,
  X,
  CheckCircle
} from 'lucide-react'

interface AccessControlProps {
  requiredLevel: 'bronze' | 'silver' | 'gold' | 'platinum'
  currentLevel?: 'bronze' | 'silver' | 'gold' | 'platinum' | null
  feature: string
  onUpgrade?: () => void
  children: React.ReactNode
}

export default function AccessControl({ 
  requiredLevel, 
  currentLevel = 'bronze', 
  feature, 
  onUpgrade,
  children 
}: AccessControlProps) {
  const [showUpgradeModal, setShowUpgradeModal] = useState(false)

  const membershipLevels = {
    bronze: { order: 1, icon: <Shield className="h-5 w-5" />, color: 'from-amber-600 to-amber-700', name: 'Bronze' },
    silver: { order: 2, icon: <Star className="h-5 w-5" />, color: 'from-gray-400 to-gray-500', name: 'Silver' },
    gold: { order: 3, icon: <Crown className="h-5 w-5" />, color: 'from-yellow-400 to-yellow-500', name: 'Gold' },
    platinum: { order: 4, icon: <Award className="h-5 w-5" />, color: 'from-purple-400 to-purple-500', name: 'Platinum' }
  }

  const currentOrder = currentLevel ? membershipLevels[currentLevel].order : 0
  const requiredOrder = membershipLevels[requiredLevel].order
  const hasAccess = currentOrder >= requiredOrder

  const benefits = {
    bronze: [
      'Accès aux templates de base',
      'Calculatrice simple',
      'Support par email',
      'Documentation publique'
    ],
    silver: [
      'Templates avancés',
      'Calculs automatisés',
      'Validation technique',
      'Chat support',
      'Bibliothèque étendue'
    ],
    gold: [
      'Templates experts',
      'Consultation directe',
      'Certification officielle',
      'Rapports personnalisés',
      'Formation exclusive',
      'Support téléphonique'
    ],
    platinum: [
      'Accès illimité total',
      'Templates sur mesure',
      'Expert dédié',
      'Certification premium',
      'Formation VIP',
      'Support prioritaire 24/7',
      'API privée'
    ]
  }

  const pricing = {
    bronze: { monthly: 0, annual: 0 },
    silver: { monthly: 25000, annual: 250000 },
    gold: { monthly: 75000, annual: 750000 },
    platinum: { monthly: 150000, annual: 1500000 }
  }

  if (hasAccess) {
    return <>{children}</>
  }

  return (
    <div className="relative">
      {/* Contenu flouté */}
      <div className="relative">
        <div className="filter blur-sm pointer-events-none select-none">
          {children}
        </div>
        
        {/* Overlay de restriction */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent flex items-center justify-center">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white rounded-lg shadow-xl p-6 max-w-md mx-4 text-center"
          >
            <div className={`w-16 h-16 bg-gradient-to-r ${membershipLevels[requiredLevel].color} rounded-full flex items-center justify-center mx-auto mb-4`}>
              <Lock className="h-8 w-8 text-white" />
            </div>
            
            <h3 className="text-xl font-bold text-gray-900 mb-2">
              Accès Restreint
            </h3>
            
            <p className="text-gray-600 mb-4">
              <strong>{feature}</strong> nécessite un niveau d'adhésion{' '}
              <span className={`font-bold bg-gradient-to-r ${membershipLevels[requiredLevel].color} bg-clip-text text-transparent`}>
                {membershipLevels[requiredLevel].name}
              </span> ou supérieur.
            </p>
            
            <div className="flex items-center justify-center space-x-2 mb-4">
              <span className="text-sm text-gray-500">Votre niveau actuel:</span>
              {currentLevel ? (
                <div className={`flex items-center space-x-1 px-3 py-1 rounded-full bg-gradient-to-r ${membershipLevels[currentLevel].color} text-white text-sm font-medium`}>
                  {membershipLevels[currentLevel].icon}
                  <span>{membershipLevels[currentLevel].name}</span>
                </div>
              ) : (
                <span className="text-sm text-gray-400">Aucun</span>
              )}
            </div>
            
            <div className="space-y-2 mb-6">
              <button
                onClick={() => setShowUpgradeModal(true)}
                className="w-full btn-premium"
              >
                <Upgrade className="h-4 w-4 mr-2" />
                Passer au niveau {membershipLevels[requiredLevel].name}
              </button>
              
              <p className="text-xs text-gray-500">
                À partir de {pricing[requiredLevel].monthly.toLocaleString()} FCFA/mois
              </p>
            </div>
            
            <div className="text-xs text-gray-400">
              Rejoignez {Math.floor(Math.random() * 500) + 200}+ professionnels qui utilisent Pro Matos
            </div>
          </motion.div>
        </div>
      </div>

      {/* Modal d'upgrade */}
      {showUpgradeModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h2 className="text-2xl font-bold text-gray-900">Choisissez Votre Niveau d'Adhésion</h2>
              <button
                onClick={() => setShowUpgradeModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            {/* Plans de pricing */}
            <div className="p-6">
              <div className="grid md:grid-cols-4 gap-6">
                {Object.entries(membershipLevels).map(([level, config]) => {
                  const isRecommended = level === requiredLevel
                  const isCurrentLevel = level === currentLevel
                  
                  return (
                    <div
                      key={level}
                      className={`relative rounded-lg border-2 p-6 ${
                        isRecommended 
                          ? 'border-amber-400 bg-amber-50' 
                          : isCurrentLevel
                          ? 'border-green-400 bg-green-50'
                          : 'border-gray-200 bg-white'
                      }`}
                    >
                      {isRecommended && (
                        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                          <span className="bg-amber-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                            Recommandé
                          </span>
                        </div>
                      )}
                      
                      {isCurrentLevel && (
                        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                          <span className="bg-green-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                            Actuel
                          </span>
                        </div>
                      )}

                      <div className="text-center mb-4">
                        <div className={`w-12 h-12 bg-gradient-to-r ${config.color} rounded-full flex items-center justify-center mx-auto mb-3`}>
                          {config.icon}
                        </div>
                        <h3 className="text-xl font-bold text-gray-900">{config.name}</h3>
                        <div className="mt-2">
                          <span className="text-3xl font-bold text-gray-900">
                            {pricing[level as keyof typeof pricing].monthly.toLocaleString()}
                          </span>
                          <span className="text-gray-600"> FCFA/mois</span>
                        </div>
                        {pricing[level as keyof typeof pricing].annual > 0 && (
                          <p className="text-sm text-gray-500 mt-1">
                            ou {pricing[level as keyof typeof pricing].annual.toLocaleString()} FCFA/an
                          </p>
                        )}
                      </div>

                      <ul className="space-y-2 mb-6">
                        {benefits[level as keyof typeof benefits].map((benefit, index) => (
                          <li key={index} className="flex items-center space-x-2 text-sm">
                            <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                            <span>{benefit}</span>
                          </li>
                        ))}
                      </ul>

                      <button
                        onClick={() => {
                          setShowUpgradeModal(false)
                          onUpgrade?.()
                        }}
                        disabled={isCurrentLevel}
                        className={`w-full py-2 px-4 rounded-lg font-medium transition-colors ${
                          isCurrentLevel
                            ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                            : isRecommended
                            ? 'bg-amber-500 text-white hover:bg-amber-600'
                            : 'bg-gray-900 text-white hover:bg-gray-800'
                        }`}
                      >
                        {isCurrentLevel ? 'Niveau actuel' : `Passer au ${config.name}`}
                      </button>
                    </div>
                  )
                })}
              </div>

              {/* Garanties et sécurité */}
              <div className="mt-8 bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 text-center">
                  Pourquoi Choisir Pro Matos ?
                </h3>
                <div className="grid md:grid-cols-3 gap-6 text-center">
                  <div>
                    <Shield className="h-8 w-8 text-green-600 mx-auto mb-2" />
                    <h4 className="font-medium text-gray-900 mb-1">Sécurité Garantie</h4>
                    <p className="text-sm text-gray-600">Calculs certifiés selon normes internationales</p>
                  </div>
                  <div>
                    <Award className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                    <h4 className="font-medium text-gray-900 mb-1">Expertise Reconnue</h4>
                    <p className="text-sm text-gray-600">Validation par experts certifiés</p>
                  </div>
                  <div>
                    <Crown className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                    <h4 className="font-medium text-gray-900 mb-1">Standard du Marché</h4>
                    <p className="text-sm text-gray-600">Référence utilisée par 500+ professionnels</p>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  )
}
