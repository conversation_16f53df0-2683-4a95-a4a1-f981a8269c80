'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  FileText, 
  Calculator, 
  Award, 
  Settings,
  Plus,
  Search,
  Filter,
  Clock,
  CheckCircle,
  AlertTriangle,
  Users,
  Building,
  Zap,
  Download,
  Share2
} from 'lucide-react'
import { useStore } from '@/store/useStore'
import { PrescriptorService } from '@/services/prescriptorService'
import { formatDate, getMembershipColor } from '@/lib/utils'
import QuickCalculator from './QuickCalculator'
import CertificateGenerator from './CertificateGenerator'
import ProjectWizard from './ProjectWizard'
import AccessControl from '../common/AccessControl'
import AnimatedCard, { AnimatedGrid } from '../ui/AnimatedCard'
import SmartLinks from '../integration/SmartLinks'

interface PrescriptorModuleProps {
  className?: string
}

export default function PrescriptorModule({ className = '' }: PrescriptorModuleProps) {
  const {
    prescriptionTemplates,
    prescriptionProjects,
    technicalNotes,
    complianceCertificates,
    calculationEngines,
    activeProject,
    selectedTemplate,
    setPrescriptionTemplates,
    setPrescriptionProjects,
    setTechnicalNotes,
    setComplianceCertificates,
    setCalculationEngines,
    setActiveProject,
    setSelectedTemplate
  } = useStore()

  const [activeTab, setActiveTab] = useState<'templates' | 'projects' | 'calculations' | 'certificates'>('templates')
  const [searchQuery, setSearchQuery] = useState('')
  const [filterCategory, setFilterCategory] = useState<'all' | 'electrical' | 'automation' | 'energy' | 'safety'>('all')
  const [showProjectWizard, setShowProjectWizard] = useState(false)
  const [currentMembershipLevel, setCurrentMembershipLevel] = useState<'bronze' | 'silver' | 'gold' | 'platinum'>('bronze')

  // Initialisation des données
  useEffect(() => {
    const loadInitialData = () => {
      setPrescriptionTemplates(PrescriptorService.generatePrescriptionTemplates())
      setPrescriptionProjects(PrescriptorService.generatePrescriptionProjects())
      setCalculationEngines(PrescriptorService.generateCalculationEngines())
      
      // Générer des notes techniques pour les projets existants
      const projects = PrescriptorService.generatePrescriptionProjects()
      const allNotes = projects.flatMap(project => 
        PrescriptorService.generateTechnicalNotes(project.id)
      )
      setTechnicalNotes(allNotes)
    }

    loadInitialData()
  }, [])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
      case 'approved':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'in_progress':
      case 'review':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'draft':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
      case 'approved':
        return <CheckCircle className="h-4 w-4" />
      case 'in_progress':
      case 'review':
        return <Clock className="h-4 w-4" />
      case 'draft':
        return <FileText className="h-4 w-4" />
      default:
        return <AlertTriangle className="h-4 w-4" />
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'electrical':
        return <Zap className="h-5 w-5" />
      case 'automation':
        return <Settings className="h-5 w-5" />
      case 'energy':
        return <Calculator className="h-5 w-5" />
      case 'safety':
        return <Award className="h-5 w-5" />
      default:
        return <FileText className="h-5 w-5" />
    }
  }

  const tabs = [
    { 
      id: 'templates', 
      label: 'Templates de Prescription', 
      icon: FileText, 
      count: prescriptionTemplates.length,
      description: 'Modèles prêts à l\'emploi pour vos prescriptions'
    },
    { 
      id: 'projects', 
      label: 'Projets en Cours', 
      icon: Building, 
      count: prescriptionProjects.length,
      description: 'Vos projets de prescription et leur avancement'
    },
    { 
      id: 'calculations', 
      label: 'Outils de Calcul', 
      icon: Calculator, 
      count: calculationEngines.length,
      description: 'Moteurs de calcul automatisés et certifiés'
    },
    { 
      id: 'certificates', 
      label: 'Certificats de Conformité', 
      icon: Award, 
      count: complianceCertificates.length,
      description: 'Certificats et garanties contractuelles'
    }
  ]

  return (
    <div className={`bg-white rounded-lg shadow-lg ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Module Prescripteur Professionnel</h2>
            <p className="text-gray-600 mt-1">Kits de prescription, calculs automatisés et certification de conformité</p>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="text-right">
              <div className="text-sm text-gray-600">Projets actifs</div>
              <div className="text-lg font-bold text-amber-600">
                {prescriptionProjects.filter(p => p.status === 'in_progress').length}
              </div>
            </div>
            
            <button
              type="button"
              onClick={() => setShowProjectWizard(true)}
              className="btn-premium"
            >
              <Plus className="h-4 w-4 mr-2" />
              Nouveau Projet
            </button>
          </div>
        </div>

        {/* Barre de recherche et filtres */}
        <div className="mt-4 flex items-center space-x-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Rechercher templates, projets, calculs..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400 focus:border-transparent"
            />
          </div>

          <select
            value={filterCategory}
            onChange={(e) => setFilterCategory(e.target.value as any)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400 focus:border-transparent"
          >
            <option value="all">Toutes catégories</option>
            <option value="electrical">Électrique</option>
            <option value="automation">Automatisme</option>
            <option value="energy">Énergie</option>
            <option value="safety">Sécurité</option>
          </select>

          {/* Sélecteur de niveau pour test */}
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">Test niveau:</span>
            <select
              value={currentMembershipLevel}
              onChange={(e) => setCurrentMembershipLevel(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400 text-sm"
            >
              <option value="bronze">🥉 Bronze</option>
              <option value="silver">🥈 Silver</option>
              <option value="gold">🥇 Gold</option>
              <option value="platinum">💎 Platinum</option>
            </select>
          </div>
        </div>
      </div>

      {/* Onglets */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6">
          {tabs.map((tab) => {
            const Icon = tab.icon
            const isActive = activeTab === tab.id
            
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  isActive
                    ? 'border-amber-500 text-amber-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="h-5 w-5" />
                <span>{tab.label}</span>
                {tab.count > 0 && (
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    isActive ? 'bg-amber-100 text-amber-800' : 'bg-gray-100 text-gray-600'
                  }`}>
                    {tab.count}
                  </span>
                )}
              </button>
            )
          })}
        </nav>
      </div>

      {/* Description de l'onglet actif */}
      <div className="px-6 py-3 bg-gradient-to-r from-amber-50 to-yellow-50 border-b border-amber-200">
        <p className="text-sm text-amber-800">
          {tabs.find(tab => tab.id === activeTab)?.description}
        </p>
      </div>

      {/* Contenu des onglets */}
      <div className="p-6">
        <AnimatePresence mode="wait">
          {activeTab === 'templates' && (
            <motion.div
              key="templates"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-6"
            >
              {/* Grille des templates */}
              <AnimatedGrid className="md:grid-cols-2 lg:grid-cols-3">
                {prescriptionTemplates.map((template, index) => (
                  <AccessControl
                    key={template.id}
                    requiredLevel={template.membership_required || 'bronze'}
                    currentLevel={currentMembershipLevel}
                    feature={`Template ${template.name}`}
                    onUpgrade={() => {
                      // Simulation d'upgrade - en réalité, redirection vers paiement
                      setCurrentMembershipLevel(template.membership_required || 'bronze')
                    }}
                  >
                    <AnimatedCard
                      hover={true}
                      tilt={true}
                      glow={selectedTemplate?.id === template.id}
                      interactive={true}
                      delay={index * 0.1}
                      onClick={() => setSelectedTemplate(template)}
                      className={selectedTemplate?.id === template.id ? 'ring-2 ring-amber-400' : ''}
                    >
                    <div className="flex items-start justify-between mb-4">
                      <div className={`w-12 h-12 bg-gradient-to-r ${
                        template.category === 'electrical' ? 'from-blue-500 to-blue-600' :
                        template.category === 'automation' ? 'from-purple-500 to-purple-600' :
                        template.category === 'energy' ? 'from-green-500 to-green-600' :
                        'from-gray-500 to-gray-600'
                      } rounded-lg flex items-center justify-center text-white`}>
                        {getCategoryIcon(template.category)}
                      </div>
                      {template.is_featured && (
                        <span className="px-2 py-1 bg-amber-100 text-amber-800 text-xs font-medium rounded-full">
                          Populaire
                        </span>
                      )}
                    </div>
                    
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">{template.name}</h3>
                    <p className="text-sm text-gray-600 mb-4 line-clamp-2">{template.description}</p>
                    
                    <div className="flex items-center justify-between mb-3">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        template.complexity_level === 'expert' ? 'bg-red-100 text-red-800' :
                        template.complexity_level === 'advanced' ? 'bg-orange-100 text-orange-800' :
                        template.complexity_level === 'intermediate' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-green-100 text-green-800'
                      }`}>
                        {template.complexity_level}
                      </span>
                      <div className="flex items-center space-x-1 text-xs text-gray-500">
                        <Users className="h-3 w-3" />
                        <span>{template.usage_count}</span>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      {template.membership_required && (
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getMembershipColor(template.membership_required)}`}>
                          {template.membership_required}+ requis
                        </span>
                      )}
                      <div className="flex items-center space-x-1 text-amber-600">
                        <span className="text-sm font-medium">★ {template.rating}</span>
                      </div>
                    </div>
                    
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <button className="w-full btn-premium text-sm py-2">
                        Utiliser ce Template
                      </button>
                    </div>
                    </AnimatedCard>
                  </AccessControl>
                ))}
              </AnimatedGrid>
            </motion.div>
          )}

          {activeTab === 'projects' && (
            <motion.div
              key="projects"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-6"
            >
              {/* Liste des projets */}
              {prescriptionProjects.length === 0 ? (
                <div className="text-center py-12 text-gray-500">
                  <Building className="h-16 w-16 mx-auto mb-4 text-gray-300" />
                  <h3 className="text-lg font-medium mb-2">Aucun projet en cours</h3>
                  <p className="mb-4">Créez votre premier projet de prescription</p>
                  <button
                    type="button"
                    onClick={() => setShowProjectWizard(true)}
                    className="btn-premium"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Nouveau Projet
                  </button>
                </div>
              ) : (
                <div className="space-y-4">
                  {prescriptionProjects.map((project) => (
                    <motion.div
                      key={project.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      className="industrial-card p-6"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-3">
                            <span className={`px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(project.status)}`}>
                              {getStatusIcon(project.status)}
                              <span className="ml-2">{project.status}</span>
                            </span>
                            {project.compliance_verified && (
                              <span className="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                                ✓ Conforme
                              </span>
                            )}
                          </div>

                          <h3 className="text-xl font-bold text-gray-900 mb-2">{project.project_name}</h3>
                          <p className="text-gray-600 mb-3">{project.project_description}</p>

                          <div className="grid md:grid-cols-3 gap-4 mb-4">
                            <div>
                              <span className="text-sm font-medium text-gray-700">Client:</span>
                              <p className="text-sm text-gray-900">{project.client_name}</p>
                              <p className="text-xs text-gray-500">{project.client_company}</p>
                            </div>
                            <div>
                              <span className="text-sm font-medium text-gray-700">Localisation:</span>
                              <p className="text-sm text-gray-900">{project.location}</p>
                            </div>
                            <div>
                              <span className="text-sm font-medium text-gray-700">Coût total:</span>
                              <p className="text-lg font-bold text-amber-600">
                                {project.total_cost.toLocaleString()} FCFA
                              </p>
                            </div>
                          </div>

                          <div className="flex items-center space-x-6 text-sm text-gray-500">
                            <span>Créé: {formatDate(project.created_at)}</span>
                            <span>Mis à jour: {formatDate(project.updated_at)}</span>
                            {project.delivery_date && (
                              <span>Livraison: {formatDate(project.delivery_date)}</span>
                            )}
                          </div>
                        </div>

                        <div className="ml-6 flex flex-col space-y-2">
                          <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                            Ouvrir
                          </button>
                          <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                            <Download className="h-4 w-4 mr-2 inline" />
                            Export
                          </button>
                          <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                            <Share2 className="h-4 w-4 mr-2 inline" />
                            Partager
                          </button>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              )}
            </motion.div>
          )}

          {activeTab === 'calculations' && (
            <motion.div
              key="calculations"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-6"
            >
              {/* Outils de calcul */}
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {calculationEngines.map((engine) => (
                  <motion.div
                    key={engine.id}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="industrial-card p-6"
                  >
                    <div className="flex items-center space-x-3 mb-4">
                      <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center text-white">
                        <Calculator className="h-6 w-6" />
                      </div>
                      {engine.is_certified && (
                        <span className="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                          ✓ Certifié
                        </span>
                      )}
                    </div>

                    <h3 className="text-lg font-semibold text-gray-900 mb-2">{engine.name}</h3>
                    <p className="text-sm text-gray-600 mb-4">{engine.description}</p>

                    <div className="space-y-2 mb-4">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Paramètres d'entrée:</span>
                        <span className="font-medium">{engine.input_parameters.length}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Paramètres de sortie:</span>
                        <span className="font-medium">{engine.output_parameters.length}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Normes:</span>
                        <span className="font-medium">{engine.safety_standards.length}</span>
                      </div>
                    </div>

                    <button className="w-full btn-premium text-sm py-2">
                      Utiliser l'Outil
                    </button>
                  </motion.div>
                ))}
              </div>

              {/* Calculatrice rapide */}
              <QuickCalculator />
            </motion.div>
          )}

          {activeTab === 'certificates' && (
            <motion.div
              key="certificates"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <CertificateGenerator />
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Project Wizard */}
      {showProjectWizard && (
        <ProjectWizard
          onClose={() => setShowProjectWizard(false)}
          onComplete={(project) => {
            setShowProjectWizard(false)
            // Optionally switch to projects tab to show the new project
            setActiveTab('projects')
          }}
        />
      )}
    </div>
  )
}
