'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  Zap,
  Shield,
  Users,
  TrendingUp,
  Bell,
  Search,
  Menu,
  X,
  Star,
  Award,
  BarChart3,
  Lightbulb
} from 'lucide-react'

export default function Home() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [currentTime, setCurrentTime] = useState(new Date())

  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000)
    return () => clearInterval(timer)
  }, [])

  const features = [
    {
      icon: <Zap className="h-8 w-8" />,
      title: "Hub d'Information Temps Réel",
      description: "Flux instantané des disponibilités stocks, nouveaux produits et alertes marché",
      color: "from-blue-500 to-blue-600"
    },
    {
      icon: <Shield className="h-8 w-8" />,
      title: "Conseil Technique Expert",
      description: "Validation technique, simulateur de compatibilité et bibliothèque de ressources",
      color: "from-green-500 to-green-600"
    },
    {
      icon: <Award className="h-8 w-8" />,
      title: "Module Prescripteur Pro",
      description: "Kits de prescription, générateurs automatisés et certification de conformité",
      color: "from-purple-500 to-purple-600"
    },
    {
      icon: <Users className="h-8 w-8" />,
      title: "Club Membre Exclusif",
      description: "Niveaux d'adhésion privilégiés avec avantages différenciés et réseau fermé",
      color: "from-amber-500 to-amber-600"
    }
  ]

  const stats = [
    { label: "Professionnels Actifs", value: "2,500+", icon: <Users className="h-5 w-5" /> },
    { label: "Produits Référencés", value: "15,000+", icon: <BarChart3 className="h-5 w-5" /> },
    { label: "Validations Techniques", value: "500+", icon: <Shield className="h-5 w-5" /> },
    { label: "Taux de Satisfaction", value: "98%", icon: <Star className="h-5 w-5" /> }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-md border-b border-slate-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className="w-10 h-10 bg-gradient-to-r from-amber-400 to-amber-500 rounded-lg flex items-center justify-center">
                  <Lightbulb className="h-6 w-6 text-slate-900" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-slate-900">Pro Matos</h1>
                  <p className="text-xs text-slate-600">Afrique Ouest</p>
                </div>
              </div>
            </div>

            <nav className="hidden md:flex items-center space-x-8">
              <a href="#features" className="text-slate-700 hover:text-amber-600 transition-colors">Fonctionnalités</a>
              <a href="#about" className="text-slate-700 hover:text-amber-600 transition-colors">À Propos</a>
              <a href="#contact" className="text-slate-700 hover:text-amber-600 transition-colors">Contact</a>
              <button className="btn-premium">
                Rejoindre le Club
              </button>
            </nav>

            <button
              className="md:hidden p-2 rounded-lg hover:bg-slate-100"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="md:hidden bg-white border-t border-slate-200"
          >
            <div className="px-4 py-4 space-y-4">
              <a href="#features" className="block text-slate-700 hover:text-amber-600">Fonctionnalités</a>
              <a href="#about" className="block text-slate-700 hover:text-amber-600">À Propos</a>
              <a href="#contact" className="block text-slate-700 hover:text-amber-600">Contact</a>
              <button className="w-full btn-premium">
                Rejoindre le Club
              </button>
            </div>
          </motion.div>
        )}
      </header>

      {/* Hero Section */}
      <section className="relative overflow-hidden py-20 lg:py-32">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h1 className="text-4xl lg:text-6xl font-bold text-slate-900 leading-tight">
                Le Hub <span className="text-transparent bg-clip-text bg-gradient-to-r from-amber-400 to-amber-600">Incontournable</span> du Matériel Électrique
              </h1>
              <p className="text-xl text-slate-600 mt-6 leading-relaxed">
                Devenez maître de l'écosystème électrique en Afrique de l'Ouest.
                Information, expertise et réseau exclusif pour les professionnels d'élite.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 mt-8">
                <a href="/hub" className="btn-premium text-lg px-8 py-4 text-center">
                  Accéder au Hub d'Information
                </a>
                <button className="px-8 py-4 border-2 border-slate-300 text-slate-700 rounded-lg hover:border-amber-400 hover:text-amber-600 transition-all duration-200">
                  Découvrir les Avantages
                </button>
              </div>

              <div className="flex items-center space-x-6 mt-8 text-sm text-slate-600">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span>Temps réel</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
                  <span>{currentTime.toLocaleTimeString('fr-FR')}</span>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="relative"
            >
              <div className="industrial-card p-8">
                <div className="grid grid-cols-2 gap-4">
                  {stats.map((stat, index) => (
                    <motion.div
                      key={stat.label}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.4 + index * 0.1 }}
                      className="text-center p-4 bg-slate-50 rounded-lg"
                    >
                      <div className="flex justify-center mb-2 text-amber-600">
                        {stat.icon}
                      </div>
                      <div className="text-2xl font-bold text-slate-900">{stat.value}</div>
                      <div className="text-sm text-slate-600">{stat.label}</div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Section Fonctionnalités */}
      <section id="features" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-4">
              Fonctionnalités <span className="text-transparent bg-clip-text bg-gradient-to-r from-amber-400 to-amber-600">Stratégiques</span>
            </h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto">
              Contrôlez l'écosystème électrique avec nos outils professionnels exclusifs
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                className="industrial-card p-8"
              >
                <div className={`w-16 h-16 bg-gradient-to-r ${feature.color} rounded-lg flex items-center justify-center text-white mb-6`}>
                  {feature.icon}
                </div>
                <h3 className="text-xl font-bold text-slate-900 mb-4">{feature.title}</h3>
                <p className="text-slate-600 leading-relaxed">{feature.description}</p>
                <div className="mt-6">
                  <a
                    href={
                      feature.title.includes('Hub') ? '/hub' :
                      feature.title.includes('Conseil') ? '/expert' :
                      feature.title.includes('Prescripteur') ? '/prescriptor' :
                      '#'
                    }
                    className="text-amber-600 hover:text-amber-700 font-medium inline-flex items-center"
                  >
                    Découvrir →
                  </a>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Section Avantages Stratégiques */}
      <section className="py-20 bg-gradient-to-br from-slate-900 to-slate-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl lg:text-4xl font-bold mb-4">
              Pourquoi Nous Sommes <span className="text-transparent bg-clip-text bg-gradient-to-r from-amber-400 to-amber-600">Incontournables</span>
            </h2>
            <p className="text-xl text-slate-300 max-w-3xl mx-auto">
              Notre stratégie de contrôle de l'écosystème vous donne un avantage concurrentiel décisif
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.1 }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-gradient-to-r from-amber-400 to-amber-500 rounded-lg flex items-center justify-center mx-auto mb-6">
                <TrendingUp className="h-8 w-8 text-slate-900" />
              </div>
              <h3 className="text-xl font-bold mb-4">Information = Pouvoir</h3>
              <p className="text-slate-300">
                Contrôlez l'information du marché, donc contrôlez le marché.
                Nos données temps réel vous donnent l'avantage décisionnel.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.2 }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-gradient-to-r from-amber-400 to-amber-500 rounded-lg flex items-center justify-center mx-auto mb-6">
                <Shield className="h-8 w-8 text-slate-900" />
              </div>
              <h3 className="text-xl font-bold mb-4">Expertise = Indispensabilité</h3>
              <p className="text-slate-300">
                Notre validation technique devient obligatoire.
                Les clients préfèrent la sécurité de notre expertise reconnue.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.3 }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-gradient-to-r from-amber-400 to-amber-500 rounded-lg flex items-center justify-center mx-auto mb-6">
                <Users className="h-8 w-8 text-slate-900" />
              </div>
              <h3 className="text-xl font-bold mb-4">Réseau = Barrière</h3>
              <p className="text-slate-300">
                Être exclu de notre club devient un handicap concurrentiel.
                Notre réseau fermé crée une barrière à l'entrée naturelle.
              </p>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  )
}
