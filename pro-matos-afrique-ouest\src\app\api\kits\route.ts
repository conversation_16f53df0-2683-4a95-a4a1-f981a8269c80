import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'

export async function GET(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })

    // Récupérer la session (optionnel pour voir les kits publics)
    const { data: { session } } = await supabase.auth.getSession()

    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')
    const includeDownloads = searchParams.get('includeDownloads') === 'true'

    // Construire la requête
    let query = supabase
      .from('kits')
      .select('*')
      .order('created_at', { ascending: false })

    // Filtrer par catégorie si spécifié
    if (category && category !== 'all') {
      query = query.eq('category', category)
    }

    const { data: kits, error } = await query

    if (error) {
      console.error('Erreur récupération kits:', error)
      return NextResponse.json(
        { error: 'Erreur lors de la récupération des kits' },
        { status: 500 }
      )
    }

    // Si l'utilisateur est connecté et demande les infos de téléchargement
    let kitsWithDownloadInfo = kits

    if (session && includeDownloads) {
      // Récupérer les téléchargements de l'utilisateur
      const { data: userDownloads } = await supabase
        .from('user_downloads')
        .select('kit_id, downloaded_at')
        .eq('user_id', session.user.id)

      const downloadedKitIds = new Set(userDownloads?.map(d => d.kit_id) || [])

      kitsWithDownloadInfo = kits?.map(kit => ({
        ...kit,
        isDownloaded: downloadedKitIds.has(kit.id),
        downloadedAt: userDownloads?.find(d => d.kit_id === kit.id)?.downloaded_at
      }))
    }

    return NextResponse.json({
      success: true,
      data: kitsWithDownloadInfo
    })

  } catch (error) {
    console.error('Erreur API kits:', error)
    return NextResponse.json(
      { error: 'Erreur serveur' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })

    // Vérifier l'authentification
    const { data: { session }, error: authError } = await supabase.auth.getSession()
    
    if (authError || !session) {
      return NextResponse.json(
        { error: 'Non authentifié' },
        { status: 401 }
      )
    }

    // Vérifier les permissions admin
    const { data: user } = await supabase
      .from('users')
      .select('role')
      .eq('id', session.user.id)
      .single()

    if (!user || user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Permissions administrateur requises' },
        { status: 403 }
      )
    }

    const { title, description, category, fileUrl, fileName, fileSize, isPremium } = await request.json()

    if (!title || !description || !category || !fileUrl || !fileName) {
      return NextResponse.json(
        { error: 'Titre, description, catégorie, URL et nom de fichier requis' },
        { status: 400 }
      )
    }

    // Créer le kit
    const { data: kit, error } = await supabase
      .from('kits')
      .insert({
        title,
        description,
        category,
        file_url: fileUrl,
        file_name: fileName,
        file_size: fileSize || 0,
        is_premium: isPremium || false,
        downloads: 0
      })
      .select()
      .single()

    if (error) {
      console.error('Erreur création kit:', error)
      return NextResponse.json(
        { error: 'Erreur lors de la création du kit' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: kit,
      message: 'Kit créé avec succès'
    })

  } catch (error) {
    console.error('Erreur API create kit:', error)
    return NextResponse.json(
      { error: 'Erreur serveur' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })

    // Vérifier l'authentification
    const { data: { session }, error: authError } = await supabase.auth.getSession()
    
    if (authError || !session) {
      return NextResponse.json(
        { error: 'Non authentifié' },
        { status: 401 }
      )
    }

    // Vérifier les permissions admin
    const { data: user } = await supabase
      .from('users')
      .select('role')
      .eq('id', session.user.id)
      .single()

    if (!user || user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Permissions administrateur requises' },
        { status: 403 }
      )
    }

    const { kitId, title, description, category, isPremium } = await request.json()

    if (!kitId) {
      return NextResponse.json(
        { error: 'ID du kit requis' },
        { status: 400 }
      )
    }

    // Mettre à jour le kit
    const { data: kit, error } = await supabase
      .from('kits')
      .update({
        title,
        description,
        category,
        is_premium: isPremium,
        updated_at: new Date().toISOString()
      })
      .eq('id', kitId)
      .select()
      .single()

    if (error) {
      console.error('Erreur mise à jour kit:', error)
      return NextResponse.json(
        { error: 'Erreur lors de la mise à jour' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: kit,
      message: 'Kit mis à jour avec succès'
    })

  } catch (error) {
    console.error('Erreur API update kit:', error)
    return NextResponse.json(
      { error: 'Erreur serveur' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })

    // Vérifier l'authentification
    const { data: { session }, error: authError } = await supabase.auth.getSession()
    
    if (authError || !session) {
      return NextResponse.json(
        { error: 'Non authentifié' },
        { status: 401 }
      )
    }

    // Vérifier les permissions admin
    const { data: user } = await supabase
      .from('users')
      .select('role')
      .eq('id', session.user.id)
      .single()

    if (!user || user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Permissions administrateur requises' },
        { status: 403 }
      )
    }

    const { kitId } = await request.json()

    if (!kitId) {
      return NextResponse.json(
        { error: 'ID du kit requis' },
        { status: 400 }
      )
    }

    // Supprimer le kit
    const { error } = await supabase
      .from('kits')
      .delete()
      .eq('id', kitId)

    if (error) {
      console.error('Erreur suppression kit:', error)
      return NextResponse.json(
        { error: 'Erreur lors de la suppression' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Kit supprimé avec succès'
    })

  } catch (error) {
    console.error('Erreur API delete kit:', error)
    return NextResponse.json(
      { error: 'Erreur serveur' },
      { status: 500 }
    )
  }
}
