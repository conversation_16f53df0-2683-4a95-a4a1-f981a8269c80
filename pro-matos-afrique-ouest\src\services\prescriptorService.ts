import { 
  PrescriptionTemplate, 
  PrescriptionProject, 
  TechnicalNote, 
  ComplianceCertificate, 
  CalculationEngine,
  CalculationParameter,
  ValidationRule,
  WarrantyContract,
  Product 
} from '@/lib/supabase'

// Service pour le Module Prescripteur Professionnel
export class PrescriptorService {
  
  // Simulation des templates de prescription
  static generatePrescriptionTemplates(): PrescriptionTemplate[] {
    return [
      {
        id: 'template_001',
        name: 'Installation Électrique Tertiaire',
        description: 'Template complet pour installations électriques de bâtiments tertiaires selon NF C 15-100',
        category: 'electrical',
        target_audience: 'engineer',
        complexity_level: 'intermediate',
        template_data: {
          sections: ['Analyse des besoins', 'Calculs de puissance', 'Schémas unifilaires', 'Liste du matériel'],
          default_values: { tension: 400, frequence: 50, facteur_puissance: 0.8 }
        },
        required_fields: ['surface_totale', 'nombre_postes_travail', 'puissance_eclairage', 'puissance_prises'],
        optional_fields: ['climatisation', 'ascenseurs', 'parking'],
        calculations: [
          {
            id: 'calc_001',
            name: 'Puissance totale installée',
            formula: 'P_total = P_eclairage + P_prises + P_force + P_climatisation',
            variables: { P_eclairage: 'number', P_prises: 'number', P_force: 'number', P_climatisation: 'number' },
            unit: 'kW',
            validation_rules: ['P_total > 0', 'P_total < 1000'],
            safety_factors: { simultaneite: 0.8, reserve: 1.2 }
          }
        ],
        compliance_standards: ['NF C 15-100', 'RT 2012', 'Accessibilité PMR'],
        membership_required: 'silver',
        is_featured: true,
        usage_count: 247,
        rating: 4.8,
        created_by: 'Pro Matos Team',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        id: 'template_002',
        name: 'Installation Photovoltaïque Résidentielle',
        description: 'Dimensionnement et prescription pour installations PV résidentielles avec stockage',
        category: 'energy',
        target_audience: 'engineer',
        complexity_level: 'advanced',
        template_data: {
          sections: ['Étude d\'ensoleillement', 'Dimensionnement PV', 'Stockage batterie', 'Raccordement réseau'],
          default_values: { irradiation_annuelle: 1800, rendement_onduleur: 0.95, degradation_annuelle: 0.005 }
        },
        required_fields: ['consommation_annuelle', 'surface_toiture', 'orientation', 'inclinaison'],
        optional_fields: ['masques_solaires', 'stockage_souhaite', 'injection_reseau'],
        calculations: [
          {
            id: 'calc_002',
            name: 'Production annuelle estimée',
            formula: 'E_prod = P_crete * irradiation * rendement_systeme * (1 - degradation)^annees',
            variables: { P_crete: 'number', irradiation: 'number', rendement_systeme: 'number', degradation: 'number', annees: 'number' },
            unit: 'kWh/an',
            validation_rules: ['P_crete > 0', 'irradiation > 800', 'rendement_systeme < 1'],
            safety_factors: { meteo: 0.9, vieillissement: 0.95 }
          }
        ],
        compliance_standards: ['NF C 15-100', 'UTE C 15-712-1', 'Arrêté tarifaire'],
        membership_required: 'gold',
        is_featured: true,
        usage_count: 189,
        rating: 4.9,
        created_by: 'Dr. Aminata Traoré',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        id: 'template_003',
        name: 'Automatisme Industriel',
        description: 'Prescription d\'automatismes pour lignes de production industrielle',
        category: 'automation',
        target_audience: 'engineer',
        complexity_level: 'expert',
        template_data: {
          sections: ['Analyse fonctionnelle', 'Architecture automate', 'Réseaux communication', 'Supervision'],
          default_values: { cycle_time: 100, safety_level: 'SIL2', communication: 'Ethernet' }
        },
        required_fields: ['nombre_entrees', 'nombre_sorties', 'vitesse_production', 'niveau_securite'],
        optional_fields: ['vision_industrielle', 'robotique', 'traçabilite'],
        calculations: [
          {
            id: 'calc_003',
            name: 'Temps de cycle automate',
            formula: 'T_cycle = (N_entrees * T_lecture + N_sorties * T_ecriture + T_traitement) * facteur_securite',
            variables: { N_entrees: 'number', T_lecture: 'number', N_sorties: 'number', T_ecriture: 'number', T_traitement: 'number', facteur_securite: 'number' },
            unit: 'ms',
            validation_rules: ['T_cycle < 100', 'T_cycle > 1'],
            safety_factors: { marge_calcul: 1.5, reserve_memoire: 2.0 }
          }
        ],
        compliance_standards: ['IEC 61131', 'IEC 61508', 'Directive Machines'],
        membership_required: 'platinum',
        is_featured: false,
        usage_count: 67,
        rating: 4.7,
        created_by: 'Ing. Jean-Baptiste Kone',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }
    ]
  }

  // Simulation des projets de prescription
  static generatePrescriptionProjects(): PrescriptionProject[] {
    return [
      {
        id: 'project_001',
        user_id: 'user_001',
        template_id: 'template_001',
        project_name: 'Immeuble de bureaux Plateau - Abidjan',
        client_name: 'SODECI Immobilier',
        client_company: 'SODECI',
        project_description: 'Installation électrique complète pour immeuble de bureaux 8 étages',
        location: 'Plateau, Abidjan, Côte d\'Ivoire',
        project_data: {
          surface_totale: 4500,
          nombre_etages: 8,
          nombre_postes_travail: 180,
          puissance_eclairage: 45,
          puissance_prises: 72,
          climatisation: true,
          ascenseurs: 2
        },
        calculated_values: {
          puissance_totale: 285.6,
          courant_nominal: 410,
          section_cable_principal: 185,
          nombre_tableaux: 9
        },
        selected_products: ['prod_001', 'prod_002', 'prod_003'],
        total_cost: 45000000, // 45M FCFA
        status: 'in_progress',
        compliance_verified: true,
        warranty_terms: 'Garantie 2 ans pièces et main d\'œuvre',
        delivery_date: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString(), // Dans 90 jours
        notes: 'Projet prioritaire - Livraison Q2 2024',
        created_at: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(), // Il y a 15 jours
        updated_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // Il y a 2 jours
      },
      {
        id: 'project_002',
        user_id: 'user_002',
        template_id: 'template_002',
        project_name: 'Villa solaire Almadies - Dakar',
        client_name: 'Famille Diop',
        client_company: 'Particulier',
        project_description: 'Installation photovoltaïque 15kWc avec stockage batterie lithium',
        location: 'Almadies, Dakar, Sénégal',
        project_data: {
          consommation_annuelle: 8500,
          surface_toiture: 120,
          orientation: 'Sud',
          inclinaison: 15,
          stockage_souhaite: true,
          capacite_batterie: 30
        },
        calculated_values: {
          puissance_crete: 15.2,
          production_annuelle: 24680,
          autonomie_jours: 3.5,
          taux_autoconsommation: 85
        },
        selected_products: ['prod_pv_001', 'prod_bat_001', 'prod_ond_001'],
        total_cost: 18500000, // 18.5M FCFA
        status: 'approved',
        compliance_verified: true,
        warranty_terms: 'Garantie 10 ans panneaux, 5 ans onduleur, 8 ans batteries',
        delivery_date: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000).toISOString(), // Dans 45 jours
        notes: 'Installation avec monitoring IoT inclus',
        created_at: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000).toISOString(), // Il y a 8 jours
        updated_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), // Il y a 1 jour
      }
    ]
  }

  // Simulation des moteurs de calcul
  static generateCalculationEngines(): CalculationEngine[] {
    return [
      {
        id: 'calc_engine_001',
        name: 'Dimensionnement Câbles BT',
        description: 'Calcul automatique des sections de câbles basse tension selon NF C 15-100',
        category: 'cable_sizing',
        input_parameters: [
          { name: 'courant', type: 'number', unit: 'A', min_value: 1, max_value: 1000, required: true, description: 'Courant nominal du circuit' },
          { name: 'longueur', type: 'number', unit: 'm', min_value: 1, max_value: 500, required: true, description: 'Longueur du câble' },
          { name: 'chute_tension_max', type: 'number', unit: '%', default_value: 3, required: true, description: 'Chute de tension maximale admissible' },
          { name: 'mode_pose', type: 'select', options: ['enterré', 'aérien', 'goulotte', 'chemin_cables'], required: true, description: 'Mode de pose du câble' }
        ],
        output_parameters: [
          { name: 'section_minimale', type: 'number', unit: 'mm²', required: true, description: 'Section minimale calculée' },
          { name: 'section_normalisee', type: 'number', unit: 'mm²', required: true, description: 'Section normalisée supérieure' },
          { name: 'chute_tension_reelle', type: 'number', unit: '%', required: true, description: 'Chute de tension réelle' }
        ],
        formula_set: {
          'section_minimale': 'S = (ρ * L * I) / (U * ΔU_max)',
          'chute_tension': 'ΔU = (ρ * L * I) / (S * U)'
        },
        validation_rules: [
          { condition: 'chute_tension_reelle <= chute_tension_max', message: 'Chute de tension respectée', severity: 'info' },
          { condition: 'section_normalisee >= section_minimale', message: 'Section normalisée suffisante', severity: 'info' },
          { condition: 'courant <= intensite_admissible', message: 'Intensité admissible dépassée', severity: 'error' }
        ],
        safety_standards: ['NF C 15-100', 'IEC 60364'],
        is_certified: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }
    ]
  }

  // Simulation des notes techniques
  static generateTechnicalNotes(projectId: string): TechnicalNote[] {
    return [
      {
        id: 'note_001',
        project_id: projectId,
        title: 'Calcul de la puissance totale installée',
        content: 'La puissance totale installée a été calculée en tenant compte des facteurs de simultanéité et des coefficients de sécurité selon la norme NF C 15-100.',
        note_type: 'calculation',
        auto_generated: true,
        template_section: 'Calculs de puissance',
        products_referenced: ['prod_001'],
        standards_referenced: ['NF C 15-100'],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        id: 'note_002',
        project_id: projectId,
        title: 'Recommandations de mise en œuvre',
        content: 'Il est recommandé d\'installer une protection différentielle 30mA sur tous les circuits prises et éclairage selon l\'article 411.3.3 de la NF C 15-100.',
        note_type: 'recommendation',
        auto_generated: false,
        template_section: 'Spécifications techniques',
        products_referenced: ['prod_002'],
        standards_referenced: ['NF C 15-100'],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }
    ]
  }

  // Méthode pour calculer automatiquement les valeurs d'un projet
  static calculateProjectValues(projectData: Record<string, any>, template: PrescriptionTemplate): Record<string, any> {
    const results: Record<string, any> = {}
    
    // Simulation de calculs basés sur le template
    if (template.id === 'template_001') {
      // Calculs pour installation électrique tertiaire
      const surface = projectData.surface_totale || 0
      const postes = projectData.nombre_postes_travail || 0
      
      results.puissance_eclairage = surface * 10 // 10W/m²
      results.puissance_prises = postes * 400 // 400W/poste
      results.puissance_totale = results.puissance_eclairage + results.puissance_prises
      results.courant_nominal = results.puissance_totale / (400 * Math.sqrt(3) * 0.8) // Triphasé 400V
      results.section_cable_principal = this.calculateCableSection(results.courant_nominal)
    }
    
    return results
  }

  // Méthode utilitaire pour calculer la section de câble
  private static calculateCableSection(current: number): number {
    const sections = [1.5, 2.5, 4, 6, 10, 16, 25, 35, 50, 70, 95, 120, 150, 185, 240, 300]
    const intensities = [16, 24, 32, 41, 57, 76, 101, 129, 157, 196, 246, 284, 319, 353, 419, 483]
    
    for (let i = 0; i < intensities.length; i++) {
      if (current <= intensities[i]) {
        return sections[i]
      }
    }
    
    return sections[sections.length - 1] // Section maximale si dépassement
  }

  // Méthode pour générer un certificat de conformité
  static generateComplianceCertificate(project: PrescriptionProject): ComplianceCertificate {
    return {
      id: `cert_${Date.now()}`,
      project_id: project.id,
      certificate_type: 'design',
      certificate_number: `PMO-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`,
      issued_by: 'Pro Matos Afrique Ouest - Bureau d\'Études Certifié',
      issued_to: `${project.client_name} - ${project.client_company}`,
      valid_from: new Date().toISOString(),
      valid_until: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(), // 1 an
      standards_covered: ['NF C 15-100', 'RT 2012'],
      test_results: {
        'Calculs de puissance': 'Conforme',
        'Dimensionnement câbles': 'Conforme',
        'Protection différentielle': 'Conforme'
      },
      limitations: ['Valable uniquement pour la conception présentée', 'Révision nécessaire en cas de modification'],
      digital_signature: 'SHA256:a1b2c3d4e5f6...',
      qr_verification: `https://promatos.com/verify/${Date.now()}`,
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }
  }
}
