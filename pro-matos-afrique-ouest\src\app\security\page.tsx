'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Shield, 
  Fingerprint, 
  FileText, 
  Activity,
  ArrowLeft,
  Lock,
  Eye,
  Database
} from 'lucide-react'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import BiometricAuth from '@/components/security/BiometricAuth'
import GDPRCompliance from '@/components/security/GDPRCompliance'
import AuditTrail from '@/components/security/AuditTrail'
import GlobalNavigation from '@/components/navigation/GlobalNavigation'
import ResponsiveLayout from '@/components/layout/ResponsiveLayout'
import { toast } from 'sonner'

export default function SecurityPage() {
  const [authSuccess, setAuthSuccess] = useState(false)

  const handleAuthSuccess = (method: string) => {
    setAuthSuccess(true)
    toast.success(`Authentification ${method} ré<PERSON>ie`)
  }

  const handleAuthFailure = (error: string) => {
    setAuthSuccess(false)
    toast.error(`Échec d'authentification: ${error}`)
  }

  const securityFeatures = [
    {
      id: 'biometric',
      title: 'Authentification Biométrique',
      description: 'Sécurisez votre compte avec empreinte digitale, reconnaissance faciale ou vocale',
      icon: <Fingerprint className="h-8 w-8" />,
      color: 'from-blue-400 to-blue-500',
      status: 'Configuré'
    },
    {
      id: 'gdpr',
      title: 'Conformité RGPD',
      description: 'Gestion transparente de vos données personnelles et respect de vos droits',
      icon: <FileText className="h-8 w-8" />,
      color: 'from-green-400 to-green-500',
      status: 'Conforme'
    },
    {
      id: 'audit',
      title: 'Journal d\'Audit',
      description: 'Traçabilité complète de toutes les actions et événements système',
      icon: <Activity className="h-8 w-8" />,
      color: 'from-purple-400 to-purple-500',
      status: 'Actif'
    }
  ]

  return (
    <ResponsiveLayout>
      <GlobalNavigation />
      
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
        {/* Header */}
        <div className="bg-white border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Link href="/hub">
                  <Button variant="ghost" size="sm">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Retour au Hub
                  </Button>
                </Link>
                
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-400 to-blue-500 rounded-lg flex items-center justify-center">
                    <Shield className="h-7 w-7 text-white" />
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold text-gray-900">Sécurité & Conformité</h1>
                    <p className="text-gray-600">Protection avancée et respect de la vie privée</p>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <Badge className="bg-green-100 text-green-800 border-green-200">
                  <Lock className="h-3 w-3 mr-1" />
                  Sécurisé
                </Badge>
                <Badge className="bg-blue-100 text-blue-800 border-blue-200">
                  <Eye className="h-3 w-3 mr-1" />
                  Transparent
                </Badge>
              </div>
            </div>
          </div>
        </div>

        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Vue d'ensemble des fonctionnalités */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8"
          >
            {securityFeatures.map((feature, index) => (
              <motion.div
                key={feature.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="hover:shadow-lg transition-shadow cursor-pointer group">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className={`w-12 h-12 bg-gradient-to-r ${feature.color} rounded-lg flex items-center justify-center text-white group-hover:scale-110 transition-transform`}>
                        {feature.icon}
                      </div>
                      <Badge variant="outline" className="text-green-600 border-green-200">
                        {feature.status}
                      </Badge>
                    </div>
                    <CardTitle className="group-hover:text-blue-600 transition-colors">
                      {feature.title}
                    </CardTitle>
                    <CardDescription>{feature.description}</CardDescription>
                  </CardHeader>
                </Card>
              </motion.div>
            ))}
          </motion.div>

          {/* Contenu principal avec onglets */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Tabs defaultValue="biometric" className="space-y-6">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="biometric" className="flex items-center space-x-2">
                  <Fingerprint className="h-4 w-4" />
                  <span>Biométrie</span>
                </TabsTrigger>
                <TabsTrigger value="gdpr" className="flex items-center space-x-2">
                  <FileText className="h-4 w-4" />
                  <span>RGPD</span>
                </TabsTrigger>
                <TabsTrigger value="audit" className="flex items-center space-x-2">
                  <Database className="h-4 w-4" />
                  <span>Audit</span>
                </TabsTrigger>
              </TabsList>

              {/* Authentification Biométrique */}
              <TabsContent value="biometric" className="space-y-6">
                <BiometricAuth
                  onAuthSuccess={handleAuthSuccess}
                  onAuthFailure={handleAuthFailure}
                />
              </TabsContent>

              {/* Conformité RGPD */}
              <TabsContent value="gdpr" className="space-y-6">
                <GDPRCompliance />
              </TabsContent>

              {/* Journal d'Audit */}
              <TabsContent value="audit" className="space-y-6">
                <AuditTrail />
              </TabsContent>
            </Tabs>
          </motion.div>

          {/* Informations de sécurité */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="mt-8"
          >
            <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
              <CardContent className="p-6">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Shield className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-blue-900 mb-2">
                      Engagement Sécurité & Confidentialité
                    </h3>
                    <p className="text-blue-700 mb-4">
                      Pro Matos Afrique Ouest s'engage à protéger vos données personnelles et professionnelles 
                      avec les plus hauts standards de sécurité. Nous respectons scrupuleusement le RGPD et 
                      les réglementations locales en matière de protection des données.
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-blue-800">Chiffrement AES-256</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-blue-800">Authentification multi-facteurs</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-blue-800">Audit trail complet</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-blue-800">Conformité RGPD</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-blue-800">Sauvegarde sécurisée</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-blue-800">Surveillance 24/7</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </main>
      </div>
    </ResponsiveLayout>
  )
}
