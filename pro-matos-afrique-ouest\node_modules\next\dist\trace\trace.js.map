{"version": 3, "sources": ["../../src/trace/trace.ts"], "names": ["Span", "clearTraceEvents", "exportTraceState", "flushAllTraces", "getTraceEvents", "initializeTraceState", "recordTraceEvents", "trace", "NUM_OF_MICROSEC_IN_NANOSEC", "BigInt", "NUM_OF_MILLISEC_IN_NANOSEC", "count", "getId", "defaultParentSpanId", "shouldSaveTraceEvents", "savedTraceEvents", "SpanStatus", "constructor", "name", "parentId", "attrs", "startTime", "undefined", "isTurbopack", "Boolean", "process", "env", "TURBOPACK", "status", "id", "_start", "hrtime", "bigint", "now", "Date", "stop", "stopTime", "end", "duration", "Number", "MAX_SAFE_INTEGER", "Error", "timestamp", "traceEvent", "tags", "reporter", "report", "push", "<PERSON><PERSON><PERSON><PERSON>", "manualTraceChild", "correction", "span", "setAttribute", "key", "value", "traceFn", "fn", "traceAsyncFn", "flushAll", "lastId", "state", "events"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;IAyBaA,IAAI;eAAJA;;;;;IAkKAC,gBAAgB;eAAhBA;;IA3BAC,gBAAgB;eAAhBA;;IALAC,cAAc;eAAdA;;IAgBGC,cAAc;eAAdA;;IANHC,oBAAoB;eAApBA;;IAUGC,iBAAiB;eAAjBA;;IA5BHC,KAAK;eAALA;;;wBAnJY;AAGzB,MAAMC,6BAA6BC,OAAO;AAC1C,MAAMC,6BAA6BD,OAAO;AAC1C,IAAIE,QAAQ;AACZ,MAAMC,QAAQ;IACZD;IACA,OAAOA;AACT;AACA,IAAIE;AACJ,IAAIC;AACJ,IAAIC,mBAAiC,EAAE;;UAI3BC;;;GAAAA,eAAAA;AASL,MAAMhB;IAWXiB,YAAY,EACVC,IAAI,EACJC,QAAQ,EACRC,KAAK,EACLC,SAAS,EAMV,CAAE;QACD,IAAI,CAACH,IAAI,GAAGA;QACZ,IAAI,CAACC,QAAQ,GAAGA,YAAYN;QAC5B,IAAI,CAACO,KAAK,GAAGA,QAAQ;YAAE,GAAGA,KAAK;QAAC,IAAI,CAAC;QACrC,IAAI,IAAI,CAACD,QAAQ,KAAKG,WAAW;YAC/B,8CAA8C;YAC9C,IAAI,CAACF,KAAK,CAACG,WAAW,GAAGC,QAAQC,QAAQC,GAAG,CAACC,SAAS;QACxD;QAEA,IAAI,CAACC,MAAM;QACX,IAAI,CAACC,EAAE,GAAGjB;QACV,IAAI,CAACkB,MAAM,GAAGT,aAAaI,QAAQM,MAAM,CAACC,MAAM;QAChD,wEAAwE;QACxE,iDAAiD;QACjD,2IAA2I;QAC3I,wDAAwD;QACxD,iFAAiF;QACjF,IAAI,CAACC,GAAG,GAAGC,KAAKD,GAAG;IACrB;IAEA,yEAAyE;IACzE,+DAA+D;IAC/D,wEAAwE;IACxE,yCAAyC;IACzCE,KAAKC,QAAiB,EAAE;QACtB,IAAI,IAAI,CAACR,MAAM,gBAAyB;YACtC,oCAAoC;YACpC,yFAAyF;YACzF;QACF;QACA,MAAMS,MAAcD,YAAYX,QAAQM,MAAM,CAACC,MAAM;QACrD,MAAMM,WAAW,AAACD,CAAAA,MAAM,IAAI,CAACP,MAAM,AAAD,IAAKtB;QACvC,IAAI,CAACoB,MAAM;QACX,IAAIU,WAAWC,OAAOC,gBAAgB,EAAE;YACtC,MAAM,IAAIC,MAAM,CAAC,4CAA4C,EAAEH,SAAS,CAAC;QAC3E;QACA,MAAMI,YAAY,IAAI,CAACZ,MAAM,GAAGtB;QAChC,MAAMmC,aAAyB;YAC7BzB,MAAM,IAAI,CAACA,IAAI;YACfoB,UAAUC,OAAOD;YACjBI,WAAWH,OAAOG;YAClBb,IAAI,IAAI,CAACA,EAAE;YACXV,UAAU,IAAI,CAACA,QAAQ;YACvByB,MAAM,IAAI,CAACxB,KAAK;YAChBC,WAAW,IAAI,CAACY,GAAG;QACrB;QACAY,gBAAQ,CAACC,MAAM,CAACH;QAChB,IAAI7B,uBAAuB;YACzBC,iBAAiBgC,IAAI,CAACJ;QACxB;IACF;IAEAK,WAAW9B,IAAY,EAAEE,KAAkB,EAAE;QAC3C,OAAO,IAAIpB,KAAK;YAAEkB;YAAMC,UAAU,IAAI,CAACU,EAAE;YAAET;QAAM;IACnD;IAEA6B,iBACE/B,IAAY,EACZ,yCAAyC;IACzCG,SAAkB,EAClB,wCAAwC;IACxCe,QAAiB,EACjBhB,KAAkB,EAClB;QACA,0FAA0F;QAC1F,MAAM8B,aACJzB,QAAQM,MAAM,CAACC,MAAM,KAAKvB,OAAOyB,KAAKD,GAAG,MAAMvB;QACjD,MAAMyC,OAAO,IAAInD,KAAK;YACpBkB;YACAC,UAAU,IAAI,CAACU,EAAE;YACjBT;YACAC,WAAWA,YAAYA,YAAY6B,aAAazB,QAAQM,MAAM,CAACC,MAAM;QACvE;QACAmB,KAAKhB,IAAI,CAACC,WAAWA,WAAWc,aAAazB,QAAQM,MAAM,CAACC,MAAM;IACpE;IAEApB,QAAQ;QACN,OAAO,IAAI,CAACiB,EAAE;IAChB;IAEAuB,aAAaC,GAAW,EAAEC,KAAa,EAAE;QACvC,IAAI,CAAClC,KAAK,CAACiC,IAAI,GAAGC;IACpB;IAEAC,QAAWC,EAAqB,EAAK;QACnC,IAAI;YACF,OAAOA,GAAG,IAAI;QAChB,SAAU;YACR,IAAI,CAACrB,IAAI;QACX;IACF;IAEA,MAAMsB,aAAgBD,EAAkC,EAAc;QACpE,IAAI;YACF,OAAO,MAAMA,GAAG,IAAI;QACtB,SAAU;YACR,IAAI,CAACrB,IAAI;QACX;IACF;AACF;AAEO,MAAM5B,QAAQ,CACnBW,MACAC,UACAC;IAEA,OAAO,IAAIpB,KAAK;QAAEkB;QAAMC;QAAUC;IAAM;AAC1C;AAEO,MAAMjB,iBAAiB,IAAM0C,gBAAQ,CAACa,QAAQ;AAK9C,MAAMxD,mBAAmB,IAAmB,CAAA;QACjDW;QACA8C,QAAQhD;QACRG;IACF,CAAA;AACO,MAAMT,uBAAuB,CAACuD;IACnCjD,QAAQiD,MAAMD,MAAM;IACpB9C,sBAAsB+C,MAAM/C,mBAAmB;IAC/CC,wBAAwB8C,MAAM9C,qBAAqB;AACrD;AAEO,SAASV;IACd,OAAOW;AACT;AAEO,SAAST,kBAAkBuD,MAAoB;IACpD,KAAK,MAAMlB,cAAckB,OAAQ;QAC/BhB,gBAAQ,CAACC,MAAM,CAACH;QAChB,IAAIA,WAAWd,EAAE,GAAGlB,OAAO;YACzBA,QAAQgC,WAAWd,EAAE,GAAG;QAC1B;IACF;IACA,IAAIf,uBAAuB;QACzBC,iBAAiBgC,IAAI,IAAIc;IAC3B;AACF;AAEO,MAAM5D,mBAAmB,IAAOc,mBAAmB,EAAE"}