{"name": "pro-matos-afrique-ouest", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "playwright test", "test:ui": "playwright test --ui", "db:seed": "echo 'Exécutez le script seed.sql dans Supabase SQL Editor'", "type-check": "tsc --noEmit"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.0.0", "@supabase/auth-helpers-nextjs": "^0.8.7", "@supabase/supabase-js": "^2.50.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.23.3", "lucide-react": "^0.525.0", "next": "15.3.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "sonner": "^1.2.4", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^4.0.5", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.40.1", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.16", "eslint": "^9", "eslint-config-next": "15.3.5", "postcss": "^8.4.32", "tailwindcss": "^4", "typescript": "^5"}}