{"name": "pro-matos-afrique-ouest", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "playwright test", "test:ui": "playwright test --ui", "db:setup": "node scripts/setup-database.js", "db:seed": "node scripts/setup-database.js", "type-check": "tsc --noEmit"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "@radix-ui/colors": "^3.0.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@supabase/auth-helpers-nextjs": "^0.8.7", "@supabase/supabase-js": "^2.50.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^17.2.0", "framer-motion": "^12.23.3", "lucide-react": "^0.525.0", "next": "14.2.4", "react": "18.2.0", "react-dom": "18.2.0", "react-hook-form": "^7.60.0", "sonner": "^1.7.4", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^4.0.5", "zustand": "^5.0.6"}, "resolutions": {"next": "14.2.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.40.1", "@tailwindcss/typography": "^0.5.10", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.16", "eslint": "^8", "eslint-config-next": "14.2.4", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "typescript": "^5"}}