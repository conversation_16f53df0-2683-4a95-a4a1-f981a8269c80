'use client'

import { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Package, 
  Download, 
  Lock, 
  Search,
  Filter,
  Star,
  FileText,
  Crown
} from 'lucide-react'
import { useAuthStore, usePermissions } from '@/lib/stores/authStore'
import { supabase, getPublicUrl } from '@/lib/supabase/client'
import { Kit } from '@/lib/types/database'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { toast } from 'sonner'
import DashboardLayout from '@/components/layout/DashboardLayout'

export default function KitsPage() {
  const { user, profile } = useAuthStore()
  const permissions = usePermissions()
  const [kits, setKits] = useState<Kit[]>([])
  const [userDownloads, setUserDownloads] = useState<number[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [filterCategory, setFilterCategory] = useState<string>('all')

  useEffect(() => {
    fetchKits()
    if (user) {
      fetchUserDownloads()
    }
  }, [user])

  const fetchKits = async () => {
    try {
      setLoading(true)

      const { data, error } = await supabase
        .from('kits')
        .select('*')
        .order('downloads', { ascending: false })

      if (error) throw error

      setKits(data || [])
    } catch (error) {
      console.error('Erreur lors du chargement des kits:', error)
      toast.error('Erreur lors du chargement des kits')
    } finally {
      setLoading(false)
    }
  }

  const fetchUserDownloads = async () => {
    try {
      const { data, error } = await supabase
        .from('kit_downloads')
        .select('kit_id')
        .eq('user_id', user?.id)

      if (error) throw error

      setUserDownloads(data?.map(d => d.kit_id) || [])
    } catch (error) {
      console.error('Erreur lors du chargement des téléchargements:', error)
    }
  }

  const handleDownload = async (kit: Kit) => {
    if (!user) {
      toast.error('Vous devez être connecté pour télécharger')
      return
    }

    if (!permissions.canAccessKits) {
      toast.error('Accès restreint', {
        description: 'Vous devez être membre pour télécharger les kits'
      })
      return
    }

    if (kit.is_premium && !permissions.isVip) {
      toast.error('Kit premium', {
        description: 'Ce kit est réservé aux membres VIP'
      })
      return
    }

    try {
      toast.loading('Téléchargement...', { id: 'download' })

      // Enregistrer le téléchargement
      const { error: downloadError } = await supabase
        .from('kit_downloads')
        .insert({
          user_id: user.id,
          kit_id: kit.id
        })

      if (downloadError && !downloadError.message.includes('duplicate')) {
        throw downloadError
      }

      // Incrémenter le compteur
      const { error: updateError } = await supabase
        .from('kits')
        .update({ downloads: kit.downloads + 1 })
        .eq('id', kit.id)

      if (updateError) throw updateError

      // Télécharger le fichier
      const fileUrl = getPublicUrl('kits', kit.file_url.replace('kits/', ''))
      
      const a = document.createElement('a')
      a.href = fileUrl
      a.download = kit.file_name || kit.title
      a.target = '_blank'
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)

      // Mettre à jour l'état local
      setKits(prev => prev.map(k => 
        k.id === kit.id ? { ...k, downloads: k.downloads + 1 } : k
      ))
      
      if (!userDownloads.includes(kit.id)) {
        setUserDownloads(prev => [...prev, kit.id])
      }

      toast.success('Kit téléchargé avec succès', { id: 'download' })

    } catch (error) {
      console.error('Erreur lors du téléchargement:', error)
      toast.error('Erreur lors du téléchargement', { id: 'download' })
    }
  }

  const filteredKits = kits.filter(kit => {
    const matchesSearch = kit.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         kit.description?.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesCategory = filterCategory === 'all' || kit.category === filterCategory
    
    return matchesSearch && matchesCategory
  })

  const categories = Array.from(new Set(kits.map(kit => kit.category)))

  if (!permissions.canAccessKits) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[60vh]">
          <Card className="w-full max-w-md">
            <CardContent className="p-6 text-center">
              <Lock className="h-12 w-12 text-amber-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Accès Membre Requis
              </h3>
              <p className="text-gray-600 mb-4">
                Les kits de prescription sont réservés aux membres. 
                Rejoignez notre communauté pour accéder à ces ressources exclusives.
              </p>
              <Button className="bg-amber-600 hover:bg-amber-700">
                Devenir Membre
              </Button>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0"
        >
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Kits de Prescription</h1>
            <p className="text-gray-600">
              Téléchargez nos guides techniques et modèles de documents professionnels
            </p>
          </div>
          
          <div className="flex items-center space-x-2">
            <Badge className="bg-purple-100 text-purple-800 border-purple-200">
              PREMIUM
            </Badge>
            <span className="text-sm text-gray-600">
              {userDownloads.length} téléchargement{userDownloads.length > 1 ? 's' : ''}
            </span>
          </div>
        </motion.div>

        {/* Info Alert */}
        {!permissions.isVip && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
          >
            <Alert>
              <Crown className="h-4 w-4" />
              <AlertDescription>
                Certains kits premium sont réservés aux membres VIP. 
                <Button variant="link" className="p-0 h-auto text-amber-600">
                  Découvrir les avantages VIP
                </Button>
              </AlertDescription>
            </Alert>
          </motion.div>
        )}

        {/* Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="flex flex-col space-y-4 md:flex-row md:items-center md:space-y-0 md:space-x-4"
        >
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Rechercher dans les kits..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <Select value={filterCategory} onValueChange={setFilterCategory}>
            <SelectTrigger className="w-full md:w-48">
              <Filter className="h-4 w-4 mr-2" />
              <SelectValue placeholder="Catégorie" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Toutes les catégories</SelectItem>
              {categories.map(category => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </motion.div>

        {/* Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-6"
        >
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <Package className="h-8 w-8 text-blue-500" />
                <div>
                  <p className="text-2xl font-bold text-gray-900">{kits.length}</p>
                  <p className="text-sm text-gray-600">Kits disponibles</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <Download className="h-8 w-8 text-green-500" />
                <div>
                  <p className="text-2xl font-bold text-gray-900">
                    {kits.reduce((sum, kit) => sum + kit.downloads, 0)}
                  </p>
                  <p className="text-sm text-gray-600">Téléchargements total</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <Crown className="h-8 w-8 text-amber-500" />
                <div>
                  <p className="text-2xl font-bold text-gray-900">
                    {kits.filter(k => k.is_premium).length}
                  </p>
                  <p className="text-sm text-gray-600">Kits premium</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Kits Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        >
          {loading ? (
            Array.from({ length: 6 }).map((_, index) => (
              <Card key={index} className="animate-pulse">
                <CardContent className="p-6">
                  <div className="h-4 bg-gray-200 rounded mb-4"></div>
                  <div className="h-3 bg-gray-200 rounded mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded mb-4"></div>
                  <div className="h-8 bg-gray-200 rounded"></div>
                </CardContent>
              </Card>
            ))
          ) : filteredKits.length === 0 ? (
            <div className="col-span-full">
              <Card>
                <CardContent className="p-12 text-center">
                  <Package className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Aucun kit trouvé
                  </h3>
                  <p className="text-gray-600">
                    Aucun kit ne correspond à vos critères de recherche.
                  </p>
                </CardContent>
              </Card>
            </div>
          ) : (
            filteredKits.map((kit, index) => {
              const isDownloaded = userDownloads.includes(kit.id)
              const canDownload = !kit.is_premium || permissions.isVip
              
              return (
                <motion.div
                  key={kit.id}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className="h-full hover:shadow-lg transition-shadow">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <CardTitle className="text-lg mb-2">{kit.title}</CardTitle>
                          <div className="flex items-center space-x-2 mb-2">
                            <Badge variant="outline">{kit.category}</Badge>
                            {kit.is_premium && (
                              <Badge className="bg-amber-100 text-amber-800 border-amber-200">
                                <Crown className="h-3 w-3 mr-1" />
                                Premium
                              </Badge>
                            )}
                            {isDownloaded && (
                              <Badge className="bg-green-100 text-green-800 border-green-200">
                                <Download className="h-3 w-3 mr-1" />
                                Téléchargé
                              </Badge>
                            )}
                          </div>
                        </div>
                        <FileText className="h-8 w-8 text-gray-400" />
                      </div>
                      <CardDescription>{kit.description}</CardDescription>
                    </CardHeader>
                    
                    <CardContent>
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                          <div className="flex items-center space-x-1">
                            <Download className="h-4 w-4" />
                            <span>{kit.downloads}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Star className="h-4 w-4 text-yellow-500" />
                            <span>4.8</span>
                          </div>
                        </div>
                      </div>
                      
                      <Button
                        onClick={() => handleDownload(kit)}
                        disabled={!canDownload}
                        className={`w-full ${
                          canDownload 
                            ? 'bg-amber-600 hover:bg-amber-700' 
                            : 'bg-gray-300 cursor-not-allowed'
                        }`}
                      >
                        {!canDownload ? (
                          <>
                            <Lock className="h-4 w-4 mr-2" />
                            VIP Requis
                          </>
                        ) : isDownloaded ? (
                          <>
                            <Download className="h-4 w-4 mr-2" />
                            Télécharger à nouveau
                          </>
                        ) : (
                          <>
                            <Download className="h-4 w-4 mr-2" />
                            Télécharger
                          </>
                        )}
                      </Button>
                    </CardContent>
                  </Card>
                </motion.div>
              )
            })
          )}
        </motion.div>
      </div>
    </DashboardLayout>
  )
}
