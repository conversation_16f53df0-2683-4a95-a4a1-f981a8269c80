'use client'

import { motion } from 'framer-motion'
import { 
  ArrowLeft,
  Crown,
  Users,
  Calendar,
  Award,
  Star,
  TrendingUp,
  Shield,
  Zap,
  MessageCircle,
  Video,
  FileText,
  Database
} from 'lucide-react'
import Link from 'next/link'
import GlobalNavigation from '@/components/navigation/GlobalNavigation'
import ResponsiveLayout from '@/components/layout/ResponsiveLayout'
import EcosystemHub from '@/components/integration/EcosystemHub'
import SmartLinks from '@/components/integration/SmartLinks'
import NetworkEffect from '@/components/club/NetworkEffect'

export default function ClubPage() {
  const memberStats = [
    {
      label: 'Membres Actifs',
      value: '1,234',
      change: '+89',
      changeType: 'increase',
      icon: <Users className="h-6 w-6" />
    },
    {
      label: 'Événements ce Mois',
      value: '12',
      change: '+3',
      changeType: 'increase',
      icon: <Calendar className="h-6 w-6" />
    },
    {
      label: 'Projets Partagés',
      value: '567',
      change: '+156',
      changeType: 'increase',
      icon: <FileText className="h-6 w-6" />
    },
    {
      label: 'Note Satisfaction',
      value: '4.9',
      change: '+0.1',
      changeType: 'increase',
      icon: <Star className="h-6 w-6" />
    }
  ]

  const membershipTiers = [
    {
      name: 'Bronze',
      members: 456,
      color: 'from-amber-600 to-amber-700',
      benefits: ['Accès forum', 'Newsletter mensuelle', 'Support email'],
      price: 'Gratuit'
    },
    {
      name: 'Silver',
      members: 389,
      color: 'from-gray-400 to-gray-500',
      benefits: ['Webinaires exclusifs', 'Templates avancés', 'Chat support'],
      price: '25,000 FCFA/mois'
    },
    {
      name: 'Gold',
      members: 267,
      color: 'from-yellow-400 to-yellow-500',
      benefits: ['Consultation directe', 'Certification officielle', 'Événements VIP'],
      price: '75,000 FCFA/mois'
    },
    {
      name: 'Platinum',
      members: 122,
      color: 'from-purple-400 to-purple-500',
      benefits: ['Expert dédié', 'Accès illimité', 'Formation personnalisée'],
      price: '150,000 FCFA/mois'
    }
  ]

  const upcomingEvents = [
    {
      id: 1,
      title: 'Webinaire: Nouvelles Normes NF C 15-100',
      date: '2024-01-20',
      time: '14:00',
      type: 'webinar',
      level: 'silver',
      attendees: 89,
      maxAttendees: 100
    },
    {
      id: 2,
      title: 'Formation: Installation Photovoltaïque',
      date: '2024-01-25',
      time: '09:00',
      type: 'training',
      level: 'gold',
      attendees: 23,
      maxAttendees: 25
    },
    {
      id: 3,
      title: 'Networking: Rencontre Professionnels Abidjan',
      date: '2024-01-30',
      time: '18:00',
      type: 'networking',
      level: 'platinum',
      attendees: 15,
      maxAttendees: 20
    }
  ]

  const showcaseProjects = [
    {
      id: 1,
      title: 'Centre Commercial Plateau - Installation Complète',
      author: 'Ing. Kouame Yao',
      company: 'SODECI',
      level: 'platinum',
      likes: 47,
      comments: 12,
      views: 234,
      image: '/api/placeholder/300/200'
    },
    {
      id: 2,
      title: 'Résidence Solaire Cocody - 50kW',
      author: 'Fatou Diallo',
      company: 'SolarTech CI',
      level: 'gold',
      likes: 38,
      comments: 8,
      views: 189,
      image: '/api/placeholder/300/200'
    },
    {
      id: 3,
      title: 'Usine Automatisée Yopougon',
      author: 'Jean Kouassi',
      company: 'AutoElec',
      level: 'gold',
      likes: 52,
      comments: 15,
      views: 298,
      image: '/api/placeholder/300/200'
    }
  ]

  const getEventTypeIcon = (type: string) => {
    switch (type) {
      case 'webinar':
        return <Video className="h-5 w-5" />
      case 'training':
        return <Award className="h-5 w-5" />
      case 'networking':
        return <Users className="h-5 w-5" />
      default:
        return <Calendar className="h-5 w-5" />
    }
  }

  const getEventTypeColor = (type: string) => {
    switch (type) {
      case 'webinar':
        return 'bg-blue-100 text-blue-800'
      case 'training':
        return 'bg-green-100 text-green-800'
      case 'networking':
        return 'bg-purple-100 text-purple-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'bronze':
        return 'bg-amber-100 text-amber-800'
      case 'silver':
        return 'bg-gray-100 text-gray-800'
      case 'gold':
        return 'bg-yellow-100 text-yellow-800'
      case 'platinum':
        return 'bg-purple-100 text-purple-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <>
      <GlobalNavigation />
      <ResponsiveLayout
        title="Club Membre Exclusif"
        subtitle="Réseau professionnel fermé pour l'élite de l'électrique en Afrique de l'Ouest"
        sidebar={
          <SmartLinks 
            context={{
              module: 'club',
              data: { memberId: 'current', level: 'gold' },
              action: 'view'
            }}
          />
        }
        actions={
          <Link 
            href="/"
            className="flex items-center space-x-2 text-slate-700 hover:text-amber-600 transition-colors"
          >
            <ArrowLeft className="h-5 w-5" />
            <span className="hidden sm:inline">Retour à l'accueil</span>
          </Link>
        }
      >
        {/* Statistiques du club */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {memberStats.map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.05 }}
              className="industrial-card p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">{stat.label}</p>
                  <div className="flex items-baseline space-x-2">
                    <p className="text-2xl font-bold text-slate-900">{stat.value}</p>
                    <span className={`text-sm font-medium ${
                      stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      +{stat.change}
                    </span>
                  </div>
                </div>
                <div className="p-3 bg-amber-100 rounded-lg text-amber-600">
                  {stat.icon}
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Effet réseau */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="mb-8"
        >
          <NetworkEffect />
        </motion.div>

        {/* Intégration écosystème */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="mb-8"
        >
          <EcosystemHub currentModule="club" />
        </motion.div>

        {/* Niveaux d'adhésion */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="mb-8"
        >
          <h2 className="text-2xl font-bold text-slate-900 mb-6">Niveaux d'Adhésion</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {membershipTiers.map((tier, index) => (
              <motion.div
                key={tier.name}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.6 + index * 0.1 }}
                className="industrial-card p-6 text-center"
              >
                <div className={`w-16 h-16 bg-gradient-to-r ${tier.color} rounded-full flex items-center justify-center mx-auto mb-4`}>
                  <Crown className="h-8 w-8 text-white" />
                </div>
                
                <h3 className="text-xl font-bold text-gray-900 mb-2">{tier.name}</h3>
                <p className="text-lg font-bold text-amber-600 mb-4">{tier.price}</p>
                
                <div className="text-sm text-gray-600 mb-4">
                  <span className="font-medium">{tier.members}</span> membres actifs
                </div>
                
                <ul className="text-sm text-gray-600 space-y-2 mb-6">
                  {tier.benefits.map((benefit, i) => (
                    <li key={i} className="flex items-center space-x-2">
                      <Shield className="h-4 w-4 text-green-500 flex-shrink-0" />
                      <span>{benefit}</span>
                    </li>
                  ))}
                </ul>
                
                <button className="w-full btn-premium text-sm py-2">
                  {tier.name === 'Bronze' ? 'Rejoindre' : 'Passer au ' + tier.name}
                </button>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Événements à venir */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
          className="mb-8"
        >
          <h2 className="text-2xl font-bold text-slate-900 mb-6">Événements Exclusifs</h2>
          <div className="space-y-4">
            {upcomingEvents.map((event, index) => (
              <motion.div
                key={event.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.9 + index * 0.1 }}
                className="industrial-card p-6"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-3">
                      <span className={`px-3 py-1 rounded-full text-sm font-medium flex items-center space-x-1 ${getEventTypeColor(event.type)}`}>
                        {getEventTypeIcon(event.type)}
                        <span className="capitalize">{event.type}</span>
                      </span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getLevelColor(event.level)}`}>
                        {event.level}+ requis
                      </span>
                    </div>
                    
                    <h3 className="text-lg font-bold text-gray-900 mb-2">{event.title}</h3>
                    <div className="flex items-center space-x-4 text-sm text-gray-600 mb-3">
                      <span>📅 {event.date}</span>
                      <span>🕐 {event.time}</span>
                      <span>👥 {event.attendees}/{event.maxAttendees} inscrits</span>
                    </div>
                    
                    <div className="w-full bg-gray-200 rounded-full h-2 mb-3">
                      <div 
                        className="bg-amber-500 h-2 rounded-full"
                        style={{ width: `${(event.attendees / event.maxAttendees) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                  
                  <div className="ml-6">
                    <button className="px-6 py-2 bg-amber-600 text-white rounded-lg hover:bg-amber-700 transition-colors">
                      S'inscrire
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Projets showcase */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.1 }}
        >
          <h2 className="text-2xl font-bold text-slate-900 mb-6">Projets Showcase Membres</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {showcaseProjects.map((project, index) => (
              <motion.div
                key={project.id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 1.2 + index * 0.1 }}
                className="industrial-card overflow-hidden"
              >
                <div className="h-48 bg-gradient-to-r from-gray-200 to-gray-300 flex items-center justify-center">
                  <Zap className="h-16 w-16 text-gray-400" />
                </div>
                
                <div className="p-6">
                  <div className="flex items-center justify-between mb-3">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getLevelColor(project.level)}`}>
                      {project.level}
                    </span>
                    <div className="flex items-center space-x-3 text-sm text-gray-500">
                      <span className="flex items-center space-x-1">
                        <Star className="h-4 w-4" />
                        <span>{project.likes}</span>
                      </span>
                      <span className="flex items-center space-x-1">
                        <MessageCircle className="h-4 w-4" />
                        <span>{project.comments}</span>
                      </span>
                    </div>
                  </div>
                  
                  <h3 className="font-bold text-gray-900 mb-2 line-clamp-2">{project.title}</h3>
                  <p className="text-sm text-gray-600 mb-1">{project.author}</p>
                  <p className="text-xs text-gray-500 mb-4">{project.company}</p>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-500">{project.views} vues</span>
                    <button className="text-amber-600 hover:text-amber-700 font-medium text-sm">
                      Voir détails →
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </ResponsiveLayout>
    </>
  )
}
