{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Pro%20Matos%20Afrique%20Ouest/pro-matos-afrique-ouest/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { motion } from 'framer-motion'\nimport {\n  Zap,\n  Shield,\n  Users,\n  TrendingUp,\n  Bell,\n  Search,\n  Menu,\n  X,\n  Star,\n  Award,\n  BarChart3,\n  Lightbulb\n} from 'lucide-react'\n\nexport default function Home() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false)\n  const [currentTime, setCurrentTime] = useState(new Date())\n\n  useEffect(() => {\n    const timer = setInterval(() => setCurrentTime(new Date()), 1000)\n    return () => clearInterval(timer)\n  }, [])\n\n  const features = [\n    {\n      icon: <Zap className=\"h-8 w-8\" />,\n      title: \"Hub d'Information Temps Réel\",\n      description: \"Flux instantané des disponibilités stocks, nouveaux produits et alertes marché\",\n      color: \"from-blue-500 to-blue-600\"\n    },\n    {\n      icon: <Shield className=\"h-8 w-8\" />,\n      title: \"Conseil Technique Expert\",\n      description: \"Validation technique, simulateur de compatibilité et bibliothèque de ressources\",\n      color: \"from-green-500 to-green-600\"\n    },\n    {\n      icon: <Award className=\"h-8 w-8\" />,\n      title: \"Module Prescripteur Pro\",\n      description: \"Kits de prescription, générateurs automatisés et certification de conformité\",\n      color: \"from-purple-500 to-purple-600\"\n    },\n    {\n      icon: <Users className=\"h-8 w-8\" />,\n      title: \"Club Membre Exclusif\",\n      description: \"Niveaux d'adhésion privilégiés avec avantages différenciés et réseau fermé\",\n      color: \"from-amber-500 to-amber-600\"\n    }\n  ]\n\n  const stats = [\n    { label: \"Professionnels Actifs\", value: \"2,500+\", icon: <Users className=\"h-5 w-5\" /> },\n    { label: \"Produits Référencés\", value: \"15,000+\", icon: <BarChart3 className=\"h-5 w-5\" /> },\n    { label: \"Validations Techniques\", value: \"500+\", icon: <Shield className=\"h-5 w-5\" /> },\n    { label: \"Taux de Satisfaction\", value: \"98%\", icon: <Star className=\"h-5 w-5\" /> }\n  ]\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100\">\n      {/* Header */}\n      <header className=\"bg-white/80 backdrop-blur-md border-b border-slate-200 sticky top-0 z-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-10 h-10 bg-gradient-to-r from-amber-400 to-amber-500 rounded-lg flex items-center justify-center\">\n                  <Lightbulb className=\"h-6 w-6 text-slate-900\" />\n                </div>\n                <div>\n                  <h1 className=\"text-xl font-bold text-slate-900\">Pro Matos</h1>\n                  <p className=\"text-xs text-slate-600\">Afrique Ouest</p>\n                </div>\n              </div>\n            </div>\n\n            <nav className=\"hidden md:flex items-center space-x-8\">\n              <a href=\"#features\" className=\"text-slate-700 hover:text-amber-600 transition-colors\">Fonctionnalités</a>\n              <a href=\"#about\" className=\"text-slate-700 hover:text-amber-600 transition-colors\">À Propos</a>\n              <a href=\"#contact\" className=\"text-slate-700 hover:text-amber-600 transition-colors\">Contact</a>\n              <button className=\"btn-premium\">\n                Rejoindre le Club\n              </button>\n            </nav>\n\n            <button\n              className=\"md:hidden p-2 rounded-lg hover:bg-slate-100\"\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n            >\n              {isMenuOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Menu */}\n        {isMenuOpen && (\n          <motion.div\n            initial={{ opacity: 0, y: -20 }}\n            animate={{ opacity: 1, y: 0 }}\n            className=\"md:hidden bg-white border-t border-slate-200\"\n          >\n            <div className=\"px-4 py-4 space-y-4\">\n              <a href=\"#features\" className=\"block text-slate-700 hover:text-amber-600\">Fonctionnalités</a>\n              <a href=\"#about\" className=\"block text-slate-700 hover:text-amber-600\">À Propos</a>\n              <a href=\"#contact\" className=\"block text-slate-700 hover:text-amber-600\">Contact</a>\n              <button className=\"w-full btn-premium\">\n                Rejoindre le Club\n              </button>\n            </div>\n          </motion.div>\n        )}\n      </header>\n\n      {/* Hero Section */}\n      <section className=\"relative overflow-hidden py-20 lg:py-32\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n            <motion.div\n              initial={{ opacity: 0, x: -50 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8 }}\n            >\n              <h1 className=\"text-4xl lg:text-6xl font-bold text-slate-900 leading-tight\">\n                Le Hub <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-amber-400 to-amber-600\">Incontournable</span> du Matériel Électrique\n              </h1>\n              <p className=\"text-xl text-slate-600 mt-6 leading-relaxed\">\n                Devenez maître de l'écosystème électrique en Afrique de l'Ouest.\n                Information, expertise et réseau exclusif pour les professionnels d'élite.\n              </p>\n\n              <div className=\"flex flex-col sm:flex-row gap-4 mt-8\">\n                <a href=\"/hub\" className=\"btn-premium text-lg px-8 py-4 text-center\">\n                  Accéder au Hub d'Information\n                </a>\n                <button className=\"px-8 py-4 border-2 border-slate-300 text-slate-700 rounded-lg hover:border-amber-400 hover:text-amber-600 transition-all duration-200\">\n                  Découvrir les Avantages\n                </button>\n              </div>\n\n              <div className=\"flex items-center space-x-6 mt-8 text-sm text-slate-600\">\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n                  <span>Temps réel</span>\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"w-2 h-2 bg-amber-500 rounded-full\"></div>\n                  <span>{currentTime.toLocaleTimeString('fr-FR')}</span>\n                </div>\n              </div>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, x: 50 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8, delay: 0.2 }}\n              className=\"relative\"\n            >\n              <div className=\"industrial-card p-8\">\n                <div className=\"grid grid-cols-2 gap-4\">\n                  {stats.map((stat, index) => (\n                    <motion.div\n                      key={stat.label}\n                      initial={{ opacity: 0, y: 20 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      transition={{ delay: 0.4 + index * 0.1 }}\n                      className=\"text-center p-4 bg-slate-50 rounded-lg\"\n                    >\n                      <div className=\"flex justify-center mb-2 text-amber-600\">\n                        {stat.icon}\n                      </div>\n                      <div className=\"text-2xl font-bold text-slate-900\">{stat.value}</div>\n                      <div className=\"text-sm text-slate-600\">{stat.label}</div>\n                    </motion.div>\n                  ))}\n                </div>\n              </div>\n            </motion.div>\n          </div>\n        </div>\n      </section>\n\n      {/* Section Fonctionnalités */}\n      <section id=\"features\" className=\"py-20 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            viewport={{ once: true }}\n            className=\"text-center mb-16\"\n          >\n            <h2 className=\"text-3xl lg:text-4xl font-bold text-slate-900 mb-4\">\n              Fonctionnalités <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-amber-400 to-amber-600\">Stratégiques</span>\n            </h2>\n            <p className=\"text-xl text-slate-600 max-w-3xl mx-auto\">\n              Contrôlez l'écosystème électrique avec nos outils professionnels exclusifs\n            </p>\n          </motion.div>\n\n          <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n            {features.map((feature, index) => (\n              <motion.div\n                key={feature.title}\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                viewport={{ once: true }}\n                transition={{ delay: index * 0.1 }}\n                className=\"industrial-card p-8\"\n              >\n                <div className={`w-16 h-16 bg-gradient-to-r ${feature.color} rounded-lg flex items-center justify-center text-white mb-6`}>\n                  {feature.icon}\n                </div>\n                <h3 className=\"text-xl font-bold text-slate-900 mb-4\">{feature.title}</h3>\n                <p className=\"text-slate-600 leading-relaxed\">{feature.description}</p>\n                <div className=\"mt-6\">\n                  <a\n                    href={\n                      feature.title.includes('Hub') ? '/hub' :\n                      feature.title.includes('Conseil') ? '/expert' :\n                      feature.title.includes('Prescripteur') ? '/prescriptor' :\n                      '#'\n                    }\n                    className=\"text-amber-600 hover:text-amber-700 font-medium inline-flex items-center\"\n                  >\n                    Découvrir →\n                  </a>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Section Avantages Stratégiques */}\n      <section className=\"py-20 bg-gradient-to-br from-slate-900 to-slate-800 text-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            viewport={{ once: true }}\n            className=\"text-center mb-16\"\n          >\n            <h2 className=\"text-3xl lg:text-4xl font-bold mb-4\">\n              Pourquoi Nous Sommes <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-amber-400 to-amber-600\">Incontournables</span>\n            </h2>\n            <p className=\"text-xl text-slate-300 max-w-3xl mx-auto\">\n              Notre stratégie de contrôle de l'écosystème vous donne un avantage concurrentiel décisif\n            </p>\n          </motion.div>\n\n          <div className=\"grid md:grid-cols-3 gap-8\">\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              viewport={{ once: true }}\n              transition={{ delay: 0.1 }}\n              className=\"text-center\"\n            >\n              <div className=\"w-16 h-16 bg-gradient-to-r from-amber-400 to-amber-500 rounded-lg flex items-center justify-center mx-auto mb-6\">\n                <TrendingUp className=\"h-8 w-8 text-slate-900\" />\n              </div>\n              <h3 className=\"text-xl font-bold mb-4\">Information = Pouvoir</h3>\n              <p className=\"text-slate-300\">\n                Contrôlez l'information du marché, donc contrôlez le marché.\n                Nos données temps réel vous donnent l'avantage décisionnel.\n              </p>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              viewport={{ once: true }}\n              transition={{ delay: 0.2 }}\n              className=\"text-center\"\n            >\n              <div className=\"w-16 h-16 bg-gradient-to-r from-amber-400 to-amber-500 rounded-lg flex items-center justify-center mx-auto mb-6\">\n                <Shield className=\"h-8 w-8 text-slate-900\" />\n              </div>\n              <h3 className=\"text-xl font-bold mb-4\">Expertise = Indispensabilité</h3>\n              <p className=\"text-slate-300\">\n                Notre validation technique devient obligatoire.\n                Les clients préfèrent la sécurité de notre expertise reconnue.\n              </p>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              viewport={{ once: true }}\n              transition={{ delay: 0.3 }}\n              className=\"text-center\"\n            >\n              <div className=\"w-16 h-16 bg-gradient-to-r from-amber-400 to-amber-500 rounded-lg flex items-center justify-center mx-auto mb-6\">\n                <Users className=\"h-8 w-8 text-slate-900\" />\n              </div>\n              <h3 className=\"text-xl font-bold mb-4\">Réseau = Barrière</h3>\n              <p className=\"text-slate-300\">\n                Être exclu de notre club devient un handicap concurrentiel.\n                Notre réseau fermé crée une barrière à l'entrée naturelle.\n              </p>\n            </motion.div>\n          </div>\n        </div>\n      </section>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAmBe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IAEnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,YAAY,IAAM,eAAe,IAAI,SAAS;QAC5D,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,MAAM,WAAW;QACf;YACE,oBAAM,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;YACrB,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,aAAa;YACb,OAAO;QACT;KACD;IAED,MAAM,QAAQ;QACZ;YAAE,OAAO;YAAyB,OAAO;YAAU,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;QAAa;QACvF;YAAE,OAAO;YAAuB,OAAO;YAAW,oBAAM,8OAAC,kNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;QAAa;QAC1F;YAAE,OAAO;YAA0B,OAAO;YAAQ,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;QAAa;QACvF;YAAE,OAAO;YAAwB,OAAO;YAAO,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;QAAa;KACnF;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;;kCAChB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,4MAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;0DAEvB,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,8OAAC;wDAAE,WAAU;kEAAyB;;;;;;;;;;;;;;;;;;;;;;;8CAK5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,MAAK;4CAAY,WAAU;sDAAwD;;;;;;sDACtF,8OAAC;4CAAE,MAAK;4CAAS,WAAU;sDAAwD;;;;;;sDACnF,8OAAC;4CAAE,MAAK;4CAAW,WAAU;sDAAwD;;;;;;sDACrF,8OAAC;4CAAO,WAAU;sDAAc;;;;;;;;;;;;8CAKlC,8OAAC;oCACC,WAAU;oCACV,SAAS,IAAM,cAAc,CAAC;8CAE7B,2BAAa,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;6DAAe,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;oBAM/D,4BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,MAAK;oCAAY,WAAU;8CAA4C;;;;;;8CAC1E,8OAAC;oCAAE,MAAK;oCAAS,WAAU;8CAA4C;;;;;;8CACvE,8OAAC;oCAAE,MAAK;oCAAW,WAAU;8CAA4C;;;;;;8CACzE,8OAAC;oCAAO,WAAU;8CAAqB;;;;;;;;;;;;;;;;;;;;;;;0BAS/C,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;gCAAI;;kDAE5B,8OAAC;wCAAG,WAAU;;4CAA8D;0DACnE,8OAAC;gDAAK,WAAU;0DAA6E;;;;;;4CAAqB;;;;;;;kDAE3H,8OAAC;wCAAE,WAAU;kDAA8C;;;;;;kDAK3D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,MAAK;gDAAO,WAAU;0DAA4C;;;;;;0DAGrE,8OAAC;gDAAO,WAAU;0DAAwI;;;;;;;;;;;;kDAK5J,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;kEAAK;;;;;;;;;;;;0DAER,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;kEAAM,YAAY,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;0CAK5C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,OAAO,MAAM,QAAQ;gDAAI;gDACvC,WAAU;;kEAEV,8OAAC;wDAAI,WAAU;kEACZ,KAAK,IAAI;;;;;;kEAEZ,8OAAC;wDAAI,WAAU;kEAAqC,KAAK,KAAK;;;;;;kEAC9D,8OAAC;wDAAI,WAAU;kEAA0B,KAAK,KAAK;;;;;;;+CAV9C,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAqB/B,8OAAC;gBAAQ,IAAG;gBAAW,WAAU;0BAC/B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;oCAAG,WAAU;;wCAAqD;sDACjD,8OAAC;4CAAK,WAAU;sDAA6E;;;;;;;;;;;;8CAE/G,8OAAC;oCAAE,WAAU;8CAA2C;;;;;;;;;;;;sCAK1D,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCAAE,OAAO,QAAQ;oCAAI;oCACjC,WAAU;;sDAEV,8OAAC;4CAAI,WAAW,CAAC,2BAA2B,EAAE,QAAQ,KAAK,CAAC,4DAA4D,CAAC;sDACtH,QAAQ,IAAI;;;;;;sDAEf,8OAAC;4CAAG,WAAU;sDAAyC,QAAQ,KAAK;;;;;;sDACpE,8OAAC;4CAAE,WAAU;sDAAkC,QAAQ,WAAW;;;;;;sDAClE,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,MACE,QAAQ,KAAK,CAAC,QAAQ,CAAC,SAAS,SAChC,QAAQ,KAAK,CAAC,QAAQ,CAAC,aAAa,YACpC,QAAQ,KAAK,CAAC,QAAQ,CAAC,kBAAkB,iBACzC;gDAEF,WAAU;0DACX;;;;;;;;;;;;mCArBE,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;0BAgC5B,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;oCAAG,WAAU;;wCAAsC;sDAC7B,8OAAC;4CAAK,WAAU;sDAA6E;;;;;;;;;;;;8CAEpH,8OAAC;oCAAE,WAAU;8CAA2C;;;;;;;;;;;;sCAK1D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCAAE,OAAO;oCAAI;oCACzB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;sDAExB,8OAAC;4CAAG,WAAU;sDAAyB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;sDAAiB;;;;;;;;;;;;8CAMhC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCAAE,OAAO;oCAAI;oCACzB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,8OAAC;4CAAG,WAAU;sDAAyB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;sDAAiB;;;;;;;;;;;;8CAMhC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCAAE,OAAO;oCAAI;oCACzB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAG,WAAU;sDAAyB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;sDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU5C", "debugId": null}}]}