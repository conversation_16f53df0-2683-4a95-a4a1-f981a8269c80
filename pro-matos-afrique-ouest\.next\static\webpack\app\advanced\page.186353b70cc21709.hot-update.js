"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/advanced/page",{

/***/ "(app-pages-browser)/./src/components/advanced/OfflineManager.tsx":
/*!****************************************************!*\
  !*** ./src/components/advanced/OfflineManager.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ OfflineManager; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Cloud,Database,Download,HardDrive,RefreshCw,Settings,Upload,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Cloud,Database,Download,HardDrive,RefreshCw,Settings,Upload,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Cloud,Database,Download,HardDrive,RefreshCw,Settings,Upload,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Cloud,Database,Download,HardDrive,RefreshCw,Settings,Upload,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Cloud,Database,Download,HardDrive,RefreshCw,Settings,Upload,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Cloud,Database,Download,HardDrive,RefreshCw,Settings,Upload,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Cloud,Database,Download,HardDrive,RefreshCw,Settings,Upload,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Cloud,Database,Download,HardDrive,RefreshCw,Settings,Upload,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hard-drive.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Cloud,Database,Download,HardDrive,RefreshCw,Settings,Upload,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Cloud,Database,Download,HardDrive,RefreshCw,Settings,Upload,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Cloud,Database,Download,HardDrive,RefreshCw,Settings,Upload,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Cloud,Database,Download,HardDrive,RefreshCw,Settings,Upload,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cloud.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction OfflineManager(param) {\n    let { className = \"\" } = param;\n    _s();\n    const [syncStatus, setSyncStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOnline: navigator.onLine,\n        lastSync: new Date().toISOString(),\n        pendingUploads: 0,\n        pendingDownloads: 0,\n        totalSize: 0,\n        availableStorage: 0,\n        autoSync: true\n    });\n    const [offlineData, setOfflineData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"1\",\n            type: \"products\",\n            name: \"Catalogue Produits Schneider\",\n            size: 15728640,\n            lastSync: new Date(Date.now() - 3600000).toISOString(),\n            status: \"synced\",\n            priority: \"high\"\n        },\n        {\n            id: \"2\",\n            type: \"alerts\",\n            name: \"Alertes et Notifications\",\n            size: 2097152,\n            lastSync: new Date(Date.now() - 1800000).toISOString(),\n            status: \"pending\",\n            priority: \"medium\"\n        },\n        {\n            id: \"3\",\n            type: \"documents\",\n            name: \"Fiches Techniques\",\n            size: 52428800,\n            lastSync: new Date(Date.now() - 7200000).toISOString(),\n            status: \"synced\",\n            priority: \"high\"\n        },\n        {\n            id: \"4\",\n            type: \"calculations\",\n            name: \"Calculs Sauvegard\\xe9s\",\n            size: 1048576,\n            lastSync: new Date(Date.now() - 900000).toISOString(),\n            status: \"error\",\n            priority: \"low\"\n        },\n        {\n            id: \"5\",\n            type: \"reports\",\n            name: \"Rapports Techniques\",\n            size: 10485760,\n            lastSync: new Date().toISOString(),\n            status: \"downloading\",\n            priority: \"medium\"\n        }\n    ]);\n    const [isSyncing, setIsSyncing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [downloadProgress, setDownloadProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Surveiller le statut de connexion\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleOnline = ()=>{\n            setSyncStatus((prev)=>({\n                    ...prev,\n                    isOnline: true\n                }));\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Connexion r\\xe9tablie - Synchronisation automatique\");\n            if (syncStatus.autoSync) {\n                handleAutoSync();\n            }\n        };\n        const handleOffline = ()=>{\n            setSyncStatus((prev)=>({\n                    ...prev,\n                    isOnline: false\n                }));\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.warning(\"Mode hors ligne activ\\xe9\");\n        };\n        window.addEventListener(\"online\", handleOnline);\n        window.addEventListener(\"offline\", handleOffline);\n        return ()=>{\n            window.removeEventListener(\"online\", handleOnline);\n            window.removeEventListener(\"offline\", handleOffline);\n        };\n    }, [\n        syncStatus.autoSync\n    ]);\n    // Calculer l'espace de stockage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const calculateStorage = async ()=>{\n            try {\n                if (\"storage\" in navigator && \"estimate\" in navigator.storage) {\n                    const estimate = await navigator.storage.estimate();\n                    const totalSize = offlineData.reduce((sum, item)=>sum + item.size, 0);\n                    setSyncStatus((prev)=>({\n                            ...prev,\n                            totalSize,\n                            availableStorage: estimate.quota || 0\n                        }));\n                }\n            } catch (error) {\n                console.error(\"Erreur calcul stockage:\", error);\n            }\n        };\n        calculateStorage();\n    }, [\n        offlineData\n    ]);\n    // Synchronisation automatique\n    const handleAutoSync = async ()=>{\n        if (!syncStatus.isOnline || isSyncing) return;\n        setIsSyncing(true);\n        try {\n            // Simuler la synchronisation\n            const pendingItems = offlineData.filter((item)=>item.status === \"pending\" || item.status === \"error\");\n            for (const item of pendingItems){\n                await simulateSync(item.id);\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n            }\n            setSyncStatus((prev)=>({\n                    ...prev,\n                    lastSync: new Date().toISOString(),\n                    pendingUploads: 0,\n                    pendingDownloads: 0\n                }));\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Synchronisation termin\\xe9e\");\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Erreur lors de la synchronisation\");\n        } finally{\n            setIsSyncing(false);\n        }\n    };\n    // Simuler la synchronisation d'un élément\n    const simulateSync = async (itemId)=>{\n        setOfflineData((prev)=>prev.map((item)=>item.id === itemId ? {\n                    ...item,\n                    status: \"downloading\"\n                } : item));\n        // Simuler le progrès de téléchargement\n        for(let progress = 0; progress <= 100; progress += 10){\n            setDownloadProgress((prev)=>({\n                    ...prev,\n                    [itemId]: progress\n                }));\n            await new Promise((resolve)=>setTimeout(resolve, 100));\n        }\n        setOfflineData((prev)=>prev.map((item)=>item.id === itemId ? {\n                    ...item,\n                    status: \"synced\",\n                    lastSync: new Date().toISOString()\n                } : item));\n        setDownloadProgress((prev)=>{\n            const newProgress = {\n                ...prev\n            };\n            delete newProgress[itemId];\n            return newProgress;\n        });\n    };\n    // Télécharger un élément spécifique\n    const downloadItem = async (itemId)=>{\n        if (!syncStatus.isOnline) {\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Connexion requise pour t\\xe9l\\xe9charger\");\n            return;\n        }\n        await simulateSync(itemId);\n        sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"T\\xe9l\\xe9chargement termin\\xe9\");\n    };\n    // Supprimer un élément du cache\n    const removeFromCache = (itemId)=>{\n        setOfflineData((prev)=>prev.filter((item)=>item.id !== itemId));\n        sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"\\xc9l\\xe9ment supprim\\xe9 du cache\");\n    };\n    // Vider tout le cache\n    const clearAllCache = ()=>{\n        setOfflineData([]);\n        setSyncStatus((prev)=>({\n                ...prev,\n                totalSize: 0\n            }));\n        sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Cache vid\\xe9\");\n    };\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return \"0 B\";\n        const k = 1024;\n        const sizes = [\n            \"B\",\n            \"KB\",\n            \"MB\",\n            \"GB\"\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"synced\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                    lineNumber: 249,\n                    columnNumber: 29\n                }, this);\n            case \"pending\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 30\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-4 w-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                    lineNumber: 251,\n                    columnNumber: 28\n                }, this);\n            case \"downloading\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-4 w-4 text-blue-500 animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 34\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const getTypeIcon = (type)=>{\n        switch(type){\n            case \"products\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 31\n                }, this);\n            case \"alerts\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                    lineNumber: 260,\n                    columnNumber: 29\n                }, this);\n            case \"documents\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                    lineNumber: 261,\n                    columnNumber: 32\n                }, this);\n            case \"calculations\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 35\n                }, this);\n            case \"reports\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                    lineNumber: 263,\n                    columnNumber: 30\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                    lineNumber: 264,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case \"critical\":\n                return \"bg-red-100 text-red-800 border-red-200\";\n            case \"high\":\n                return \"bg-orange-100 text-orange-800 border-orange-200\";\n            case \"medium\":\n                return \"bg-blue-100 text-blue-800 border-blue-200\";\n            default:\n                return \"bg-gray-100 text-gray-800 border-gray-200\";\n        }\n    };\n    const storageUsagePercent = syncStatus.availableStorage > 0 ? syncStatus.totalSize / syncStatus.availableStorage * 100 : 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 rounded-lg flex items-center justify-center \".concat(syncStatus.isOnline ? \"bg-gradient-to-r from-green-400 to-green-500\" : \"bg-gradient-to-r from-gray-400 to-gray-500\"),\n                                            children: syncStatus.isOnline ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-6 w-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-6 w-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Gestionnaire Hors Ligne\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            className: syncStatus.isOnline ? \"bg-green-100 text-green-800\" : \"bg-gray-100 text-gray-800\",\n                                                            children: syncStatus.isOnline ? \"En ligne\" : \"Hors ligne\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                    children: [\n                                                        \"Derni\\xe8re sync: \",\n                                                        new Date(syncStatus.lastSync).toLocaleString(\"fr-FR\")\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>setShowSettings(!showSettings),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: handleAutoSync,\n                                            disabled: !syncStatus.isOnline || isSyncing,\n                                            className: \"bg-blue-500 hover:bg-blue-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2 \".concat(isSyncing ? \"animate-spin\" : \"\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 17\n                                                }, this),\n                                                isSyncing ? \"Synchronisation...\" : \"Synchroniser\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 9\n                    }, this),\n                    showSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"border-t border-gray-200 bg-gray-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: \"Synchronisation automatique\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Synchroniser automatiquement quand la connexion est r\\xe9tablie\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_6__.Switch, {\n                                            checked: syncStatus.autoSync,\n                                            onCheckedChange: (checked)=>setSyncStatus((prev)=>({\n                                                        ...prev,\n                                                        autoSync: checked\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-900\",\n                                            children: \"Vider tout le cache\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"destructive\",\n                                            size: \"sm\",\n                                            onClick: clearAllCache,\n                                            children: \"Vider\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                lineNumber: 284,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Espace utilis\\xe9\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg font-bold\",\n                                                children: formatFileSize(syncStatus.totalSize)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                            lineNumber: 363,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-8 w-8 text-green-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"\\xc9l\\xe9ments synchronis\\xe9s\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg font-bold\",\n                                                children: offlineData.filter((item)=>item.status === \"synced\").length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                lineNumber: 376,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                            lineNumber: 375,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                        lineNumber: 374,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-8 w-8 text-yellow-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"En attente\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg font-bold\",\n                                                children: offlineData.filter((item)=>item.status === \"pending\" || item.status === \"error\").length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                            lineNumber: 389,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                lineNumber: 361,\n                columnNumber: 7\n            }, this),\n            syncStatus.availableStorage > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-lg\",\n                            children: \"Utilisation du Stockage\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                            lineNumber: 407,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                        lineNumber: 406,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Utilis\\xe9: \",\n                                                formatFileSize(syncStatus.totalSize)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Disponible: \",\n                                                formatFileSize(syncStatus.availableStorage)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_5__.Progress, {\n                                    value: storageUsagePercent,\n                                    className: \"h-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-600\",\n                                    children: [\n                                        storageUsagePercent.toFixed(1),\n                                        \"% de l'espace de stockage utilis\\xe9\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                            lineNumber: 410,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                        lineNumber: 409,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                lineNumber: 405,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                children: \"Donn\\xe9es Hors Ligne\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                lineNumber: 427,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: \"G\\xe9rez vos donn\\xe9es disponibles en mode hors ligne\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                lineNumber: 428,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                        lineNumber: 426,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: offlineData.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_20__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    className: \"flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        getTypeIcon(item.type),\n                                                        getStatusIcon(item.status)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: item.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                            lineNumber: 448,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-4 text-sm text-gray-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: formatFileSize(item.size)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                                    lineNumber: 450,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"•\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                                    lineNumber: 451,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"Sync: \",\n                                                                        new Date(item.lastSync).toLocaleString(\"fr-FR\")\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                                    lineNumber: 452,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    className: getPriorityColor(item.priority),\n                                                                    children: item.priority\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                                    lineNumber: 453,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                            lineNumber: 449,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        item.status === \"downloading\" && downloadProgress[item.id] !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_5__.Progress, {\n                                                                    value: downloadProgress[item.id],\n                                                                    className: \"h-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                                    lineNumber: 461,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500 mt-1\",\n                                                                    children: [\n                                                                        \"T\\xe9l\\xe9chargement: \",\n                                                                        downloadProgress[item.id],\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                                    lineNumber: 462,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                item.status === \"error\" || item.status === \"pending\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    size: \"sm\",\n                                                    onClick: ()=>downloadItem(item.id),\n                                                    disabled: !syncStatus.isOnline,\n                                                    className: \"bg-blue-500 hover:bg-blue-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                            lineNumber: 478,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"T\\xe9l\\xe9charger\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 21\n                                                }, this) : item.status === \"synced\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>downloadItem(item.id),\n                                                    disabled: !syncStatus.isOnline,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                            lineNumber: 488,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Actualiser\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 21\n                                                }, this) : null,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>removeFromCache(item.id),\n                                                    children: \"Supprimer\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                    lineNumber: 493,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                            lineNumber: 470,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, item.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                            lineNumber: 433,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                        lineNumber: 432,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                lineNumber: 425,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_21__.AnimatePresence, {\n                children: !syncStatus.isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_20__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 50\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        y: 50\n                    },\n                    className: \"fixed bottom-4 right-4 z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-yellow-50 border-yellow-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Cloud_Database_Download_HardDrive_RefreshCw_Settings_Upload_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-5 w-5 text-yellow-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                        lineNumber: 519,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium text-yellow-900\",\n                                                children: \"Mode Hors Ligne\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                lineNumber: 521,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-yellow-700\",\n                                                children: \"Utilisation des donn\\xe9es en cache\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                        lineNumber: 520,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                                lineNumber: 518,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                            lineNumber: 517,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                        lineNumber: 516,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                    lineNumber: 510,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n                lineNumber: 508,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Pro Matos Afrique Ouest\\\\pro-matos-afrique-ouest\\\\src\\\\components\\\\advanced\\\\OfflineManager.tsx\",\n        lineNumber: 282,\n        columnNumber: 5\n    }, this);\n}\n_s(OfflineManager, \"hEhoeJc53JJhzOfhaWIHZsE9vFU=\");\n_c = OfflineManager;\nvar _c;\n$RefreshReg$(_c, \"OfflineManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/advanced/OfflineManager.tsx\n"));

/***/ })

});