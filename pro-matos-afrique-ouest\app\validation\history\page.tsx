'use client'

import { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Clock, 
  CheckCircle, 
  XCircle, 
  FileText, 
  Download,
  ArrowLeft,
  Calendar
} from 'lucide-react'
import { useAuthStore } from '@/lib/stores/authStore'
import { supabase } from '@/lib/supabase/client'
import { Validation } from '@/lib/types/database'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { formatDate } from '@/lib/utils'
import { toast } from 'sonner'
import DashboardLayout from '@/components/layout/DashboardLayout'
import Link from 'next/link'

export default function ValidationHistoryPage() {
  const { user } = useAuthStore()
  const [validations, setValidations] = useState<Validation[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (user) {
      fetchValidations()
    }
  }, [user])

  const fetchValidations = async () => {
    try {
      setLoading(true)

      const { data, error } = await supabase
        .from('validations')
        .select('*')
        .eq('user_id', user?.id)
        .order('created_at', { ascending: false })

      if (error) throw error

      setValidations(data || [])
    } catch (error) {
      console.error('Erreur lors du chargement des validations:', error)
      toast.error('Erreur lors du chargement de l\'historique')
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Validé':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'Refusé':
        return <XCircle className="h-5 w-5 text-red-500" />
      default:
        return <Clock className="h-5 w-5 text-yellow-500" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'Validé':
        return <Badge className="bg-green-100 text-green-800 border-green-200">Validé</Badge>
      case 'Refusé':
        return <Badge className="bg-red-100 text-red-800 border-red-200">Refusé</Badge>
      default:
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">En cours</Badge>
    }
  }

  const handleDownload = async (fileUrl: string, fileName: string) => {
    try {
      toast.loading('Téléchargement...', { id: 'download' })
      
      const { data, error } = await supabase.storage
        .from('validations')
        .download(fileUrl.replace('validations/', ''))

      if (error) throw error

      // Créer un lien de téléchargement
      const url = URL.createObjectURL(data)
      const a = document.createElement('a')
      a.href = url
      a.download = fileName
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      toast.success('Fichier téléchargé', { id: 'download' })
    } catch (error) {
      console.error('Erreur lors du téléchargement:', error)
      toast.error('Erreur lors du téléchargement', { id: 'download' })
    }
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center space-x-4"
        >
          <Link href="/validation">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Retour
            </Button>
          </Link>
          
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Historique des Validations</h1>
            <p className="text-gray-600">
              Consultez le statut de toutes vos demandes de validation
            </p>
          </div>
        </motion.div>

        {/* Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-4 gap-6"
        >
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <FileText className="h-8 w-8 text-blue-500" />
                <div>
                  <p className="text-2xl font-bold text-gray-900">{validations.length}</p>
                  <p className="text-sm text-gray-600">Total</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <Clock className="h-8 w-8 text-yellow-500" />
                <div>
                  <p className="text-2xl font-bold text-gray-900">
                    {validations.filter(v => v.status === 'En cours').length}
                  </p>
                  <p className="text-sm text-gray-600">En cours</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-8 w-8 text-green-500" />
                <div>
                  <p className="text-2xl font-bold text-gray-900">
                    {validations.filter(v => v.status === 'Validé').length}
                  </p>
                  <p className="text-sm text-gray-600">Validées</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <XCircle className="h-8 w-8 text-red-500" />
                <div>
                  <p className="text-2xl font-bold text-gray-900">
                    {validations.filter(v => v.status === 'Refusé').length}
                  </p>
                  <p className="text-sm text-gray-600">Refusées</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Validations List */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="space-y-4"
        >
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-500"></div>
            </div>
          ) : validations.length === 0 ? (
            <Card>
              <CardContent className="p-12 text-center">
                <FileText className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Aucune validation
                </h3>
                <p className="text-gray-600 mb-4">
                  Vous n'avez encore soumis aucune demande de validation.
                </p>
                <Link href="/validation">
                  <Button className="bg-amber-600 hover:bg-amber-700">
                    Créer une demande
                  </Button>
                </Link>
              </CardContent>
            </Card>
          ) : (
            validations.map((validation, index) => (
              <motion.div
                key={validation.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-4 flex-1">
                        {getStatusIcon(validation.status)}
                        
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <h3 className="text-lg font-semibold text-gray-900">
                              {validation.title}
                            </h3>
                            {getStatusBadge(validation.status)}
                          </div>
                          
                          <p className="text-gray-600 mb-3">
                            {validation.description}
                          </p>
                          
                          <div className="flex items-center space-x-4 text-sm text-gray-500">
                            <div className="flex items-center space-x-1">
                              <Calendar className="h-4 w-4" />
                              <span>Créé le {formatDate(validation.created_at)}</span>
                            </div>
                            
                            {validation.file_name && (
                              <div className="flex items-center space-x-1">
                                <FileText className="h-4 w-4" />
                                <span>{validation.file_name}</span>
                              </div>
                            )}
                          </div>
                          
                          {validation.admin_notes && (
                            <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                              <p className="text-sm font-medium text-gray-900 mb-1">
                                Notes de l'expert :
                              </p>
                              <p className="text-sm text-gray-600">
                                {validation.admin_notes}
                              </p>
                            </div>
                          )}
                        </div>
                      </div>
                      
                      <div className="ml-4 space-y-2">
                        {validation.file_url && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDownload(validation.file_url!, validation.file_name!)}
                          >
                            <Download className="h-4 w-4 mr-2" />
                            Télécharger
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))
          )}
        </motion.div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="flex justify-center"
        >
          <Link href="/validation">
            <Button className="bg-amber-600 hover:bg-amber-700">
              Nouvelle demande de validation
            </Button>
          </Link>
        </motion.div>
      </div>
    </DashboardLayout>
  )
}
